import { Provider } from 'react-redux';
import type { AppProps } from 'next/app';
import Head from 'next/head';
import { MsalProvider } from '@azure/msal-react';
import { config } from '@fortawesome/fontawesome-svg-core';
import '@/styles/tailwind.css';
import '@fortawesome/fontawesome-svg-core/styles.css';
import { Layout } from '@/components/layout';
import { msalInstance } from '@/services/auth';
import '@/services/fontawesome';
import { store } from '@/services/store';
import { NextPage } from 'next/types';
import { ReactElement, ReactNode } from 'react';

export type NextPageWithLayout<P = {}, IP = P> = NextPage<P, IP> & {
  getLayout?: (page: ReactElement) => ReactNode;
};

type AppPropsWithLayout = AppProps & {
  Component: NextPageWithLayout;
};

config.autoAddCss = false;

export default function App({ Component, pageProps }: AppPropsWithLayout) {
  // If the page has a getLayout function, use it to render the page
  const getLayout = Component.getLayout ?? ((page) => page);

  return (
    <>
      <Head>
        <meta
          name="viewport"
          content="width=device-width,initial-scale=1"
        ></meta>
      </Head>
      <MsalProvider instance={msalInstance}>
        <Provider store={store}>
          <Layout>{getLayout(<Component {...pageProps} />)}</Layout>
        </Provider>
      </MsalProvider>
    </>
  );
}
