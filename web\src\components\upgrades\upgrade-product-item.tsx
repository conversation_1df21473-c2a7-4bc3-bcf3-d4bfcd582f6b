import React, { Fragment, useState, useMemo } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import * as HeadlessUI from '@headlessui/react';
import * as futureOrders from 'api/models/future-orders';
import { usePermissions } from '@/services/auth';
import { useAppDispatch } from '@/services/hooks';
import { routes } from '@/services/routes';
import { Alert } from '@/components/alert';
import { Icon } from '@/components/icon';
import { getPackQuantity } from '@/components/future-orders/item-functions';
import { classNames } from '@/utils/class-names';
import { equals } from '@/utils/equals';
import { formatDate, formatNumber } from '@/utils/format';
import {
  setItemPropertyValue,
  setUpgradeConfirmed,
  setPriority,
  setUpcPrinted,
  setUpcUnprinted,
} from './upgrade-item-list-slice';
import { UpgradeItemContainerPickDescription } from './upgrade-item-field-container-pick-description';
import { UpgradeItemLabourHours } from './upgrade-item-field-labour-hours';
import { UpgradeItemField } from './upgrade-item-field';
import { UpgradeProductItemAttachments } from './upgrade-product-item-attachments';

interface UpgradeProductItemProps {
  items: futureOrders.UpgradeItem[];
  refetch: () => void;
}

export function UpgradeProductItem({
  items,
  refetch,
}: UpgradeProductItemProps) {
  const dispatch = useAppDispatch(),
    router = useRouter(),
    { can } = usePermissions(),
    [showFutureOrderWarning, setShowFutureOrderWarning] =
      useState<futureOrders.UpgradeItem | null>(null),
    [tooltipOpen, setTooltipOpen] = useState(false),
    [potCoverTooltipOpen, setPotCoverTooltipOpen] = useState(false),
    [showUpcReprintWarning, setShowUpcReprintWarning] = useState<number | null>(
      null
    ),
    [showUpcUnprintWarning, setShowUpcUnprintWarning] = useState<number | null>(
      null
    ),
    item = useMemo(() => items[0], [items]),
    upcDisplay = useMemo(
      () =>
        [
          item.upc,
          item.dateCode,
          item.retail,
          item.weightsAndMeasures ? 'W&M' : '',
        ]
          .filter((u) => u)
          .join(' | '),
      [item]
    ),
    totalOrderQuantity = useMemo(
      () => items.reduce((total, item) => total + item.orderQuantity, 0),
      [items]
    ),
    readonly = !can('Sales Team');

  const handleTooltipMouseEnter = () => {
    setTooltipOpen(true);
  };

  const handleTooltipMouseLeave = () => {
    setTooltipOpen(false);
  };

  const handlePotCoverTooltipMouseEnter = () => {
    setPotCoverTooltipOpen(true);
  };

  const handlePotCoverTooltipMouseLeave = () => {
    setPotCoverTooltipOpen(false);
  };

  const handleAddUpcApproval = () => {
    const propName: keyof futureOrders.UpgradeItem = 'upcApprovalRequired',
      args = { id: item.id, propName, value: true };
    dispatch(setItemPropertyValue(args));
  };

  const handleAddUpcRemoveApproval = () => {
    const propName: keyof futureOrders.UpgradeItem = 'upcApprovalRequired',
      args = { id: item.id, propName, value: false };
    dispatch(setItemPropertyValue(args));
  };

  const handlePrebookLinkClick = (
    e: React.MouseEvent,
    item: futureOrders.UpgradeItem
  ) => {
    if (item.futureOrderId) {
      e.preventDefault();

      setShowFutureOrderWarning(item);
    }
  };

  const handleEditPrebook = () => {
    if (showFutureOrderWarning) {
      setShowFutureOrderWarning(null);
      router.push(routes.prebooks.detail.to(showFutureOrderWarning.prebookId));
    }
  };

  const handleEditFutureOrder = () => {
    if (showFutureOrderWarning) {
      setShowFutureOrderWarning(null);
      if (showFutureOrderWarning.futureOrderId) {
        router.push(
          routes.futureOrders.detail.to(showFutureOrderWarning.futureOrderId)
        );
      }
    }
  };

  const handleSetUpgradeConfirmedClick = async () => {
    await Promise.all(
      items.map(({ id }) => {
        return dispatch(setUpgradeConfirmed(id));
      })
    );

    setTooltipOpen(false);
    console.log('Refetching from Upgrade Product Item Confirmed');
    refetch();
  };

  const handleChangePriority = (id: number, priority: boolean) => {
    dispatch(setPriority({ id, priority }));
  };

  const handleUpcPrinted = (id: number) => {
    if (item.upcPrinted) {
      setShowUpcUnprintWarning(id);
    } else {
      if (item.upcPrintedPrev) {
        setShowUpcReprintWarning(id);
      } else {
        dispatch(setUpcPrinted(id));
      }
    }
  };

  const handleUpcReprintCancel = () => {
    setShowUpcReprintWarning(null);
  };

  const handleUpcReprintConfirm = () => {
    if (showUpcReprintWarning) {
      dispatch(setUpcPrinted(showUpcReprintWarning));
      setShowUpcReprintWarning(null);
    }
  };

  const handleUpcUnprintCancel = () => {
    setShowUpcUnprintWarning(null);
  };

  const handleUpcUnprintConfirm = () => {
    if (showUpcUnprintWarning) {
      dispatch(setUpcUnprinted(showUpcUnprintWarning));
      setShowUpcUnprintWarning(null);
    }
  };

  return (
    <tr
      className={classNames(
        'border-b text-center text-xs font-normal text-gray-700',
        item.upgradeConfirmed
          ? 'border-gray-200'
          : 'border-yellow-500 text-yellow-500',
        equals(item.productComingFrom, 'upgrades') ? 'bg-yellow-100' : ''
      )}
    >
      <td className="whitespace-nowrap py-2 align-top">
        <div className="flex flex-row">
          <button
            type="button"
            className="small secondary rounded px-2"
            onClick={() => handleChangePriority(item.id, !item.priority)}
          >
            <FontAwesomeIcon
              icon={[item.priority ? 'fas' : 'fal', 'star']}
              className={classNames(
                item.priority ? 'text-yellow-500' : 'text-gray-300'
              )}
            />
          </button>
          <button
            type="button"
            className="small secondary rounded px-2"
            onClick={() => handleUpcPrinted(item.id)}
          >
            <Icon
              icon="barcode"
              className={classNames(item.upcPrinted ? '' : 'text-gray-300')}
            />
          </button>
          <div>
            <HeadlessUI.Popover
              className="inline-block cursor-pointer md:relative"
              onMouseEnter={handleTooltipMouseEnter}
              onMouseLeave={handleTooltipMouseLeave}
            >
              <>
                <HeadlessUI.Popover.Button
                  as="div"
                  className="font-lg px-2 py-1"
                >
                  <Icon
                    icon={
                      item.upgradeConfirmed
                        ? 'check-circle'
                        : 'triangle-exclamation'
                    }
                    className={
                      item.upgradeConfirmed
                        ? 'text-green-600'
                        : 'text-yellow-600'
                    }
                  />
                </HeadlessUI.Popover.Button>
                <HeadlessUI.Transition
                  as={Fragment}
                  show={tooltipOpen}
                  enter="transition ease-out duration-200"
                  enterFrom="opacity-0 translate-y-1"
                  enterTo="opacity-100 translate-y-0"
                  leave="transition ease-in duration-150"
                  leaveFrom="opacity-100 translate-y-0"
                  leaveTo="opacity-0 translate-y-1"
                >
                  <HeadlessUI.Popover.Panel
                    static
                    className="absolute left-[30px] z-10 -translate-y-1/2 transform bg-white"
                  >
                    <div className="whitespace-nowrap rounded-lg border p-4 shadow-lg">
                      {!item.upgradeConfirmed && (
                        <p className="my-2 text-xs font-bold italic text-yellow-500">
                          <span className="mr-4 inline-block">
                            Container / Pick has not been confirmed
                          </span>
                          {!readonly && (
                            <button
                              type="button"
                              onClick={handleSetUpgradeConfirmedClick}
                              className="btn-secondary px-2 py-1 text-xs"
                            >
                              Confirm
                            </button>
                          )}
                        </p>
                      )}
                      {!!item.upgradeConfirmed && (
                        <p className="my-2 text-xs text-gray-500">
                          Container / Pick Confirmed by
                          <br />
                          <span className="font-medium">
                            {item.upgradeConfirmedBy}
                          </span>
                          <br />
                          on{' '}
                          <span className="font-medium">
                            {formatDate(item.upgradeConfirmed)}
                          </span>{' '}
                          @{' '}
                          <span className="font-medium">
                            {formatDate(item.upgradeConfirmed, 'h:mm a')}
                          </span>
                        </p>
                      )}
                    </div>
                  </HeadlessUI.Popover.Panel>
                </HeadlessUI.Transition>
              </>
            </HeadlessUI.Popover>
            <UpgradeProductItemAttachments items={items} />
          </div>
        </div>
      </td>
      <td className="cursor-default py-2 align-top">
        {formatNumber(totalOrderQuantity)}
        {item.isApproximate && (
          <span title="Quantity is approximate" className="cursor-pointer">
            &nbsp;
            <Icon icon="plus-minus" />
          </span>
        )}
      </td>
      <td className="cursor-default py-2 align-top">{getPackQuantity(item)}</td>
      <td className="p-2 px-1 text-left text-gray-900">
        <div className="flex max-w-xs cursor-default">
          <div className="flex-grow truncate">{item.description}</div>
          {!!item.potCover && (
            <div>
              <HeadlessUI.Popover
                className="inline-block cursor-pointer md:relative"
                onMouseEnter={handlePotCoverTooltipMouseEnter}
                onMouseLeave={handlePotCoverTooltipMouseLeave}
              >
                <>
                  <HeadlessUI.Popover.Button
                    as="div"
                    className="font-lg px-2 py-1"
                  >
                    <Icon icon="can-food" />
                  </HeadlessUI.Popover.Button>
                  <HeadlessUI.Transition
                    as={Fragment}
                    show={potCoverTooltipOpen}
                    enter="transition ease-out duration-200"
                    enterFrom="opacity-0 translate-y-1"
                    enterTo="opacity-100 translate-y-0"
                    leave="transition ease-in duration-150"
                    leaveFrom="opacity-100 translate-y-0"
                    leaveTo="opacity-0 translate-y-1"
                  >
                    <HeadlessUI.Popover.Panel
                      static
                      className="absolute left-[30px] z-10 -translate-y-1/2 transform bg-white"
                    >
                      <div className="whitespace-nowrap rounded-lg border p-4 shadow-lg">
                        {item.potCover}
                      </div>
                    </HeadlessUI.Popover.Panel>
                  </HeadlessUI.Transition>
                </>
              </HeadlessUI.Popover>
            </div>
          )}
        </div>
        {!!item.comments && (
          <div className="max-w-xs cursor-default italic text-gray-500">
            {item.comments}
          </div>
        )}
        {!!item.growerItemNotes && (
          <div className="max-w-xs cursor-default italic text-gray-500">
            {item.growerItemNotes}
          </div>
        )}
        {!!item.itemGrowerItemNotes && (
          <div className="max-w-xs cursor-default italic text-gray-500">
            {item.itemGrowerItemNotes}
          </div>
        )}
        <div className="mt-1 flex max-w-xs">
          <UpgradeItemField
            ids={items.map(({ id }) => id)}
            propName="upgradeComments"
            value={item.upgradeComments}
            multiline
            left
          />
        </div>
      </td>
      <td className="px-1 py-2 text-left align-top">
        <UpgradeItemField
          ids={items.map(({ id }) => id)}
          propName="productComingFrom"
          value={item.productComingFrom}
          multiline
        />
      </td>
      <td className="px-1 py-2 align-top">
        <div className="cursor-default whitespace-nowrap">{upcDisplay}</div>
        {item.upcApprovalRequired && (
          <div className="font-semi mx-1">
            Approval required &nbsp;
            <button
              type="button"
              className="btn-secondary border-transparent px-2 py-0 text-xs text-red-500 shadow-none"
              onClick={handleAddUpcRemoveApproval}
            >
              <Icon icon="x" />
            </button>
          </div>
        )}
        {!item.upcApprovalRequired && (
          <div
            className="inline cursor-pointer border-gray-300 hover:border-gray-500 hover:bg-yellow-100"
            onClick={handleAddUpcApproval}
          >
            <div className="mx-auto inline-block border-b-2 border-dotted">
              <Icon icon="plus" />
            </div>
          </div>
        )}
      </td>
      <td className="px-1 py-2 align-top">
        <UpgradeItemContainerPickDescription item={item} />
      </td>
      <td className="px-1 py-2 align-top">
        <UpgradeItemField
          ids={items.map(({ id }) => id)}
          propName="origins"
          value={item.origins}
          textboxClassName="w-16"
          defaultDisplay="NA"
        />
      </td>
      <td className="px-1 py-2 align-top">
        <UpgradeItemField
          ids={items.map(({ id }) => id)}
          propName="costs"
          value={item.costs}
          textboxClassName="w-24 text-center"
          defaultDisplay="NA"
        />
      </td>
      <td className="cursor-default px-1 py-2 align-top">
        {items.length === 1 && item.boxCode}

        {items.length > 1 &&
          items.map((i) => (
            <div key={i.id}>
              {i.boxCode}&nbsp;&ndash;&nbsp;{i.orderQuantity}
            </div>
          ))}
      </td>
      <td className="px-1 py-2 align-top">
        <UpgradeItemLabourHours items={items} />
      </td>
      <td className="py-2 align-top">
        {items.map((i) => (
          <Link
            key={i.id}
            className="block"
            href={routes.prebooks.detail.to(i.prebookId)}
            onClick={(e) => handlePrebookLinkClick(e, i)}
          >
            {formatNumber(i.prebookId, '00000')}
          </Link>
        ))}
      </td>
      <Alert
        open={!!showFutureOrderWarning}
        icon="question-circle"
        title="Edit Prebook"
        message="Making changes to this Prebook will not update the Future Order. What would you like to do?"
        colour="info"
        confirmButtonText="Continue to Prebook"
        confirm={handleEditPrebook}
        cancelButtonText="Edit Future Order"
        cancel={handleEditFutureOrder}
      />
      <Alert
        open={!!showUpcReprintWarning}
        colour="danger"
        title="UPC Already Printed"
        message="The UPCs for this item had already been printed prior to the most-recent change, please be sure to discard of the original UPC’s."
        confirm={handleUpcReprintConfirm}
        cancel={handleUpcReprintCancel}
      />
      <Alert
        open={!!showUpcUnprintWarning}
        colour="danger"
        title="Unprint UPCs?"
        message="Are you sure you want to unprint the UPCs for this item?"
        confirm={handleUpcUnprintConfirm}
        cancel={handleUpcUnprintCancel}
      />
    </tr>
  );
}
