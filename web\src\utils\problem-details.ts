export interface ValidationErrors {
  [errorMember: string]: string[];
}

export interface ProblemDetails {
  title: string;
  status: number;
  detail: string;
  instance: string;
  errors?: ValidationErrors;
}

export function createProblemDetails(
  title: string,
  detail: string = '',
  status = 0,
  instance: string = ''
): ProblemDetails {
  return { title, detail, status, instance };
}

export function isProblemDetails(value: any): value is ProblemDetails {
  return (
    value &&
    (value.title === undefined || typeof value.title === 'string') &&
    (value.status === undefined || typeof value.status === 'number') &&
    (value.detail === undefined || typeof value.detail === 'string') &&
    (value.instance === undefined || typeof value.instance === 'string') &&
    (value.title || value.detail)
  );
}
