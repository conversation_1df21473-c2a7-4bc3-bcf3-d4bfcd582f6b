import React, { useState, useRef } from 'react';
import * as futureOrders from 'api/models/future-orders';
import { useAppDispatch } from '@/services/hooks';
import { Icon } from '@/components/icon';
import { classNames } from '@/utils/class-names';
import { handleFocus } from '@/utils/focus';
import { SetItemArgs, setItemPropertyValue } from './upgrade-item-list-slice';
import { parseQuantity } from '@/utils/format';

interface UpgradeItemFieldProps {
  ids: number[];
  propName: keyof futureOrders.UpgradeItem;
  value: string | null;
  className?: string;
  textboxClassName?: string;
  defaultDisplay?: string;
  multiline?: boolean;
  numeric?: boolean;
  left?: boolean;
}

export function UpgradeItemField({
  ids,
  className,
  textboxClassName,
  propName,
  value,
  defaultDisplay,
  multiline,
  numeric,
  left,
}: UpgradeItemFieldProps) {
  const dispatch = useAppDispatch(),
    [editing, setEditing] = useState(false),
    [editValue, setEditValue] = useState(''),
    editRef = useRef<HTMLInputElement>(null),
    editMultilineRef = useRef<HTMLTextAreaElement>(null),
    displayValue = value == null || value === '' ? defaultDisplay : value;

  const handleEditClick = () => {
    setEditing(true);
    setEditValue(value || '');

    window.setTimeout(() => {
      editRef.current?.focus();
      editMultilineRef.current?.focus();
    }, 100);
  };

  const handleCancelClick = () => {
    setEditing(false);
  };

  const handleSaveClick = async () => {
    const value = numeric ? parseQuantity(editValue) : editValue;
    await Promise.all(
      ids.map((id) => {
        const args: SetItemArgs = { id, propName, value };
        return dispatch(setItemPropertyValue(args));
      })
    );

    setEditing(false);
  };

  const handleEditChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setEditValue(e.target.value);
  };

  const handleEditKeyUp = (e: React.KeyboardEvent) => {
    if (!multiline && e.key === 'Enter') {
      e.preventDefault();
      handleSaveClick();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancelClick();
    }
  };

  if (editing) {
    return (
      <div
        className={classNames(
          'flex flex-grow rounded-md',
          !multiline && 'height-[25px]'
        )}
      >
        <div className="relative flex flex-grow focus-within:z-10">
          {multiline && (
            <textarea
              rows={3}
              ref={editMultilineRef}
              value={editValue}
              onChange={handleEditChange}
              onKeyUp={handleEditKeyUp}
              onFocus={handleFocus}
              className={classNames(
                textboxClassName,
                'flex flex-grow rounded border-0 py-0 text-xs text-gray-900 ring-1 ring-inset ring-gray-300 focus:border-blue-500'
              )}
            ></textarea>
          )}
          {!multiline && (
            <input
              type="text"
              ref={editRef}
              value={editValue}
              onChange={handleEditChange}
              onKeyUp={handleEditKeyUp}
              onFocus={handleFocus}
              className={classNames(
                textboxClassName,
                'flex flex-grow rounded border-0 py-0 text-xs text-gray-900 ring-1 ring-inset ring-gray-300 focus:border-blue-500'
              )}
            />
          )}
        </div>
        <button
          type="button"
          className={classNames(
            'btn-secondary px-2',
            multiline ? 'flex self-start py-1' : 'py-0'
          )}
          onClick={handleCancelClick}
        >
          <Icon icon="undo" />
        </button>
        <button
          type="button"
          className={classNames(
            'btn-secondary border-green-700 px-2',
            multiline ? 'flex self-start py-1' : 'py-0'
          )}
        >
          <Icon
            icon="check"
            className="text-green-900"
            onClick={handleSaveClick}
          />
        </button>
      </div>
    );
  }

  return (
    <div
      className={classNames(
        className,
        multiline && 'w-full whitespace-pre',
        displayValue && 'block border-b-2 border-dotted',
        left ? 'text-left' : 'text-center',
        'block max-w-xs cursor-pointer truncate border-gray-300 hover:border-gray-500 hover:bg-yellow-100'
      )}
      onClick={handleEditClick}
    >
      {displayValue}
      {!displayValue && (
        <div className="mx-auto inline-block border-b-2 border-dotted">
          <Icon icon="plus" />
        </div>
      )}
    </div>
  );
}
