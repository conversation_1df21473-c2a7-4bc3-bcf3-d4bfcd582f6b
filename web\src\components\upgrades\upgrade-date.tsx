import { Fragment, useMemo } from 'react';
import { useAppDispatch } from '@/services/hooks';
import { Icon } from '@/components/icon';
import { formatDate, formatNumber } from '@/utils/format';
import {
  downloadUpgradeItemReport,
  setPrintWithAdditionsDate,
  UpgradeItemDate,
} from './upgrade-item-list-slice';
import { UpgradeProductItem } from './upgrade-product-item';
import { matchesBoxCode } from './upgrade-functions';
import { DateTime } from 'luxon';

interface UpgradeDateProps {
  date: UpgradeItemDate;
  refetch: () => void;
}

export function UpgradeDate({
  date: upgradeItemDate,
  refetch,
}: UpgradeDateProps) {
  const dispatch = useAppDispatch(),
    { date, season, itemGroups } = upgradeItemDate,
    labourHours = useMemo(
      () =>
        itemGroups.reduce((total, items) => {
          const maxLabourHours = Math.max(
              ...items.map(({ labourHours }) => labourHours || 0)
            ),
            totalOrderQuantity = items.reduce(
              (total, item) => total + item.orderQuantity,
              0
            ),
            rawHours =
              maxLabourHours === 0 ? 0 : totalOrderQuantity / maxLabourHours,
            totalLabourHours = Math.round(
              rawHours > 0 && rawHours < 1 ? 1 : rawHours
            );
          return total + totalLabourHours;
        }, 0),
      [itemGroups]
    );

  const handlePrintClick = () => {
    const items = itemGroups.flatMap((group) => group),
      suffix = date
        ? DateTime.fromFormat(date, 'yyyy-MM-dd').toFormat('MM-dd-yyyy')
        : DateTime.now().toFormat('MM-dd-yyyy'),
      args = {
        filename: `Upgrade Sheet ${suffix}.pdf`,
        items,
      };
    dispatch(downloadUpgradeItemReport(args));
  };

  const handlePrintWithAdditionsClick = () => {
    dispatch(setPrintWithAdditionsDate(date));
  };

  return (
    <tbody className="odd:bg-gray-100 even:bg-white">
      <tr className="sticky top-[54px] z-10 border-b bg-gray-200">
        <th colSpan={10} className="text-gray-7000 p-2 text-left text-sm">
          {season || formatDate(date, 'EEEE MMM d')}
          <button
            type="button"
            className="btn-secondary ml-4 bg-transparent"
            onClick={handlePrintClick}
          >
            <Icon icon="print" />
          </button>
          <button
            type="button"
            className="btn-secondary ml-2 bg-transparent"
            onClick={handlePrintWithAdditionsClick}
          >
            <Icon icon="print" />
            &nbsp;
            <Icon icon="plus" />
          </button>
        </th>
        <th className="px-1 py-2 text-center text-sm text-gray-700">
          {formatNumber(labourHours)}
        </th>
        <th>&nbsp;</th>
      </tr>
      {itemGroups.map((group, index) => (
        <Fragment key={group.map((p) => p.id).join('|')}>
          <UpgradeProductItem items={group} refetch={refetch} />
          {!matchesBoxCode(group, itemGroups[index + 1]) && (
            <tr className="border-b-2 border-black text-[0px]">
              <td colSpan={12}>&nbsp;</td>
            </tr>
          )}
        </Fragment>
      ))}
    </tbody>
  );
}
