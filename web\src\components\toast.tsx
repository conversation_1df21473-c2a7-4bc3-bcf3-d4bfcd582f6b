import { Fragment } from 'react';
import * as Headless<PERSON> from '@headlessui/react';
import { useAppDispatch } from '@/services/hooks';
import { classNames } from '@/utils/class-names';
import { Icon } from './icon';
import { ToastMessage, removeToast } from './toaster-slice';

interface ToastProps {
  message: ToastMessage;
}

export function Toast({ message }: ToastProps) {
  const dispatch = useAppDispatch();

  const handleCloseClick = () => {
    dispatch(removeToast(message.message));
  };
  return (
    <HeadlessUI.Transition
      show={true}
      as={Fragment}
      enter="transition duration-1000 ease-in-out"
      enterFrom="opacity-0 scale-50"
      enterTo="opacity-100 scale-100"
      leave="transition duration-1000 ease-in-out"
      leaveFrom="opacity-100"
      leaveTo="opacity-0"
    >
      <div
        className={classNames(
          'pointer-events-auto w-full max-w-sm overflow-hidden rounded-lg border bg-white shadow-lg ring-1 ring-black ring-opacity-5',
          message.type === 'success' ? 'border-green-500' : 'border-red-600'
        )}
      >
        <div className="p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <Icon
                icon={
                  message.type === 'success' ? 'circle-check' : 'circle-xmark'
                }
                className={classNames(
                  'h-6 w-6',
                  message.type === 'success' ? 'text-green-400' : 'text-red-600'
                )}
              />
            </div>
            <div className="ml-3 w-0 flex-1 pt-0.5">
              {!!message.title && (
                <p className="text-sm font-medium text-gray-900">
                  {message.title}
                </p>
              )}
              <p className="mt-1 text-sm text-gray-500">{message.message}</p>
            </div>
            <div className="ml-4 flex flex-shrink-0">
              <button
                type="button"
                className="inline-flex"
                onClick={handleCloseClick}
              >
                <span className="sr-only">Close</span>
                <Icon icon="x" className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </HeadlessUI.Transition>
  );
}
