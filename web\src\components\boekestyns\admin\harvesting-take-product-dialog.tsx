import { useState, Fragment, useMemo } from 'react';
import * as HeadlessUI from '@headlessui/react';
import { Icon } from '@/components/icon';
import { useStickingOrdersQuery } from 'api/boekestyn-sticking-service';
import { StickingOrder } from 'api/models/boekestyns';
import { TakeProductArgs } from 'api/boekestyn-harvesting-service';

interface HarvestingTakeProductDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (args: TakeProductArgs) => void;
}

export function HarvestingTakeProductDialog({
  open,
  onClose,
  onSave,
}: HarvestingTakeProductDialogProps) {
  const [lotNumber, setLotNumber] = useState('');
  const [quantity, setQuantity] = useState<number>(0);
  const [lotQuery, setLotQuery] = useState('');

  const {data: allStickingOrders} = useStickingOrdersQuery({ startDate: '', endDate: '' });
  const allLotNumbers = useMemo(() => 
    allStickingOrders?.orders.map((o) => o.orderNumber) || [], 
  [allStickingOrders?.orders]);

  console.log(allStickingOrders);

  // Filter lot numbers based on search query
  const filteredLotNumbers = lotQuery
    ? allLotNumbers.filter((lot) =>
        lot.toLowerCase().includes(lotQuery.toLowerCase())
      )
    : allLotNumbers;

  const handleTransitionAfterEnter = () => {
    setLotNumber('');
    setQuantity(0);
    setLotQuery('');
  };

  const handleLotNumberChange = (value: string) => {
    setLotNumber(value);
    setLotQuery(value);
  };

  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setQuantity(e.target.valueAsNumber || 0);
  };

  const handleSaveClick = () => {
    if (lotNumber && quantity > 0) {
      onSave({ lotNumber, quantity });
      onClose();
    }
  };

  const handleCancelClick = () => {
    onClose();
  };

  return (
    <HeadlessUI.Transition.Root
      show={open}
      as={Fragment}
      afterEnter={handleTransitionAfterEnter}
    >
      <HeadlessUI.Dialog as="div" className="relative z-10" onClose={onClose}>
        <HeadlessUI.Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </HeadlessUI.Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <HeadlessUI.Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-0 scale-95"
              enterTo="opacity-100 translate-y-0 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 scale-100"
              leaveTo="opacity-0 translate-y-0 scale-95"
            >
              <HeadlessUI.Dialog.Panel className="relative my-8 w-full max-w-md transform overflow-hidden rounded-lg bg-white p-6 text-left shadow-xl transition-all">
                <div>
                  <div className="flex items-center">
                    <div className="flex flex-col">
                      <HeadlessUI.Dialog.Title
                        as="h3"
                        className="text-lg font-medium leading-6 text-gray-900"
                      >
                        Take Product
                      </HeadlessUI.Dialog.Title>
                    </div>
                  </div>

                  <form className="mt-6 space-y-4">
                    <div>
                      <HeadlessUI.Combobox
                        value={lotNumber}
                        onChange={handleLotNumberChange}
                      >
                        <HeadlessUI.Combobox.Label className="block text-sm font-medium text-gray-700">
                          Lot Number
                        </HeadlessUI.Combobox.Label>
                        <div className="relative mt-1">
                          <HeadlessUI.Combobox.Input
                            type="search"
                            className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                            placeholder="Search lot numbers..."
                            autoComplete="off"
                            onChange={(e) => setLotQuery(e.target.value)}
                            displayValue={(value: string) => value}
                          />
                          <HeadlessUI.Combobox.Button className="absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none">
                            <Icon
                              icon="caret-down"
                              className="h-5 w-5 text-gray-400"
                              aria-hidden="true"
                            />
                          </HeadlessUI.Combobox.Button>
                        </div>

                        {filteredLotNumbers.length > 0 && (
                          <HeadlessUI.Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                            {filteredLotNumbers.map((lot) => (
                              <HeadlessUI.Combobox.Option
                                key={lot}
                                value={lot}
                                className={({ active }) =>
                                  `relative cursor-default select-none py-2 pl-3 pr-9 ${
                                    active
                                      ? 'bg-blue-600 text-white'
                                      : 'text-gray-900'
                                  }`
                                }
                              >
                                {({ active, selected }) => (
                                  <>
                                    <span
                                      className={`block truncate ${
                                        selected ? 'font-semibold' : 'font-normal'
                                      }`}
                                    >
                                      {lot}
                                    </span>
                                    {selected && (
                                      <span
                                        className={`absolute inset-y-0 right-0 flex items-center pr-4 ${
                                          active ? 'text-white' : 'text-blue-600'
                                        }`}
                                      >
                                        <Icon
                                          icon="check"
                                          className="h-5 w-5"
                                          aria-hidden="true"
                                        />
                                      </span>
                                    )}
                                  </>
                                )}
                              </HeadlessUI.Combobox.Option>
                            ))}
                          </HeadlessUI.Combobox.Options>
                        )}
                      </HeadlessUI.Combobox>
                    </div>

                    {/* Quantity Input */}
                    <div>
                      <label
                        htmlFor="quantity"
                        className="block text-sm font-medium text-gray-700"
                      >
                        Quantity
                      </label>
                      <input
                        type="number"
                        id="quantity"
                        name="quantity"
                        min="0"
                        step="1"
                        className="mt-1 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                        placeholder="Enter quantity"
                        value={quantity || ''}
                        onChange={handleQuantityChange}
                      />
                    </div>
                  </form>
                </div>

                {/* Action Buttons */}
                <div className="mt-6 flex justify-end space-x-3">
                  <button
                    type="button"
                    className="btn-secondary"
                    onClick={handleCancelClick}
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn-primary"
                    onClick={handleSaveClick}
                    disabled={!lotNumber || quantity <= 0}
                  >
                    Save
                  </button>
                </div>
              </HeadlessUI.Dialog.Panel>
            </HeadlessUI.Transition.Child>
          </div>
        </div>
      </HeadlessUI.Dialog>
    </HeadlessUI.Transition.Root>
  );
}