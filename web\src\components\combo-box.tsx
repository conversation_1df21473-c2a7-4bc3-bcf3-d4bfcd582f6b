import { useState, MutableRefObject } from 'react';
import * as HeadlessUI from '@headlessui/react';
import { ComboboxItem } from './combo-box-item';
import { Icon } from './icon';

interface ComboboxProps<T extends object | string> {
  value: T | null;
  onChange: (value: T | null) => void;
  label?: string;
  required?: boolean;
  filter: (query: string, value: T) => boolean;
  collection: T[] | undefined;
  nullDisplayText?: string;
  idProp?: string;
  displayTextProp?: string;
  secondaryDisplayTextProp?: keyof T & string;
  inputRef?: MutableRefObject<HTMLInputElement | null>;
  autofocus?: boolean;
  disabled?: boolean;
  allowCustom?: boolean;
}

export function Combobox<T extends object | string>({
  value,
  onChange,
  label,
  required,
  filter,
  nullDisplayText,
  collection,
  idProp = 'id',
  displayTextProp = 'name',
  secondaryDisplayTextProp,
  inputRef,
  autofocus,
  disabled,
  allowCustom,
}: ComboboxProps<T>) {
  const [query, setQuery] = useState(''),
    sureCollection = disabled ? [] : collection || [],
    filteredCollection = query
      ? sureCollection.filter((item) => filter(query, item)) || []
      : sureCollection;
  return (
    <HeadlessUI.Combobox
      as="div"
      value={value}
      onChange={onChange}
      nullable
      disabled={disabled}
    >
      {!!label && (
        <HeadlessUI.Combobox.Label className="block text-sm font-medium text-gray-500">
          {label}
          {required && (
            <>
              &nbsp;<span className="text-red-500">*</span>
            </>
          )}
        </HeadlessUI.Combobox.Label>
      )}
      <div className="relative mt-1">
        <HeadlessUI.Combobox.Input
          type={disabled ? 'text' : 'search'}
          className="w-full rounded-md border border-gray-300 bg-white py-2 pl-3 pr-10 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 sm:text-sm"
          onChange={(e) => setQuery(e.target.value)}
          displayValue={(o: object | string) => getProp(o, displayTextProp)}
          autoComplete="off"
          ref={inputRef}
          autoFocus={autofocus}
        />
        {!disabled && (
          <HeadlessUI.Combobox.Button className="absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none">
            <Icon
              icon="caret-down"
              className="h-5 w-5 text-gray-400"
              aria-hidden="true"
            />
          </HeadlessUI.Combobox.Button>
        )}

        <HeadlessUI.Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full max-w-lg overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
          {!!nullDisplayText && <ComboboxItem displayText={nullDisplayText} />}
          {allowCustom && query.length > 0 && (
            <ComboboxItem
              value={{ [idProp]: null, [displayTextProp]: query }}
              displayText={query}
            />
          )}
          {filteredCollection.length > 0 &&
            filteredCollection.map((item) => (
              <ComboboxItem
                key={getProp(item, idProp)}
                value={item}
                displayText={getProp(item, displayTextProp)}
                secondaryDisplayText={
                  secondaryDisplayTextProp
                    ? getProp(item, secondaryDisplayTextProp)
                    : ''
                }
              />
            ))}
        </HeadlessUI.Combobox.Options>
      </div>
    </HeadlessUI.Combobox>
  );
}

function getProp(
  value: { [propName: string]: any } | string | null | undefined,
  propName: string,
  defaultValue: string = ''
): string {
  if (value == null) {
    return defaultValue;
  }

  if (typeof value === 'string') {
    return value;
  }

  const id = value[propName] || defaultValue;
  return id.toString();
}
