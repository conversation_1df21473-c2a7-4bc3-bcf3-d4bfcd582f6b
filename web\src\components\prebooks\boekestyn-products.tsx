import React, { Fragment, useEffect } from 'react';
import * as HeadlessUI from '@headlessui/react';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import { parsePackQuantity } from '@/components/future-orders/item-functions';
import * as settings from 'api/models/settings';
import { createProblemDetails } from '@/utils/problem-details';
import {
  selectShowDialog,
  selectError,
  setError,
  setShowDialog,
  getData,
  selectBoekestynProducts,
  setBoekestynProducts,
  BoekestynProduct,
} from './boekestyn-product-slice';
import { BoekestynProductsMultipleItem } from './boekestyn-products-multiple-item';

interface BoekestynProductsProps {
  onSave: (itemId: number, boekestynProducts: BoekestynProduct[]) => void;
  productDefaults: settings.ProductDefault[];
}

export function BoekestynProducts({
  onSave,
  productDefaults,
}: BoekestynProductsProps) {
  const dispatch = useAppDispatch(),
    boekestynProducts = useAppSelector(selectBoekestynProducts),
    showDialog = useAppSelector(selectShowDialog),
    error = useAppSelector(selectError),
    prebookItemId = showDialog?.id || 0,
    itemDescription = showDialog?.description || '',
    packQuantity = parsePackQuantity(itemDescription),
    productDefault = productDefaults.find(
      (d) => d.spireInventoryId === showDialog?.spireInventoryId
    );

  useEffect(() => {
    dispatch(getData());
  }, [dispatch]);

  const handleAddMappingClick = () => {
    const id = boekestynProducts.reduce((min, p) => Math.min(min, p.id), 0) - 1;
    dispatch(
      setBoekestynProducts(
        boekestynProducts.concat({
          id,
          prebookItemId,
          boekestynPlantId: '',
          boekestynCustomerAbbreviation: null,
          quantityPerFinishedItem: 1,
        })
      )
    );
  };

  const handleCancelClick = () => {
    dispatch(setShowDialog(null));
  };

  const handleSaveClick = async () => {
    if (!boekestynProducts.length) {
      return dispatch(
        setError(
          createProblemDetails(
            'Please ensure there is at least one plant mapping.'
          )
        )
      );
    }

    if (boekestynProducts.some((m) => !m.boekestynPlantId)) {
      return dispatch(
        setError(
          createProblemDetails(
            'Please ensure all mappings have a Boekestyn Plant Id selected.'
          )
        )
      );
    }
    if (boekestynProducts.some((m) => !m.quantityPerFinishedItem)) {
      return dispatch(
        setError(
          createProblemDetails(
            'Please ensure all mappings have a Qty / Finished Item set.'
          )
        )
      );
    }

    if (
      !productDefault?.ignoreOverrideQuantity &&
      boekestynProducts.reduce(
        (total, m) => total + m.quantityPerFinishedItem,
        0
      ) !== packQuantity
    ) {
      return dispatch(
        setError(
          createProblemDetails(
            "Please ensure the total of all mappings' Qty / Finished Item equals the Pack Quantity of the product."
          )
        )
      );
    }

    dispatch(setError(null));

    if (showDialog) {
      onSave(showDialog.id, boekestynProducts);
    }
    dispatch(setShowDialog(null));
  };

  const handleClose = () => {
    dispatch(setShowDialog(null));
  };

  const handleClearErrorClick = () => {
    dispatch(setError(null));
  };

  return (
    <HeadlessUI.Transition.Root show={!!showDialog} as={Fragment}>
      <HeadlessUI.Dialog
        as="div"
        className="relative z-30"
        onClose={handleClose}
      >
        <HeadlessUI.Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </HeadlessUI.Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex items-center justify-center p-0 text-center">
            <HeadlessUI.Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-0 scale-95"
              enterTo="opacity-100 translate-y-0 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 scale-100"
              leaveTo="opacity-0 translate-y-0 scale-95"
            >
              <HeadlessUI.Dialog.Panel className="relative h-screen w-screen transform overflow-hidden p-6 transition-all">
                <div
                  className="mx-auto flex max-w-3xl flex-col overflow-y-auto rounded-lg bg-white text-left shadow-xl"
                  style={{ maxHeight: 'calc(100vh - 3rem)' }}
                >
                  <div className="m-4 flex justify-center border-b-2 pb-4">
                    <HeadlessUI.Dialog.Title
                      as="h3"
                      className="text-lg font-medium leading-6 text-gray-900"
                    >
                      <Icon
                        icon="boxes-stacked"
                        className="h-6 w-6"
                        aria-hidden="true"
                      />
                      &nbsp; Edit Boekestyn Product Mappings
                    </HeadlessUI.Dialog.Title>
                  </div>
                  <div className="m-4 flex flex-grow flex-col overflow-y-auto">
                    <form className="flex w-full overflow-y-auto">
                      <div className="flex w-full flex-col overflow-y-auto">
                        <label className="mt-2 block pt-2 text-sm font-medium text-gray-700">
                          This screen allows you to map the Future Order product
                          to multiple Boekestyn products (e.g. Blooming
                          Assorted).
                        </label>

                        {!!productDefault?.ignoreOverrideQuantity && (
                          <label className="mt-2 block rounded bg-yellow-50 py-2 text-center text-xs italic text-gray-500">
                            The quantity of plants does not have to add up to
                            the item&apos;s Pack Quantity.
                          </label>
                        )}

                        <div className="flex flex-col overflow-auto">
                          <div className="p-2">
                            <table className="mt-2 w-full">
                              <thead>
                                <tr>
                                  <th className="p-2">Plant</th>
                                  <th className="p-2">Customer</th>
                                  <th className="whitespace-nowrap p-2 text-center">
                                    Qty / Finished Item
                                  </th>
                                  <th>&nbsp;</th>
                                </tr>
                              </thead>
                              <tbody>
                                {boekestynProducts.map((product) => (
                                  <BoekestynProductsMultipleItem
                                    key={product.id}
                                    product={product}
                                  />
                                ))}
                              </tbody>
                              <tfoot>
                                <tr>
                                  <td className="p-2" colSpan={2}>
                                    <button
                                      type="button"
                                      className="btn-new px-2 py-1"
                                      onClick={handleAddMappingClick}
                                    >
                                      Add Plant
                                    </button>
                                  </td>
                                  <td className="p-2 text-center">
                                    {!productDefault?.ignoreOverrideQuantity &&
                                      `Pack Qty: ${packQuantity}`}
                                  </td>
                                  <td>&nbsp;</td>
                                </tr>
                              </tfoot>
                            </table>
                          </div>
                        </div>
                      </div>
                    </form>
                  </div>

                  <Error error={error} clear={handleClearErrorClick} />

                  <div className="w-100 flex flex-row justify-end bg-gray-50 px-6 py-3">
                    <div>
                      <button
                        type="button"
                        className="btn-secondary text-lg"
                        onClick={handleCancelClick}
                      >
                        Cancel
                      </button>
                      <button
                        type="button"
                        className="btn-primary ml-4 text-lg"
                        onClick={handleSaveClick}
                      >
                        Save&nbsp;
                        <Icon icon="save" className="ml-2" />
                      </button>
                    </div>
                  </div>
                </div>
              </HeadlessUI.Dialog.Panel>
            </HeadlessUI.Transition.Child>
          </div>
        </div>
      </HeadlessUI.Dialog>
    </HeadlessUI.Transition.Root>
  );
}
