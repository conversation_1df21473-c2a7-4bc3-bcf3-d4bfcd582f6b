import { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { Toast } from './toast';
import { removeToast, selectToasts } from './toaster-slice';

export function Toaster() {
  const dispatch = useAppDispatch(),
    toasts = useAppSelector(selectToasts);

  useEffect(() => {
    const lastToast = toasts[toasts.length - 1];
    if (lastToast?.timeout) {
      window.setTimeout(() => {
        dispatch(removeToast(lastToast.message));
      }, lastToast.timeout * 1000);
    }
  }, [dispatch, toasts]);

  return (
    <div className="pointer-events-none fixed inset-0 z-50 flex flex-col items-end justify-end space-y-4 p-6">
      {toasts.map((toast) => (
        <Toast key={toast.message} message={toast} />
      ))}
    </div>
  );
}
