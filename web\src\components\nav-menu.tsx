import { Fragment } from 'react';
import Link from 'next/link';
import { useMsal } from '@azure/msal-react';
import { Disclosure, Menu, Transition } from '@headlessui/react';
import { usePermissions } from '@/services/auth';
import { routes } from '@/services/routes';
import { Icon } from './icon';
import { NavMenuItem } from './nav-menu-item';
import { NavMenuItemMobile } from './nav-menu-item-mobile';

function classNames(...classes: any[]) {
  return classes.filter(Boolean).join(' ');
}

export function NavMenu() {
  const { instance } = useMsal(),
    { can } = usePermissions(),
    isOnSalesTeam = can('Sales Team'),
    isAdministrator = can('Administrators'),
    title = window.document.title;

  const handleHomeClick = () => {
    const home = routes.home.to();

    if (window.location.href === home) {
      window.location.reload();
    } else {
      window.location.href = home;
    }
  };

  const handleLogoutClick = () => {
    instance.logoutRedirect();
  };

  return (
    <Disclosure as="nav" className="border-b border-gray-200 bg-white">
      {({ open }) => (
        <>
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="flex h-16 justify-between">
              <div className="flex">
                <div className="flex flex-shrink-0 items-center">
                  <button type="button" onClick={handleHomeClick}>
                    <img
                      className="h-8 w-8"
                      src="/favicon-32x32.png"
                      alt="Flora Pack Inc."
                    />
                  </button>
                </div>
                <div className="-my-px ml-6 hidden space-x-8 md:flex">
                  <div className="ml-10 flex space-x-8">
                    <NavMenuItem href={routes.home.to()} text="Home" />
                    <NavMenuItem
                      href={routes.futureOrders.list.to()}
                      text="Future Orders"
                    />
                    <NavMenuItem
                      href={routes.prebooks.list.to()}
                      text="Prebooks"
                    />
                    <NavMenuItem
                      href={routes.boekestyns.list.to()}
                      text="Boeks"
                    />
                    <NavMenuItem
                      href={routes.upgrades.list.to()}
                      text="Upgrades"
                    />
                    <NavMenuItem href={routes.labels.list.to()} text="Labels" />
                  </div>
                </div>
              </div>
              <div className="hidden md:ml-6 md:flex md:items-center">
                {/* Profile dropdown */}
                <Menu as="div" className="relative ml-3">
                  <div>
                    <Menu.Button className="flex max-w-xs items-center rounded-full bg-white text-sm hover:text-gray-500 focus:outline-none">
                      <span className="sr-only">Open user menu</span>
                      <Icon icon="gear" className="text-xl" />
                    </Menu.Button>
                  </div>
                  <Transition
                    as={Fragment}
                    enter="transition ease-out duration-100"
                    enterFrom="transform opacity-0 scale-95"
                    enterTo="transform opacity-100 scale-100"
                    leave="transition ease-in duration-75"
                    leaveFrom="transform opacity-100 scale-100"
                    leaveTo="transform opacity-0 scale-95"
                  >
                    <Menu.Items className="absolute right-0 z-40 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                      {(isOnSalesTeam || isAdministrator) && (
                        <Menu.Item>
                          {({ active }) => (
                            <Link
                              href={routes.settings.home.to()}
                              className={classNames(
                                'block w-full px-4 py-2 text-left text-sm no-underline hover:no-underline',
                                active
                                  ? 'bg-gray-100 text-blue-900'
                                  : 'text-gray-700'
                              )}
                            >
                              <Icon icon="cogs" fixedWidth />
                              &nbsp; Settings
                            </Link>
                          )}
                        </Menu.Item>
                      )}
                      <Menu.Item>
                        {({ active }) => (
                          <button
                            type="button"
                            onClick={handleLogoutClick}
                            className={classNames(
                              'block w-full px-4 py-2 text-left text-sm',
                              active
                                ? 'bg-gray-100 text-blue-900'
                                : 'text-gray-700'
                            )}
                          >
                            <Icon icon="right-from-bracket" fixedWidth />
                            &nbsp; Logout
                          </button>
                        )}
                      </Menu.Item>
                    </Menu.Items>
                  </Transition>
                </Menu>
              </div>
              <h2 className="flex-grow text-ellipsis whitespace-nowrap pt-4 text-center text-xl font-bold md:hidden">
                {title}
              </h2>
              <div className="-mr-2 flex md:hidden">
                {/* Mobile menu button */}
                <Disclosure.Button>
                  <span className="sr-only">Open main menu</span>
                  {open ? (
                    <Icon
                      icon="x"
                      className="block h-6 w-6"
                      aria-hidden="true"
                    />
                  ) : (
                    <Icon
                      icon="bars"
                      className="block h-6 w-6"
                      aria-hidden="true"
                    />
                  )}
                </Disclosure.Button>
              </div>
            </div>
          </div>

          <Disclosure.Panel className="md:hidden">
            <div className="border-t border-gray-200 pb-3 pt-4">
              <div className="mt-3 space-y-1 px-2">
                <NavMenuItemMobile text="Home" href={routes.home.to()} />
                <NavMenuItemMobile
                  text="Boekestyn Item List"
                  href={routes.boekestyns.list.to()}
                />
                <NavMenuItemMobile
                  text="Boekestyn Sales"
                  href={routes.boekestyns.sales.to()}
                />
                <NavMenuItemMobile
                  text="Upgrade Items"
                  href={routes.upgrades.list.to()}
                />
              </div>
            </div>
          </Disclosure.Panel>
        </>
      )}
    </Disclosure>
  );
}
