import {
  EventType,
  EventMessage,
  LogLevel,
  PublicClientApplication,
  AuthenticationResult,
  AccountInfo,
} from '@azure/msal-browser';
import { useIsAuthenticated, useMsal } from '@azure/msal-react';
import axios from 'api/api-base';
import { useEffect, useState } from 'react';
import { useMeQuery } from 'api/security-service';
import * as security from 'api/models/security';
import { equals } from '@/utils/equals';

const clientId = process.env.NEXT_PUBLIC_AZURE_CLIENT_ID || '';
const authority = process.env.NEXT_PUBLIC_AZURE_AUTHORITY || '';
export const scopes = [process.env.NEXT_PUBLIC_AZURE_SCOPE || ''];

export const msalConfig = {
  auth: {
    clientId,
    authority,
    redirectUri: '/',
    postLogoutRedirectUri: '/',
  },
  cache: {
    cacheLocation: 'localStorage',
  },
  system: {
    loggerOptions: {
      loggerCallback: (
        level: LogLevel,
        message: string,
        containsPii: boolean
      ) => {
        if (containsPii) {
          return;
        }
        switch (level) {
          case LogLevel.Error:
            console.error(message);
            return;
          case LogLevel.Warning:
            console.warn(message);
            return;
          default:
            return;
        }
      },
    },
  },
};

export const msalInstance = new PublicClientApplication(msalConfig);

// Account selection logic is app dependent. Adjust as needed for different use cases.
const accounts = msalInstance.getAllAccounts();
if (accounts.length > 0) {
  const account = accounts[0];
  updateToken(account);
}

msalInstance.addEventCallback((event: EventMessage) => {
  const payload = event.payload as AuthenticationResult;
  if (event.eventType === EventType.LOGIN_SUCCESS && payload.account) {
    const { account } = payload;
    updateToken(account);
  }
});

function updateToken(account: AccountInfo, callback?: () => void) {
  const acquireTokenRequest = {
    scopes,
    account,
  };

  msalInstance.setActiveAccount(account);

  msalInstance
    .acquireTokenSilent(acquireTokenRequest)
    .then((accessTokenResponse) => {
      // Acquire token silent success
      const token = accessTokenResponse.accessToken;
      // Call your API with token
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      //useHasToken(true);
    })
    .catch((error) => {
      console.log(error);
      //useHasToken(false);
    });
}

export function useAuth() {
  const isAuthenticated = useIsAuthenticated(),
    { instance, accounts } = useMsal(),
    [hasToken, setHasToken] = useState(false);

  useEffect(() => {
    if (isAuthenticated) {
      if (accounts.length) {
        const request = {
          scopes,
          account: accounts[0],
        };
        instance.acquireTokenSilent(request).then((response) => {
          // Acquire token silent success
          const token = response.accessToken;
          // Call your API with token
          axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
          setHasToken(true);
        });
      }
    }
  }, [accounts, instance, isAuthenticated, setHasToken]);

  return { hasToken };
}

export function usePermissions() {
  const { data } = useMeQuery();

  function can(group: security.Groups) {
    return !!data?.groups.some((g) => equals(g, group));
  }

  return { can };
}
