import * as HeadlessUI from '@headlessui/react';
import { useState, Fragment, useEffect } from 'react';
import * as boeks from 'api/models/boekestyns';
import { Icon } from '@/components/icon';
import { handleFocus } from '@/utils/focus';

export type StickingWorkOrderDialogProps = {
  open: boolean;
  onClose: () => void;
  onSave: (data: {
    crewSize: number;
    flowerDate: string;
    varietyQuantities: { name: string; quantity: number }[];
  }) => void;
  order: boeks.StickingWorkOrder | boeks.StickingOrder | null;
};

export function StickingWorkOrderDialog({
  open,
  onClose,
  onSave,
  order,
}: StickingWorkOrderDialogProps) {
  const [crewSize, setCrewSize] = useState<number | null>(null);
  const [flowerDate, setFlowerDate] = useState('');
  const [varietyActualQuantites, setVarietyActualQuantites] = useState<
    { name: string; quantity: number }[]
  >([]);

  useEffect(() => {
    if (open && order) {
      setCrewSize(
        'crewSize' in order
          ? order.crewSize
          : 'plant' in order
          ? order.plant.defaultStickingCrewSize
          : 1
      );
      setFlowerDate(order.flowerDate);
      setVarietyActualQuantites(
        order.varieties.map((v) => ({ name: v.name, quantity: v.pots }))
      );
    }
  }, [open, order]);

  const setVarietyQuantity = (name: string, quantity: number) => {
    setVarietyActualQuantites(
      varietyActualQuantites.map((v) =>
        v.name === name ? { ...v, quantity } : v
      )
    );
  };

  const handleCrewSizeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(event.target.value, 10);
    setCrewSize(isNaN(value) ? null : value);
  };

  const handleSave = () => {
    const data = {
      crewSize: crewSize ?? 0,
      flowerDate,
      varietyQuantities: varietyActualQuantites,
    };

    onSave(data);
  };

  return (
    <HeadlessUI.Transition.Root show={open} as={Fragment}>
      <HeadlessUI.Dialog as="div" className="relative z-30" onClose={onClose}>
        <HeadlessUI.Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </HeadlessUI.Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex max-h-full min-h-full justify-center p-0 text-center">
            <HeadlessUI.Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-0 scale-95"
              enterTo="opacity-100 translate-y-0 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 scale-100"
              leaveTo="opacity-0 translate-y-0 scale-95"
            >
              <HeadlessUI.Dialog.Panel className="relative my-8 flex w-full max-w-lg transform flex-col rounded-lg bg-white p-4 text-left shadow-xl transition-all">
                <div className="flex flex-grow flex-col overflow-y-auto rounded border p-8">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {order?.orderNumber ?? 'Sticking Work Order'}
                  </h3>

                  <div className="mt-2">
                    <label className="block text-sm font-medium text-gray-500">
                      Crew Size
                    </label>
                    <input
                      type="number"
                      value={crewSize ?? ''}
                      onChange={handleCrewSizeChange}
                      onFocus={handleFocus}
                      className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    />
                  </div>

                  <div className="mt-2">
                    <label className="block text-sm font-medium text-gray-500">
                      Flower Date
                    </label>
                    <input
                      type="date"
                      value={flowerDate}
                      onChange={(e) => setFlowerDate(e.target.value)}
                      className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    />
                  </div>

                  <div className="mt-4 flex flex-grow flex-col overflow-y-auto">
                    <table className="min-w-full divide-y divide-gray-300 text-sm">
                      <thead>
                        <tr className="sticky top-0 border-b bg-white shadow">
                          <th className="px-2 py-1 text-left">Variety</th>
                          <th className="px-2 py-1 text-left">
                            Scheduled Pots
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {order?.varieties.map((variety) => (
                          <tr key={variety.name}>
                            <td className="px-2 py-1">{variety.name}</td>
                            <td className="px-2 py-1">
                              <input
                                type="number"
                                value={
                                  varietyActualQuantites.find(
                                    (v) => v.name === variety.name
                                  )?.quantity ?? variety.pots
                                }
                                onChange={(e) =>
                                  setVarietyQuantity(
                                    variety.name,
                                    parseInt(e.target.value)
                                  )
                                }
                                onFocus={handleFocus}
                                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                              />
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>

                <div className="mt-4 text-right">
                  <button
                    type="button"
                    className="btn-secondary"
                    onClick={onClose}
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn-primary ml-2"
                    onClick={handleSave}
                  >
                    Save &nbsp;
                    <Icon icon="save" />
                  </button>
                </div>
              </HeadlessUI.Dialog.Panel>
            </HeadlessUI.Transition.Child>
          </div>
        </div>
      </HeadlessUI.Dialog>
    </HeadlessUI.Transition.Root>
  );
}
