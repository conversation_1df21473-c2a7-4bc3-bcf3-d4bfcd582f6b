'use client';

import {
  createAction,
  createSelector,
  createSlice,
  PayloadAction,
  AsyncThunk,
  createAsyncThunk,
} from '@reduxjs/toolkit';
import { DateTime } from 'luxon';
import { boekestynListApi, boekestynApi } from 'api/boekestyn-service';
import * as models from 'api/models/boekestyns';
import { RootState } from '@/services/store';
import { ProblemDetails } from '@/utils/problem-details';
import { contains, equals, startsWith } from '@/utils/equals';
import { sortBy } from '@/utils/sort';

const sortByDate = sortBy('date'),
  sortBySeason = sortBy('season', 'descending');

export const blankShipToFilter = 'No Ship To';
export const blankCustomerFilter = 'No Customer';

export const downloadItemList: AsyncThunk<
  void,
  models.ItemListItem[],
  { state: RootState }
> = createAsyncThunk(
  'boekestyn-list-downloadList',
  async (items, { rejectWithValue, getState }) => {
    try {
      const rootState = getState() as RootState,
        { startDate, endDate } = rootState.boekestynList,
        args = {
          startDate: startDate || null,
          endDate: endDate || null,
          items,
        };

      return await boekestynApi.boekestynItemListDownload(args);
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

interface SetPriorityArgs {
  id: number;
  priority: boolean;
}

export const setPriority: AsyncThunk<
  undefined,
  SetPriorityArgs,
  { state: RootState }
> = createAsyncThunk(
  'boekestyn-list-setPriority',
  async ({ id, priority }, { rejectWithValue, dispatch }) => {
    try {
      await boekestynApi.setPrebookItemPriority(id, priority);
      dispatch(boekestynListSlice.actions.setPriority({ id, priority }));
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const setUpcPrinted: AsyncThunk<
  undefined,
  number,
  { state: RootState }
> = createAsyncThunk(
  'boekestyn-list-setUpcPrinted',
  async (id, { rejectWithValue, dispatch }) => {
    try {
      await boekestynApi.completePrebookItemUpc(id);
      dispatch(boekestynListSlice.actions.setUpcPrinted(id));
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const setUpcUnprinted: AsyncThunk<
  undefined,
  number,
  { state: RootState }
> = createAsyncThunk(
  'boekestyn-list-setUpcUnprinted',
  async (id, { rejectWithValue, dispatch }) => {
    try {
      await boekestynApi.uncompletePrebookItemUpc(id);
      dispatch(boekestynListSlice.actions.setUpcUnprinted(id));
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

const downloadItemListPending = createAction(downloadItemList.pending.type),
  downloadItemListFulfilled = createAction(downloadItemList.fulfilled.type),
  downloadItemListRejected = createAction<ProblemDetails>(
    downloadItemList.rejected.type
  );

interface BoekestynItemFilters {
  customer: string | null;
  shipTo: string | null;
}

interface FutureOrderListState {
  startDate: string;
  endDate: string;
  search: string;
  sort: keyof models.ItemListItem;
  sortDescending: boolean;
  filter: BoekestynItemFilters;
  items: models.ItemListItem[];
  highlightPrintedUPCs: boolean;
  isLoading: boolean;
  error: ProblemDetails | null;
}

const initialState: FutureOrderListState = {
  startDate: DateTime.now().toFormat('yyyy-MM-dd'),
  endDate: '',
  search: '',
  sort: 'requiredDate',
  sortDescending: false,
  filter: {
    customer: null,
    shipTo: null,
  },
  items: [],
  highlightPrintedUPCs: false,
  isLoading: false,
  error: null,
};

export interface BoekestynItemSortArgs {
  sort: keyof models.ItemListItem;
  sortDescending: boolean;
}

export const boekestynListSlice = createSlice({
  name: 'boekestyn-list',
  initialState,
  reducers: {
    clearError(state) {
      state.error = null;
    },
    clearSearchAndFilters(state) {
      return {
        ...state,
        startDate: DateTime.now().toFormat('yyyy-MM-dd'),
        endDate: '',
        search: '',
        sort: 'requiredDate',
        sortDescending: false,
        filter: {
          customer: null,
          shipTo: null,
        },
      };
    },
    setStartDate(state, { payload }: PayloadAction<string>) {
      state.startDate = payload;
    },
    setEndDate(state, { payload }: PayloadAction<string>) {
      state.endDate = payload;
    },
    setSearch(state, { payload }: PayloadAction<string>) {
      state.search = payload;
    },
    setSort(state, { payload }: PayloadAction<BoekestynItemSortArgs>) {
      state.sort = payload.sort;
      state.sortDescending = payload.sortDescending;
    },
    setCustomerFilter(state, { payload }: PayloadAction<string | null>) {
      const filter = { ...state.filter, customer: payload };
      state.filter = filter;
    },
    setShipToFilter(state, { payload }: PayloadAction<string | null>) {
      const filter = { ...state.filter, shipTo: payload };
      state.filter = filter;
    },
    setPriority(state, { payload }: PayloadAction<SetPriorityArgs>) {
      const items = state.items.map((t) => ({ ...t })),
        item = items.find((i) => i.id === payload.id);
      if (item) {
        item.priority = payload.priority;
      }

      state.items = items;
    },
    setUpcPrinted(state, { payload }: PayloadAction<number>) {
      const items = state.items.map((t) => ({ ...t })),
        item = items.find((i) => i.id === payload);
      if (item) {
        item.upcPrinted = new Date().toISOString();
      }

      state.items = items;
    },
    setUpcUnprinted(state, { payload }: PayloadAction<number>) {
      const items = state.items.map((t) => ({ ...t })),
        item = items.find((i) => i.id === payload);
      if (item) {
        item.upcPrinted = null;
      }

      state.items = items;
    },
    setHighlightPrintedUPCs(state, { payload }: PayloadAction<boolean>) {
      state.highlightPrintedUPCs = payload;
      if (payload) {
        window.localStorage.setItem('highlightPrintedUPCs', 'true');
      } else {
        window.localStorage.removeItem('highlightPrintedUPCs');
      }
    },
  },
  extraReducers: (builder) =>
    builder
      .addCase(downloadItemListPending, (state) => {
        state.isLoading = true;
      })
      .addCase(downloadItemListFulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(downloadItemListRejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      .addMatcher(boekestynListApi.endpoints.itemList.matchPending, (state) => {
        state.isLoading = true;
      })
      .addMatcher(
        boekestynListApi.endpoints.itemList.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.items = payload.items;
        }
      )
      .addMatcher(
        boekestynListApi.endpoints.itemList.matchRejected,
        (state, { payload }) => {
          state.isLoading = false;
          if (payload) {
            state.error = payload;
          }
        }
      ),
});

export const {
  clearError,
  clearSearchAndFilters,
  setStartDate,
  setEndDate,
  setSearch,
  setSort,
  setCustomerFilter,
  setShipToFilter,
  setHighlightPrintedUPCs,
} = boekestynListSlice.actions;

export const selectError = ({ boekestynList }: RootState) =>
  boekestynList.error;
export const selectStartDate = ({ boekestynList }: RootState) =>
  boekestynList.startDate;
export const selectEndDate = ({ boekestynList }: RootState) =>
  boekestynList.endDate;
export const selectSearch = ({ boekestynList }: RootState) =>
  boekestynList.search;
export const selectSort = ({ boekestynList }: RootState) => boekestynList.sort;
export const selectSortDescending = ({ boekestynList }: RootState) =>
  boekestynList.sortDescending;
export const selectFilter = ({ boekestynList }: RootState) =>
  boekestynList.filter;
export const selectIsLoading = ({ boekestynList }: RootState) =>
  boekestynList.isLoading;
const selectAllItems = ({ boekestynList }: RootState) => boekestynList.items;
export const selectHighlightPrintedUPCs = ({ boekestynList }: RootState) =>
  boekestynList.highlightPrintedUPCs;

export const selectItemCustomers = createSelector(selectAllItems, (items) => {
  const customers = items
    .reduce(
      (memo, p) =>
        p.customer && memo.indexOf(p.customer) === -1
          ? memo.concat([p.customer])
          : memo,
      [] as string[]
    )
    .sort();

  if (items.some((o) => !o.customer)) {
    customers.unshift(blankCustomerFilter);
  }

  return customers;
});
export const selectItemShipTos = createSelector(
  selectAllItems,
  selectFilter,
  (items, { customer }) => {
    const shipTos = items
      // if there's a customer filter, only show those ones
      .filter(
        (o) =>
          !customer ||
          equals(customer, o.customer) ||
          (customer === blankCustomerFilter && !o.customer)
      )
      .reduce(
        (memo, p) =>
          p.shipTo && memo.indexOf(p.shipTo) === -1
            ? memo.concat([p.shipTo])
            : memo,
        [] as string[]
      )
      .sort();

    if (items.some((o) => !o.shipTo)) {
      shipTos.unshift(blankShipToFilter);
    }

    return shipTos;
  }
);

const sortByPriority = sortBy('priority', 'descending');

const sortByCustomerAndShipTo = (sortDescending: boolean) => {
  const direction = sortDescending ? 'descending' : '',
    sortByCustomer = sortBy('customer', direction),
    sortByShipTo = sortBy('shipTo', direction);

  return function sort(a: models.ItemListItem, b: models.ItemListItem) {
    return sortByPriority(a, b) || sortByCustomer(a, b) || sortByShipTo(a, b);
  };
};

const sortByOther = (
  sortField: keyof models.ItemListItem,
  sortDescending: boolean
) => {
  const direction = sortDescending ? 'descending' : '',
    sortByField = sortBy(sortField, direction);
  return function sort(a: models.ItemListItem, b: models.ItemListItem) {
    return sortByPriority(a, b) || sortByField(a, b);
  };
};

export const selectItems = createSelector(
  selectAllItems,
  selectSearch,
  selectSort,
  selectSortDescending,
  selectFilter,
  (items, search, sortField, sortDescending, filter) => {
    const sort =
        sortField === 'customer'
          ? sortByCustomerAndShipTo(sortDescending)
          : sortByOther(sortField, sortDescending),
      list = items
        .filter(
          (o) =>
            (!search ||
              contains(o.prebookId.toString(), search) ||
              contains(o.futureOrderId?.toString(), search) ||
              startsWith(o.customer, search) ||
              startsWith(o.shipTo, search) ||
              contains(o.spirePartNumber, search) ||
              contains(o.description, search) ||
              contains(o.itemComments, search) ||
              contains(o.potCover, search)) &&
            (!filter.customer ||
              equals(filter.customer, o.customer) ||
              (filter.customer === blankCustomerFilter && !o.customer)) &&
            (!filter.shipTo ||
              equals(filter.shipTo, o.shipTo) ||
              (filter.shipTo === blankShipToFilter && !o.shipTo))
        )
        .map((o) => ({ ...o } as models.ItemListItem))
        .sort(sort);

    return list;
  }
);

export const selectItemDates = createSelector(selectItems, (items) =>
  items
    .reduce((memo, { requiredDate: date, season }) => {
      if (date && !memo.some((m) => m.date === date && m.season === season)) {
        memo.push({ date, season });
      }
      return memo;
    }, [] as { date: string; season: string | null }[])
    .sort((a, b) => sortByDate(a, b) || sortBySeason(a, b))
);

export default boekestynListSlice.reducer;
