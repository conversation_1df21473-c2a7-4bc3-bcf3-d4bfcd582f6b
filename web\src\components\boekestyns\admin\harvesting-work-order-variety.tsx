import { useMemo, useState, Fragment } from 'react';
import { formatNumber } from '@/utils/format';
import * as HeadlessUI from '@headlessui/react';
import {
  setVarietySelection,
  setVarietyExpectedPercentage,
  WorkOrderData,
  selectSchedules,
  ScheduleData,
  VarietyData,
  selectSelectedOrder,
} from './harvesting-work-orders-slice';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { classNames } from '@/utils/class-names';
import { handleFocus } from '@/utils/focus';
import { Icon } from '@/components/icon';
import { HarvestingWorkOrderLabourVarietyItem } from 'api/models/boekestyns';

export type HarvestingWorkOrderVarietyRowProps = {
  schedule: ScheduleData;
  variety: VarietyData;
  workOrder: WorkOrderData;
  labour: HarvestingWorkOrderLabourVarietyItem[];
};

export function HarvestingWorkOrderVarietyRow({
  schedule,
  variety,
  workOrder,
  labour,
}: HarvestingWorkOrderVarietyRowProps) {
  const dispatch = useAppDispatch(),
    schedules = useAppSelector(selectSchedules),
    order = useAppSelector(selectSelectedOrder),
    finalRound = workOrder.finalRound,
    [showWarningTooltip, setShowWarningTooltip] = useState(false),
    varietyLabour = useMemo(
      () => labour.filter((l) => l.varietyName === variety.name),
      [labour, variety.name]
    ),
    workOrderLabour = useMemo(
      () => labour.filter((l) => l.workOrderId === workOrder.id),
      [labour, workOrder.id]
    ),
    varietyData = useMemo(
      () => order?.varieties.find((v) => v.name === variety.name),
      [order?.varieties, variety.name]
    ),
    varietyScheduledPots = useMemo(
      () =>
        schedules.reduce((total, s) => {
          if (s.date >= schedule.date) return total;

          const scheduledVariety = s.workOrder.varieties.find(
              (v) => v.name === variety.name
            ),
            percentage =
              (scheduledVariety?.expectedHarvestPercentage ?? 100) / 100,
            pots = scheduledVariety?.pots ?? 0,
            scheduled = Math.round((pots - total) * percentage),
            hasHarvest = labour.some((l) => l.workOrderId === s.workOrder.id),
            scheduleHarvest = hasHarvest
              ? varietyLabour
                  .filter((l) => l.workOrderId == s.workOrder.id)
                  .reduce((total, l) => total + l.harvested, 0)
              : 0,
            scheduleThrownOut = hasHarvest
              ? varietyLabour
                  .filter((l) => l.workOrderId == s.workOrder.id)
                  .reduce((total, l) => total + l.thrownOut, 0)
              : 0;

          return (
            total +
            (hasHarvest ? scheduleHarvest + scheduleThrownOut : scheduled)
          );
        }, 0),
      [schedules, schedule.date, labour, varietyLabour, variety.name]
    ),
    harvested = useMemo(
      () => varietyLabour.reduce((total, l) => total + l.harvested, 0),
      [varietyLabour]
    ),
    thrownOut = useMemo(
      () => varietyLabour.reduce((total, l) => total + l.thrownOut, 0),
      [varietyLabour]
    ),
    varietyAvailablePots = useMemo(() => {
      if (harvested + thrownOut > 0) {
        return Math.max(variety.pots - harvested - thrownOut, 0);
      } else {
        return Math.max(variety.pots - varietyScheduledPots, 0);
      }
    }, [variety.pots, varietyScheduledPots, harvested, thrownOut]),
    varietyToSchedule = useMemo(() => {
      if (finalRound) {
        return varietyAvailablePots;
      }

      const scheduledForThisRound = Math.round(
        varietyAvailablePots *
          ((variety?.expectedHarvestPercentage ?? 100) / 100)
      );

      return Math.min(scheduledForThisRound, varietyAvailablePots);
    }, [finalRound, varietyAvailablePots, variety?.expectedHarvestPercentage]),
    varietyOverscheduled = useMemo(() => {
      // check if there are scheduled dates with 100% expected harvest percentage followed by a non-zero value
      let fullDay = false;

      for (let i = 0; i < schedules.length; i++) {
        const s = schedules[i],
          scheduledVariety = s.workOrder.varieties.find(
            (v) => v.name === variety.name
          ),
          percentage = scheduledVariety?.expectedHarvestPercentage ?? 100;

        if (percentage > 0 && fullDay) {
          return true;
        }

        if (percentage === 100) {
          fullDay = true;
        }
      }

      return false;
    }, [schedules, variety.name]);

  const handleSelectVarietyChanged = (selected: boolean) => {
    dispatch(
      setVarietySelection({
        workOrderId: workOrder.id,
        varietyName: variety.name,
        selected,
      })
    );
  };

  const handleExpectedHarvestPercentageChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = e.target.valueAsNumber,
      percentage = isNaN(value) ? 0 : value;

    dispatch(
      setVarietyExpectedPercentage({
        workOrderId: workOrder.id,
        varietyName: variety.name,
        percentage,
      })
    );
  };

  const handleWarningTooltipMouseEnter = () => {
    setShowWarningTooltip(true);
  };

  const handleWarningTooltipMouseLeave = () => {
    setShowWarningTooltip(false);
  };

  return (
    <tr key={variety.name}>
      <td className="whitespace-nowrap p-2 text-left">{variety.name}</td>
      <td className="w-1 p-2 text-right">
        {formatNumber(variety.pots, '0,0')}
      </td>
      {!!workOrderLabour?.length ? (
        <>
          <td className="w-1 p-2 text-right">
            {formatNumber(harvested, '0,0')}
          </td>
          <td className="w-1 p-2 text-right">
            {formatNumber(thrownOut, '0,0')}
          </td>
        </>
      ) : (
        <td className="w-1 p-2 text-right">
          {formatNumber(varietyScheduledPots, '0,0')}
        </td>
      )}
      <td className="w-1 p-2 text-right">
        {formatNumber(varietyAvailablePots, '0,0')}
      </td>
      {!workOrderLabour.length && (
        <>
          <td className="w-1 p-2 text-right">
            <input
              type="number"
              value={
                finalRound ? 100 : variety?.expectedHarvestPercentage ?? 100
              }
              onChange={handleExpectedHarvestPercentageChange}
              onFocus={handleFocus}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              min="0"
              max="100"
              step="1"
              disabled={finalRound}
            />
          </td>
          <td className="w-1 p-2 text-right">
            {formatNumber(varietyToSchedule, '0,0')}
          </td>
          <td className="text-center">
            <HeadlessUI.Switch
              onChange={handleSelectVarietyChanged}
              checked={!!variety?.selected}
              className={classNames(
                variety?.selected ? 'bg-blue-400' : 'bg-gray-200',
                'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
              )}
            >
              <span
                aria-hidden="true"
                className={classNames(
                  variety?.selected ? 'translate-x-5' : 'translate-x-0',
                  'pointer-events-none inline-block size-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                )}
              />
            </HeadlessUI.Switch>
            {varietyOverscheduled && (
              <HeadlessUI.Popover
                className="relative z-40 inline-block cursor-pointer"
                onMouseEnter={handleWarningTooltipMouseEnter}
                onMouseLeave={handleWarningTooltipMouseLeave}
              >
                <>
                  <div className="text-yellow-500">
                    <Icon icon="exclamation-triangle" />
                  </div>
                  <HeadlessUI.Transition
                    as={Fragment}
                    show={showWarningTooltip}
                    enter="transition ease-out duration-200"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="transition ease-in duration-150"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                  >
                    <HeadlessUI.Popover.Panel
                      static
                      className="absolute left-0 top-1/2 z-10 -translate-x-full -translate-y-1/2 transform bg-yellow-50"
                    >
                      <div className="w-auto whitespace-nowrap rounded-lg border p-4 shadow-lg">
                        <div className="text-sm text-gray-700">
                          This variety has a day with 100% harvest followed by
                          additional harvest days.
                        </div>
                      </div>
                    </HeadlessUI.Popover.Panel>
                  </HeadlessUI.Transition>
                </>
              </HeadlessUI.Popover>
            )}
          </td>
        </>
      )}
    </tr>
  );
}
