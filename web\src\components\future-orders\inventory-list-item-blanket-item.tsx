import * as HeadlessUI from '@headlessui/react';
import * as prebooks from 'api/models/prebooks';
import * as settings from 'api/models/settings';
import * as spire from 'api/models/spire';
import { Icon } from '../icon';
import { classNames } from '@/utils/class-names';
import { formatDate, formatNumber } from '@/utils/format';
import { weekFromId } from '@/utils/weeks';

interface InventoryListItemBlanketItemProps {
  blanketItem: prebooks.PrebookBlanketItem;
  item: spire.InventoryItem;
  customer: spire.Customer | null;
  shipTo: spire.CustomerShipTo | null;
  customerItemCodeDefault: settings.CustomerItemCodeDefault | undefined;
}

export function InventoryListItemBlanketItem({
  blanketItem,
  item,
  customer,
  shipTo,
  customerItemCodeDefault,
}: InventoryListItemBlanketItemProps) {
  const week = weekFromId(blanketItem.blanketWeekId);

  return (
    <HeadlessUI.Combobox.Option
      as="div"
      key={blanketItem.id}
      value={{
        item,
        blanketItemId: blanketItem.id,
        boekestynPlantId: blanketItem.boekestynPlantId,
        boekestynCustomerAbbreviation:
          blanketItem.boekestynCustomerAbbreviation,
        hasBlanketItems: true,
      }}
      className={({ active }) =>
        classNames(
          'm-2 grid cursor-pointer grid-cols-4 rounded border border-gray-200 py-2 pl-4 pr-2 text-left shadow-md',
          active ? 'bg-blue-600' : 'bg-white'
        )
      }
    >
      {({ active }) => (
        <>
          <div
            className={classNames(
              'col-span-4 mb-2 cursor-pointer truncate text-sm',
              active ? 'text-white' : 'text-gray-900'
            )}
          >
            <span
              className={classNames(
                'font-medium',
                active ? 'text-white' : 'text-gray-900'
              )}
            >
              {item.partNo}
            </span>
            <br />
            <span
              className={classNames(
                'col-span-3 col-start-1 cursor-pointer truncate text-sm',
                active ? 'text-white' : 'text-gray-500'
              )}
            >
              {item.description}
            </span>
            {!!customerItemCodeDefault && (
              <div
                className={classNames(
                  'cursor-pointer truncate text-sm italic',
                  active ? 'text-white' : 'text-gray-500'
                )}
              >
                {customer?.name}
                {!!customerItemCodeDefault.shipToId && !!shipTo && (
                  <>
                    &nbsp;
                    <span
                      className={classNames(
                        'cursor-pointer text-xs italic',
                        active ? 'text-white' : 'text-gray-400'
                      )}
                    >
                      ({shipTo.name})
                    </span>
                    &nbsp;
                  </>
                )}
                Item Code {customerItemCodeDefault.customerItemCode}
              </div>
            )}
          </div>
          <div
            className={classNames(
              'col-span-2 flex cursor-pointer flex-col truncate italic',
              active ? 'text-white' : 'text-gray-500'
            )}
          >
            <div className="truncate">{blanketItem.vendorName}</div>

            <div className="truncate text-xs">
              {!!blanketItem.blanketStartDate &&
                `${formatDate(blanketItem.blanketStartDate, 'MMM d, yyyy')} - `}
              {formatDate(blanketItem.requiredDate, 'MMM d, yyyy')}
              {!!week && (
                <span className="truncate italic">
                  &nbsp;(Week {week.week})
                </span>
              )}
              <div className="mt-1 truncate font-medium">
                {blanketItem.customerName
                  ? `${blanketItem.customerName} BLANKET`
                  : 'FP GENERAL BLANKET'}
              </div>
            </div>
          </div>
          <div className="my-auto flex cursor-pointer flex-col">
            {!!blanketItem.bookedQuantity && (
              <>
                <div
                  className={classNames(
                    'cursor-pointer text-right',
                    active && 'text-white'
                  )}
                >
                  {formatNumber(blanketItem.blanketQuantity)}
                </div>
                <div
                  className={classNames(
                    'cursor-pointer text-right',
                    active && 'text-white'
                  )}
                >
                  {`- ${formatNumber(blanketItem.bookedQuantity)}`}
                </div>
              </>
            )}
            <div
              className={classNames(
                'text-md cursor-pointer text-right font-bold',
                blanketItem.blanketQuantity >= blanketItem.bookedQuantity
                  ? 'text-green-600'
                  : 'text-red-500'
              )}
            >
              {!!blanketItem.bookedQuantity && <>&nbsp;=&nbsp;</>}
              {formatNumber(
                blanketItem.blanketQuantity - blanketItem.bookedQuantity
              )}
            </div>
          </div>

          <div
            className={classNames(
              'my-auto flex flex-row justify-between border-gray-200 px-4',
              active ? 'text-white' : 'text-blue-500'
            )}
          >
            <span className="cursor-pointer ">Select </span>
            <Icon icon="chevron-right" />
          </div>
        </>
      )}
    </HeadlessUI.Combobox.Option>
  );
}
