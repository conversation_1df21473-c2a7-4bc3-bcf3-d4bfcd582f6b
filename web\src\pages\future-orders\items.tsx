import React, { Fragment } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import * as HeadlessUI from '@headlessui/react';
import { useItemSummaryQuery } from 'api/future-orders-service';
import * as models from 'api/models/future-orders';
import { usePermissions } from '@/services/auth';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { routes } from '@/services/routes';
import {
  clearError,
  clearItemSearchAndFilters,
  setStartDate,
  setEndDate,
  setItemSearch,
  setCustomerFilter,
  setShipToFilter,
  setVendorFilter,
  setSalespersonFilter,
  selectStartDate,
  selectEndDate,
  selectItemSearch,
  selectIsLoading,
  selectError,
  selectFutureOrderItems,
  selectFutureOrderItemCustomers,
  selectFutureOrderItemShipTos,
  selectFutureOrderItemVendors,
  selectFutureOrderItemSalespeople,
  selectFutureOrderItemSeasons,
  selectFilter,
  selectFutureOrderItemsSort,
  selectSortFutureOrderItemsDescending,
  setSeasonFilter,
  setFutureOrderItemsSort,
  downloadItemsList,
} from '@/components/future-orders/future-order-list-slice';
import { ItemListItem } from '@/components/future-orders/item-list-item';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import { Loading } from '@/components/loading';
import { classNames } from '@/utils/class-names';

export default function Items() {
  const dispatch = useAppDispatch(),
    { can } = usePermissions(),
    items = useAppSelector(selectFutureOrderItems),
    customers = useAppSelector(selectFutureOrderItemCustomers),
    shipTos = useAppSelector(selectFutureOrderItemShipTos),
    vendors = useAppSelector(selectFutureOrderItemVendors),
    salespeople = useAppSelector(selectFutureOrderItemSalespeople),
    seasons = useAppSelector(selectFutureOrderItemSeasons),
    startDate = useAppSelector(selectStartDate),
    endDate = useAppSelector(selectEndDate),
    search = useAppSelector(selectItemSearch),
    sort = useAppSelector(selectFutureOrderItemsSort),
    sortDescending = useAppSelector(selectSortFutureOrderItemsDescending),
    isLoading = useAppSelector(selectIsLoading),
    error = useAppSelector(selectError),
    filter = useAppSelector(selectFilter),
    { refetch } = useItemSummaryQuery({ startDate, endDate }),
    readonly = !can('Sales Team');

  const handleDownloadClick = () => {
    const args = {
      items,
    };
    dispatch(downloadItemsList(args));
  };

  const handleDownloadNotOnPrebookClick = () => {
    const noPrebooks = items.filter((i) => !i.prebookIdList),
      args = {
        items: noPrebooks,
      };
    dispatch(downloadItemsList(args));
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setItemSearch(e.target.value));
  };

  const handleStartDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setStartDate(e.target.value || ''));
  };

  const handleEndDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setEndDate(e.target.value || ''));
  };

  const handleResetSearchClick = async () => {
    await dispatch(clearItemSearchAndFilters());
    window.setTimeout(refetch);
  };

  const handleRefreshClick = () => {
    refetch();
  };

  const handleClearError = () => {
    dispatch(clearError());
  };

  const handleColumnSort = (sortProp: keyof models.FutureOrderSummaryItem) => {
    const descending = sortProp === sort ? !sortDescending : false;
    dispatch(
      setFutureOrderItemsSort({ sort: sortProp, sortDescending: descending })
    );
  };

  const handleCustomerFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setCustomerFilter(e.target.value || null));
  };

  const handleShipToFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setShipToFilter(e.target.value || null));
  };

  const handleSalespersonFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setSalespersonFilter(e.target.value || null));
  };

  const handleSeasonFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setSeasonFilter(e.target.value || null));
  };

  const handleVendorFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setVendorFilter(e.target.value || null));
  };

  interface HeaderButtonProps {
    text: string;
    propName?: keyof models.FutureOrderSummaryItem;
    className?: string;
  }

  const HeaderButton = ({ text, propName, className }: HeaderButtonProps) => (
    <button
      type="button"
      className={classNames(
        'group inline-flex',
        !propName && 'cursor-auto',
        className
      )}
      onClick={() => handleColumnSort(propName || 'date')}
    >
      {text}
      {!!propName && (
        <span
          className={classNames(
            'ml-2 flex-none rounded text-gray-400 group-hover:visible group-focus:visible',
            sort !== propName && 'invisible'
          )}
        >
          <Icon
            icon={sortDescending ? 'chevron-down' : 'chevron-up'}
            className="h-5 w-5"
            aria-hidden="true"
          />
        </span>
      )}
    </button>
  );

  return (
    <>
      <Head>
        <title>Future Order Items</title>
      </Head>
      <header className="bg-white shadow">
        <div className="grid grid-cols-1">
          <div className="mb-4 border-b shadow">
            <div className="mx-auto flex max-w-7xl items-center justify-between px-8">
              <div className="flex">
                <nav className="ml-24 flex flex-row">
                  <Link
                    href={routes.futureOrders.list.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Future Order List
                  </Link>
                  <div className="group relative my-2 text-nowrap rounded-lg bg-blue-500 p-2 text-center text-sm font-medium text-white">
                    Future Order Items
                  </div>
                </nav>
              </div>
            </div>
          </div>
          <div className="bg-gray-100 py-2">
            <div className="mx-auto flex max-w-7xl items-center justify-between px-8">
              <div className="flex flex-grow items-center align-top">
                <div className="flex w-full flex-col rounded p-2">
                  <div className="grid w-full grid-cols-8 gap-2 rounded-sm text-xs">
                    <div>
                      <label htmlFor="start-date">From Date</label>
                      <input
                        type="date"
                        max="2050-01-01"
                        id="start-date"
                        value={startDate}
                        onChange={handleStartDateChange}
                        className="w-full !max-w-none text-xs"
                      />
                    </div>
                    <div>
                      <label htmlFor="end-date">To Date</label>
                      <input
                        type="date"
                        max="2050-01-01"
                        id="end-date"
                        value={endDate}
                        onChange={handleEndDateChange}
                        className="w-full !max-w-none text-xs"
                      />
                    </div>
                    <div className="col-span-4">
                      <label htmlFor="end-date">Search</label>
                      <div className="flex">
                        <input
                          type="search"
                          id="search"
                          name="search"
                          value={search}
                          onChange={handleSearchChange}
                          className="flex-grow text-xs"
                          placeholder="Search"
                          autoComplete="off"
                        />
                        <button
                          type="button"
                          className="btn-secondary flex px-1 focus:ring-0"
                          title="Reset Search Filters"
                          onClick={handleResetSearchClick}
                        >
                          <Icon
                            icon="magnifying-glass-arrows-rotate"
                            className="h-5 w-5"
                          />
                        </button>
                      </div>
                    </div>
                    <div className="flex items-start justify-center pt-3">
                      <button
                        type="button"
                        onClick={handleRefreshClick}
                        className="btn-secondary flex p-3 text-blue-700 "
                      >
                        <Icon icon="refresh" spin={isLoading} />
                      </button>
                      <button
                        type="button"
                        onClick={handleDownloadClick}
                        className="btn-secondary flex p-3 text-green-700"
                      >
                        <Icon icon="file-excel" />
                      </button>
                      <HeadlessUI.Menu as="div" className="relative">
                        <HeadlessUI.Menu.Button className="btn-secondary flex p-3">
                          <Icon icon="chevron-down" />
                        </HeadlessUI.Menu.Button>
                        <HeadlessUI.Transition
                          as={Fragment}
                          enter="transition ease-out duration-100"
                          enterFrom="transform opacity-0 scale-95"
                          enterTo="opacity-100 scale-100"
                          leave="transition ease-in duration-75"
                          leaveFrom="opacity-100 scale-100"
                          leaveTo="opacity-0 scale-95"
                        >
                          <HeadlessUI.Menu.Items className="absolute right-0 z-40 mt-2 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                            <HeadlessUI.Menu.Item>
                              {({ active }) => (
                                <button
                                  type="button"
                                  className={classNames(
                                    'block w-full whitespace-nowrap px-4 py-2 text-left text-sm no-underline hover:no-underline',
                                    active
                                      ? 'bg-gray-100 text-blue-900'
                                      : 'text-gray-700'
                                  )}
                                  onClick={handleDownloadNotOnPrebookClick}
                                >
                                  <Icon icon="file-archive" fixedWidth />
                                  &nbsp; &quot;Not on a Prebook&quot; Report
                                </button>
                              )}
                            </HeadlessUI.Menu.Item>
                          </HeadlessUI.Menu.Items>
                        </HeadlessUI.Transition>
                      </HeadlessUI.Menu>
                    </div>
                    {!readonly && (
                      <div className="flex items-start justify-center pt-3">
                        <Link
                          href={routes.futureOrders.new.to()}
                          className="btn-new flex flex-nowrap"
                        >
                          <Icon icon="plus-circle" className="flex" />
                          &nbsp;
                          <div className="flex whitespace-nowrap">
                            New Future Order
                          </div>
                        </Link>
                      </div>
                    )}
                    <div className="col-start-1">
                      <label htmlFor="customer-filter">Customer</label>
                      <select
                        id="customer-filter"
                        value={filter.customer || ''}
                        onChange={handleCustomerFilterChange}
                        className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                      >
                        <option value="">All Customers</option>
                        {customers.map((customer) => (
                          <option key={customer}>{customer}</option>
                        ))}
                      </select>
                    </div>
                    <div className="">
                      <label htmlFor="ship-to-filter">Ship To</label>
                      <select
                        id="ship-to-filter"
                        value={filter.shipTo || ''}
                        onChange={handleShipToFilterChange}
                        className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                      >
                        <option value="">All Ship Tos</option>
                        {shipTos.map((shipTo) => (
                          <option key={shipTo}>{shipTo}</option>
                        ))}
                      </select>
                    </div>
                    <div className="">
                      <label htmlFor="salesperson-filter">Salesperson</label>
                      <select
                        id="salesperson-filter"
                        value={filter.salesperson || ''}
                        onChange={handleSalespersonFilterChange}
                        className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                      >
                        <option value="">All Salespeople</option>
                        {salespeople.map((salesperson) => (
                          <option key={salesperson}>{salesperson}</option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label htmlFor="season-filter">Season</label>
                      <select
                        id="season-filter"
                        value={filter.season || ''}
                        onChange={handleSeasonFilterChange}
                        className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                      >
                        <option value="">All Seasons</option>
                        {seasons.map((season) => (
                          <option key={season}>{season}</option>
                        ))}
                      </select>
                    </div>
                    <div className="col-span-2">
                      <label htmlFor="vendor-filter">Vendor</label>
                      <select
                        id="vendor-filter"
                        value={filter.vendor || ''}
                        onChange={handleVendorFilterChange}
                        className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                      >
                        <option value="">All Vendors</option>
                        {vendors.map((vendor) => (
                          <option key={vendor}>{vendor}</option>
                        ))}
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>
      <main className="flex-grow overflow-auto">
        <div className="mx-auto h-full px-8">
          <Error error={error} clear={handleClearError} />
          {isLoading && <Loading />}
          <div className="mt-8 flex h-full flex-col">
            <div className="-mx-8 -my-2 h-full">
              <div className="inline-block min-w-full px-8 py-2 align-middle">
                <div className="rounded-lg shadow ring-1 ring-black ring-opacity-5">
                  <table className="min-w-full divide-y divide-gray-300">
                    <thead>
                      <tr className="sticky top-0 z-10">
                        <th
                          scope="col"
                          className="bg-gray-100 py-3.5 pl-3 pr-6 text-center text-sm font-semibold text-gray-900"
                        >
                          <HeaderButton text="Id" propName="futureOrderId" />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          <HeaderButton
                            text="Product"
                            propName="spirePartNumber"
                          />
                          &nbsp;/&nbsp;
                          <HeaderButton
                            text="Description"
                            propName="description"
                            className="ml-4"
                          />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          <HeaderButton text="Date" propName="date" />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          <HeaderButton text="Season" propName="season" />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          <HeaderButton text="Customer" propName="customer" />
                          &nbsp;/&nbsp;
                          <HeaderButton
                            text="Ship To"
                            propName="shipTo"
                            className="ml-4"
                          />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          <HeaderButton
                            text="Salesperson"
                            propName="salesperson"
                          />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          <HeaderButton text="Vendor" propName="vendor" />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-center text-sm font-semibold text-gray-900"
                        >
                          <HeaderButton text="Cases" propName="orderQuantity" />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-center text-sm font-semibold text-gray-900"
                        >
                          <HeaderButton
                            text="Prebook"
                            propName="prebookIdList"
                          />
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white">
                      {items.map((item) => (
                        <ItemListItem key={item.id} item={item} />
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </>
  );
}
