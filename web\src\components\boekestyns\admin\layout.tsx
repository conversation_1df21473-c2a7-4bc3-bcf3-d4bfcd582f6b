import React from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import Head from 'next/head';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { routes } from '@/services/routes';
import { classNames } from '@/utils/class-names';

function NavbarItem({
  selected,
  path,
  label,
}: {
  selected: boolean;
  path?: string;
  label: string;
}) {
  return path ? (
    <Link
      href={path}
      className={classNames(
        'w-full p-2 text-left text-sm font-medium',
        selected
          ? 'cursor-default bg-blue-500 text-white hover:text-white hover:no-underline'
          : 'bg-white text-black'
      )}
    >
      {label}
    </Link>
  ) : (
    <button
      disabled
      className="w-full bg-white p-2 text-left text-sm font-medium text-gray-400"
    >
      {label}
    </button>
  );
}

export function AdminLayout({ children }: { children: React.ReactElement }) {
  const pathname = usePathname(),
    tabs = [
      { label: 'Sticking', path: routes.boekestyns.admin.sticking.to() },
      { label: 'Spacing', path: routes.boekestyns.admin.spacing.to() },
      { label: 'Harvesting', path: routes.boekestyns.admin.harvesting.to() },
      { label: 'Packing' },
      { label: 'Stickering' },
      { label: 'Job Board' },
    ];

  return (
    <div className="flex h-full flex-col overflow-y-auto">
      <Head>
        <title>Boekestyn Admin</title>
      </Head>
      <header className="bg-white shadow">
        <div className="mb-4 border-b shadow">
          <div className="mx-auto flex max-w-7xl items-center justify-between px-8">
            <div className="flex">
              <nav className="ml-24 flex flex-row">
                <Link
                  href={routes.boekestyns.list.to()}
                  className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                >
                  Boekestyn Item List
                </Link>
                <Link
                  href={routes.boekestyns.sales.to()}
                  className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                >
                  Boekestyn Sales
                </Link>
                <Link
                  href={routes.boekestyns.sticking.to()}
                  className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                >
                  Sticking
                </Link>
                <Link
                  href={routes.boekestyns.spacing.to()}
                  className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                >
                  Spacing
                </Link>
                <Link
                  href={routes.boekestyns.harvesting.to()}
                  className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                >
                  Harvesting
                </Link>
                <Link
                  href={routes.boekestyns.upcs.to()}
                  className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                >
                  UPCs
                </Link>
                <Link
                  href={routes.boekestyns.prep.to()}
                  className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                >
                  Order Prep
                </Link>
                <Link
                  href={routes.boekestyns.packing.to()}
                  className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                >
                  Order Packing
                </Link>
                <div className="group relative my-2 text-nowrap rounded-lg bg-blue-500 p-2 text-center text-sm font-medium text-white">
                  Admin
                </div>
              </nav>
            </div>
          </div>
        </div>
      </header>
      <DndProvider backend={HTML5Backend}>
        <main className="flex-grow overflow-y-auto">
          <div className="flex h-full flex-row overflow-y-auto">
            <div className="h-full w-48 overflow-y-auto">
              <nav>
                <ul className="flex flex-col">
                  {tabs.map((tab) => (
                    <NavbarItem
                      key={tab.label}
                      selected={pathname === tab.path}
                      path={tab.path}
                      label={tab.label}
                    />
                  ))}
                </ul>
              </nav>
            </div>
            <div className="h-full flex-grow overflow-y-auto">{children}</div>
          </div>
        </main>
      </DndProvider>
    </div>
  );
}
