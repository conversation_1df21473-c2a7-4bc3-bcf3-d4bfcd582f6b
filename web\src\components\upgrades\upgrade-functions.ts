import * as futureOrders from 'api/models/future-orders';
import { equals } from '@/utils/equals';

export function matchesGroup(
  item: futureOrders.UpgradeItem,
  group: futureOrders.UpgradeItem[]
) {
  if (!item.upgradeConfirmed) {
    return false;
  }

  if (item.priority) {
    return group.some((g) => g.priority);
  }

  return group.some(
    (g) =>
      g.upgradeConfirmed &&
      item.customerId === g.customerId &&
      equals(item.description, g.description) &&
      equals(item.containerPickDescription, g.containerPickDescription) &&
      equals(item.upc, g.upc) &&
      equals(item.dateCode, g.dateCode) &&
      equals(item.retail, g.retail) &&
      equals(item.productComingFrom, g.productComingFrom)
  );
}

export function matchesItem(
  a: futureOrders.UpgradeItem,
  b: futureOrders.UpgradeItem
) {
  if (!a.upgradeConfirmed || !b.upgradeConfirmed) {
    return false;
  }

  if (a.priority && b.priority) {
    return true;
  }

  return (
    a.customerId === b.customerId &&
    equals(a.description, b.description) &&
    equals(a.containerPickDescription, b.containerPickDescription) &&
    equals(a.upc, b.upc) &&
    equals(a.dateCode, b.dateCode) &&
    equals(a.retail, b.retail) &&
    equals(a.productComingFrom, b.productComingFrom)
  );
}

export function matchesBoxCode(
  group: futureOrders.UpgradeItem[],
  nextGroup: futureOrders.UpgradeItem[] | undefined
) {
  if (!nextGroup) {
    return true;
  }

  if (group.length > 1 || nextGroup.length > 1) {
    return false;
  }

  return equals(group[0]?.boxCode, nextGroup[0]?.boxCode);
}
