import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import * as boeks from 'api/models/boekestyns';
import { RootState } from '@/services/store';
import { ProblemDetails } from '@/utils/problem-details';

interface ProductDefaultDetailState {
  boekestynPlantId: string | null;
  boekestynCustomerAbbreviation: string | null;
  quantityPerFinishedItem: number | null;
  upgradeLabourHours: number | null;
  isUpgrade: boolean;
  ignoreOverrideQuantity: boolean;
  multipleProducts: boolean;
  mappings: boeks.BoekestynPrebookItemProduct[];
  overrides: boeks.BoekestynPrebookItemProductOverride[];
  error: ProblemDetails | null;
}

const initialState: ProductDefaultDetailState = {
  boekestynPlantId: null,
  boekestynCustomerAbbreviation: null,
  quantityPerFinishedItem: null,
  upgradeLabourHours: null,
  isUpgrade: false,
  ignoreOverrideQuantity: false,
  multipleProducts: false,
  mappings: [],
  overrides: [],
  error: null,
};

export const productDefaultDetailSlice = createSlice({
  name: 'product-default-detail',
  initialState,
  reducers: {
    setError(state, { payload }: PayloadAction<ProblemDetails | null>) {
      state.error = payload;
    },
    setBoekestynPlantId(state, { payload }: PayloadAction<string | null>) {
      state.boekestynPlantId = payload;
    },
    setBoekestynCustomerAbbreviation(
      state,
      { payload }: PayloadAction<string | null>
    ) {
      state.boekestynCustomerAbbreviation = payload;
    },
    setQuantityPerFinishedItem(
      state,
      { payload }: PayloadAction<number | null>
    ) {
      state.quantityPerFinishedItem = payload;
    },
    setUpgradeLabourHours(state, { payload }: PayloadAction<number | null>) {
      state.upgradeLabourHours = payload;
    },
    setIsUpgrade(state, { payload }: PayloadAction<boolean>) {
      state.isUpgrade = payload;
    },
    setIgnoreOverrideQuantity(state, { payload }: PayloadAction<boolean>) {
      state.ignoreOverrideQuantity = payload;
    },
    setMultipleProducts(state, { payload }: PayloadAction<boolean>) {
      state.multipleProducts = payload;
      if (payload) {
        state.boekestynPlantId = null;
        state.boekestynCustomerAbbreviation = null;
      } else {
        state.mappings = [];
        state.overrides = [];
      }
    },
    setMappings(
      state,
      { payload }: PayloadAction<boeks.BoekestynPrebookItemProduct[]>
    ) {
      state.mappings = payload;
    },
    setOverrides(
      state,
      { payload }: PayloadAction<boeks.BoekestynPrebookItemProductOverride[]>
    ) {
      state.overrides = payload;
    },
  },
});

export const {
  setError,
  setBoekestynPlantId,
  setBoekestynCustomerAbbreviation,
  setQuantityPerFinishedItem,
  setUpgradeLabourHours,
  setIsUpgrade,
  setIgnoreOverrideQuantity,
  setMultipleProducts,
  setMappings,
  setOverrides,
} = productDefaultDetailSlice.actions;

export const selectError = ({ productDefaultDetail }: RootState) =>
  productDefaultDetail.error;
export const selectBoekestynPlantId = ({ productDefaultDetail }: RootState) =>
  productDefaultDetail.boekestynPlantId;
export const selectBoekestynCustomerAbbreviation = ({
  productDefaultDetail,
}: RootState) => productDefaultDetail.boekestynCustomerAbbreviation;
export const selectQuantityPerFinishedItem = ({
  productDefaultDetail,
}: RootState) => productDefaultDetail.quantityPerFinishedItem;
export const selectUpgradeLabourHours = ({ productDefaultDetail }: RootState) =>
  productDefaultDetail.upgradeLabourHours;
export const selectIsUpgrade = ({ productDefaultDetail }: RootState) =>
  productDefaultDetail.isUpgrade;
export const selectIgnoreOverrideQuantity = ({
  productDefaultDetail,
}: RootState) => productDefaultDetail.ignoreOverrideQuantity;
export const selectMultipleProducts = ({ productDefaultDetail }: RootState) =>
  productDefaultDetail.multipleProducts;
export const selectMappings = ({ productDefaultDetail }: RootState) =>
  productDefaultDetail.mappings;
export const selectOverrides = ({ productDefaultDetail }: RootState) =>
  productDefaultDetail.overrides;

export default productDefaultDetailSlice.reducer;
