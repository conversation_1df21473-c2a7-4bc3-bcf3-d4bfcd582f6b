import React, { Fragment, useEffect, useRef, useState } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { GlobalHotKeys, configure as configureHotkeys } from 'react-hotkeys';
import { DateTime } from 'luxon';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import * as HeadlessUI from '@headlessui/react';
import { apiUrl } from 'api/api-base';
import {
  useLazyItemPricesQuery,
  useLazyHolidaysQuery,
  FutureOrderCreateResponse,
} from 'api/future-orders-service';
import {
  useOpenBlanketItemsQuery,
  prebooksApi,
  useSeasonsQuery,
} from 'api/prebooks-service';
import {
  useDefaultVendorOverridesQuery,
  usePriceDeviationWarningsQuery,
} from 'api/settings-service';
import {
  useCustomersQuery,
  useInventoryItemsQuery,
  useSalespeopleQuery,
  useShippingMethodsQuery,
  useVendorsQuery,
  useInventoryCommentsQuery,
  spireService,
} from 'api/spire-service';
import * as boeks from 'api/models/boekestyns';
import * as futureOrders from 'api/models/future-orders';
import * as prebooks from 'api/models/prebooks';
import * as spire from 'api/models/spire';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { routes } from '@/services/routes';
import { Alert } from '@/components/alert';
import { useAlert } from '@/components/useAlert';
import { Combobox } from '@/components/combo-box';
import { DropdownMenu } from '@/components/drop-down-menu';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import { CreateSeason } from '@/components/create-season';
import { noPotCover } from '@/components/pot-covers';
import { ShipToCombobox } from '@/components/ship-to-combo-box';
import { BlankSlate } from '@/components/future-orders/blank-slate';
import { Inventory } from '@/components/future-orders/inventory';
import {
  ClearItems,
  ClearItemsResults,
} from '@/components/future-orders/clear-items';
import {
  clearState,
  clearError,
  setError,
  setShipTo,
  setSalesperson,
  setRequiredDate,
  setArrivalDate,
  setSeasonName,
  setShipVia,
  setBoxCode,
  setRequiresLabels,
  setCustomerPurchaseOrderNumber,
  setFreightPerCase,
  setFreightPerLoad,
  setFreightIsActual,
  setComments,
  setInternalComments,
  setGrowerItemNotes,
  setSpireNotes,
  setCustomerDetail,
  addItem,
  selectError,
  selectCustomer,
  selectSalesperson,
  selectShipTo,
  selectShipVia,
  selectRequiredDate,
  selectArrivalDate,
  selectSeasonName,
  selectBoxCode,
  selectRequiresLabels,
  selectCustomerPurchaseOrderNumber,
  selectFreightPerCase,
  selectFreightPerLoad,
  selectFreightIsActual,
  selectGrowerItemNotes,
  selectCustomerInfo,
  selectSpireNotes,
  selectComments,
  selectInternalComments,
  selectItems,
  selectCustomerDetail,
  getCustomerDetail,
  selectCustomerItemCodeDefaults,
  InventoryItemWithDefaults,
  createFutureOrder,
  copyFutureOrder,
  setAllWeightsAndMeasures,
  setAllUseAvailabilityPricing,
  setAllHasPotCovers,
  setAllPotCovers,
  setAllDateCodes,
  setAllRetails,
  setAllUpcs,
  setAllUnitPrices,
  setAllExpanded,
  setShowInventoryDialog,
  selectShowInventoryDialog,
  selectPotCovers,
  setItemBoekestynProducts,
  setCustomerInfo,
  setItemAvailabilityPrice,
  selectAvailabilityUnitPrices,
  selectDefaultVendorOverrides,
  selectHoliday,
} from '@/components/future-orders/future-order-create-slice';
import {
  setCustomerFilter,
  setShipToFilter,
} from '@/components/future-orders/add-from-order-slice';
import { BoekestynProducts } from '@/components/future-orders/boekestyn-products';
import { NewFutureOrderItemExpanded } from '@/components/future-orders/new-future-order-item-expanded';
import { NewFutureOrderItemCollapsed } from '@/components/future-orders/new-future-order-item-collapsed';
import { FutureOrderPrebookEmails } from '@/components/future-orders/future-order-prebook-emails';
import { AddFromOrder } from '@/components/future-orders/add-from-order';
import {
  duplicateUpcWarning,
  findDefaultVendorOverride,
  itemIsOverbooked,
  showPriceWarning,
} from '@/components/future-orders/item-functions';
import { CustomerInfoDialog } from '@/components/future-orders/customer-info-dialog';
import { classNames } from '@/utils/class-names';
import { dateIsInPast, startsWith } from '@/utils/equals';
import { handleFocus } from '@/utils/focus';
import {
  formatCurrency,
  formatDate,
  formatNumber,
  parseNullableCurrency,
  parseQuantity,
} from '@/utils/format';

export default function New() {
  const dispatch = useAppDispatch(),
    router = useRouter(),
    { from } = router.query,
    { data: customers } = useCustomersQuery(),
    { data: salespeople } = useSalespeopleQuery(),
    { data: vendors } = useVendorsQuery(),
    { data: shippingMethods } = useShippingMethodsQuery(),
    { data: openBlanketItemsResponse } = useOpenBlanketItemsQuery(),
    { data: seasons } = useSeasonsQuery(),
    { data: inventoryItemsData } = useInventoryItemsQuery(),
    { data: inventoryComments } = useInventoryCommentsQuery(),
    [itemPricesQuery, { data: itemPrices }] = useLazyItemPricesQuery(),
    [holidays] = useLazyHolidaysQuery(),
    { data: priceDeviationWarnings } = usePriceDeviationWarningsQuery(),
    error = useAppSelector(selectError),
    shipTo = useAppSelector(selectShipTo),
    customer = useAppSelector(selectCustomer),
    salesperson = useAppSelector(selectSalesperson),
    shipVia = useAppSelector(selectShipVia),
    requiredDate = useAppSelector(selectRequiredDate),
    arrivalDate = useAppSelector(selectArrivalDate),
    seasonName = useAppSelector(selectSeasonName),
    boxCode = useAppSelector(selectBoxCode),
    requiresLabels = useAppSelector(selectRequiresLabels),
    customerPurchaseOrderNumber = useAppSelector(
      selectCustomerPurchaseOrderNumber
    ),
    freightPerCase = useAppSelector(selectFreightPerCase),
    freightPerLoad = useAppSelector(selectFreightPerLoad),
    freightIsActual = useAppSelector(selectFreightIsActual),
    comments = useAppSelector(selectComments),
    internalComments = useAppSelector(selectInternalComments),
    spireNotes = useAppSelector(selectSpireNotes),
    growerItemNotes = useAppSelector(selectGrowerItemNotes),
    customerInfo = useAppSelector(selectCustomerInfo),
    items = useAppSelector(selectItems),
    customerDetail = useAppSelector(selectCustomerDetail),
    productCustomerDefaults = useAppSelector(selectCustomerItemCodeDefaults),
    availabilityUnitPrices = useAppSelector(selectAvailabilityUnitPrices),
    showInventoryDialog = useAppSelector(selectShowInventoryDialog),
    potCovers = useAppSelector(selectPotCovers),
    defaultVendorOverrides = useAppSelector(selectDefaultVendorOverrides),
    holiday = useAppSelector(selectHoliday),
    [saving, setSaving] = useState(false),
    [allWeightsAndMeasuresSelected, setAllWeightsAndMeasuresSelected] =
      useState(false),
    [allHasPotCover, setAllHasPotCover] = useState(false),
    [allPotCover, setAllPotCover] = useState(''),
    [allDateCode, setAllDateCode] = useState(''),
    [allAvailabilityPricingSelected, setAllAvailabilityPricingSelected] =
      useState(false),
    [showBlankSlate, setShowBlankSlate] = useState(true),
    [showAddSeasonDialog, setShowAddSeasonDialog] = useState(false),
    [showExpandAllTooltip, setShowExpandAllTooltip] = useState(false),
    [showCollapseAllTooltip, setShowCollapseAllTooltip] = useState(false),
    [showClearAllTooltip, setShowClearAllTooltip] = useState(false),
    [showClearAllDialog, setShowClearAllDialog] = useState(false),
    [showCustomerInfoTooltip, setShowCustomerInfoTooltip] = useState(false),
    [showCustomerInfoDialog, setShowCustomerInfoDialog] = useState(false),
    [showAddFromOrder, setShowAddFromOrder] = useState(false),
    [prebooks, setPrebooks] = useState<prebooks.PrebookDetail[]>([]),
    [showRequiredDateDialog, setShowRequiredDateDialog] = useState(false),
    [showConfirmPriceWarnings, setShowConfirmPriceWarnings] = useState<
      string | null
    >(null),
    [priceWarningsConfirmed, setPriceWarningsConfirmed] = useState(false),
    [freightPerCaseFormatted, setFreightPerCaseFormatted] = useState<
      string | null
    >(freightPerCase ? formatCurrency(freightPerCase) : null),
    [freightPerLoadFormatted, setFreightPerLoadFormatted] = useState<
      string | null
    >(freightPerLoad ? formatCurrency(freightPerLoad) : null),
    { show: showConfirmDirty, alertJSX: confirmDirtyJSX } = useAlert(),
    { show: showConfirmDateInPast, alertJSX: confirmDateInPastJSX } =
      useAlert(),
    requiredDateRef = useRef<HTMLInputElement | null>(null),
    shipToRef = useRef<HTMLInputElement | null>(null),
    potCoverRef = useRef<HTMLInputElement | null>(null),
    blanketItems = openBlanketItemsResponse?.blanketItems || [],
    productDefaults = inventoryItemsData?.productDefaults || [],
    seasonNames = (seasons || []).map((s) => s.name),
    cellClassName = 'py-2 px-1 text-left text-sm font-semibold text-gray-900',
    overbookedError = items.some((i) => itemIsOverbooked(i, blanketItems))
      ? 'Please ensure no blanket items are overbooked'
      : null,
    totalCases = items.reduce((total, i) => total + (i.orderQuantity || 0), 0),
    totalDollars = items.reduce((total, i) => {
      const unitPrice = i.useAvailabilityPricing
        ? availabilityUnitPrices[i.id]
        : i.unitPrice;
      return total + (i.orderQuantity || 0) * (unitPrice || 0);
    }, 0),
    duplicateUpcs = duplicateUpcWarning(items),
    keyMap = { ADD_ITEM: 'alt+a', ADD_EXISTING: 'alt+x' },
    hotKeysHandlers = {
      ADD_ITEM: () => dispatch(setShowInventoryDialog(true)),
      ADD_EXISTING: () => setShowAddFromOrder(true),
    },
    requiredDateInPastError = dateIsInPast(requiredDate)
      ? 'The Required Date is in the past'
      : null;

  useDefaultVendorOverridesQuery();

  configureHotkeys({ ignoreTags: [] });

  useEffect(() => {
    return function cleanup() {
      setShowConfirmPriceWarnings(null);
    };
  }, []);

  useEffect(() => {
    dispatch(clearState());
  }, [dispatch]);

  useEffect(() => {
    if (typeof from === 'string' && parseQuantity(from)) {
      setShowBlankSlate(false);
      setShowRequiredDateDialog(true);
    }
  }, [dispatch, from]);

  useEffect(() => {
    const shipTos = customerDetail?.shippingAddresses || [];
    if (shipTo && !shipTos.find((st) => st.id === shipTo.id)) {
      dispatch(setShipTo(null));
    } else if (!shipTo && shipTos.length === 1) {
      dispatch(setShipTo(shipTos[0]));
    }
  }, [shipTo, customerDetail, dispatch]);

  useEffect(() => {
    const customerId = customer?.id,
      shipToId = shipTo?.id,
      availabilityPricedItems = items.filter((i) => i.useAvailabilityPricing),
      ids = availabilityPricedItems.flatMap((i) =>
        i.spireInventoryId ? [i.spireInventoryId] : []
      );
    itemPricesQuery({ customerId, shipToId, requiredDate, items: ids }).then(
      (result) => {
        if (result.data) {
          availabilityPricedItems.forEach((i) => {
            const price = result.data!.find(
              (p) => p.spireInventoryItemId === i.spireInventoryId
            );
            if (price) {
              dispatch(
                setItemAvailabilityPrice({ itemId: i.id, value: price.price })
              );
            }
          });
        }
      }
    );
  }, [customer, shipTo, requiredDate, items, itemPricesQuery, dispatch]);

  useEffect(() => {
    if (shipTo) {
      spireService.shipToDetail(shipTo.id).then(({ shipTo }) => {
        dispatch(setCustomerInfo(shipTo?.customerInfo || null));
      });
    } else {
      dispatch(setCustomerInfo(null));
    }
  }, [dispatch, shipTo]);

  useEffect(() => {
    if (requiredDate) {
      const date = DateTime.fromFormat(requiredDate, 'yyyy-MM-dd');
      holidays(date.year);
    }
  }, [holidays, requiredDate]);

  useEffect(() => {
    if (freightPerCase) {
      setFreightPerCaseFormatted(formatCurrency(freightPerCase));
    } else {
      setFreightPerCaseFormatted(null);
    }
  }, [freightPerCase]);

  useEffect(() => {
    if (freightPerLoad) {
      setFreightPerLoadFormatted(formatCurrency(freightPerLoad));
    } else {
      setFreightPerLoadFormatted(null);
    }
  }, [freightPerLoad]);

  useEffect(() => {
    if (freightPerCase) {
      const perLoad = Math.round(freightPerCase * totalCases * 100) / 100;
      dispatch(setFreightPerLoad(perLoad));
    }
  }, [dispatch, freightPerCase, totalCases]);

  const handleClearErrorClick = () => {
    dispatch(clearError());
  };

  const handleCustomerChange = async (customer: spire.Customer | null) => {
    await dispatch(getCustomerDetail(customer?.id || null));

    if (customer) {
      shipToRef.current?.focus();
    }
  };

  const handleShipToChange = (
    shipTo: spire.CustomerShipTo | null,
    customer?: spire.CustomerDetail
  ) => {
    if (customer) {
      dispatch(setCustomerDetail(customer));
    }

    dispatch(setShipTo(shipTo));
  };

  const handleCustomerInfoTooltipMouseEnter = () => {
    setShowCustomerInfoTooltip(true);
  };

  const handleCustomerInfoTooltipMouseLeave = () => {
    setShowCustomerInfoTooltip(false);
  };

  const handleCustomerInfoClick = () => {
    setShowCustomerInfoDialog(true);
  };

  const handleCustomerInfoDialogClose = () => {
    setShowCustomerInfoDialog(false);
  };

  const handleCustomerInfoDialogSave = async (info: string | null) => {
    console.log(info);
  };

  const handleRequiresLabelsChange = (value: boolean) => {
    dispatch(setRequiresLabels(value));
  };

  const handleSalespersonChange = (salesperson: spire.Salesperson | null) => {
    dispatch(setSalesperson(salesperson));
  };

  const handleShipViaChange = (shipVia: spire.ShippingMethod | null) => {
    dispatch(setShipVia(shipVia));
  };

  const handleRequiredDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value || null;
    dispatch(setRequiredDate(value));
  };

  const handleRequiredDateKeyUp = async (e: React.KeyboardEvent) => {
    if (showRequiredDateDialog && requiredDate && e.key === 'Enter') {
      e.preventDefault();

      await handleRequiredDateConfirm();
    }
  };

  const handleRequiredDateConfirm = async () => {
    if (requiredDate && typeof from === 'string' && parseQuantity(from)) {
      await dispatch(copyFutureOrder(parseQuantity(from)));
      setShowRequiredDateDialog(false);
    }
  };

  const handleArrivalDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setArrivalDate(e.target.value || null));
  };

  const handleSeasonNameChange = (season: string | null) => {
    dispatch(setSeasonName(season));
  };

  const handleAddSeasonClick = () => {
    setShowAddSeasonDialog(true);
  };

  const handleAddSeasonCancel = () => {
    setShowAddSeasonDialog(false);
  };

  const handleAddSeasonConfirm = async (season: prebooks.Season) => {
    dispatch(setSeasonName(season.name));
    setShowAddSeasonDialog(false);
  };

  const handleBoxCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setBoxCode(e.target.value || null));
  };

  const handleCustomerPurchaseOrderNumberChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    dispatch(setCustomerPurchaseOrderNumber(e.target.value || null));
  };

  const handleFreightPerCaseChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setFreightPerCaseFormatted(e.target.value);
  };

  const handleFreightPerCaseBlur = () => {
    const value = parseNullableCurrency(freightPerCaseFormatted),
      formatted = value ? formatCurrency(value) : null,
      perLoad =
        value == null || totalCases === 0
          ? null
          : formatted === freightPerCaseFormatted
          ? freightPerLoad
          : Math.round(value * totalCases * 100) / 100;

    dispatch(setFreightPerCase(value));
    dispatch(setFreightPerLoad(perLoad));
  };

  const handleFreightPerLoadChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setFreightPerLoadFormatted(e.target.value);
  };

  const handleFreightPerLoadBlur = () => {
    const value = parseNullableCurrency(freightPerLoadFormatted),
      formatted = value ? formatCurrency(value) : null,
      perCase =
        value == null || totalCases === 0
          ? null
          : formatted === freightPerLoadFormatted
          ? freightPerCase
          : Math.round((value / totalCases) * 100) / 100;

    dispatch(setFreightPerLoad(value));
    dispatch(setFreightPerCase(perCase));
  };

  const handleFreightIsActualChange = (value: boolean) => {
    dispatch(setFreightIsActual(value));
  };

  const handleInternalCommentsChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const comment = e.target.value || null;
    dispatch(setInternalComments(comment));
  };

  const handleAddStandardComment = (comment: spire.InventoryComment) => {
    const updated = comments.map((c) => ({ ...c })),
      id = comments.reduce((min, c) => Math.min(min, c.id), 0) - 1;

    updated.push({
      id,
      comments: comment.comments,
      isStandardComment: true,
    });

    dispatch(setComments(updated));
  };

  const handleDeleteStandardCommentClick = (id: number) => {
    const updated = comments.filter((c) => c.id !== id);

    dispatch(setComments(updated));
  };

  const handleSpireNotesChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    dispatch(setSpireNotes(e.target.value || null));
  };

  const handleGrowerItemNotesChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    dispatch(setGrowerItemNotes(e.target.value || null));
  };

  const handleAllWeightsAndMeasuresSelectedChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const selected = e.target.checked;
    setAllWeightsAndMeasuresSelected(selected);
  };

  const handleAllWeightsAndMeasuresClick = () => {
    dispatch(setAllWeightsAndMeasures(allWeightsAndMeasuresSelected));
  };

  const handleAllUseAvailabilityPricingSelectedChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const selected = e.target.checked;
    setAllAvailabilityPricingSelected(selected);
  };

  const handleAllUseAvailabilityPricingClick = () => {
    dispatch(setAllUseAvailabilityPricing(allAvailabilityPricingSelected));
  };

  const handleAllHasPotCoverSelectedChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const selected = e.target.checked;
    setAllHasPotCover(selected);

    if (selected) {
      setAllPotCover('PC');
      window.setTimeout(() => potCoverRef.current?.focus(), 100);
    } else {
      setAllPotCover('');
    }
  };

  const handleAllPotCoverChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.toUpperCase();
    setAllPotCover(value);
  };

  const handleAllPotCoverSelected = (value: string) => {
    setAllPotCover(value);
  };

  const handleAllPotCoverClick = () => {
    dispatch(setAllHasPotCovers(allHasPotCover));
    dispatch(setAllPotCovers(allPotCover));
  };

  const handleAllDateCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.toUpperCase();
    setAllDateCode(value);
  };

  const handleAllDateCodeClick = () => {
    dispatch(setAllDateCodes(allDateCode));
  };

  const handleExpandAllTooltipMouseEnter = () => {
    setShowExpandAllTooltip(true);
  };

  const handleExpandAllTooltipMouseLeave = () => {
    setShowExpandAllTooltip(false);
  };

  const handleExpandAllClick = () => {
    dispatch(setAllExpanded(true));
  };

  const handleCollapseAllTooltipMouseEnter = () => {
    setShowCollapseAllTooltip(true);
  };

  const handleCollapseAllTooltipMouseLeave = () => {
    setShowCollapseAllTooltip(false);
  };

  const handleCollapseAllClick = () => {
    dispatch(setAllExpanded(false));
  };

  const handleClearAllTooltipMouseEnter = () => {
    setShowClearAllTooltip(true);
  };

  const handleClearAllTooltipMouseLeave = () => {
    setShowClearAllTooltip(false);
  };

  const handleClearAllClick = () => {
    setShowClearAllDialog(true);
  };

  const handleClearAllCancel = () => {
    setShowClearAllDialog(false);
  };

  const handleClearAllConfirm = (results: ClearItemsResults) => {
    const {
      upc,
      upcValue,
      pricing,
      dateCode,
      retail,
      weightsAndMeasures,
      potCover,
    } = results;
    if (upc) {
      dispatch(setAllUpcs(upcValue));
    }
    if (pricing) {
      dispatch(setAllUnitPrices(null));
    }
    if (dateCode) {
      dispatch(setAllDateCodes(null));
    }
    if (retail) {
      dispatch(setAllRetails(null));
    }
    if (weightsAndMeasures) {
      dispatch(setAllWeightsAndMeasures(false));
    }
    if (potCover) {
      dispatch(setAllPotCovers(null));
      dispatch(setAllHasPotCovers(false));
    }
    setShowClearAllDialog(false);
  };

  const handleAddItemClick = () => {
    dispatch(setShowInventoryDialog(true));
  };

  const handleAddItemConfirm = async (
    result: spire.InventoryItem,
    blanketItemId: number | null,
    boekestynPlantId: string | null,
    boekestynCustomerAbbreviation: string | null,
    comments: string | null
  ) => {
    const item: InventoryItemWithDefaults = {
      ...result,
      blanketItemId: null,
      comments: null,
      vendorId: null,
      vendorName: null,
      phytoRequired: false,
      boekestynPlantId: null,
      boekestynCustomerAbbreviation: null,
      useAvailabilityPricing: false,
      upgradeLabourHours: 0,
      quantityPerFinishedItem: null,
    };

    if (blanketItemId && blanketItemId > 0) {
      item.blanketItemId = blanketItemId;
    }
    if (comments) {
      item.comments = comments;
    }

    if (customer && shipTo) {
      const { default: details } = await prebooksApi.productShipToDefaults(
        item.id,
        customer.id,
        shipTo.id
      );

      if (details.id) {
        item.customerItemCode = details.customerItemCode;
        item.hasPotCover = details.hasPotCover;
        item.potCover = details.potCover;
        item.retail = details.retail;
        item.unitPrice = details.unitPrice;
        item.upc = details.upc;
        item.weightsAndMeasures = details.weightsAndMeasures;
      }
    }

    if (allHasPotCover && allPotCover) {
      item.hasPotCover = true;
      item.potCover = allPotCover;
    }

    if (allDateCode) {
      item.dateCode = allDateCode;
    }

    if (allWeightsAndMeasuresSelected) {
      item.weightsAndMeasures = true;
    }

    if (allAvailabilityPricingSelected) {
      item.useAvailabilityPricing = true;
      item.unitPrice = null;
    }

    if (item.partNo.endsWith('PC') && !item.potCover) {
      item.hasPotCover = true;
      item.potCover = 'PC';
    } else if (noPotCover(item.partNo, item.description)) {
      item.hasPotCover = false;
      item.potCover = null;
    }

    if (item.blanketItemId) {
      const blanketItem = blanketItems.find((i) => i.id === item.blanketItemId);

      if (blanketItem) {
        item.vendorId = blanketItem.vendorId;
        item.vendorName = blanketItem.vendorName;
      }
    } else {
      const override = findDefaultVendorOverride(
          item.partNo,
          requiredDate,
          defaultVendorOverrides
        ),
        overrideVendor = vendors?.find((v) => v.id === override?.vendorId);
      if (overrideVendor) {
        item.vendorId = overrideVendor.id;
        item.vendorName = overrideVendor.name;
      }
    }

    if (!item.vendorId && result.primaryVendor?.vendorNo) {
      const primaryVendor = vendors?.find(
        (v) => v.vendorNo === result.primaryVendor?.vendorNo
      );
      if (primaryVendor) {
        item.vendorId = primaryVendor.id;
        item.vendorName = primaryVendor.name;
      }
    }

    const productDefault = productDefaults.find(
        (d) => d.spireInventoryId === result.id
      ),
      isBoekestyn =
        item.vendorId === boeks.BoekestynVendorId || !!boekestynPlantId;

    if (productDefault?.upgradeLabourHours) {
      item.upgradeLabourHours = productDefault.upgradeLabourHours;
    }

    if (productDefault?.isUpgrade) {
      item.upgradeSheet = true;
    }

    if (isBoekestyn) {
      item.boekestynPlantId =
        boekestynPlantId || productDefault?.boekestynPlantId || null;
      item.boekestynCustomerAbbreviation =
        boekestynCustomerAbbreviation ||
        productDefault?.boekestynCustomerAbbreviation ||
        boeks.WeeklyCustomerAbbreviation;
      item.quantityPerFinishedItem =
        productDefault?.quantityPerFinishedItem || 0;
      item.vendorId = boeks.BoekestynVendorId;
      item.vendorName = boeks.BoekestynVendorName;
    }

    await dispatch(addItem(item));
  };

  const handleAddItemCancel = () => {
    dispatch(setShowInventoryDialog(false));
  };

  const handleAddFromExistingClick = () => {
    dispatch(setCustomerFilter(customer?.name || ''));
    dispatch(setShipToFilter(shipTo?.shipId || ''));
    setShowAddFromOrder(true);
  };

  const handleAddFromExistingClose = () => {
    setShowAddFromOrder(false);
  };

  const handleAddFromExistingAddItem = (
    detail: futureOrders.FutureOrderDetailItem
  ) => {
    if (
      detail.spireInventoryId &&
      detail.spirePartNumber &&
      detail.description
    ) {
      const item = {
        id: detail.spireInventoryId,
        partNo: detail.spirePartNumber,
        description: detail.description,
        uom: {
          location: detail.phytoRequired ? spire.PhytoLocation : '',
        },
        dateCode: null,
        hasPotCover: detail.hasPotCover,
        potCover: detail.potCover,
        upc: detail.upc,
        weightsAndMeasures: detail.weightsAndMeasures,
        retail: detail.retail,
        unitPrice: detail.unitPrice,
        customerItemCode: detail.customerItemCode,
        upgradeSheet: detail.upgradeSheet,
        phytoRequired: detail.phytoRequired,
        blanketItemId: detail.blanketItemId,
        comments: detail.comments,
        vendorId: detail.vendorId,
        vendorName: detail.vendorName,
        boekestynPlantId: detail.boekestynPlantId,
        boekestynCustomerAbbreviation: detail.boekestynCustomerAbbreviation,
        quantityPerFinishedItem: detail.quantityPerFinishedItem,
        useAvailabilityPricing: detail.useAvailabilityPricing,
        specialPrice: detail.specialPrice,
        growerItemNotes: detail.growerItemNotes,
      };

      dispatch(addItem(item));
    }
  };

  const handleBlankSlateClose = () => {
    setShowBlankSlate(false);
  };

  const handleShowEmailsClose = () => {
    setPrebooks([]);
  };

  const handleBoekestynProductSaved = (
    itemId: number,
    value: futureOrders.FutureOrderBoekestynProduct[]
  ) => {
    dispatch(setItemBoekestynProducts({ itemId, value }));
  };

  const handlePriceWarningConfirm = () => {
    setPriceWarningsConfirmed(true);
    setShowConfirmPriceWarnings(null);
  };

  const handlePriceWarningCancel = () => {
    setShowConfirmPriceWarnings(null);
  };

  const handleCancelClick = () => {
    if (window.history.length > 1) {
      router.back();
    } else {
      router.push(routes.futureOrders.list.to());
    }
  };

  const handleSaveAndCloseClick = async () => {
    const sendEmails = await showConfirmDirty({
        title: 'Unsent changes',
        message:
          'Prebooks have not been sent to the Growers. Would you like to send them now?',
        colour: 'info',
        confirmButtonText: 'Yes',
        cancelButtonText: 'No',
        icon: 'question-circle',
      }),
      saved = await save();
    if (saved) {
      if (sendEmails) {
        router.push(`${routes.futureOrders.detail.to(saved)}?action=send`);
      } else {
        router.push(routes.futureOrders.list.to());
      }
    }
  };

  const handleSaveAndPrintClick = async () => {
    const saved = await save();
    if (saved) {
      window.open(apiUrl(`reports/future-orders/${saved}`), '_blank');
      router.push(routes.futureOrders.list.to());
    }
  };

  const handleSaveAndPrintPrebooksClick = async () => {
    const saved = await save();
    if (saved) {
      window.open(apiUrl(`reports/future-orders/${saved}/prebooks`), '_blank');
      router.push(routes.futureOrders.list.to());
    }
  };

  const handleSaveAndSendClick = async () => {
    const saved = await save();
    if (saved) {
      router.push(`${routes.futureOrders.detail.to(saved)}?action=send`);
    }
  };

  const save = async () => {
    try {
      setSaving(true);

      if (!requiredDate && !seasonName) {
        const error =
          !requiredDate && !seasonName
            ? 'Please enter either the Required Date or the Season'
            : 'Please enter the Required Date';
        dispatch(setError(error));
      } else if (!customer) {
        dispatch(setError('Please select the Customer.'));
      } else if (!items.length) {
        dispatch(setError('Please add one or more items.'));
      } else if (items.some((i) => !i.orderQuantity)) {
        dispatch(setError('Please ensure all items have a non-0 quantity.'));
      } else if (items.some((i) => !i.vendorId)) {
        dispatch(setError('Please choose a vendor for all items.'));
      } else {
        const dateInPastResponse =
          !requiredDateInPastError ||
          (await showConfirmDateInPast({
            colour: 'danger',
            title: 'Required Date in past',
            message: `The Required Date ${formatDate(
              requiredDate,
              'MMM d, yyyy'
            )} is in the past. Are you sure you would like to save this Future Order?`,
            confirmButtonText: 'Yes',
            cancelButtonText: 'No',
          }));
        if (!dateInPastResponse) {
          return false;
        }

        const response: any = await dispatch(createFutureOrder());
        if (response?.payload) {
          const payload = response.payload as FutureOrderCreateResponse;
          return payload.id;
        }
      }

      return false;
    } finally {
      setSaving(false);
    }
  };

  return (
    <div>
      <GlobalHotKeys keyMap={keyMap} handlers={hotKeysHandlers} />
      <Head>
        <title>New Future Order</title>
      </Head>
      <header className="sticky top-0 z-20 bg-white shadow">
        <div className="flex w-full items-center justify-between px-8 py-6">
          <div className="flex flex-grow justify-between">
            <h2 className="text-2xl font-bold leading-7 text-gray-900">
              <Icon icon="file-check" />
              &nbsp; New Future Order
              {!!from && ` (copied from #${formatNumber(from, '00000')})`}
            </h2>
            <HeadlessUI.Switch.Group as="div" className="mx-8">
              <HeadlessUI.Switch
                checked={requiresLabels}
                onChange={handleRequiresLabelsChange}
                className={classNames(
                  requiresLabels ? 'bg-blue-400' : 'bg-gray-200',
                  'relative inline-flex h-4 w-8 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
                )}
              >
                <span
                  aria-hidden="true"
                  className={classNames(
                    requiresLabels ? 'translate-x-4' : 'translate-x-0',
                    'pointer-events-none inline-block h-3 w-3 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                  )}
                />
              </HeadlessUI.Switch>
              <HeadlessUI.Switch.Label className="ml-2">
                <span className="text-xs">Special Labels</span>
              </HeadlessUI.Switch.Label>
            </HeadlessUI.Switch.Group>
          </div>
          <div className="flex">
            <button
              type="button"
              onClick={handleCancelClick}
              disabled={saving}
              className="btn-secondary inline-flex"
            >
              Cancel
            </button>
            <button
              type="button"
              className="btn-secondary ml-3 inline-flex border-blue-600 px-2 text-blue-600 disabled:border-blue-300 disabled:text-blue-300 disabled:hover:border-blue-300 disabled:hover:text-blue-300"
              disabled={saving}
              onClick={handleSaveAndCloseClick}
            >
              Save &amp; Close &nbsp;
              <Icon icon="save" />
            </button>
            {confirmDirtyJSX}
            <button
              type="button"
              className="btn-secondary ml-3 inline-flex border-blue-600 px-2 text-blue-600"
              disabled={saving}
              onClick={handleSaveAndPrintClick}
            >
              Save &amp; Print &nbsp;
              <Icon icon="save" />
              &nbsp;
              <Icon icon="print" />
            </button>
            <button
              type="button"
              className="btn-secondary ml-3 inline-flex border-blue-600 px-2 text-blue-600"
              disabled={saving}
              onClick={handleSaveAndPrintPrebooksClick}
            >
              Save &amp; Print Prebooks &nbsp;
              <Icon icon="save" />
              &nbsp;
              <Icon icon="print-magnifying-glass" />
            </button>

            <button
              type="button"
              className="btn-primary ml-3 inline-flex"
              disabled={saving}
              onClick={handleSaveAndSendClick}
            >
              Save &amp; Send &nbsp;
              <Icon icon="save" />
              &nbsp;
              <Icon icon="paper-plane" />
            </button>
          </div>
        </div>
      </header>
      <main>
        <div className="mx-auto max-w-7xl px-8">
          <Error
            error={error}
            clear={handleClearErrorClick}
            containerClasses="w-full mt-4"
          />
          <Error
            error={overbookedError}
            type="warning"
            containerClasses="w-full mt-4 whitespace-pre"
          />
          <Error
            error={requiredDateInPastError}
            type="warning"
            containerClasses="w-full mt-4 whitespace-pre"
          />
          <Error
            error={duplicateUpcs}
            type="warning"
            containerClasses="w-full mt-4"
          />
          <Error
            error={
              holiday
                ? `The Required Date for this order is on a holiday: ${holiday.nameEn}`
                : null
            }
            type="warning"
            containerClasses="w-full mt-4"
          />
          <form className="space-y-8 divide-y divide-gray-200">
            <div className="space-y-8 divide-y divide-gray-200">
              <div>
                <div className="mt-6 grid grid-cols-4 gap-x-4 gap-y-6">
                  <div className="grid grid-cols-2 gap-x-2">
                    <label
                      htmlFor="required-date"
                      className="block text-sm font-medium text-gray-500"
                    >
                      Required Date &nbsp;
                    </label>
                    <label
                      htmlFor="required-date"
                      className="block text-sm font-medium text-gray-500"
                    >
                      Arrival Date
                    </label>
                    <div className="mt-1">
                      <input
                        type="date"
                        max="2050-01-01"
                        name="requiredDate"
                        id="required-date"
                        value={requiredDate || ''}
                        onChange={handleRequiredDateChange}
                        ref={requiredDateRef}
                        className="block w-full rounded-md border-gray-300 text-sm shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                    <div className="mt-1">
                      <input
                        type="date"
                        max="2050-01-01"
                        name="arrivalDate"
                        id="arrival-date"
                        value={arrivalDate || ''}
                        min={requiredDate || ''}
                        onChange={handleArrivalDateChange}
                        className="block w-full rounded-md border-gray-300 text-sm shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                  <div>
                    <div className="flex align-bottom">
                      <div className="flex-grow rounded shadow-sm">
                        <Combobox
                          value={seasonName}
                          onChange={handleSeasonNameChange}
                          label="Season / Holiday"
                          collection={seasonNames}
                          filter={(q, s) => startsWith(s, q)}
                        />
                      </div>
                      <div className="mt-auto flex">
                        <button
                          type="button"
                          className="btn-secondary"
                          onClick={handleAddSeasonClick}
                          tabIndex={-1}
                        >
                          <Icon icon="plus" />
                        </button>
                      </div>
                    </div>
                  </div>
                  <div>
                    <div className="mt-1 rounded-md shadow-sm">
                      <Combobox
                        value={customer}
                        onChange={handleCustomerChange}
                        label="Customer"
                        required
                        collection={customers}
                        filter={(q, c) =>
                          startsWith(c.name, q) || startsWith(c.customerNo, q)
                        }
                        secondaryDisplayTextProp="customerNo"
                        nullDisplayText="No Customer"
                      />
                    </div>
                  </div>
                  <div>
                    <div className="mt-1 rounded-md shadow-sm">
                      <label className="block text-sm font-medium text-gray-500">
                        Ship-To
                        <HeadlessUI.Popover
                          className="relative inline-block cursor-pointer"
                          onMouseEnter={handleCustomerInfoTooltipMouseEnter}
                          onMouseLeave={handleCustomerInfoTooltipMouseLeave}
                        >
                          {!!customerInfo && (
                            <>
                              <button
                                type="button"
                                className="btn-secondary small ml-2 border-transparent p-0 text-yellow-500 shadow-none"
                                onClick={handleCustomerInfoClick}
                              >
                                <FontAwesomeIcon
                                  icon={['fas', 'sticky-note']}
                                  className="text-yellow-500"
                                />
                              </button>
                              <HeadlessUI.Transition
                                as={Fragment}
                                show={showCustomerInfoTooltip}
                                enter="transition ease-out duration-200"
                                enterFrom="opacity-0"
                                enterTo="opacity-100"
                                leave="transition ease-in duration-150"
                                leaveFrom="opacity-100"
                                leaveTo="opacity-0"
                              >
                                <HeadlessUI.Popover.Panel
                                  static
                                  className="absolute left-1/2 top-0 z-10 -translate-x-1/2 translate-y-1/2 transform bg-yellow-50"
                                >
                                  <div className="w-auto whitespace-nowrap rounded-lg border p-4 shadow-lg">
                                    <Icon icon="info-circle" />
                                    &nbsp;Additional customer info
                                  </div>
                                </HeadlessUI.Popover.Panel>
                              </HeadlessUI.Transition>
                            </>
                          )}
                        </HeadlessUI.Popover>
                      </label>
                      <ShipToCombobox
                        value={shipTo}
                        onChange={handleShipToChange}
                        inputRef={shipToRef}
                        customer={customerDetail}
                        hideLabel
                      />
                    </div>
                  </div>
                  <div className="col-start-1">
                    <div className="mt-1 rounded shadow-sm">
                      <Combobox
                        value={salesperson}
                        onChange={handleSalespersonChange}
                        label="Salesperson"
                        collection={salespeople}
                        filter={(q, s) => startsWith(s.name, q)}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="mt-1">
                      <div className="mt-1 rounded shadow-sm">
                        <Combobox
                          value={shipVia}
                          onChange={handleShipViaChange}
                          label="Truck"
                          displayTextProp="description"
                          collection={shippingMethods}
                          filter={(q, s) => startsWith(s.description, q)}
                          allowCustom
                        />
                      </div>
                    </div>
                  </div>
                  <div>
                    <label
                      htmlFor="box-code"
                      className="block text-sm font-medium text-gray-500"
                    >
                      Box Code
                    </label>
                    <div className="mt-1">
                      <input
                        type="text"
                        name="boxCode"
                        id="box-code"
                        autoComplete="off"
                        value={boxCode || ''}
                        onChange={handleBoxCodeChange}
                        className="block w-full rounded-md border-gray-300 text-sm shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                  <div>
                    <label
                      htmlFor="customer-purchase-order-number"
                      className="block text-sm font-medium text-gray-500"
                    >
                      Customer PO
                    </label>
                    <div className="mt-1">
                      <input
                        type="text"
                        name="customerPurchaseOrderNumber"
                        id="customer-purchase-order-number"
                        maxLength={20}
                        value={customerPurchaseOrderNumber || ''}
                        autoComplete="off"
                        onChange={handleCustomerPurchaseOrderNumberChange}
                        className="block w-full rounded-md border-gray-300 text-sm shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                  <div className="col-span-2">
                    <label
                      htmlFor="spire-notes"
                      className="block text-sm font-medium text-gray-500"
                    >
                      Shipment Details&nbsp;
                      <HeadlessUI.Popover className="relative inline-block">
                        <HeadlessUI.Popover.Button
                          className="btn-secondary mb-1 rounded-full px-1 py-0 text-sm text-blue-500 focus:ring-0"
                          tabIndex={-1}
                        >
                          <Icon icon="question-circle" />
                        </HeadlessUI.Popover.Button>
                        <HeadlessUI.Transition
                          as={Fragment}
                          enter="transition ease-out duration-200"
                          enterFrom="opacity-0 translate-y-1"
                          enterTo="opacity-100 translate-y-0"
                          leave="transition ease-in duration-150"
                          leaveFrom="opacity-100 translate-y-0"
                          leaveTo="opacity-0 translate-y-1"
                        >
                          <HeadlessUI.Popover.Panel className="absolute bottom-10 z-10 w-96 -translate-x-1/2 transform px-4">
                            <div className="rounded-lg bg-yellow-50 p-4 font-normal shadow-lg ring-1 ring-black ring-opacity-5">
                              Shipment Details will go into the Ship Sheet
                              Comments field on the User Defined tab in the
                              Spire Order. These notes will not be seen by the
                              Customer.
                            </div>
                          </HeadlessUI.Popover.Panel>
                        </HeadlessUI.Transition>
                      </HeadlessUI.Popover>
                    </label>
                    <textarea
                      rows={4}
                      name="spireNotes"
                      id="spire-notes"
                      value={spireNotes || ''}
                      onChange={handleSpireNotesChange}
                      className="block w-full rounded-md border-gray-300 text-sm shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                    />
                  </div>
                  <div className="col-span-2">
                    <label
                      htmlFor="comments"
                      className="block text-sm font-medium text-gray-500"
                    >
                      Internal Comments &nbsp;
                      <HeadlessUI.Popover className="relative inline-block">
                        <HeadlessUI.Popover.Button
                          className="btn-secondary mb-1 rounded-full px-1 py-0 text-sm text-blue-500 focus:ring-0"
                          tabIndex={-1}
                        >
                          <Icon icon="question-circle" />
                        </HeadlessUI.Popover.Button>
                        <HeadlessUI.Transition
                          as={Fragment}
                          enter="transition ease-out duration-200"
                          enterFrom="opacity-0 translate-y-1"
                          enterTo="opacity-100 translate-y-0"
                          leave="transition ease-in duration-150"
                          leaveFrom="opacity-100 translate-y-0"
                          leaveTo="opacity-0 translate-y-1"
                        >
                          <HeadlessUI.Popover.Panel className="absolute bottom-10 z-10 w-96 -translate-x-1/2 transform px-4">
                            <div className="rounded-lg bg-yellow-50 p-4 font-normal shadow-lg ring-1 ring-black ring-opacity-5">
                              Internal notes are Flora Pack notes that will not
                              be visible to the Customer or Growers.
                            </div>
                          </HeadlessUI.Popover.Panel>
                        </HeadlessUI.Transition>
                      </HeadlessUI.Popover>
                    </label>
                    <textarea
                      rows={4}
                      name="comments"
                      id="comment"
                      value={internalComments || ''}
                      onChange={handleInternalCommentsChange}
                      className="block w-full rounded-md border-gray-300 text-sm shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                    />
                  </div>
                  <div className="col-span-2">
                    <label
                      htmlFor="grower-item-notes"
                      className="mt-2 block text-sm font-medium text-gray-500"
                    >
                      Grower Item Notes&nbsp;
                      <HeadlessUI.Popover className="relative inline-block">
                        <HeadlessUI.Popover.Button
                          className="btn-secondary mb-1 rounded-full px-1 py-0 text-sm text-blue-500 focus:ring-0"
                          tabIndex={-1}
                        >
                          <Icon icon="question-circle" />
                        </HeadlessUI.Popover.Button>
                        <HeadlessUI.Transition
                          as={Fragment}
                          enter="transition ease-out duration-200"
                          enterFrom="opacity-0 translate-y-1"
                          enterTo="opacity-100 translate-y-0"
                          leave="transition ease-in duration-150"
                          leaveFrom="opacity-100 translate-y-0"
                          leaveTo="opacity-0 translate-y-1"
                        >
                          <HeadlessUI.Popover.Panel className="absolute bottom-10 z-10 w-96 -translate-x-1/2 transform px-4">
                            <div className="rounded-lg bg-yellow-50 p-4 font-normal shadow-lg ring-1 ring-black ring-opacity-5">
                              Grower Item notes will be included on{' '}
                              <span className="font-semibold italic">
                                each item
                              </span>{' '}
                              of{' '}
                              <span className="font-semibold italic">
                                each Prebook
                              </span>
                              .
                            </div>
                          </HeadlessUI.Popover.Panel>
                        </HeadlessUI.Transition>
                      </HeadlessUI.Popover>
                    </label>
                    <textarea
                      rows={4}
                      name="growerItemNotes"
                      id="grower-item-notes"
                      value={growerItemNotes || ''}
                      onChange={handleGrowerItemNotesChange}
                      className="block w-full rounded-md border-gray-300 text-sm shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                    />
                  </div>
                  <div>
                    <label
                      htmlFor="comments"
                      className="mt-2 block text-sm font-medium text-gray-500"
                    >
                      Standard Comments &nbsp;
                      <HeadlessUI.Popover className="relative inline-block">
                        <HeadlessUI.Popover.Button
                          className="btn-secondary mb-1 ml-1 rounded-full px-1 py-0 text-sm text-blue-500 focus:ring-0"
                          tabIndex={-1}
                        >
                          <Icon icon="question-circle" />
                        </HeadlessUI.Popover.Button>
                        <HeadlessUI.Transition
                          as={Fragment}
                          enter="transition ease-out duration-200"
                          enterFrom="opacity-0 translate-y-1"
                          enterTo="opacity-100 translate-y-0"
                          leave="transition ease-in duration-150"
                          leaveFrom="opacity-100 translate-y-0"
                          leaveTo="opacity-0 translate-y-1"
                        >
                          <HeadlessUI.Popover.Panel className="absolute bottom-10 z-10 w-96 -translate-x-1/2 transform px-4">
                            <div className="rounded-lg bg-yellow-50 p-4 font-normal shadow-lg ring-1 ring-black ring-opacity-5">
                              Standard Comments will be included as line-items
                              on the Spire order.
                            </div>
                          </HeadlessUI.Popover.Panel>
                        </HeadlessUI.Transition>
                      </HeadlessUI.Popover>
                    </label>
                    <HeadlessUI.Menu as="div" className="relative mt-1">
                      <HeadlessUI.Menu.Button className="btn-secondary mb-1 border-blue-600 px-2 py-1 text-xs text-blue-600">
                        <Icon icon="comment-alt-captions" />
                        &nbsp; Add Standard Comment&nbsp;
                        <Icon icon="chevron-down" />
                      </HeadlessUI.Menu.Button>
                      <HeadlessUI.Transition
                        as={Fragment}
                        enter="transition duration-100 ease-out"
                        enterFrom="transform scale-95 opacity-0"
                        enterTo="transform scale-100 opacity-100"
                        leave="transition duration-75 ease-out"
                        leaveFrom="transform scale-100 opacity-100"
                        leaveTo="transform scale-95 opacity-0"
                      >
                        <HeadlessUI.Menu.Items className="absolute right-0 z-40 mt-2 max-h-60 w-56 origin-top-right divide-y divide-gray-100 overflow-y-auto rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                          {(inventoryComments || []).map((comment) => (
                            <div key={comment.id} className="px-1 py-1 ">
                              <HeadlessUI.Menu.Item>
                                {({ active }) => (
                                  <button
                                    type="button"
                                    className={`${
                                      active
                                        ? 'bg-blue-500 text-white'
                                        : 'text-gray-900'
                                    } group flex w-full flex-col items-start rounded-md px-2 py-1 text-sm`}
                                    onClick={() =>
                                      handleAddStandardComment(comment)
                                    }
                                  >
                                    <div>{comment.code}</div>
                                    <div className="truncate text-xs italic">
                                      {comment.description}
                                    </div>
                                  </button>
                                )}
                              </HeadlessUI.Menu.Item>
                            </div>
                          ))}
                        </HeadlessUI.Menu.Items>
                      </HeadlessUI.Transition>
                    </HeadlessUI.Menu>
                    <div className="overflow-y-auto">
                      {comments.map((comment) => (
                        <div
                          key={comment.id}
                          className="border-b-2 text-xs text-gray-500"
                        >
                          <button
                            type="button"
                            className="btn-delete border-transparent px-2 py-1"
                            onClick={() =>
                              handleDeleteStandardCommentClick(comment.id)
                            }
                          >
                            <Icon icon="trash" />
                          </button>
                          {comment.comments}
                        </div>
                      ))}
                    </div>
                  </div>
                  <div>
                    <div>
                      <label
                        htmlFor="freight-per-case"
                        className="block text-xs font-medium text-gray-500"
                      >
                        Freight ($/Case)
                      </label>
                      <div className="mt-1">
                        <input
                          type="text"
                          name="freightPerCase"
                          id="freight-per-case"
                          autoComplete="off"
                          value={freightPerCaseFormatted || ''}
                          onChange={handleFreightPerCaseChange}
                          onBlur={handleFreightPerCaseBlur}
                          onFocus={handleFocus}
                          className="block w-full max-w-48 rounded-md border-gray-300 text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        />
                      </div>
                    </div>
                    <div className="mt-2">
                      <label
                        htmlFor="freight-per-load"
                        className="block text-xs font-medium text-gray-500"
                      >
                        Freight Total
                      </label>
                      <div className="mt-1">
                        <input
                          type="text"
                          name="freightPerLoad"
                          id="freight-per-load"
                          autoComplete="off"
                          value={freightPerLoadFormatted || ''}
                          onChange={handleFreightPerLoadChange}
                          onBlur={handleFreightPerLoadBlur}
                          onFocus={handleFocus}
                          className="block w-full max-w-48 rounded-md border-gray-300 text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        />
                      </div>
                    </div>
                    <HeadlessUI.Switch.Group as="div" className="mt-1">
                      <HeadlessUI.Switch
                        checked={freightIsActual}
                        onChange={handleFreightIsActualChange}
                        className={classNames(
                          freightIsActual ? 'bg-blue-400' : 'bg-gray-200',
                          'relative inline-flex h-4 w-8 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
                        )}
                      >
                        <span
                          aria-hidden="true"
                          className={classNames(
                            freightIsActual ? 'translate-x-4' : 'translate-x-0',
                            'pointer-events-none inline-block h-3 w-3 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                          )}
                        />
                      </HeadlessUI.Switch>
                      <HeadlessUI.Switch.Label className="ml-2">
                        <span className="text-xs">Actual Freight</span>
                      </HeadlessUI.Switch.Label>
                    </HeadlessUI.Switch.Group>
                  </div>
                </div>
                <DndProvider backend={HTML5Backend}>
                  <div className="mt-8">
                    <div className="rounded-lg shadow ring-1 ring-black ring-opacity-5">
                      <table className="mb-4 min-w-full divide-y divide-gray-300">
                        <thead className="bg-gray-50">
                          <tr>
                            <th
                              colSpan={2}
                              className="py-2 pl-4 pr-1 text-left align-middle font-normal text-gray-700"
                            >
                              <HeadlessUI.Popover
                                className="relative inline-block cursor-pointer"
                                onMouseEnter={
                                  handleCollapseAllTooltipMouseEnter
                                }
                                onMouseLeave={
                                  handleCollapseAllTooltipMouseLeave
                                }
                              >
                                <>
                                  <button
                                    type="button"
                                    className="btn-secondary h-8 w-8 px-2 py-1"
                                    onClick={handleCollapseAllClick}
                                  >
                                    <Icon icon="arrows-to-line" />
                                  </button>
                                  <HeadlessUI.Transition
                                    as={Fragment}
                                    show={showCollapseAllTooltip}
                                    enter="transition ease-out duration-200"
                                    enterFrom="opacity-0"
                                    enterTo="opacity-100"
                                    leave="transition ease-in duration-150"
                                    leaveFrom="opacity-100"
                                    leaveTo="opacity-0"
                                  >
                                    <HeadlessUI.Popover.Panel
                                      static
                                      className="absolute left-1/2 top-0 z-10 -translate-x-1/2 -translate-y-full transform bg-yellow-50"
                                    >
                                      <div className="w-auto whitespace-nowrap rounded-lg border p-4 shadow-lg">
                                        Collapse All Items
                                      </div>
                                    </HeadlessUI.Popover.Panel>
                                  </HeadlessUI.Transition>
                                </>
                              </HeadlessUI.Popover>
                              <HeadlessUI.Popover
                                className="relative inline-block cursor-pointer"
                                onMouseEnter={handleExpandAllTooltipMouseEnter}
                                onMouseLeave={handleExpandAllTooltipMouseLeave}
                              >
                                <>
                                  <button
                                    type="button"
                                    className="btn-secondary h-8 w-8 px-2 py-1"
                                    onClick={handleExpandAllClick}
                                  >
                                    <Icon icon="arrows-from-line" />
                                  </button>
                                  <HeadlessUI.Transition
                                    as={Fragment}
                                    show={showExpandAllTooltip}
                                    enter="transition ease-out duration-200"
                                    enterFrom="opacity-0"
                                    enterTo="opacity-100"
                                    leave="transition ease-in duration-150"
                                    leaveFrom="opacity-100"
                                    leaveTo="opacity-0"
                                  >
                                    <HeadlessUI.Popover.Panel
                                      static
                                      className="absolute left-1/2 top-0 z-10 -translate-x-1/2 -translate-y-full transform bg-yellow-50"
                                    >
                                      <div className="w-auto whitespace-nowrap rounded-lg border p-4 shadow-lg">
                                        Expand All Items
                                      </div>
                                    </HeadlessUI.Popover.Panel>
                                  </HeadlessUI.Transition>
                                </>
                              </HeadlessUI.Popover>
                              <HeadlessUI.Popover
                                className="relative inline-block cursor-pointer"
                                onMouseEnter={handleClearAllTooltipMouseEnter}
                                onMouseLeave={handleClearAllTooltipMouseLeave}
                              >
                                <>
                                  <button
                                    type="button"
                                    className="btn-secondary ml-4 h-8 w-8 px-2 py-1 text-red-600"
                                    onClick={handleClearAllClick}
                                  >
                                    <Icon icon="trash-can-xmark" />
                                  </button>
                                  <HeadlessUI.Transition
                                    as={Fragment}
                                    show={showClearAllTooltip}
                                    enter="transition ease-out duration-200"
                                    enterFrom="opacity-0"
                                    enterTo="opacity-100"
                                    leave="transition ease-in duration-150"
                                    leaveFrom="opacity-100"
                                    leaveTo="opacity-0"
                                  >
                                    <HeadlessUI.Popover.Panel
                                      static
                                      className="absolute left-1/2 top-0 z-10 -translate-x-1/4 -translate-y-full transform bg-yellow-50"
                                    >
                                      <div className="w-auto whitespace-nowrap rounded-lg border p-4 shadow-lg">
                                        <div className="semibold">
                                          Clear all item information
                                        </div>
                                        <div className="text-xs italic">
                                          Allows you to selectively clear Pot
                                          Cover, UPC, Price, Date Code, Retail
                                          and W&M information.
                                        </div>
                                      </div>
                                    </HeadlessUI.Popover.Panel>
                                  </HeadlessUI.Transition>
                                </>
                              </HeadlessUI.Popover>
                            </th>
                            <th
                              colSpan={2}
                              className="px-1 py-2 text-left font-normal text-gray-500"
                            >
                              <div className="grid grid-cols-2">
                                <label
                                  className="text-left text-xs italic"
                                  html-for="all-pot-covers"
                                >
                                  <HeadlessUI.Popover className="relative inline-block">
                                    <HeadlessUI.Popover.Button
                                      className="btn-secondary mb-1 mr-1 rounded-full px-1 py-0 text-sm text-blue-500 focus:ring-0"
                                      tabIndex={-1}
                                    >
                                      <Icon icon="question-circle" />
                                    </HeadlessUI.Popover.Button>
                                    <HeadlessUI.Transition
                                      as={Fragment}
                                      enter="transition ease-out duration-200"
                                      enterFrom="opacity-0 translate-y-1"
                                      enterTo="opacity-100 translate-y-0"
                                      leave="transition ease-in duration-150"
                                      leaveFrom="opacity-100 translate-y-0"
                                      leaveTo="opacity-0 translate-y-1"
                                    >
                                      <HeadlessUI.Popover.Panel className="absolute bottom-10 z-10 w-96 transform px-4">
                                        <div className="rounded-lg bg-yellow-50 p-4 font-normal not-italic shadow-lg ring-1 ring-black ring-opacity-5">
                                          <p className="font-semibold">
                                            Pot Covers for new items will be set
                                            with the following priority:
                                          </p>
                                          <ol className="ml-4 list-decimal">
                                            <li>
                                              If the Product Number ends in{' '}
                                              <span className="italic">
                                                &quot;NF&quot;
                                              </span>{' '}
                                              or the Description contains
                                              <span className="italic">
                                                &quot;Planter&quot;
                                              </span>{' '}
                                              it will{' '}
                                              <span className="font-semibold">
                                                not
                                              </span>{' '}
                                              be given a Pot Cover.
                                            </li>
                                            <li>
                                              If a Pot Cover is set here in the
                                              header, it will be used.
                                            </li>
                                            {!!shipTo && (
                                              <li>
                                                If the product has a default for{' '}
                                                {shipTo.shipId}, it will be
                                                used.
                                              </li>
                                            )}
                                            <li>
                                              If the Product Number ends in{' '}
                                              <span className="italic">
                                                &quot;PC&quot;
                                              </span>{' '}
                                              , a{' '}
                                              <span className="italic">
                                                &quot;PC&quot;
                                              </span>{' '}
                                              pot cover will be added.
                                            </li>
                                          </ol>
                                          <p className="mt-2 font-semibold">
                                            Click the{' '}
                                            <span className="border p-1">
                                              <Icon icon="arrow-down" />
                                            </span>{' '}
                                            button to use the selected Pot Cover
                                            for all existing items.
                                          </p>
                                        </div>
                                      </HeadlessUI.Popover.Panel>
                                    </HeadlessUI.Transition>
                                  </HeadlessUI.Popover>
                                  Pot Cover
                                </label>
                                <label className="whitespace-nowrap text-center text-xs font-normal italic text-gray-500">
                                  <HeadlessUI.Popover className="relative inline-block whitespace-normal">
                                    <HeadlessUI.Popover.Button
                                      className="btn-secondary mb-1 mr-1 rounded-full px-1 py-0 text-sm text-blue-500 focus:ring-0"
                                      tabIndex={-1}
                                    >
                                      <Icon icon="question-circle" />
                                    </HeadlessUI.Popover.Button>
                                    <HeadlessUI.Transition
                                      as={Fragment}
                                      enter="transition ease-out duration-200"
                                      enterFrom="opacity-0 translate-y-1"
                                      enterTo="opacity-100 translate-y-0"
                                      leave="transition ease-in duration-150"
                                      leaveFrom="opacity-100 translate-y-0"
                                      leaveTo="opacity-0 translate-y-1"
                                    >
                                      <HeadlessUI.Popover.Panel className="absolute bottom-10 z-10 w-96 -translate-x-1/2 transform px-4">
                                        <div className="rounded-lg bg-yellow-50 p-4 font-normal not-italic shadow-lg ring-1 ring-black ring-opacity-5">
                                          <p className="">
                                            If this checkbox is checked, all new
                                            items will have Use Availability
                                            Pricing checked.
                                          </p>
                                          <p className="mt-2">
                                            To turn Use Availability Pricing on
                                            or off for all existing items, click
                                            the{' '}
                                            <span className="border p-1">
                                              <Icon icon="arrow-down" />
                                            </span>{' '}
                                            button.
                                          </p>
                                        </div>
                                      </HeadlessUI.Popover.Panel>
                                    </HeadlessUI.Transition>
                                  </HeadlessUI.Popover>
                                  Use Availability Pricing
                                </label>
                                <div className="flex max-w-[200px] flex-row rounded-md border border-gray-300 bg-white shadow-sm">
                                  <div className="flex items-center px-2">
                                    <input
                                      id="all-pot-covers"
                                      type="checkbox"
                                      checked={allHasPotCover}
                                      onChange={
                                        handleAllHasPotCoverSelectedChange
                                      }
                                      className="h-4 w-4 rounded border-gray-300 text-blue-600 ring-0 focus:ring-0"
                                    />
                                  </div>
                                  <input
                                    type="text"
                                    autoComplete="off"
                                    value={allPotCover}
                                    onChange={handleAllPotCoverChange}
                                    onFocus={handleFocus}
                                    className={classNames(
                                      'flex max-w-[115px] flex-grow rounded-md border-transparent text-center text-xs shadow-sm placeholder:italic placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500',
                                      allHasPotCover ? '' : 'invisible'
                                    )}
                                    ref={potCoverRef}
                                    placeholder="PC"
                                  />
                                  <div
                                    className={classNames(
                                      'flex px-2 pt-1 font-normal',
                                      allHasPotCover ? '' : 'invisible'
                                    )}
                                  >
                                    <DropdownMenu
                                      items={potCovers.map((text) => ({
                                        text,
                                        selected: handleAllPotCoverSelected,
                                      }))}
                                    />
                                  </div>
                                  <button
                                    type="button"
                                    className="btn-secondary ml-4 px-2 py-1 text-xs"
                                    onClick={handleAllPotCoverClick}
                                  >
                                    <Icon icon="arrow-down" />
                                  </button>
                                </div>
                                <div className="whitespace-nowrap text-center">
                                  <input
                                    type="checkbox"
                                    className="h-4 w-4 rounded border-gray-300 text-blue-600 ring-0 focus:ring-0"
                                    checked={allAvailabilityPricingSelected}
                                    onChange={
                                      handleAllUseAvailabilityPricingSelectedChange
                                    }
                                  />
                                  <button
                                    type="button"
                                    className="btn-secondary ml-1 px-2 text-xs"
                                    onClick={
                                      handleAllUseAvailabilityPricingClick
                                    }
                                  >
                                    <Icon icon="arrow-down" />
                                  </button>
                                </div>
                              </div>
                            </th>
                            <th className="px-1 py-2 text-left text-xs font-normal text-gray-500">
                              <label className="text-left italic">
                                <HeadlessUI.Popover className="relative inline-block">
                                  <HeadlessUI.Popover.Button
                                    className="btn-secondary mb-1 mr-1 rounded-full px-1 py-0 text-sm text-blue-500 focus:ring-0"
                                    tabIndex={-1}
                                  >
                                    <Icon icon="question-circle" />
                                  </HeadlessUI.Popover.Button>
                                  <HeadlessUI.Transition
                                    as={Fragment}
                                    enter="transition ease-out duration-200"
                                    enterFrom="opacity-0 translate-y-1"
                                    enterTo="opacity-100 translate-y-0"
                                    leave="transition ease-in duration-150"
                                    leaveFrom="opacity-100 translate-y-0"
                                    leaveTo="opacity-0 translate-y-1"
                                  >
                                    <HeadlessUI.Popover.Panel className="absolute bottom-10 z-10 w-96 -translate-x-1/2 transform px-4">
                                      <div className="rounded-lg bg-yellow-50 p-4 font-normal not-italic shadow-lg ring-1 ring-black ring-opacity-5">
                                        <p className="">
                                          If there is a date code here, it will
                                          be used for all new items.
                                        </p>
                                        <p className="mt-2">
                                          To use this date code for all existing
                                          items, click the{' '}
                                          <span className="border p-1">
                                            <Icon icon="arrow-down" />
                                          </span>{' '}
                                          button.
                                        </p>
                                      </div>
                                    </HeadlessUI.Popover.Panel>
                                  </HeadlessUI.Transition>
                                </HeadlessUI.Popover>
                                Date Code
                              </label>
                              <div className="whitespace-nowrap">
                                <input
                                  type="text"
                                  value={allDateCode}
                                  onChange={handleAllDateCodeChange}
                                  onFocus={handleFocus}
                                  className="w-full max-w-[125px] rounded-md border-gray-300 text-center text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                />
                                <button
                                  type="button"
                                  className="btn-secondary ml-1 px-2 text-xs"
                                  onClick={handleAllDateCodeClick}
                                >
                                  <Icon icon="arrow-down" />
                                </button>
                              </div>
                            </th>
                            <th className="px-1 py-2">&nbsp;</th>
                            <th className="px-1 py-2 text-left text-sm font-semibold text-gray-900">
                              <label className="whitespace-nowrap text-left text-xs font-normal italic text-gray-500">
                                <HeadlessUI.Popover className="relative inline-block whitespace-normal">
                                  <HeadlessUI.Popover.Button
                                    className="btn-secondary mb-1 mr-1 rounded-full px-1 py-0 text-sm text-blue-500 focus:ring-0"
                                    tabIndex={-1}
                                  >
                                    <Icon icon="question-circle" />
                                  </HeadlessUI.Popover.Button>
                                  <HeadlessUI.Transition
                                    as={Fragment}
                                    enter="transition ease-out duration-200"
                                    enterFrom="opacity-0 translate-y-1"
                                    enterTo="opacity-100 translate-y-0"
                                    leave="transition ease-in duration-150"
                                    leaveFrom="opacity-100 translate-y-0"
                                    leaveTo="opacity-0 translate-y-1"
                                  >
                                    <HeadlessUI.Popover.Panel className="absolute bottom-10 z-10 w-96 -translate-x-1/2 transform px-4">
                                      <div className="rounded-lg bg-yellow-50 p-4 font-normal not-italic shadow-lg ring-1 ring-black ring-opacity-5">
                                        <p className="">
                                          If this checkbox is checked, all new
                                          items will have Weights & Measures
                                          checked.
                                        </p>
                                        <p className="mt-2">
                                          To turn Weights & Measures on or off
                                          for all existing items, click the{' '}
                                          <span className="border p-1">
                                            <Icon icon="arrow-down" />
                                          </span>{' '}
                                          button.
                                        </p>
                                      </div>
                                    </HeadlessUI.Popover.Panel>
                                  </HeadlessUI.Transition>
                                </HeadlessUI.Popover>
                                W&M
                              </label>
                              <div className="whitespace-nowrap">
                                <input
                                  type="checkbox"
                                  className="h-4 w-4 rounded border-gray-300 text-blue-600 ring-0 focus:ring-0"
                                  checked={allWeightsAndMeasuresSelected}
                                  onChange={
                                    handleAllWeightsAndMeasuresSelectedChange
                                  }
                                />
                                <button
                                  type="button"
                                  className="btn-secondary ml-1 px-2 text-xs"
                                  onClick={handleAllWeightsAndMeasuresClick}
                                >
                                  <Icon icon="arrow-down" />
                                </button>
                              </div>
                            </th>
                            <th className="px-1 py-2 text-left text-sm font-semibold text-gray-900">
                              &nbsp;
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white">
                          {items.map((item) => (
                            <Fragment key={item.id}>
                              {!!item.expanded && (
                                <NewFutureOrderItemExpanded item={item} />
                              )}
                              {!item.expanded && (
                                <NewFutureOrderItemCollapsed item={item} />
                              )}
                            </Fragment>
                          ))}
                        </tbody>
                        <tfoot>
                          <tr>
                            <td colSpan={3} className={cellClassName}>
                              <button
                                type="button"
                                className="btn-new"
                                onClick={handleAddItemClick}
                              >
                                <Icon icon="plus" />
                                &nbsp; Add Item &nbsp;
                                <span className="text-xs text-gray-300">
                                  (Alt + A)
                                </span>
                              </button>
                              <button
                                type="button"
                                className="btn-new ml-2"
                                onClick={handleAddFromExistingClick}
                              >
                                <Icon icon="layer-plus" />
                                &nbsp; Add From Existing &nbsp;
                                <span className="text-xs text-gray-300">
                                  (Alt + X)
                                </span>
                              </button>
                            </td>
                            <th colSpan={4}>
                              {!!totalCases && (
                                <div className="flex flex-row justify-end">
                                  <div>Total:</div>
                                  <div className="ml-2">{totalCases} Cases</div>
                                  {!!totalDollars && (
                                    <div className="ml-2">
                                      ({formatCurrency(totalDollars)})
                                    </div>
                                  )}
                                </div>
                              )}
                            </th>
                          </tr>
                        </tfoot>
                      </table>
                    </div>
                  </div>
                </DndProvider>
              </div>
            </div>
          </form>
        </div>
      </main>
      <Inventory
        open={showInventoryDialog}
        cancel={handleAddItemCancel}
        confirm={handleAddItemConfirm}
        customer={customer}
        shipTo={shipTo}
        requiredDate={requiredDate}
        seasonName={seasonName}
        customerItemCodeDefaults={productCustomerDefaults}
      />
      <CreateSeason
        open={showAddSeasonDialog}
        cancel={handleAddSeasonCancel}
        confirm={handleAddSeasonConfirm}
      />
      {showBlankSlate && <BlankSlate close={handleBlankSlateClose} />}
      {!!prebooks.length && (
        <FutureOrderPrebookEmails
          close={handleShowEmailsClose}
          prebooks={prebooks}
        />
      )}
      <HeadlessUI.Dialog
        open={showRequiredDateDialog}
        onClose={() => console.log('')}
        className="relative z-50"
      >
        {/* The backdrop, rendered as a fixed sibling to the panel container */}
        <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

        {/* Full-screen container to center the panel */}
        <div className="fixed inset-0 flex items-center justify-center p-4">
          {/* The actual dialog panel  */}
          <HeadlessUI.Dialog.Panel className="absolute top-1/4 mx-auto rounded bg-white px-10 py-8">
            <HeadlessUI.Dialog.Title>
              Choose the Required Date for the new Order
            </HeadlessUI.Dialog.Title>

            <div className="mt-4 flex flex-col">
              <div className="flex justify-between">
                <label
                  htmlFor="copy-required-date"
                  className="block py-4 text-sm font-medium text-gray-500"
                >
                  Required Date
                </label>
                <div className="mt-2">
                  <input
                    type="date"
                    max="2050-01-01"
                    name="requiredDate"
                    id="copy-required-date"
                    value={requiredDate || ''}
                    onChange={handleRequiredDateChange}
                    onKeyUp={handleRequiredDateKeyUp}
                    className="block w-full rounded-md border-gray-300 text-sm shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  />
                </div>
              </div>

              <div className="mt-4 flex justify-between">
                <button
                  type="button"
                  className="btn-secondary"
                  onClick={handleCancelClick}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="btn-primary ml-2"
                  onClick={handleRequiredDateConfirm}
                  disabled={!requiredDate}
                >
                  Continue &nbsp;
                  <Icon icon="chevron-right" />
                </button>
              </div>
            </div>
          </HeadlessUI.Dialog.Panel>
        </div>
      </HeadlessUI.Dialog>
      <ClearItems
        open={showClearAllDialog}
        cancel={handleClearAllCancel}
        confirm={handleClearAllConfirm}
      />
      <AddFromOrder
        open={showAddFromOrder}
        addItem={handleAddFromExistingAddItem}
        cancel={handleAddFromExistingClose}
      />
      {confirmDateInPastJSX}
      <BoekestynProducts
        onSave={handleBoekestynProductSaved}
        productDefaults={productDefaults}
      />
      <CustomerInfoDialog
        open={showCustomerInfoDialog}
        customerInfo={customerInfo}
        close={handleCustomerInfoDialogClose}
        save={handleCustomerInfoDialogSave}
      />
      <HeadlessUI.Transition.Root
        show={!!showConfirmPriceWarnings}
        as={Fragment}
      >
        <HeadlessUI.Dialog
          as="div"
          className="relative z-40"
          onClose={handlePriceWarningCancel}
        >
          <HeadlessUI.Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
          </HeadlessUI.Transition.Child>

          <div className="fixed inset-0 z-40 overflow-y-auto">
            <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
              <HeadlessUI.Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                enterTo="opacity-100 translate-y-0 sm:scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              >
                <HeadlessUI.Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg">
                  <div className="bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                    <div className="sm:flex sm:items-start">
                      <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                        <Icon
                          icon="exclamation-triangle"
                          className="h-6 w-6 text-red-600"
                        />
                      </div>
                      <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                        <HeadlessUI.Dialog.Title
                          as="h3"
                          className="text-lg font-medium leading-6 text-gray-900"
                        >
                          Price Warning
                        </HeadlessUI.Dialog.Title>
                        <div className="mt-2">
                          <p className="whitespace-pre-wrap text-sm text-gray-500">
                            {showConfirmPriceWarnings}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                    <button
                      type="button"
                      className="focus:ring-blue-red-500 inline-flex w-full justify-center rounded-md border border-transparent bg-red-500 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm"
                      onClick={handlePriceWarningConfirm}
                    >
                      Yes
                    </button>
                    <button
                      type="button"
                      className="mt-3 inline-flex w-auto justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 sm:ml-3 sm:mt-0"
                      onClick={handlePriceWarningCancel}
                    >
                      No
                    </button>
                  </div>
                </HeadlessUI.Dialog.Panel>
              </HeadlessUI.Transition.Child>
            </div>
          </div>
        </HeadlessUI.Dialog>
      </HeadlessUI.Transition.Root>
    </div>
  );
}
