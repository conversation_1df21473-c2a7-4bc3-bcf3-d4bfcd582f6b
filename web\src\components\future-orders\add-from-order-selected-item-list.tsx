import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { classNames } from '@/utils/class-names';
import { formatCurrency, formatNumber } from '@/utils/format';
import {
  selectSelectedItems,
  removeItem,
  FutureOrderDetailSelectedItem,
} from './add-from-order-slice';
import { Icon } from '../icon';

export function AddFromOrderSelectedItemList() {
  const dispatch = useAppDispatch(),
    selectedItems = useAppSelector(selectSelectedItems),
    cellClassName =
      'whitespace-nowrap px-1 py-1 text-gray-500 border-bottom-0 text-xs align-top';

  const handleRemoveClick = (item: FutureOrderDetailSelectedItem) => {
    dispatch(removeItem(item));
  };

  return (
    <div className="h-40 overflow-y-auto">
      <h2 className="mb-2 block text-xl font-semibold">Items to Add</h2>
      <table className="min-w-full divide-y divide-gray-300 text-xs">
        <thead>
          <tr className="sticky top-0 z-10">
            <th className="w-1 bg-gray-100">&nbsp;</th>
            <th className="whitespace-nowrap bg-gray-100 p-2 text-center">
              Order #
            </th>
            <th className="bg-gray-100 p-2">Customer</th>
            <th className="bg-gray-100 p-2">Ship To</th>
            <th className="bg-gray-100 p-2">Product</th>
            <th className="bg-gray-100 p-2">Description</th>
            <th className="w-1 whitespace-nowrap bg-gray-100 p-2 text-center">
              Order Quantity
            </th>
            <th className="w-1 bg-gray-100 p-2 text-center">Price</th>
          </tr>
        </thead>
        <tbody>
          {selectedItems.map((item) => (
            <tr key={item.id} className="border border-b-2 border-gray-200">
              <td className="w-1 px-2 text-center">
                <button
                  type="button"
                  className="delete p-1 text-red-500"
                  onClick={() => handleRemoveClick(item)}
                >
                  <Icon icon="trash" />
                </button>
              </td>
              <td className={classNames(cellClassName, 'text-center')}>
                {item.orderNumber}
              </td>
              <td className={cellClassName}>{item.customer}</td>
              <td className={cellClassName}>{item.shipTo}</td>
              <td className={cellClassName}>{item.spirePartNumber}</td>
              <td className={cellClassName}>{item.description}</td>
              <td className={classNames(cellClassName, 'w-1 text-center')}>
                {formatNumber(item.orderQuantity)}
              </td>
              <td className={classNames(cellClassName, 'w-1 text-center')}>
                {formatCurrency(item.unitPrice)}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
