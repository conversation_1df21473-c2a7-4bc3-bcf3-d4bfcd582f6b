import { useAppSelector } from '@/services/hooks';
import { selectLines } from './spacing-slice';
import { SpacingSchedule } from './spacing-schedule';

interface SpacingScheduleDayProps {
  date: string;
}

export function SpacingScheduleDay({ date }: SpacingScheduleDayProps) {
  const lines = useAppSelector(selectLines);

  return (
    <div className="mb-2 flex-grow overflow-y-auto px-2">
      {lines.map((line) => (
        <SpacingSchedule key={line.id} line={line} date={date} />
      ))}
    </div>
  );
}
