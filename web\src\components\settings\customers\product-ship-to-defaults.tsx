import { useMemo, useState, useEffect } from 'react';
import * as HeadlessUI from '@headlessui/react';
import { useUpdateShipToDefaultMutation } from 'api/settings-service';
import * as spire from 'api/models/spire';
import { useAppSelector, useAppDispatch } from '@/services/hooks';
import { classNames } from '@/utils/class-names';
import { handleFocus } from '@/utils/focus';
import { formatCurrency, formatNumber } from '@/utils/format';
import {
  selectProductShipToDefaults,
  selectCustomerItemCodeByShipTo,
  selectQuery,
  setQuery,
  selectShipToDefaults,
  selectPriceLevelFreightRates,
} from './customer-settings-slice';
import { ProductShipToDefault } from './product-ship-to-default';

interface ProductShipToDefaultsProps {
  shipTo: spire.CustomerShipTo;
}

export function ProductShipToDefaults({ shipTo }: ProductShipToDefaultsProps) {
  const dispatch = useAppDispatch(),
    [updateShipToDefault] = useUpdateShipToDefaultMutation(),
    allProductShipToDefaults = useAppSelector(selectProductShipToDefaults),
    customerItemCodeByShipTo = useAppSelector(selectCustomerItemCodeByShipTo),
    query = useAppSelector(selectQuery),
    shipToDefaults = useAppSelector(selectShipToDefaults),
    priceLevelFreightRates = useAppSelector(selectPriceLevelFreightRates),
    productShipToDefaults = useMemo(
      () => allProductShipToDefaults.filter((d) => d.shipToId === shipTo.id),
      [allProductShipToDefaults, shipTo.id]
    ),
    [customFreight, setCustomFreight] = useState(false),
    [freightRate, setFreightRate] = useState<number | null>(null),
    [freightRateValue, setFreightRateValue] = useState('');

  useEffect(() => {
    const shipToDefault = shipToDefaults.find(
        (d) => d.shipToCode === shipTo.shipId
      ),
      priceLevelFreightRate = priceLevelFreightRates.find(
        (d) => d.priceLevel === shipTo.priceLevel
      ),
      customFreight = shipToDefault != null,
      freightRate =
        shipToDefault?.defaultFreightPerCase ??
        priceLevelFreightRate?.defaultFreightPerCase ??
        null;

    setCustomFreight(customFreight);
    setFreightRate(freightRate);
    if (customFreight) {
      setFreightRateValue(
        freightRate ? formatNumber(freightRate, '0.00') : 'Unknown'
      );
    } else {
      setFreightRateValue(freightRate ? formatCurrency(freightRate) : '0.00');
    }
  }, [
    priceLevelFreightRates,
    shipTo.priceLevel,
    shipTo.shipId,
    shipToDefaults,
  ]);

  const handleQueryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setQuery(e.target.value));
  };

  const handleFreightRateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFreightRateValue(e.target.value);
  };

  const handleFreightRateBlur = async (
    e: React.FocusEvent<HTMLInputElement>
  ) => {
    const value = parseFloat(e.target.value) || 0;

    await updateShipToDefault({
      shipToCode: shipTo.shipId,
      defaultFreightPerCase: value,
    });

    setFreightRate(value);
    setFreightRateValue(formatNumber(value, '0.00'));
  };

  const handleCustomFreightChange = async (checked: boolean) => {
    setCustomFreight(checked);
    if (checked) {
      setFreightRateValue(freightRate ? formatNumber(freightRate, '0.00') : '');
    } else {
      const priceLevelFreightRate = priceLevelFreightRates.find(
          (d) => d.priceLevel === shipTo.priceLevel
        ),
        freightRate = priceLevelFreightRate?.defaultFreightPerCase ?? null;

      setFreightRate(freightRate);
      setFreightRateValue(
        freightRate ? formatCurrency(freightRate) : 'Unknown'
      );
      await updateShipToDefault({
        shipToCode: shipTo.shipId,
        defaultFreightPerCase: null,
      });
    }
  };

  return (
    <div className="flex h-full flex-col">
      <div className="mb-2 flex items-center gap-4">
        <div className="flex flex-grow flex-col">
          <h2 className="p-2 text-lg font-bold text-blue-500">
            {shipTo.shipId}
          </h2>
          <div className="ml-2 flex items-center gap-2">
            <label htmlFor="item-search" className="text-sm">
              Search for Products
            </label>
            <input
              id="item-search"
              type="search"
              value={query}
              className="block text-xs"
              onChange={handleQueryChange}
            />
          </div>
        </div>
        <div className="flex flex-col gap-2">
          <div className="flex items-center gap-2">
            <label>Spire Price Level:&nbsp;</label>
            <span className="font-semibold">{shipTo.priceLevel}</span>
          </div>
          <HeadlessUI.Switch.Group as="div" className="flex items-center gap-2">
            <HeadlessUI.Switch.Label className="mr-2">
              Use Price Level Default
            </HeadlessUI.Switch.Label>
            <HeadlessUI.Switch
              checked={customFreight}
              onChange={handleCustomFreightChange}
              className={classNames(
                customFreight
                  ? 'bg-blue-400 outline-none ring-2 ring-blue-500 ring-offset-2'
                  : 'bg-gray-200',
                'relative inline-flex h-4 w-8 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out'
              )}
            >
              <span
                aria-hidden="true"
                className={classNames(
                  customFreight ? 'translate-x-4' : 'translate-x-0',
                  'pointer-events-none inline-block h-3 w-3 transform rounded-full bg-white text-center shadow ring-0 transition duration-200 ease-in-out'
                )}
              />
            </HeadlessUI.Switch>
            <HeadlessUI.Switch.Label className="ml-2">
              Custom Freight
            </HeadlessUI.Switch.Label>
          </HeadlessUI.Switch.Group>
          <div className="flex items-center gap-2">
            <label>Default Freight $ / case:&nbsp;</label>
            {customFreight ? (
              <input
                type="number"
                className=" w-28 rounded-md border-gray-300 text-center text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
                onChange={handleFreightRateChange}
                onBlur={handleFreightRateBlur}
                onFocus={handleFocus}
                value={freightRateValue}
              />
            ) : (
              <input
                type="text"
                readOnly
                disabled
                className="w-28 rounded-md border-transparent text-xs font-semibold"
                value={freightRateValue}
              />
            )}
          </div>
        </div>
      </div>
      <div className="flex flex-grow overflow-y-auto">
        <table className="min-w-full divide-y divide-gray-300">
          <thead>
            <tr className="sticky top-0 z-10">
              <th className="bg-gray-100 px-2 py-3.5 pl-3 text-left font-semibold text-gray-900">
                Product
              </th>
              <th className="bg-gray-100 px-2 py-3.5 pl-3 text-center font-semibold text-gray-900">
                Pot Cover
              </th>
              <th className="bg-gray-100 px-2 py-3.5 pl-3 text-center font-semibold text-gray-900">
                UPC
              </th>
              <th className="bg-gray-100 px-2 py-3.5 pl-3 text-center font-semibold text-gray-900">
                W&M
              </th>
              <th className="bg-gray-100 px-2 py-3.5 pl-3 text-center font-semibold text-gray-900">
                Retail
              </th>
              <th className="bg-gray-100 px-2 py-3.5 pl-3 text-center font-semibold text-gray-900">
                Price
              </th>
              {customerItemCodeByShipTo && (
                <th className="bg-gray-100 px-2 py-3.5 pl-3 text-center font-semibold text-gray-900">
                  Customer Item Code
                </th>
              )}
              <th className="bg-gray-100 px-2 py-3.5 pl-3 text-center font-semibold text-gray-900">
                &nbsp;
              </th>
            </tr>
          </thead>
          <tbody>
            {productShipToDefaults.map((d) => (
              <ProductShipToDefault key={d.id} productShipToDefault={d} />
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
