import { useRef, useState, Fragment } from 'react';
import * as HeadlessUI from '@headlessui/react';
import { prebooksApi } from 'api/prebooks-service';
import * as futureOrders from 'api/models/future-orders';
import { Error } from '../error';
import { Icon } from '@/components/icon';
import { ProblemDetails } from '@/utils/problem-details';

interface FutureOrderPrebookEmailImageAddButtonProps {
  id: number;
  onAdded: (attachment: futureOrders.UpgradeItemAttachment) => void;
}

// https://www.rlvision.com/blog/show-react-dialogs-fluently-with-hooks-and-promises
export function FutureOrderPrebookEmailImageAddButton({
  id,
  onAdded,
}: FutureOrderPrebookEmailImageAddButtonProps) {
  const [showDialog, setShowDialog] = useState(false),
    [error, setError] = useState<ProblemDetails | null>(null),
    inputRef = useRef<HTMLInputElement>(null);

  const handleClearError = () => {
    setError(null);
  };

  const onCancel = () => {
    setShowDialog(false);
    inputRef.current!.value = '';
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (file) {
      const args = { file, prebookItemIds: [id] },
        response = await prebooksApi.addUpgradeAttachment(args);

      if ('error' in response) {
        setError(response.error as ProblemDetails);
      } else {
        for (const attachment of response.data.attachments) {
          onAdded(attachment);
        }

        setShowDialog(false);
      }
    } else {
      onCancel();
    }

    e.target.value = '';
  };

  function handleAddImageClick() {
    setShowDialog(true);
    window.setTimeout(() => {
      inputRef.current?.click();
    }, 50);
  }

  return (
    <>
      <button
        type="button"
        className="btn-secondary"
        onClick={handleAddImageClick}
      >
        <Icon icon="plus" />
        &nbsp; Add Image
      </button>
      {showDialog && (
        <HeadlessUI.Transition.Root show={true} as={Fragment}>
          <HeadlessUI.Dialog
            as="div"
            className="relative z-50"
            onClose={onCancel}
          >
            <HeadlessUI.Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0"
              enterTo="opacity-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
            </HeadlessUI.Transition.Child>

            <div className="fixed inset-0 z-50 overflow-y-auto">
              <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                <HeadlessUI.Transition.Child
                  as={Fragment}
                  enter="ease-out duration-300"
                  enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                  enterTo="opacity-100 translate-y-0 sm:scale-100"
                  leave="ease-in duration-200"
                  leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                  leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                >
                  <HeadlessUI.Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg">
                    <div className="bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                      <div className="sm:flex sm:items-start">
                        <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full sm:mx-0 sm:h-10 sm:w-10">
                          <Icon
                            icon="exclamation-triangle"
                            className="h-6 w-6"
                          />
                        </div>
                        <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                          <HeadlessUI.Dialog.Title
                            as="h3"
                            className="text-lg font-medium leading-6 text-gray-900"
                          >
                            Choose the image file
                          </HeadlessUI.Dialog.Title>
                          <div className="mt-2">
                            <p className="text-sm text-gray-500">
                              <input
                                type="file"
                                ref={inputRef}
                                onChange={handleFileChange}
                                className="hidden"
                              />
                            </p>
                            <Error error={error} clear={handleClearError} />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                      <button
                        type="button"
                        className="mt-3 inline-flex w-auto justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 sm:ml-3 sm:mt-0"
                        onClick={onCancel}
                      >
                        Cancel
                      </button>
                    </div>
                  </HeadlessUI.Dialog.Panel>
                </HeadlessUI.Transition.Child>
              </div>
            </div>
          </HeadlessUI.Dialog>
        </HeadlessUI.Transition.Root>
      )}
    </>
  );
}
