import React, { Fragment, useEffect, useRef, useState } from 'react';
import { GlobalHotKeys, configure as configureHotkeys } from 'react-hotkeys';
import Head from 'next/head';
import { useRouter } from 'next/router';
import * as HeadlessUI from '@headlessui/react';
import {
  useSeasonsQuery,
  prebooksApi,
  PrebookCreateResponse,
} from 'api/prebooks-service';
import {
  useCustomersQuery,
  useVendorsQuery,
  useSalespeopleQuery,
} from 'api/spire-service';
import * as prebooks from 'api/models/prebooks';
import * as spire from 'api/models/spire';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { routes } from '@/services/routes';
import { Alert } from '@/components/alert';
import { Combobox } from '@/components/combo-box';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import { CreateSeason } from '@/components/create-season';
import { ShipToCombobox } from '@/components/ship-to-combo-box';
import { BlankSlate } from '@/components/prebooks/blank-slate';
import { Item } from '@/components/prebooks/item';
import { Inventory } from '@/components/prebooks/inventory';
import {
  selectError,
  selectSeasonName,
  selectBoxCode,
  selectComments,
  selectGrowerItemNotes,
  selectCustomer,
  selectItems,
  selectName,
  selectRequiredDate,
  selectSalesperson,
  selectShipTo,
  selectVendor,
  setCustomer,
  selectCustomerDetail,
  getCustomerDetail,
  setName,
  setSalesperson,
  setVendor,
  setShipTo,
  setRequiredDate,
  setSeasonName,
  setBoxCode,
  setComments,
  setGrowerItemNotes,
  addItem,
  clearState,
  createPrebook,
  setError,
  clearError,
  InventoryItemWithDefaults,
  setIsBlanket,
  selectIsBlanket,
  getBlanketItems,
  selectBlanketStartDate,
  setBlanketStartDate,
  selectBlanketItems,
  setCustomerDetail,
  setItemBoekestynProducts,
  selectProductDefaults,
} from '@/components/prebooks/prebook-detail-slice';
import { BoekestynProduct } from '@/components/prebooks/boekestyn-product-slice';
import { BoekestynProducts } from '@/components/prebooks/boekestyn-products';
import { classNames } from '@/utils/class-names';
import { dateIsInPast, startsWith } from '@/utils/equals';
import { formatDate } from '@/utils/format';
import { createProblemDetails } from '@/utils/problem-details';
import { itemIsOverbooked } from '@/components/prebooks/item-functions';
import { noPotCover } from '@/components/pot-covers';

export default function New() {
  const dispatch = useAppDispatch(),
    router = useRouter(),
    { blanket } = router.query,
    { data: vendors } = useVendorsQuery(),
    { data: customers } = useCustomersQuery(),
    { data: salespeople } = useSalespeopleQuery(),
    { data: seasons } = useSeasonsQuery(),
    error = useAppSelector(selectError),
    name = useAppSelector(selectName),
    vendor = useAppSelector(selectVendor),
    shipTo = useAppSelector(selectShipTo),
    customer = useAppSelector(selectCustomer),
    salesperson = useAppSelector(selectSalesperson),
    requiredDate = useAppSelector(selectRequiredDate),
    isBlanket = useAppSelector(selectIsBlanket),
    blanketStartDate = useAppSelector(selectBlanketStartDate),
    seasonName = useAppSelector(selectSeasonName),
    boxCode = useAppSelector(selectBoxCode),
    comments = useAppSelector(selectComments),
    growerItemNotes = useAppSelector(selectGrowerItemNotes),
    items = useAppSelector(selectItems),
    blanketItems = useAppSelector(selectBlanketItems),
    customerDetail = useAppSelector(selectCustomerDetail),
    productDefaults = useAppSelector(selectProductDefaults),
    [showInventoryDialog, setShowInventoryDialog] = useState(false),
    [showAddSeasonDialog, setShowAddSeasonDialog] = useState(false),
    [showBlankSlate, setShowBlankSlate] = useState(true),
    [showRequiredDateInPastDialog, setShowRequiredDateInPastDialog] = useState<
      | ((options?: SaveContinuationOptions) => Promise<void>)
      | ((options?: SaveContinuationOptions) => void)
      | null
    >(null),
    seasonNames = (seasons || []).map((s) => s.name),
    requiredDateRef = useRef<HTMLInputElement | null>(null),
    shipToRef = useRef<HTMLInputElement | null>(null),
    cellClassName = 'py-2 px-1 text-left text-sm font-semibold text-gray-900',
    overbookedError =
      !isBlanket && items.some((i) => itemIsOverbooked(i, blanketItems))
        ? createProblemDetails('Please ensure no blanket items are overbooked')
        : null,
    keyMap = { ADD_ITEM: 'alt+a' },
    hotKeysHandlers = {
      ADD_ITEM: () => setShowInventoryDialog(true),
    },
    requiredDateInPastError = dateIsInPast(requiredDate)
      ? 'The Required Date is in the past'
      : null;

  configureHotkeys({ ignoreTags: [] });

  useEffect(() => {
    dispatch(clearState());
    dispatch(getBlanketItems());
  }, [dispatch]);

  useEffect(() => {
    if (blanket === 'true') {
      dispatch(setIsBlanket(true));
    }
  }, [dispatch, blanket]);

  useEffect(() => {
    const shipTos = customerDetail?.shippingAddresses || [];
    if (shipTo && !shipTos.find((st) => st.id === shipTo.id)) {
      setShipTo(null);
    } else if (!shipTo && shipTos.length === 1) {
      setShipTo(shipTos[0]);
    }
  }, [shipTo, customerDetail]);

  const handleClearErrorClick = () => {
    dispatch(clearError());
  };

  const handleVendorChange = (vendor: spire.Vendor | null) => {
    dispatch(setVendor(vendor));
    if (vendor) {
      requiredDateRef.current?.focus();
    }
  };

  const handleCustomerChange = async (customer: spire.Customer | null) => {
    if (customer) {
      await dispatch(getCustomerDetail(customer.id));
    }
    dispatch(setCustomer(customer));
    if (customer) {
      shipToRef.current?.focus();
    }
  };

  const handleShipToChange = (
    shipTo: spire.CustomerShipTo | null,
    customer?: spire.CustomerDetail
  ) => {
    if (customer) {
      dispatch(setCustomerDetail(customer));
    }
    dispatch(setShipTo(shipTo));
    const salesperson =
      salespeople?.find((s) => s.code === shipTo?.salesperson?.code) || null;
    dispatch(setSalesperson(salesperson));
    dispatch(setBoxCode(shipTo?.boxCode || null));
  };

  const handleSalespersonChange = (salesperson: spire.Salesperson | null) => {
    dispatch(setSalesperson(salesperson));
  };

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setName(e.target.value || ''));
  };

  const handleRequiredDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setRequiredDate(e.target.value || null));
  };

  const handleIsBlanketChange = (isBlanket: boolean) => {
    dispatch(setIsBlanket(isBlanket));
  };

  const handleBlanketStartDateChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    dispatch(setBlanketStartDate(e.target.value || null));
  };

  const handleSeasonNameChange = (season: string | null) => {
    dispatch(setSeasonName(season));
  };

  const handleBoxCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setBoxCode(e.target.value || null));
  };

  const handleCommentsChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    dispatch(setComments(e.target.value || null));
  };

  const handleGrowerItemNotesChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    dispatch(setGrowerItemNotes(e.target.value || null));
  };

  const handleAddItemClick = () => {
    setShowInventoryDialog(true);
  };

  const handleAddItemConfirm = async (
    result: spire.InventoryItem,
    blanketItemId: number | null,
    comments: string | null
  ) => {
    const item: InventoryItemWithDefaults = { ...result };

    if (blanketItemId) {
      item.blanketItemId = blanketItemId;
    }
    if (comments) {
      item.comments = comments;
    }

    if (item.partNo.endsWith('PC')) {
      item.hasPotCover = true;
    } else if (noPotCover(item.partNo, item.description)) {
      item.hasPotCover = false;
      item.potCover = null;
    }
    if (customer && shipTo) {
      const { default: details } = await prebooksApi.productShipToDefaults(
        item.id,
        customer.id,
        shipTo.id
      );

      if (details.id) {
        item.customerItemCode = details.customerItemCode;
        item.hasPotCover = details.hasPotCover;
        item.potCover = details.potCover;
        item.retail = details.retail;
        item.unitPrice = details.unitPrice;
        item.upc = details.upc;
        item.weightsAndMeasures = details.weightsAndMeasures;
      }
    }
    await dispatch(addItem(item));
  };

  const handleAddItemCancel = () => {
    setShowInventoryDialog(false);
  };

  const handleAddSeasonClick = () => {
    setShowAddSeasonDialog(true);
  };

  const handleAddSeasonCancel = () => {
    setShowAddSeasonDialog(false);
  };

  const handleAddSeasonConfirm = async (season: prebooks.Season) => {
    dispatch(setSeasonName(season.name));
    setShowAddSeasonDialog(false);
  };

  const handleBlankSlateClose = () => {
    setShowBlankSlate(false);
  };

  const handleRequiredDateInPastConfirm = () => {
    if (showRequiredDateInPastDialog) {
      showRequiredDateInPastDialog({ continueRequiredDateInPast: true });
    }
  };

  const handleRequiredDateInPastCancel = () => {
    setShowRequiredDateInPastDialog(null);
  };

  const handleBoekestynProductSaved = (
    itemId: number,
    value: BoekestynProduct[]
  ) => {
    dispatch(setItemBoekestynProducts({ itemId, value }));
  };

  const handleSaveAndNewClick = async (
    options?: SaveContinuationOptions | React.MouseEvent<HTMLButtonElement>
  ) => {
    if (options && 'continueRequiredDateInPast' in options) {
      setShowRequiredDateInPastDialog(null);
    } else if (!!requiredDateInPastError) {
      setShowRequiredDateInPastDialog(() => handleSaveAndNewClick);
      return;
    }

    const saved = await save();
    if (saved) {
      dispatch(clearState());
      router.push(routes.prebooks.new.to());
    }
  };

  const handleCancelClick = () => {
    if (window.history.length > 1) {
      router.back();
    } else {
      router.push(routes.prebooks.list.to());
    }
  };

  const handleSaveAndCloseClick = async (
    options?: SaveContinuationOptions | React.MouseEvent<HTMLButtonElement>
  ) => {
    if (options && 'continueRequiredDateInPast' in options) {
      setShowRequiredDateInPastDialog(null);
    } else if (!!requiredDateInPastError) {
      setShowRequiredDateInPastDialog(() => handleSaveAndCloseClick);
      return;
    }

    const saved = await save();
    if (saved) {
      if (window.history.length > 1) {
        router.back();
      } else {
        router.push(routes.prebooks.list.to());
      }
    }
  };

  const handleSaveAndSendClick = async (
    options?: SaveContinuationOptions | React.MouseEvent<HTMLButtonElement>
  ) => {
    if (options && 'continueRequiredDateInPast' in options) {
      setShowRequiredDateInPastDialog(null);
    } else if (!!requiredDateInPastError) {
      setShowRequiredDateInPastDialog(() => handleSaveAndSendClick);
      return;
    }

    const saved = await save();
    if (saved) {
      router.push(routes.prebooks.detail.to(saved) + `?action=send`);
    }
  };

  const save = async () => {
    if (!vendor) {
      dispatch(setError(createProblemDetails('Please choose the Vendor.')));
    } else if (!requiredDate && !seasonName) {
      dispatch(
        setError(
          createProblemDetails(
            'Please enter either the Required Date or the Season'
          )
        )
      );
    } else if (
      blanketStartDate &&
      requiredDate &&
      blanketStartDate > requiredDate
    ) {
      dispatch(
        setError(
          createProblemDetails(
            'Please ensure the Blanket From date precedes the To date.'
          )
        )
      );
    } else if (!items.length) {
      dispatch(setError(createProblemDetails('Please add one or more items.')));
    } else {
      var response: any = await dispatch(createPrebook());
      if (!response?.error) {
        const payload = response.payload as PrebookCreateResponse;
        return payload.id;
      }
    }

    return false;
  };

  return (
    <div>
      <GlobalHotKeys keyMap={keyMap} handlers={hotKeysHandlers} />
      <Head>
        <title>New Prebook</title>
      </Head>
      <header className="sticky top-0 z-20 bg-white shadow">
        <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 md:flex md:items-center md:justify-between lg:px-8">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight md:flex-shrink-0">
            <Icon icon="file-check" />
            &nbsp; New Prebook
          </h2>
          <div className="flex">
            <button
              type="button"
              onClick={handleCancelClick}
              className="btn-secondary inline-flex"
            >
              Cancel
            </button>
            <button
              type="button"
              className="btn-secondary ml-3 inline-flex border-blue-600 px-2 text-blue-600"
              onClick={handleSaveAndCloseClick}
            >
              Save &amp; Close &nbsp;
              <Icon icon="save" />
            </button>
            <button
              type="button"
              className="btn-secondary ml-3 inline-flex border-green-600 px-2 text-green-600"
              onClick={handleSaveAndNewClick}
            >
              Save &amp; New &nbsp;
              <Icon icon="save" />
              &nbsp;
              <Icon icon="plus" />
            </button>

            <button
              type="button"
              className="btn-primary ml-3 inline-flex"
              onClick={handleSaveAndSendClick}
            >
              Save &amp; Send &nbsp;
              <Icon icon="save" />
              &nbsp;
              <Icon icon="paper-plane" />
            </button>
          </div>
        </div>
      </header>
      <main className="flex-grow overflow-auto">
        <div className="mx-auto max-w-7xl py-6 sm:px-6 lg:px-8">
          <Error
            error={error}
            clear={handleClearErrorClick}
            containerClasses="w-full mt-4"
          />
          <Error
            error={overbookedError}
            type="warning"
            containerClasses="w-full mt-4 whitespace-pre"
          />
          <Error
            error={requiredDateInPastError}
            type="warning"
            containerClasses="w-full mt-4 whitespace-pre"
          />
          <form className="space-y-8 divide-y divide-gray-200">
            <div className="space-y-8 divide-y divide-gray-200">
              <div>
                <div className="mt-6 grid grid-cols-1 gap-x-4 gap-y-6 lg:grid-cols-3 xl:grid-cols-4">
                  <div>
                    <div className="mt-1 rounded-md shadow-sm">
                      <Combobox
                        value={vendor}
                        onChange={handleVendorChange}
                        label="Vendor"
                        required
                        collection={vendors}
                        filter={(q, v) => startsWith(v.name, q)}
                        secondaryDisplayTextProp="vendorNo"
                        autofocus
                      />
                    </div>
                  </div>
                  {!isBlanket && (
                    <div>
                      <label
                        htmlFor="required-date"
                        className="block text-sm font-medium text-gray-500"
                      >
                        Required Date &nbsp;
                        <span className="text-red-500">&nbsp;*</span>
                      </label>
                      <div className="mt-1">
                        <input
                          type="date"
                          max="2050-01-01"
                          name="requiredDate"
                          id="required-date"
                          value={requiredDate || ''}
                          onChange={handleRequiredDateChange}
                          ref={requiredDateRef}
                          className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        />
                      </div>
                    </div>
                  )}
                  {isBlanket && (
                    <div>
                      <div className="grid grid-cols-2">
                        <div>
                          <label
                            htmlFor="blanket-start-date"
                            className="block text-sm font-medium text-gray-500"
                          >
                            Blanket From
                          </label>
                        </div>
                        <div>
                          <label
                            htmlFor="required-date"
                            className="block text-sm font-medium text-gray-500"
                          >
                            To
                            <span className="text-red-500">&nbsp;*</span>
                          </label>
                        </div>
                        <div className="col-start-1 mt-1">
                          <input
                            type="date"
                            max="2050-01-01"
                            name="blanketStartDate"
                            id="blanket-start-date"
                            value={blanketStartDate || ''}
                            onChange={handleBlanketStartDateChange}
                            ref={requiredDateRef}
                            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                          />
                        </div>
                        <div className="mt-1">
                          <input
                            type="date"
                            max="2050-01-01"
                            name="requiredDate"
                            id="required-date"
                            value={requiredDate || ''}
                            min={blanketStartDate || ''}
                            onChange={handleRequiredDateChange}
                            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                          />
                        </div>
                      </div>
                    </div>
                  )}
                  <div>
                    <label
                      htmlFor="is-blanket"
                      className="block text-sm font-medium text-gray-500"
                    >
                      Prebook is a Blanket
                    </label>
                    <div className="mt-1 h-10">
                      <HeadlessUI.Switch
                        id="is-blanket"
                        className={classNames(
                          isBlanket ? 'bg-blue-600' : 'bg-gray-200',
                          'relative mt-2 inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
                        )}
                        onChange={handleIsBlanketChange}
                        checked={isBlanket}
                      >
                        <span
                          aria-hidden="true"
                          className={classNames(
                            isBlanket ? 'translate-x-5' : 'translate-x-0',
                            'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                          )}
                        />
                      </HeadlessUI.Switch>
                    </div>
                  </div>
                  <div className="col-start-1">
                    <div className="mt-1 rounded-md shadow-sm">
                      <Combobox
                        value={customer}
                        onChange={handleCustomerChange}
                        label="Customer"
                        collection={customers}
                        filter={(q, c) =>
                          startsWith(c.name, q) || startsWith(c.customerNo, q)
                        }
                        secondaryDisplayTextProp="customerNo"
                        nullDisplayText="No Customer"
                      />
                    </div>
                  </div>
                  <div className="col-auto">
                    <div className="mt-1 rounded-md shadow-sm">
                      <ShipToCombobox
                        value={shipTo}
                        onChange={handleShipToChange}
                        inputRef={shipToRef}
                        customer={customerDetail}
                      />
                    </div>
                  </div>
                  <div className="col-auto">
                    <div className="mt-1 rounded shadow-sm">
                      <Combobox
                        value={salesperson}
                        onChange={handleSalespersonChange}
                        label="Salesperson"
                        collection={salespeople}
                        filter={(q, s) => startsWith(s.name, q)}
                      />
                    </div>
                  </div>
                  <div className="col-start-1">
                    <label
                      htmlFor="prebook-name"
                      className="block text-sm font-medium text-gray-500"
                    >
                      Name (optional)
                    </label>
                    <div className="mt-1">
                      <input
                        type="text"
                        id="prebook-name"
                        name="name"
                        value={name || ''}
                        onChange={handleNameChange}
                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        placeholder="e.g. Easter"
                      />
                    </div>
                  </div>
                  <div className="col-auto">
                    <label
                      htmlFor="box-code"
                      className="block text-sm font-medium text-gray-500"
                    >
                      Box Code
                    </label>
                    <div className="mt-1">
                      <input
                        type="text"
                        name="boxCode"
                        id="box-code"
                        value={boxCode || ''}
                        onChange={handleBoxCodeChange}
                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      />
                    </div>
                  </div>
                  <div className="col-auto">
                    <div className="flex align-bottom">
                      <div className="flex-grow rounded shadow-sm">
                        <Combobox
                          value={seasonName}
                          onChange={handleSeasonNameChange}
                          label="Season / Holiday"
                          collection={seasonNames}
                          filter={(q, s) => startsWith(s, q)}
                        />
                      </div>
                      <div className="mt-auto flex">
                        <button
                          type="button"
                          className="btn-secondary"
                          onClick={handleAddSeasonClick}
                          tabIndex={-1}
                        >
                          <Icon icon="plus" />
                        </button>
                      </div>
                    </div>
                  </div>
                  <div className="col-span-2">
                    <label
                      htmlFor="comments"
                      className="block text-sm font-medium text-gray-500"
                    >
                      Comments
                    </label>
                    <textarea
                      rows={4}
                      name="comments"
                      id="comment"
                      value={comments || ''}
                      onChange={handleCommentsChange}
                      className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    />
                  </div>
                  <div>
                    <label
                      htmlFor="grower-item-notes"
                      className="block text-sm font-medium text-gray-500"
                    >
                      Grower Item Notes&nbsp;
                      <HeadlessUI.Popover className="relative inline-block">
                        <HeadlessUI.Popover.Button
                          className="btn-secondary mb-1 rounded-full px-1 py-0 text-sm text-blue-500 focus:ring-0"
                          tabIndex={-1}
                        >
                          <Icon icon="question-circle" />
                        </HeadlessUI.Popover.Button>
                        <HeadlessUI.Transition
                          as={Fragment}
                          enter="transition ease-out duration-200"
                          enterFrom="opacity-0 translate-y-1"
                          enterTo="opacity-100 translate-y-0"
                          leave="transition ease-in duration-150"
                          leaveFrom="opacity-100 translate-y-0"
                          leaveTo="opacity-0 translate-y-1"
                        >
                          <HeadlessUI.Popover.Panel className="absolute bottom-10 z-10 w-96 -translate-x-1/2 transform px-4">
                            <div className="rounded-lg bg-yellow-50 p-4 font-normal shadow-lg ring-1 ring-black ring-opacity-5">
                              Grower Item notes will be included on{' '}
                              <span className="font-semibold italic">
                                each item
                              </span>{' '}
                              of the Prebook.
                            </div>
                          </HeadlessUI.Popover.Panel>
                        </HeadlessUI.Transition>
                      </HeadlessUI.Popover>
                    </label>
                    <textarea
                      rows={4}
                      name="growerItemNotes"
                      id="grower-item-notes"
                      value={growerItemNotes || ''}
                      onChange={handleGrowerItemNotesChange}
                      className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    />
                  </div>
                  <div className="col-span-1 lg:col-span-3 xl:col-span-4">
                    <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                      <table className="min-w-full">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className={classNames(cellClassName, 'pl-4')}>
                              Product
                            </th>
                            <th
                              className={classNames(
                                cellClassName,
                                'text-center'
                              )}
                            >
                              Order Qty
                            </th>
                            <th
                              className={classNames(
                                cellClassName,
                                'text-center'
                              )}
                            >
                              Pot Cover
                            </th>
                            <th
                              className={classNames(
                                cellClassName,
                                'text-center'
                              )}
                            >
                              UPC
                            </th>
                            <th
                              className={classNames(
                                cellClassName,
                                'text-center'
                              )}
                            >
                              Date Code
                            </th>
                            <th
                              className={classNames(
                                cellClassName,
                                'text-center'
                              )}
                            >
                              Retail
                            </th>
                            <th
                              className={classNames(
                                cellClassName,
                                'text-center'
                              )}
                            >
                              W & M
                            </th>
                            <th className={cellClassName}>&nbsp;</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200 bg-white">
                          {items.map((item) => (
                            <Item key={item.id} item={item} />
                          ))}
                        </tbody>
                        <tfoot>
                          <tr>
                            <td
                              colSpan={7}
                              className={classNames(cellClassName, 'pr-4')}
                            >
                              <button
                                type="button"
                                className="btn-new"
                                onClick={handleAddItemClick}
                              >
                                <Icon icon="plus" />
                                &nbsp; Add Item &nbsp;
                                <span className="text-xs text-gray-300">
                                  (Alt + A)
                                </span>
                              </button>
                            </td>
                          </tr>
                        </tfoot>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
      </main>
      <Inventory
        open={showInventoryDialog}
        cancel={handleAddItemCancel}
        confirm={handleAddItemConfirm}
        vendor={vendor}
      />
      <CreateSeason
        open={showAddSeasonDialog}
        cancel={handleAddSeasonCancel}
        confirm={handleAddSeasonConfirm}
      />
      <BlankSlate open={showBlankSlate} close={handleBlankSlateClose} />
      <Alert
        open={!!showRequiredDateInPastDialog}
        colour="danger"
        title="Required Date in past"
        message={`The Required Date ${formatDate(
          requiredDate,
          'MMM d, yyyy'
        )} is in the past. Are you sure you would like to save this Future Order?`}
        confirmButtonText="Yes"
        cancelButtonText="No"
        confirm={handleRequiredDateInPastConfirm}
        cancel={handleRequiredDateInPastCancel}
      />
      <BoekestynProducts
        onSave={handleBoekestynProductSaved}
        productDefaults={productDefaults}
      />
    </div>
  );
}

interface SaveContinuationOptions {
  continueRequiredDateInPast?: boolean;
}
