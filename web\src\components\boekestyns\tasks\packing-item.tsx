import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Icon } from '@/components/icon';
import { useAppSelector, useAppDispatch } from '@/services/hooks';
import { classNames } from '@/utils/class-names';
import { isUpc } from '@/utils/equals';
import { formatDate } from '@/utils/format';
import { selectTaskItems, startPacking, completePacking } from './task-slice';

const today = formatDate(new Date(), 'yyyy-MM-dd');

interface PackingItemProps {
  date: string;
}

export function PackingItem({ date }: PackingItemProps) {
  const dispatch = useAppDispatch(),
    tasks = useAppSelector(selectTaskItems),
    dateTasks = tasks.filter(
      (t) => t.requiredDate === date && t.prepComplete && !t.packComplete
    ),
    isToday = date === today;

  const handlePackingStart = (id: number) => {
    dispatch(startPacking(id));
  };

  const handlePackingComplete = (id: number) => {
    dispatch(completePacking(id));
  };

  if (!dateTasks.length) {
    return null;
  }

  return (
    <tbody
      key={date}
      className={classNames(
        'divide-y',
        isToday ? 'divide-green-700' : 'divide-gray-200'
      )}
    >
      <tr
        className={classNames(
          'sticky top-[40px] border-b',
          isToday ? 'bg-green-700' : 'bg-gray-200'
        )}
      >
        <td
          colSpan={7}
          className={classNames(
            'p-2 font-semibold ',
            isToday ? 'text-white' : 'text-gray-900'
          )}
        >
          {date}
        </td>
      </tr>
      {dateTasks.map((task) => (
        <tr key={task.id}>
          <td className="p-2 text-center">
            <FontAwesomeIcon
              icon={['fas', 'star']}
              className={classNames(
                'text-yellow-500',
                task.priority ? '' : 'invisible'
              )}
            />
          </td>
          <td className="p-2 text-center">{task.boxCode}</td>
          <td className="p-2 text-center">
            {task.goodSkids && <Icon icon="check" />}
          </td>
          <td className="p-2 text-center">{task.orderQty}</td>
          <td className="p-2">{task.description}</td>
          <td className="p-2 text-center">
            {!task.packStart && (
              <button
                type="button"
                className="small secondary m-2 rounded border px-2"
                onClick={() => handlePackingStart(task.id)}
              >
                <Icon icon="check" />
              </button>
            )}
            {!!task.packStart && (
              <div className="text-center text-green-700">
                <Icon icon="check-circle" />
              </div>
            )}
          </td>
          <td className="p-2 text-center">
            {!task.packComplete && (
              <button
                type="button"
                className="small secondary m-2 rounded border px-2"
                onClick={() => handlePackingComplete(task.id)}
              >
                <Icon icon="check" />
              </button>
            )}
            {!!task.packComplete && (
              <div className="text-center text-green-700">
                <Icon icon="check-circle" />
              </div>
            )}
          </td>
        </tr>
      ))}
    </tbody>
  );
}
