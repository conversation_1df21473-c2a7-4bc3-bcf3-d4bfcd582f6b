import { Fragment, useEffect, useState } from 'react';
import Link from 'next/link';
import * as HeadlessUI from '@headlessui/react';
import * as prebooks from 'api/models/prebooks';
import { Icon } from '@/components/icon';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { routes } from '@/services/routes';
import { classNames } from '@/utils/class-names';
import { handleFocus } from '@/utils/focus';
import { formatNumber, parseQuantity } from '@/utils/format';
import {
  setPrebookItemOrderQuantity,
  selectFutureOrder,
} from './future-order-detail-slice';

interface FutureOrderDetailPrebookItemProps {
  item: prebooks.PrebookDetailItem;
}

export function FutureOrderDetailPrebookItem({
  item,
}: FutureOrderDetailPrebookItemProps) {
  const dispatch = useAppDispatch(),
    futureOrderDetail = useAppSelector(selectFutureOrder),
    [orderQuantity, setOrderQuantity] = useState(
      formatNumber(item.orderQuantity)
    ),
    [showTooltip, setShowTooltip] = useState(false),
    isOnFutureOrder = item.futureOrderId === futureOrderDetail?.id,
    cellClassName =
      'whitespace-nowrap px-1 py-1 border-bottom-0 ' +
      (isOnFutureOrder
        ? 'text-gray-500 bg-white'
        : 'bg-gray-50 text-gray-500 italic');

  useEffect(() => {
    setOrderQuantity(formatNumber(item.orderQuantity));
  }, [item.orderQuantity]);

  const handleOrderQuantityChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setOrderQuantity(e.target.value);
  };

  const handleOrderQuantityBlur = () => {
    const value = parseQuantity(orderQuantity);
    dispatch(setPrebookItemOrderQuantity({ itemId: item.id, value }));
  };

  const handleTooltipMouseEnter = () => {
    if (!isOnFutureOrder) {
      setShowTooltip(true);
    }
  };

  const handleTooltipMouseLeave = () => {
    setShowTooltip(false);
  };

  return (
    <tr className="text-xs">
      <td className={cellClassName}>
        <div className="flex flex-row">
          <HeadlessUI.Popover
            className="relative inline-block cursor-pointer"
            onMouseEnter={handleTooltipMouseEnter}
            onMouseLeave={handleTooltipMouseLeave}
          >
            <>
              <div
                className={classNames(
                  'mx-2',
                  isOnFutureOrder ? 'invisible' : 'cursor-pointer'
                )}
              >
                <Icon icon="exclamation-triangle" className="text-yellow-600" />
              </div>
              <HeadlessUI.Transition
                as={Fragment}
                show={showTooltip}
                enter="transition ease-out duration-200"
                enterFrom="opacity-0"
                enterTo="opacity-100"
                leave="transition ease-in duration-150"
                leaveFrom="opacity-100"
                leaveTo="opacity-0"
              >
                <HeadlessUI.Popover.Panel
                  static
                  className="absolute left-1/2 top-0 z-10 -translate-x-1/2 -translate-y-full transform bg-yellow-50"
                >
                  <div className="w-auto whitespace-nowrap rounded-lg border p-4 shadow-lg">
                    This item is on Future Order{' '}
                    <Link
                      href={routes.futureOrders.detail.to(item.futureOrderId!)}
                    >
                      #{formatNumber(item.futureOrderId, '00000')}
                    </Link>
                  </div>
                </HeadlessUI.Popover.Panel>
              </HeadlessUI.Transition>
            </>
          </HeadlessUI.Popover>
          <div className="flex-grow">{item.spirePartNumber}</div>
        </div>
      </td>
      <td className={cellClassName}>{item.description}</td>
      <td className={classNames(cellClassName, 'text-center')}>
        <Icon icon={item.upgradeSheet ? 'check-square' : 'square'} />
      </td>
      <td className={classNames(cellClassName, 'text-center')}>
        <input
          type="text"
          className={classNames(
            'mx-auto w-full max-w-[100px] rounded-md border-gray-300 text-right text-xs shadow-sm',
            isOnFutureOrder
              ? 'bg-white focus:border-blue-500 focus:ring-blue-500'
              : 'cursor-default bg-gray-50'
          )}
          value={orderQuantity}
          disabled={!isOnFutureOrder}
          onChange={handleOrderQuantityChange}
          onBlur={handleOrderQuantityBlur}
          onFocus={handleFocus}
        />
        {item.isApproximate && <Icon icon="plus-minus" className="ml-2" />}
      </td>
      <td className={classNames(cellClassName, 'text-center')}>
        {item.potCover}
      </td>
      <td className={classNames(cellClassName, 'text-center')}>{item.upc}</td>
      <td className={classNames(cellClassName, 'text-center')}>
        {item.dateCode}
      </td>
      <td className={classNames(cellClassName, 'text-center')}>
        <Icon icon={item.weightsAndMeasures ? 'check-square' : 'square'} />
      </td>
      <td className={classNames(cellClassName, 'text-center')}>
        {item.retail}
      </td>
    </tr>
  );
}
