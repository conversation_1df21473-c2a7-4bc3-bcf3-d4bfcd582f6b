import { Fragment, useEffect, useRef, useState } from 'react';
import * as HeadlessUI from '@headlessui/react';
import { attachmentUrl } from 'api/api-base';
import { prebooksApi } from 'api/prebooks-service';
import * as futureOrders from 'api/models/future-orders';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import { usePermissions } from '@/services/auth';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { classNames } from '@/utils/class-names';
import { ProblemDetails } from '@/utils/problem-details';
import {
  selectAttachments,
  addAttachments,
  deleteAttachments,
} from './upgrade-item-list-slice';

export interface UpgradeProductItemAttachmentsProps {
  items: futureOrders.UpgradeItem[];
}

export function UpgradeProductItemAttachments({
  items,
}: UpgradeProductItemAttachmentsProps) {
  const dispatch = useAppDispatch(),
    attachments = useAppSelector(selectAttachments),
    [attachmentTooltipOpen, setAttachmentTooltipOpen] = useState(false),
    [attachmentDialogOpen, setAttachmentDialogOpen] = useState(false),
    [isOver, setIsOver] = useState(false),
    [error, setError] = useState<ProblemDetails | null>(null),
    [itemAttachments, setItemAttachments] = useState<
      futureOrders.UpgradeItemAttachment[]
    >([]),
    inputRef = useRef<HTMLInputElement>(null),
    { can } = usePermissions(),
    canEdit = can('Sales Team'),
    canOpenDialog = canEdit || !!itemAttachments.length;

  useEffect(() => {
    const // the same attachment might be attached with multiple items, so we need to filter out duplicates
      itemAttachments = attachments.reduce((memo, a) => {
        if (
          !memo.some((m) => m.filename === a.filename) &&
          items.some((i) => i.id === a.prebookItemId)
        ) {
          memo.push(a);
        }
        return memo;
      }, [] as futureOrders.UpgradeItemAttachment[]);
    setItemAttachments(itemAttachments);
  }, [attachments, items]);

  const handleAddAttachmentClick = () => {
    inputRef.current?.click();
  };

  const handleDeleteClick = (
    attachment: futureOrders.UpgradeItemAttachment
  ) => {
    const fileAttachments = attachments.filter(
      (a) => a.filename === attachment.filename
    );
    fileAttachments.forEach(
      async (a) => await prebooksApi.deleteUpgradeAttachment(a.id)
    );
    dispatch(deleteAttachments(fileAttachments));
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (file) {
      const args = { file, prebookItemIds: items.map((i) => i.id) },
        response = await prebooksApi.addUpgradeAttachment(args);

      if ('error' in response) {
        setError(response.error as ProblemDetails);
      } else {
        const { attachments } = response.data;
        dispatch(addAttachments(attachments));
      }
    }

    e.target.value = '';
  };

  const handleAttachmentTooltipMouseEnter = () => {
    if (canOpenDialog) {
      setAttachmentTooltipOpen(true);
    }
  };

  const handleAttachmentTooltipMouseLeave = () => {
    setAttachmentTooltipOpen(false);
  };

  const handleShowAttachmentClick = () => {
    if (canOpenDialog) {
      setAttachmentDialogOpen(true);
      setAttachmentTooltipOpen(false);
    }
  };

  const handleShowAttachmentClose = () => {
    setAttachmentDialogOpen(false);
  };

  const handleClearError = () => {
    setError(null);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsOver(true);
  };

  const handleDragLeave = () => {
    setIsOver(false);
  };

  const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();

    const file = e.dataTransfer.files?.[0];

    if (file) {
      const args = { file, prebookItemIds: items.map((i) => i.id) },
        response = await prebooksApi.addUpgradeAttachment(args);

      if ('error' in response) {
        setError(response.error as ProblemDetails);
      } else {
        const { attachments } = response.data;
        dispatch(addAttachments(attachments));
      }
    }

    setIsOver(false);
  };

  return (
    <>
      <HeadlessUI.Popover
        className="inline-block cursor-pointer md:relative"
        onMouseEnter={handleAttachmentTooltipMouseEnter}
        onMouseLeave={handleAttachmentTooltipMouseLeave}
      >
        <HeadlessUI.Popover.Button
          className={classNames(
            'font-lg px-2 py-1',
            itemAttachments.length ? '' : 'text-gray-300'
          )}
          onClick={handleShowAttachmentClick}
        >
          <Icon icon="camera" />
        </HeadlessUI.Popover.Button>
        <HeadlessUI.Transition
          as={Fragment}
          show={attachmentTooltipOpen}
          afterEnter={handleClearError}
          enter="transition ease-out duration-200"
          enterFrom="opacity-0 translate-y-1"
          enterTo="opacity-100 translate-y-0"
          leave="transition ease-in duration-150"
          leaveFrom="opacity-100 translate-y-0"
          leaveTo="opacity-0 translate-y-1"
        >
          <HeadlessUI.Popover.Panel
            static
            className="absolute left-[30px] z-10 -translate-y-1/2 transform bg-white"
            onClick={handleShowAttachmentClick}
          >
            <div className="w-96 whitespace-nowrap rounded-lg border p-4 shadow-lg">
              {!!itemAttachments.length && (
                <img
                  src={attachmentUrl(itemAttachments[0].filename)}
                  alt={itemAttachments[0].filename}
                />
              )}
              {itemAttachments.length
                ? `Click for Image${itemAttachments.length > 1 ? 's' : ''}`
                : 'Add Images'}
            </div>
          </HeadlessUI.Popover.Panel>
        </HeadlessUI.Transition>
      </HeadlessUI.Popover>
      <HeadlessUI.Transition.Root show={attachmentDialogOpen} as={Fragment}>
        <HeadlessUI.Dialog
          as="div"
          className="relative z-40"
          onClose={handleShowAttachmentClose}
        >
          <HeadlessUI.Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
          </HeadlessUI.Transition.Child>

          <div className="fixed inset-0 z-40 overflow-y-auto">
            <div className="flex min-h-full items-start justify-center p-4 text-center sm:items-center sm:p-0">
              <HeadlessUI.Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                enterTo="opacity-100 translate-y-0 sm:scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              >
                <HeadlessUI.Dialog.Panel className="relative m-4 w-full transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8">
                  <div className="flex flex-row flex-wrap justify-center gap-4 bg-white p-4 sm:p-6">
                    <div
                      onDragOver={handleDragOver}
                      onDragLeave={handleDragLeave}
                      onDrop={handleDrop}
                      className={classNames(
                        'flex-grow rounded',
                        isOver ? 'border border-green-700' : ''
                      )}
                    >
                      {itemAttachments.map((attachment) => (
                        <div key={attachment.filename} className="relative">
                          <img
                            src={attachmentUrl(attachment.filename)}
                            alt={attachment.filename}
                            className="w-full md:h-64 md:w-auto"
                          />
                          <button
                            type="button"
                            className="absolute end-0 top-0 text-red-700"
                            onClick={() => handleDeleteClick(attachment)}
                          >
                            <Icon icon="x" />
                          </button>
                        </div>
                      ))}
                      {!itemAttachments.length && (
                        <div className="h-24 p-8 text-center">
                          <h1 className="text-xl">Drag image here</h1>
                        </div>
                      )}
                    </div>
                  </div>
                  <Error error={error} clear={handleClearError} />
                  <div className="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                    {canEdit && (
                      <button
                        type="button"
                        onClick={handleAddAttachmentClick}
                        className="btn-new ml-2"
                      >
                        <Icon icon="plus" />
                        &nbsp;Add Image
                      </button>
                    )}
                    <button
                      type="button"
                      onClick={handleShowAttachmentClose}
                      className="btn-secondary"
                    >
                      Close
                    </button>
                  </div>
                </HeadlessUI.Dialog.Panel>
              </HeadlessUI.Transition.Child>
            </div>
          </div>
        </HeadlessUI.Dialog>
      </HeadlessUI.Transition.Root>
      <input
        type="file"
        ref={inputRef}
        onChange={handleFileChange}
        className="hidden"
      />
    </>
  );
}
