import { formatCurrency, formatNumber, parseQuantity } from '@/utils/format';
import { sortBy } from '@/utils/sort';
import { weekFromDate } from '@/utils/weeks';
import * as models from 'api/models/future-orders';
import * as prebooks from 'api/models/prebooks';
import * as settings from 'api/models/settings';

const sortByScore = sortBy('score', 'descending');

export function itemIsOverbooked(
  item: models.FutureOrderCreateItem | models.FutureOrderDetailItem,
  blanketItems: prebooks.PrebookBlanketItem[]
) {
  const blanketItem = blanketItems?.find((i) => i.id === item.blanketItemId);
  if (!item || !blanketItem) {
    return false;
  }

  return (
    blanketItem.blanketQuantity - blanketItem.bookedQuantity <
    item.orderQuantity
  );
}

const regex = /[xX] *(\d+)$/;

export function parsePackQuantity(description: string | null | undefined) {
  if (!description || !regex.test(description)) {
    return null;
  }

  const results = regex.exec(description),
    value = results ? results[1] : null,
    caseQuantity = parseQuantity(value);

  return caseQuantity;
}

export function getPackQuantity(
  item:
    | models.FutureOrderDetailItem
    | models.FutureOrderCreateItem
    | models.UpgradeItem
) {
  return parsePackQuantity(item.description);
}

export function getUnitPrice(
  item: models.FutureOrderDetailItem | models.FutureOrderCreateItem,
  casePrice?: number | null
) {
  var price = casePrice === undefined ? item.unitPrice : casePrice;

  if (!item.description || !regex.test(item.description) || !price) {
    return null;
  }

  const caseQuantity = getPackQuantity(item),
    unitPrice = caseQuantity
      ? Math.round((price / caseQuantity) * 100) / 100
      : null;

  return unitPrice;
}

export function getCasePrice(
  item: models.FutureOrderDetailItem | models.FutureOrderCreateItem,
  unitPrice: number | null
) {
  if (!item.description || !regex.test(item.description) || !unitPrice) {
    return null;
  }

  const caseQuantity = getPackQuantity(item),
    casePrice = caseQuantity ? unitPrice * caseQuantity : null;

  return casePrice;
}

export function showPriceWarning(
  customerId: number | null | undefined,
  shipToId: number | null | undefined,
  item: models.FutureOrderDetailItem | models.FutureOrderCreateItem,
  availabilityPrice: number | null | undefined,
  deviationWarnings: settings.PriceDeviationWarning[] | undefined
) {
  const casePrice = item.unitPrice,
    price = getUnitPrice(item);

  if (
    item.useAvailabilityPricing ||
    !!item.specialPrice ||
    !availabilityPrice ||
    !price ||
    !casePrice ||
    !deviationWarnings
  ) {
    return null;
  }

  const packQuantity = getPackQuantity(item) || 1,
    devianceScores = deviationWarnings
      .filter((w) => {
        if (!!w.customerId && customerId !== w.customerId) {
          return false;
        }

        if (!!w.shipToId && shipToId !== w.shipToId) {
          return false;
        }

        if (!!w.maxUnitPrice && price > w.maxUnitPrice) {
          return false;
        }

        if (!!w.minPackSize && packQuantity < w.minPackSize) {
          return false;
        }

        return true;
      })
      .map((w) => {
        const score =
          (!!w.customerId ? 100 : 0) +
          (!!w.shipToId ? 50 : 0) +
          (!!w.maxUnitPrice ? 1 / (w.maxUnitPrice - price) : 0) +
          (!!w.minPackSize ? 1 / (packQuantity - w.minPackSize) : 0);

        return { score, w };
      })
      .sort(sortByScore),
    deviance = devianceScores[0]?.w,
    difference = (1 - casePrice / availabilityPrice) * 100;

  if (!deviance) {
    return null;
  }

  if (difference <= deviance.allowableDeviation) {
    return null;
  }

  return {
    difference,
    allowableDeviation: deviance.allowableDeviation,
    unitPrice: casePrice,
    availabilityPrice,
    packQuantity,
    message: `The Case Price of ${formatCurrency(casePrice)} is ${formatNumber(
      difference / 100,
      '0%'
    )} less than the Availability Price of ${formatCurrency(
      availabilityPrice
    )}.`,
  };
}

export function labelLengthWarning(
  item: models.FutureOrderDetailItem | models.FutureOrderCreateItem
) {
  if (!item.comments) {
    return null;
  }

  const maxLength = 24,
    labelTextRegex = /\[(.+)\]/,
    match = labelTextRegex.exec(item.comments);

  if (match && match[1].length > maxLength) {
    return `The comment text in square brackets is longer than ${maxLength} characters.`;
  }
}

export function colourLengthWarning(
  item: models.FutureOrderDetailItem | models.FutureOrderCreateItem
) {
  if (!item.comments) {
    return null;
  }

  const maxLength = 40,
    labelTextRegex = /\<(.+)\>/,
    match = labelTextRegex.exec(item.comments);

  if (match && match[1].length > maxLength) {
    return `The comment text in angle brackets is longer than ${maxLength} characters.`;
  }
}

export function duplicateUpcWarning(items: { upc: string | null }[]) {
  const upcs = items.filter((i) => i.upc).map((i) => i.upc as string),
    duplicates = upcs.filter((upc, index) => upcs.indexOf(upc) !== index),
    one = duplicates.length === 1;

  if (duplicates.length === 0) {
    return null;
  }

  return `The UPC${one ? '' : 's'} ${duplicates.join(', ')} ${
    one ? 'is' : 'are'
  } duplicated in this order.`;
}

export function findDefaultVendorOverride(
  spirePartNumber: string | null,
  requiredDate: string | null,
  defaultVendorOverrides: settings.DefaultVendorOverride[]
) {
  if (requiredDate) {
    const week = weekFromDate(requiredDate)?.week || 0,
      override = defaultVendorOverrides.find(
        (o) =>
          !!spirePartNumber &&
          o.spirePartNumbers.indexOf(spirePartNumber) !== -1
      );

    if (override) {
      const item = override.items.find(
        (i) => i.startWeek <= week && i.endWeek >= week
      );

      return item || null;
    }

    return null;
  }

  return null;
}
