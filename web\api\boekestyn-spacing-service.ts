import { createApi } from '@reduxjs/toolkit/query/react';
import { axiosBaseQuery } from './api-base';
import * as models from './models/boekestyns';

export const boekestynSpacingApi = createApi({
  reducerPath: 'boekestyn-spacing-api',
  baseQuery: axiosBaseQuery('boekestyns/spacing/'),
  refetchOnMountOrArgChange: true,
  tagTypes: ['SpacingSchedules', 'SpacingWorkOrdersByDate'],
  endpoints: (builder) => ({
    spacing: builder.query<BoekestynSpacingResponse, void>({
      query: () => ({
        url: '',
      }),
    }),
    spacingOrders: builder.query<
      BoekestynSpacingOrdersResponse,
      BoekestynSpacingOrdersArgs
    >({
      query: ({ startDate, endDate }) => ({
        url: `orders?startDate=${startDate}&endDate=${endDate}`,
      }),
    }),
    spacingSchedules: builder.query<
      BoekestynSpacingSchedulesResponse,
      BoekestynSpacingSchedulesArgs
    >({
      query: ({ date }) => ({
        url: `schedules?date=${date}`,
      }),
      providesTags: ['SpacingSchedules'],
    }),
    addSpacingOrderToSpacingSchedule: builder.mutation<
      void,
      AddSpacingOrderToSpacingScheduleArgs
    >({
      query: (data) => ({
        url: `workOrders`,
        method: 'POST',
        data,
      }),
      invalidatesTags: ['SpacingSchedules'],
    }),
    sortSpacingWorkOrders: builder.mutation<void, SortSpacingWorkOrdersArgs>({
      query: (data) => ({
        url: `workOrders/sort`,
        method: 'POST',
        data,
      }),
      invalidatesTags: ['SpacingSchedules'],
    }),
    updateSpacingWorkOrderComment: builder.mutation<
      void,
      UpdateSpacingWorkOrderCommentArgs
    >({
      query: ({ id, comment }) => ({
        url: `workOrders/${id}/comment`,
        method: 'POST',
        data: { comment },
      }),
      invalidatesTags: ['SpacingSchedules'],
    }),
    deleteSpacingWorkOrder: builder.mutation<void, DeleteSpacingWorkOrderArgs>({
      query: (data) => ({
        url: `workOrders/${data.id}/remove`,
        method: 'POST',
        data,
      }),
      invalidatesTags: ['SpacingSchedules'],
    }),
    getSpacingWorkOrdersByDate: builder.query<
      SpacingWorkOrdersByDateResponse,
      string
    >({
      query: (date) => ({
        url: `workOrders?date=${date}`,
      }),
      providesTags: ['SpacingWorkOrdersByDate'],
    }),
    startSpacingWorkOrderLabour: builder.mutation<
      void,
      StartSpacingWorkOrderLabourArgs
    >({
      query: (data) => ({
        url: `workOrders/${data.workOrderId}/labour/start`,
        method: 'POST',
        data,
      }),
      invalidatesTags: ['SpacingWorkOrdersByDate'],
    }),
    pauseSpacingWorkOrderLabour: builder.mutation<
      void,
      PauseSpacingWorkOrderLabourArgs
    >({
      query: (data) => ({
        url: `workOrders/${data.workOrderId}/labour/pause`,
        method: 'POST',
        data,
      }),
      invalidatesTags: ['SpacingWorkOrdersByDate'],
    }),
    stopSpacingWorkOrderLabour: builder.mutation<
      void,
      StopSpacingWorkOrderLabourArgs
    >({
      query: (data) => ({
        url: `workOrders/${data.workOrderId}/labour/stop`,
        method: 'POST',
        data,
      }),
      invalidatesTags: ['SpacingWorkOrdersByDate'],
    }),
  }),
});

export interface BoekestynSpacingResponse {
  lines: models.SpacingLine[];
}

interface BoekestynSpacingOrdersArgs {
  startDate: string;
  endDate: string;
}

export interface BoekestynSpacingOrdersResponse {
  orders: models.SpacingOrder[];
}

interface BoekestynSpacingSchedulesArgs {
  date: string;
}

export interface BoekestynSpacingSchedulesResponse {
  schedules: models.SpacingSchedule[];
}

export interface AddSpacingOrderToSpacingScheduleArgs {
  crewSize: number;
  potsToSpace: number;
  fromSpaceType: models.SpacingTypes;
  toSpaceType: models.SpacingTypes;
  requiresPinching: boolean;
  robotProgram: string;
  comments: string | null;
  schedule: models.SpacingSchedule;
  order: models.SpacingOrder;
}

export interface DeleteSpacingWorkOrderArgs {
  id: number;
  orderId: string;
}

export interface SortSpacingWorkOrdersArgs {
  workOrders: { workOrderId: number; sortOrder: number }[];
}

export interface UpdateSpacingWorkOrderCommentArgs {
  id: number;
  comment: string | null;
}

export interface SpacingWorkOrdersByDateResponse {
  orders: models.SpacingWorkOrderItem[];
}

export interface StartSpacingWorkOrderLabourArgs {
  workOrderId: number;
  crewSize: number;
}

export interface PauseSpacingWorkOrderLabourArgs {
  workOrderId: number;
  crewSize: number;
  comments: string | null;
}

export interface StopSpacingWorkOrderLabourArgs {
  workOrderId: number;
  crewSize: number;
  comments: string | null;
}

export const {
  useSpacingQuery,
  useSpacingOrdersQuery,
  useSpacingSchedulesQuery,
  useAddSpacingOrderToSpacingScheduleMutation,
  useSortSpacingWorkOrdersMutation,
  useDeleteSpacingWorkOrderMutation,
  useUpdateSpacingWorkOrderCommentMutation,
  useGetSpacingWorkOrdersByDateQuery,
  useStartSpacingWorkOrderLabourMutation,
  usePauseSpacingWorkOrderLabourMutation,
  useStopSpacingWorkOrderLabourMutation,
} = boekestynSpacingApi;
