export const PhytoLocation = 'PHYTO';

export interface InventoryItem {
  id: number;
  partNo: string;
  description: string;
  primaryVendor?: InventoryItemVendor | null;
  uom?: InventoryItemUnitOfMeasure | null;
}

export interface InventoryItemVendor {
  vendorNo: string | null;
}

export interface InventoryItemUnitOfMeasure {
  location: string | null;
}

export interface Vendor {
  id: number;
  vendorNo?: string;
  name: string;
}

export interface Customer {
  id: number;
  customerNo?: string;
  name: string;
  defaultShipTo: string | null;
}

export interface CustomerDetail extends Customer {
  shippingAddresses: CustomerShipTo[];
}

export interface Address {
  id: number;
  name: string;
  shipId: string;
  salesperson: CustomerSalesperson | null;
}

export interface CustomerAddress extends Address {
  linkNo: string;
}

export interface CustomerShipTo extends Address {
  boxCode: string | null;
  labels: string | null;
  customerInfo: string | null;
  priceLevel: string | null;
  defaultFreightPerCase: number | null;
}

export interface CustomerSalesperson {
  code: string;
  name: string;
}

export interface Salesperson {
  id: number;
  code: string;
  name: string;
}

export interface EmailTemplate {
  id: number;
  name: string;
  subject: string;
  body: string;
}

export interface ShippingMethod {
  id: number | null;
  code: string;
  description: string;
}

export interface InventoryComment {
  id: number;
  code: string;
  description: string;
  comments: string;
}

export interface PriceLevel {
  id: number;
  code: string;
}
