export const routes = {
  home: {
    to: () => '/',
  },
  prebooks: {
    list: {
      to: () => '/prebooks',
    },
    items: {
      to: () => '/prebooks/items',
    },
    blanketItems: {
      to: () => '/prebooks/blanket-items',
    },
    detail: {
      to: (id: number) => `/prebooks/${id}`,
    },
    new: {
      to: () => '/prebooks/new',
    },
  },
  futureOrders: {
    list: {
      to: () => '/future-orders',
    },
    items: {
      to: () => '/future-orders/items',
    },
    new: {
      to: () => '/future-orders/new',
    },
    detail: {
      to: (id: number) => `/future-orders/${id}`,
    },
  },
  boekestyns: {
    list: {
      to: () => '/boekestyns',
    },
    sales: {
      to: () => '/boekestyns/sales',
    },
    upcs: {
      to: () => '/boekestyns/upcs',
    },
    prep: {
      to: () => '/boekestyns/prep',
    },
    packing: {
      to: () => '/boekestyns/packing',
    },
    admin: {
      to: () => '/boekestyns/admin',
      sticking: {
        to: () => '/boekestyns/admin/sticking',
      },
      spacing: {
        to: () => '/boekestyns/admin/spacing',
      },
      harvesting: {
        to: () => '/boekestyns/admin/harvesting',
      },
      packing: {
        to: () => '/boekestyns/admin/packing',
      },
      stickering: {
        to: () => '/boekestyns/admin/stickering',
      },
      jobBoard: {
        to: () => '/boekestyns/admin/job-board',
      },
    },
    sticking: {
      to: () => '/boekestyns/sticking',
    },
    spacing: {
      to: () => '/boekestyns/spacing',
    },
    harvesting: {
      to: () => '/boekestyns/harvesting',
    },
  },
  upgrades: {
    list: {
      to: () => '/upgrades',
    },
  },
  labels: {
    list: {
      to: () => '/labels',
    },
    coborns: {
      to: () => '/labels/coborns',
    },
    albrecht: {
      to: () => '/labels/albrecht',
    },
    heinens: {
      to: () => '/labels/heinens',
    },
  },
  settings: {
    home: {
      to: () => '/settings',
    },
    customers: {
      to: () => '/settings/customers',
    },
    users: {
      to: () => '/settings/users',
    },
    seasons: {
      to: () => '/settings/seasons',
    },
    spire: {
      to: () => '/settings/spire',
    },
    productDefaults: {
      to: () => '/settings/product-defaults',
    },
    upgradeOptions: {
      to: () => '/settings/upgrade-options',
    },
    defaultVendorOverrides: {
      to: () => '/settings/default-vendor-overrides',
    },
    freightRates: {
      to: () => '/settings/freight-rates',
    },
  },
};
