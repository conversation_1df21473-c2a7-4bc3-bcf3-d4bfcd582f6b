import { AccountInfo } from '@azure/msal-browser';
import {
  AsyncThunk,
  createAction,
  createAsyncThunk,
  createSlice,
  PayloadAction,
} from '@reduxjs/toolkit';
import { prebooksApi, PendingBatchEmailResponse } from 'api/prebooks-service';
import * as spire from 'api/models/spire';
import { RootState } from '@/services/store';
import { ProblemDetails, createProblemDetails } from '@/utils/problem-details';

interface EmailBatchState {
  to: string;
  cc: string | null;
  bcc: string | null;
  subject: string;
  body: string;
  account: AccountInfo | null;
  templates: spire.EmailTemplate[];
  isLoading: boolean;
  error: ProblemDetails | null;
}

const initialState: EmailBatchState = {
  to: '',
  cc: null,
  bcc: null,
  subject: '',
  body: '',
  account: null,
  templates: [],
  isLoading: false,
  error: null,
};

export const getPrebookBatchEmail: AsyncThunk<
  PendingBatchEmailResponse,
  number,
  { state: RootState }
> = createAsyncThunk(
  'prebook-email-batch-getPrebookBatchEmail',
  async (vendorId, { rejectWithValue }) => {
    try {
      return await prebooksApi.pendingBatchEmail(vendorId);
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const createPrebookBatchEmail: AsyncThunk<
  void,
  number[],
  { state: RootState }
> = createAsyncThunk(
  'prebook-email-batch-createPrebookBatchEmail',
  async (ids, { rejectWithValue, getState }) => {
    try {
      const state = getState() as RootState,
        { to, cc, bcc, subject, body } = state.emailBatch;
      return await prebooksApi.createBatchEmail(
        ids,
        to,
        cc,
        bcc,
        subject,
        body
      );
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

const getPrebookBatchEmailPending = createAction(
    getPrebookBatchEmail.pending.type
  ),
  getPrebookBatchEmailFulfilled = createAction<PendingBatchEmailResponse>(
    getPrebookBatchEmail.fulfilled.type
  ),
  getPrebookBatchEmailRejected = createAction<ProblemDetails>(
    getPrebookBatchEmail.rejected.type
  ),
  createPrebookBatchEmailPending = createAction(
    createPrebookBatchEmail.pending.type
  ),
  createPrebookBatchEmailFulfilled = createAction(
    createPrebookBatchEmail.fulfilled.type
  ),
  createPrebookBatchEmailRejected = createAction<ProblemDetails>(
    createPrebookBatchEmail.rejected.type
  );

export const emailBatchSlice = createSlice({
  name: 'email-batch',
  initialState,
  reducers: {
    clearState(state) {
      Object.assign(state, { ...initialState });
    },
    clearError(state) {
      state.error = null;
    },
    setError(state, { payload }: PayloadAction<string>) {
      state.error = createProblemDetails(payload);
    },
    setTo(state, { payload }: PayloadAction<string>) {
      state.to = payload;
    },
    setCc(state, { payload }: PayloadAction<string | null>) {
      state.cc = payload;
    },
    setBcc(state, { payload }: PayloadAction<string | null>) {
      state.bcc = payload;
    },
    setSubject(state, { payload }: PayloadAction<string>) {
      state.subject = payload;
    },
    setBody(state, { payload }: PayloadAction<string>) {
      state.body = payload;
    },
    setAccount(state, { payload }: PayloadAction<AccountInfo | null>) {
      state.account = payload;
    },
  },
  extraReducers: (builder) =>
    builder
      .addCase(getPrebookBatchEmailPending, (state) => {
        state.isLoading = true;
      })
      .addCase(getPrebookBatchEmailFulfilled, (state, { payload }) => {
        state.isLoading = false;
        state.to = payload.toAddresses.join(';');
      })
      .addCase(getPrebookBatchEmailRejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      .addCase(createPrebookBatchEmailPending, (state) => {
        state.isLoading = true;
      })
      .addCase(createPrebookBatchEmailFulfilled, (state, { payload }) => {
        state.isLoading = false;
      })
      .addCase(createPrebookBatchEmailRejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      }),
});

export const {
  clearState,
  setTo,
  setCc,
  setBcc,
  setSubject,
  setBody,
  setAccount,
  clearError,
  setError,
} = emailBatchSlice.actions;

export const selectTo = (state: RootState) => state.emailBatch.to;
export const selectCc = (state: RootState) => state.emailBatch.cc;
export const selectBcc = (state: RootState) => state.emailBatch.bcc;
export const selectSubject = (state: RootState) => state.emailBatch.subject;
export const selectBody = (state: RootState) => state.emailBatch.body;
export const selectError = (state: RootState) => state.emailBatch.error;
export const selectIsLoading = (state: RootState) => state.emailBatch.isLoading;

export default emailBatchSlice.reducer;
