import { DateTime } from 'luxon';
import numeral from 'numeral';

export function formatDate(
  date: string | Date | null | undefined,
  format = 'yyyy-MM-dd'
): string {
  if (date == null) {
    return '';
  }

  const luxon =
    date instanceof Date ? DateTime.fromJSDate(date) : DateTime.fromISO(date);

  if (!luxon.isValid) {
    return '';
  }

  return luxon.toFormat(format);
}
export function formatNumber(value: any, format = '0,0'): string {
  if (value == null) {
    return '0';
  }

  return numeral(value).format(format);
}

export function formatCurrency(value: any, format = '$0,0.00'): string {
  if (value == null) {
    return '$0.00';
  }

  return numeral(value).format(format);
}

export function parseQuantity(
  value: string | null | undefined,
  defaultValue: number = 0
) {
  const quantity = numeral(value).value();
  if (quantity == null) {
    return defaultValue;
  }

  return Math.floor(quantity);
}

export function parseNullableQuantity(value: string | null) {
  const quantity = numeral(value).value();
  if (quantity == null) {
    return null;
  }

  return Math.floor(quantity);
}

export function parseCurrency(value: string | null, defaultValue: number = 0) {
  const quantity = numeral(value).value();

  if (quantity == null) {
    return defaultValue;
  }

  return Math.round(quantity * 100) / 100;
}

export function parseNullableCurrency(value: string | null) {
  const quantity = numeral(value).value();

  if (quantity == null) {
    return null;
  }

  return Math.round(quantity * 100) / 100;
}
