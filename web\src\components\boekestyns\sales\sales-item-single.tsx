import { useAppSelector } from '@/services/hooks';
import { equals } from '@/utils/equals';
import { formatNumber } from '@/utils/format';
import { Week } from '@/utils/weeks';
import { selectSingleProductItems, selectPlants } from './sales-slice';
import { boekCaseQuantity } from './boekestyn-sales-functions';

interface SalesItemSingleProps {
  week: Week;
  customer: string;
}

export function SalesItemSingle({ week, customer }: SalesItemSingleProps) {
  const prebookItems = useAppSelector(selectSingleProductItems),
    plants = useAppSelector(selectPlants),
    quantity = prebookItems
      .filter(
        (p) =>
          p.week === week.week &&
          p.year === week.year &&
          equals(p.boekestynCustomerAbbreviation, customer)
      )
      .reduce((total, p) => total + boekCaseQuantity(plants, p), 0);

  return <th className="border font-semibold">{formatNumber(quantity)}</th>;
}
