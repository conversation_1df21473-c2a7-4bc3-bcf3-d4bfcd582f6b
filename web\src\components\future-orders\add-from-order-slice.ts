import {
  AsyncThunk,
  createAction,
  createAsyncThunk,
  createSelector,
  createSlice,
  PayloadAction,
} from '@reduxjs/toolkit';
import { DateTime } from 'luxon';
import {
  FutureOrderDetailResponse,
  futureOrdersApi,
  futureOrdersListApi,
} from 'api/future-orders-service';
import * as models from 'api/models/future-orders';
import { RootState } from '@/services/store';
import { contains, equals, startsWith } from '@/utils/equals';
import { ProblemDetails } from '@/utils/problem-details';
import { sortBy } from '@/utils/sort';
import { formatNumber } from '@/utils/format';

export const blankShipToFilter = 'No Ship To';
export const blankCustomerFilter = 'No Customer';

export interface FutureOrderDetailSelectedItem
  extends models.FutureOrderDetailItem {
  orderNumber: string;
  customer: string;
  shipTo: string;
}

export const getDetail: AsyncThunk<
  FutureOrderDetailResponse,
  number,
  { state: RootState }
> = createAsyncThunk(
  'future-item-list-downloadList',
  async (id, { rejectWithValue }) => {
    try {
      return await futureOrdersApi.detail(id);
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

interface Filters {
  customer: string | null;
  shipTo: string | null;
}

interface AddFromOrderState {
  startDate: string;
  endDate: string;
  search: string;
  sort: keyof models.FutureOrderSummaryItem;
  sortDescending: boolean;
  filter: Filters;
  futureOrders: models.FutureOrderSummaryItem[];
  futureOrderDetail: models.FutureOrderDetail | null;
  items: models.FutureOrderDetailItem[];
  selectedItems: FutureOrderDetailSelectedItem[];
  isLoading: boolean;
  error: ProblemDetails | null;
}

const initialState: AddFromOrderState = {
  startDate: DateTime.now().toFormat('yyyy-MM-dd'),
  endDate: '',
  search: '',
  sort: 'date',
  sortDescending: false,
  filter: { customer: null, shipTo: null },
  futureOrders: [],
  futureOrderDetail: null,
  items: [],
  selectedItems: [],
  isLoading: false,
  error: null,
};

const getDetailFulfilled = createAction<FutureOrderDetailResponse>(
    getDetail.fulfilled.type
  ),
  getDetailPending = createAction(getDetail.pending.type),
  getDetailRejected = createAction<ProblemDetails>(getDetail.rejected.type);

export const addFromOrderSlice = createSlice({
  name: 'add-from-order',
  initialState,
  reducers: {
    clearState(state) {
      Object.assign(state, { ...initialState });
    },
    clearError(state) {
      state.error = null;
    },
    clearSelectedItems(state) {
      state.selectedItems = [];
    },
    addItem(state, { payload }: PayloadAction<FutureOrderDetailSelectedItem>) {
      state.selectedItems.push(payload);
    },
    removeItem(
      state,
      { payload }: PayloadAction<models.FutureOrderDetailItem>
    ) {
      state.selectedItems = state.selectedItems.filter(
        (i) => i.id !== payload.id
      );
    },
    addAllItems(state) {
      if (state.futureOrderDetail) {
        const orderNumber = formatNumber(state.futureOrderDetail.id, '00000'),
          customer = state.futureOrderDetail.customerName || '',
          shipTo = state.futureOrderDetail.shipToName || '';
        state.selectedItems = state.selectedItems.concat(
          state.items.map((i) => ({ ...i, orderNumber, customer, shipTo }))
        );
      }
    },
    removeAllItems(state) {
      state.selectedItems = [];
    },
    setStartDate(state, { payload }: PayloadAction<string>) {
      state.startDate = payload;
    },
    setEndDate(state, { payload }: PayloadAction<string>) {
      state.endDate = payload;
    },
    setSearch(state, { payload }: PayloadAction<string>) {
      state.search = payload;
    },
    setSort(
      state,
      {
        payload,
      }: PayloadAction<{
        sort: keyof models.FutureOrderSummaryItem;
        sortDescending: boolean;
      }>
    ) {
      state.sort = payload.sort;
      state.sortDescending = payload.sortDescending;
    },
    setCustomerFilter(state, { payload }: PayloadAction<string | null>) {
      const filter = { ...state.filter, customer: payload };
      state.filter = filter;
    },
    setShipToFilter(state, { payload }: PayloadAction<string | null>) {
      const filter = { ...state.filter, shipTo: payload };
      state.filter = filter;
    },
  },
  extraReducers: (builder) =>
    builder
      .addCase(getDetailPending, (state) => {
        state.isLoading = true;
        state.futureOrderDetail = null;
        state.items = [];
      })
      .addCase(getDetailFulfilled, (state, { payload }) => {
        state.isLoading = false;
        state.futureOrderDetail = payload.futureOrder;
        state.items = payload.futureOrder.items.map((i) => ({ ...i }));
      })
      .addCase(getDetailRejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      .addMatcher(futureOrdersListApi.endpoints.list.matchPending, (state) => {
        state.isLoading = true;
      })
      .addMatcher(
        futureOrdersListApi.endpoints.itemSummary.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.futureOrders = payload.items;
        }
      )
      .addMatcher(
        futureOrdersListApi.endpoints.list.matchRejected,
        (state, { payload }) => {
          state.isLoading = false;
          if (payload) {
            state.error = payload;
          }
        }
      ),
});

export const {
  clearState,
  clearError,
  clearSelectedItems,
  addAllItems,
  removeAllItems,
  addItem,
  removeItem,
  setStartDate,
  setEndDate,
  setSearch,
  setSort,
  setCustomerFilter,
  setShipToFilter,
} = addFromOrderSlice.actions;

export const selectError = (state: RootState) => state.addFromFutureOrder.error;
export const selectIsLoading = (state: RootState) =>
  state.addFromFutureOrder.isLoading;
export const selectStartDate = (state: RootState) =>
  state.addFromFutureOrder.startDate;
export const selectEndDate = (state: RootState) =>
  state.addFromFutureOrder.endDate;
export const selectSearch = (state: RootState) =>
  state.addFromFutureOrder.search;
export const selectSort = (state: RootState) => state.addFromFutureOrder.sort;
export const selectSortDescending = (state: RootState) =>
  state.addFromFutureOrder.sortDescending;
export const selectFilter = (state: RootState) =>
  state.addFromFutureOrder.filter;
export const selectFutureOrderDetail = (state: RootState) =>
  state.addFromFutureOrder.futureOrderDetail;
export const selectAllItems = (state: RootState) =>
  state.addFromFutureOrder.items;
export const selectSelectedItems = (state: RootState) =>
  state.addFromFutureOrder.selectedItems;

const selectAllFutureOrders = (state: RootState) =>
  state.addFromFutureOrder.futureOrders;

export const selectFutureOrderCustomers = createSelector(
  selectAllFutureOrders,
  (futureOrders) => {
    const customers = futureOrders
      .reduce(
        (memo, p) =>
          p.customer && memo.indexOf(p.customer) === -1
            ? memo.concat([p.customer])
            : memo,
        [] as string[]
      )
      .sort();

    if (futureOrders.some((o) => !o.customer)) {
      customers.unshift(blankCustomerFilter);
    }

    return customers;
  }
);
export const selectFutureOrderShipTos = createSelector(
  selectAllFutureOrders,
  selectFilter,
  (futureOrders, { customer }) => {
    const shipTos = futureOrders
      // if there's a customer filter, only show those ones
      .filter(
        (o) =>
          !customer ||
          equals(customer, o.customer) ||
          (customer === blankCustomerFilter && !o.customer)
      )
      .reduce(
        (memo, p) =>
          p.shipTo && memo.indexOf(p.shipTo) === -1
            ? memo.concat([p.shipTo])
            : memo,
        [] as string[]
      )
      .sort();

    if (futureOrders.some((o) => !o.shipTo)) {
      shipTos.unshift(blankShipToFilter);
    }

    return shipTos;
  }
);

export const selectFutureOrders = createSelector(
  selectAllFutureOrders,
  selectSearch,
  selectSort,
  selectSortDescending,
  selectFilter,
  (futureOrders, search, sortField, sortDescending, filter) => {
    const sort = sortBy(sortField, sortDescending ? 'descending' : ''),
      list = futureOrders
        .filter(
          (o) =>
            (!search ||
              contains(formatNumber(o.futureOrderId, '00000'), search) ||
              startsWith(o.customer, search) ||
              startsWith(o.shipTo, search) ||
              startsWith(o.salesperson, search) ||
              //startsWith(o.truck, search) ||
              contains(o.spirePartNumber, search) ||
              contains(o.customerItemCode, search)) &&
            (!filter.customer ||
              equals(filter.customer, o.customer) ||
              (filter.customer === blankCustomerFilter && !o.customer)) &&
            (!filter.shipTo ||
              equals(filter.shipTo, o.shipTo) ||
              (filter.shipTo === blankShipToFilter && !o.shipTo))
        )
        .reduce((memo, o) => {
          if (!memo.some((m) => m.futureOrderId === o.futureOrderId)) {
            memo.push(o);
          }
          return memo;
        }, [] as models.FutureOrderSummaryItem[])
        .sort(sort);

    return list;
  }
);

export const selectItems = createSelector(
  selectAllItems,
  selectAllFutureOrders,
  selectSearch,
  (items, futureOrders, search) =>
    items.filter(
      (i) =>
        !search ||
        futureOrders.some(
          (o) =>
            contains(formatNumber(o.futureOrderId, '00000'), search) ||
            startsWith(o.customer, search) ||
            startsWith(o.shipTo, search) ||
            startsWith(o.salesperson, search)
        ) ||
        contains(i.spirePartNumber, search) ||
        contains(i.customerItemCode, search)
    )
);

export default addFromOrderSlice.reducer;
