import Head from 'next/head';
import Link from 'next/link';
import { useGetFreightRatesQuery } from 'api/settings-service';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { routes } from '@/services/routes';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import { Loading } from '@/components/loading';
import {
  clearError,
  selectError,
  selectIsLoading,
  selectPriceLevels,
} from '@/components/settings/freight-rates/freight-rates-slice';
import { PriceLevel } from '@/components/settings/freight-rates/freight-rate';

export default function Seasons() {
  const dispatch = useAppDispatch(),
    isLoading = useAppSelector(selectIsLoading),
    error = useAppSelector(selectError),
    priceLevels = useAppSelector(selectPriceLevels);

  useGetFreightRatesQuery();

  const handleClearError = () => {
    dispatch(clearError());
  };

  return (
    <>
      <Head>
        <title>Settings: Default Freight Rates</title>
      </Head>
      <header className="sticky top-0 z-20 flex-wrap bg-white shadow">
        <div className="mx-auto flex max-w-7xl items-center justify-between px-8 py-6">
          <h2 className="flex-shrink-0 text-2xl font-bold leading-7 text-gray-900">
            <Icon icon="truck-fast" />
            &nbsp; Default Freight Rates
          </h2>
          <div className="flex">
            <Link
              href={routes.settings.home.to()}
              className="btn-secondary inline-flex"
            >
              Close
            </Link>
          </div>
        </div>
      </header>
      <main className="flex-grow overflow-auto">
        <div className="mx-auto h-full px-8">
          <Error error={error} clear={handleClearError} />
          {isLoading && <Loading />}
          <div className="mt-8 flex h-full flex-col">
            <div className="-mx-8 -my-2 h-full">
              <div className="inline-block min-w-full px-8 py-2 align-middle">
                <div className="rounded-lg shadow ring-1 ring-black ring-opacity-5">
                  <table className="min-w-full divide-y divide-gray-300">
                    <thead>
                      <tr className="sticky top-0">
                        <th className="w-1 whitespace-nowrap bg-gray-100 p-4 text-left align-top text-gray-900">
                          Spire Price Level
                        </th>
                        <th className="w-1 bg-gray-100 p-4 text-center align-top text-gray-900">
                          <div className="whitespace-nowrap">
                            Default Freight Rate
                          </div>
                          <div className="whitespace-nowrap">($ / Case)</div>
                        </th>
                        <th className="bg-gray-100 p-4">&nbsp;</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-300 bg-white">
                      {priceLevels.map((priceLevel) => (
                        <PriceLevel
                          key={priceLevel.id}
                          priceLevel={priceLevel}
                        />
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </>
  );
}
