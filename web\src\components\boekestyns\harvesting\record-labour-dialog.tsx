import { useState, Fragment, useEffect, useMemo } from 'react';
import * as HeadlessUI from '@headlessui/react';
import * as boeks from 'api/models/boekestyns';
import * as models from 'api/models/boekestyns';
import { Error } from '@/components/error';
import { classNames } from '@/utils/class-names';
import { ProblemDetails, createProblemDetails } from '@/utils/problem-details';
import {
  useLazySchedulesForOrderQuery,
  useHarvestingLabourQuery,
  HarvestingLabourVariety,
} from 'api/boekestyn-harvesting-service';
import { RecordLabourDialogItem } from './record-labour-dialog-item';

interface PauseLabourDialogProps {
  workOrder: boeks.HarvestingWorkOrderItem;
  open: boolean;
  onClose: () => void;
  onRecord: (args: {
    comments: string | null;
    crewSize: number;
    varieties: HarvestingLabourVariety[];
    harvestComplete: boolean;
    remainderThrownOut: boolean;
    remainderSentBack: boolean;
    remainderNumberTwos: boolean;
  }) => void;
  inProcess: boeks.HarvestingWorkOrderLabour | null;
}

export function RecordLabourDialog({
  workOrder,
  open,
  onClose,
  onRecord,
  inProcess,
}: PauseLabourDialogProps) {
  const [comments, setComments] = useState('');
  const [harvestComplete, setHarvestComplete] = useState(false);
  const [crewSize, setCrewSize] = useState('1');
  const [remainderThrownOut, setRemainderThrownOut] = useState(false);
  const [remainderSentBack, setRemainderSentBack] = useState(false);
  const [remainderNumberTwos, setRemainderNumberTwos] = useState(false);
  const [harvestingOrder, setHarvestingOrder] =
    useState<models.HarvestingAdminOrderItem | null>(null);
  const [labourVarieties, setLabourVarieties] = useState<
    HarvestingLabourVariety[]
  >([]);
  const [error, setError] = useState<ProblemDetails | null>(null);
  const showFinalActions = harvestComplete && workOrder.finalRound;
  const { data: harvestingLabour } = useHarvestingLabourQuery(
    workOrder.orderId,
    { skip: !workOrder.orderId }
  );
  const [query] = useLazySchedulesForOrderQuery();
  const history = useMemo(
    () => harvestingLabour?.labour ?? [],
    [harvestingLabour]
  );

  useEffect(() => {
    async function fetchHarvestingOrders() {
      setRemainderThrownOut(false);
      setRemainderSentBack(false);
      setRemainderNumberTwos(false);
      setError(null);

      if (workOrder) {

        if (inProcess) {
          setCrewSize(inProcess.crewSize.toString());
        } else {
          setCrewSize(workOrder.crewSize.toString());
        }

        const { data } = await query({
          orderId: workOrder.orderId,
        });
        const matchingOrder = data?.order;
        if (matchingOrder) {
          setHarvestingOrder(matchingOrder);
          setLabourVarieties(
            matchingOrder.varieties.map((v) => ({
              varietyName: v.name,
              harvested: 0,
              thrownOut: 0,
              numberTwos: 0,
            }))
          );
        }
      }
    }

    fetchHarvestingOrders();
  }, [query, workOrder, inProcess]);

  const handleClearError = () => {
    setError(null);
  };

  const handleTransitionAfterEnter = () => {
    setComments('');
  };

  const handleCrewSizeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCrewSize(e.target.value);
  };

  const handlePauseChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setComments(e.target.value);
  };

  const handleHarvestCompleteChange = (value: boolean) => {
    setHarvestComplete(value);
    if (!value) {
      setRemainderThrownOut(false);
      setRemainderSentBack(false);
      setRemainderNumberTwos(false);
    }
  };

  const handleRemainderThrownOutChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setRemainderThrownOut(e.target.checked);
    if (e.target.checked) {
      setRemainderSentBack(false);
      setRemainderNumberTwos(false);
    }
  };

  const handleRemainderSentBackChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setRemainderSentBack(e.target.checked);
    if (e.target.checked) {
      setRemainderThrownOut(false);
      setRemainderNumberTwos(false);
    }
  };

  const handleRemainderNumberTwosChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setRemainderNumberTwos(e.target.checked);
    if (e.target.checked) {
      setRemainderThrownOut(false);
      setRemainderSentBack(false);
    }
  };

  const handleSaveClick = () => {
    if (
      showFinalActions &&
      !remainderThrownOut &&
      !remainderSentBack &&
      !remainderNumberTwos
    ) {
      setError(
        createProblemDetails(
          'Final Actions Required',
          'Please select how the remaining product was disposed.'
        )
      );
      return;
    }

    const crewSizeValue = isNaN(parseInt(crewSize, 10))
        ? 1
        : parseInt(crewSize, 10),
      varieties = labourVarieties
        .map((v) => ({ ...v }))
        .filter((v) => v.harvested > 0 || v.thrownOut > 0);

    onRecord({
      comments,
      crewSize: crewSizeValue,
      varieties,
      harvestComplete,
      remainderThrownOut,
      remainderSentBack,
      remainderNumberTwos,
    });
    onClose();
  };

  const handleVarietyChange = (
    varietyName: string,
    property: 'harvested' | 'thrownOut' | 'numberTwos',
    value: number
  ) => {
    const varieties = labourVarieties.map((v) => ({ ...v })),
      variety = varieties.find((v) => v.varietyName === varietyName);

    if (variety) {
      variety[property] = value;
    }
    setLabourVarieties(varieties);
  };

  return (
    <HeadlessUI.Transition.Root
      show={open}
      as={Fragment}
      afterEnter={handleTransitionAfterEnter}
    >
      <HeadlessUI.Dialog as="div" className="relative z-10" onClose={onClose}>
        <HeadlessUI.Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </HeadlessUI.Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-0 text-center">
            <HeadlessUI.Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-0 scale-95"
              enterTo="opacity-100 translate-y-0 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 scale-100"
              leaveTo="opacity-0 translate-y-0 scale-95"
            >
              <HeadlessUI.Dialog.Panel className="relative my-8 w-full max-w-4xl transform overflow-hidden rounded-lg bg-white p-6 text-left shadow-xl transition-all">
                <div>
                  <div className="mt-3 text-center">
                    <HeadlessUI.Dialog.Title
                      as="h3"
                      className="text-base font-semibold leading-6 text-gray-900"
                    >
                      Record Harvest
                    </HeadlessUI.Dialog.Title>
                    <form className="mt-5">
                      <div className="mt-5">
                        <div className="text-left">
                          <label>Crew Size</label>
                          <input
                            type="number"
                            name="comments"
                            className="block w-40 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                            value={crewSize}
                            onChange={handleCrewSizeChange}
                          />
                        </div>
                        <table className="min-w-full divide-y divide-gray-300 text-sm">
                          <thead>
                            <tr>
                              <th className="w-1 p-2 align-top">Variety</th>
                              <th className="w-1 p-2 text-right align-top">
                                Planted
                              </th>
                              <th className="w-1 p-2 text-right align-top">
                                Previous Harvest
                              </th>
                              <th className="w-1 p-2 text-right align-top">
                                Remaining
                              </th>
                              <th className="w-1 whitespace-nowrap p-2 text-center align-top">
                                Harvested
                              </th>
                              <th className="w-1 whitespace-nowrap p-2 text-center align-top">
                                Thrown Out
                              </th>
                              <th>
                                #2's
                              </th>
                              <th>&nbsp;</th>
                            </tr>
                          </thead>
                          <tbody>
                            {workOrder.varieties.map((variety) => (
                              <RecordLabourDialogItem
                                key={variety.name}
                                harvestingOrder={harvestingOrder}
                                variety={variety}
                                history={history}
                                onChange={handleVarietyChange}
                              />
                            ))}
                          </tbody>
                        </table>
                      </div>

                      <div className="text-left">
                        <label>Comments</label>
                        <textarea
                          name="comments"
                          className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                          value={comments}
                          onChange={handlePauseChange}
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-500">
                          Harvest Complete?
                        </label>
                        <div>
                          <HeadlessUI.Switch
                            checked={harvestComplete}
                            onChange={handleHarvestCompleteChange}
                            className={classNames(
                              harvestComplete ? 'bg-blue-400' : 'bg-gray-200',
                              'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
                            )}
                          >
                            <span
                              aria-hidden="true"
                              className={classNames(
                                harvestComplete
                                  ? 'translate-x-5'
                                  : 'translate-x-0',
                                'pointer-events-none inline-block size-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                              )}
                            />
                          </HeadlessUI.Switch>
                        </div>
                      </div>
                      {showFinalActions && (
                        <div className="grid grid-cols-3">
                          <div className="col-span-3 font-semibold">
                            Disposal of Remaining Product
                          </div>
                          <fieldset className="h-full -space-y-px rounded-md bg-white">
                            <label className="group flex cursor-pointer border border-gray-200 p-4 first:rounded-tl-md first:rounded-tr-md last:rounded-bl-md last:rounded-br-md focus:outline-none has-[:checked]:relative has-[:checked]:border-blue-200 has-[:checked]:bg-blue-50">
                              <input
                                value="Thrown Out"
                                name="final-action"
                                type="radio"
                                onChange={handleRemainderThrownOutChange}
                                className="relative mt-0.5 size-4 shrink-0 appearance-none rounded-full border border-gray-300 bg-white before:absolute before:inset-1 before:rounded-full before:bg-white checked:border-blue-600 checked:bg-blue-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:before:bg-gray-400 forced-colors:appearance-auto forced-colors:before:hidden [&:not(:checked)]:before:hidden"
                              />
                              <span className="ml-3 flex flex-col">
                                <span className="block text-left text-sm font-medium text-gray-900 group-has-[:checked]:text-blue-900">
                                  Thrown Out
                                </span>
                                <span className="block text-xs text-gray-500 group-has-[:checked]:text-blue-700">
                                  Product was thrown out.
                                </span>
                              </span>
                            </label>
                          </fieldset>
                          <fieldset className="h-full -space-y-px rounded-md bg-white">
                            <label className="group flex cursor-pointer border border-gray-200 p-4 first:rounded-tl-md first:rounded-tr-md last:rounded-bl-md last:rounded-br-md focus:outline-none has-[:checked]:relative has-[:checked]:border-blue-200 has-[:checked]:bg-blue-50">
                              <input
                                value="Sent Back"
                                name="final-action"
                                type="radio"
                                onChange={handleRemainderSentBackChange}
                                className="relative mt-0.5 size-4 shrink-0 appearance-none rounded-full border border-gray-300 bg-white before:absolute before:inset-1 before:rounded-full before:bg-white checked:border-blue-600 checked:bg-blue-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:before:bg-gray-400 forced-colors:appearance-auto forced-colors:before:hidden [&:not(:checked)]:before:hidden"
                              />
                              <span className="ml-3 flex flex-col">
                                <span className="block text-left text-sm font-medium text-gray-900 group-has-[:checked]:text-blue-900">
                                  Sent Back
                                </span>
                                <span className="block text-xs text-gray-500 group-has-[:checked]:text-blue-700">
                                  Sent back to the Greenhouse.
                                </span>
                              </span>
                            </label>
                          </fieldset>
                          <fieldset className="h-full -space-y-px rounded-md bg-white">
                            <label className="group flex cursor-pointer border border-gray-200 p-4 first:rounded-tl-md first:rounded-tr-md last:rounded-bl-md last:rounded-br-md focus:outline-none has-[:checked]:relative has-[:checked]:border-blue-200 has-[:checked]:bg-blue-50">
                              <input
                                value="Number Two"
                                name="final-action"
                                type="radio"
                                onChange={handleRemainderNumberTwosChange}
                                className="relative mt-0.5 size-4 shrink-0 appearance-none rounded-full border border-gray-300 bg-white before:absolute before:inset-1 before:rounded-full before:bg-white checked:border-blue-600 checked:bg-blue-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:before:bg-gray-400 forced-colors:appearance-auto forced-colors:before:hidden [&:not(:checked)]:before:hidden"
                              />
                              <span className="ml-3 flex flex-col">
                                <span className="block text-left text-sm font-medium text-gray-900 group-has-[:checked]:text-blue-900">
                                  # 2&apos;s
                                </span>
                                <span className="block text-xs text-gray-500 group-has-[:checked]:text-blue-700">
                                  Harvested as # 2&apos;s.
                                </span>
                              </span>
                            </label>
                          </fieldset>
                        </div>
                      )}
                    </form>
                  </div>
                  <Error error={error} clear={handleClearError} />
                </div>
                <div className="mt-6 text-right">
                  <button
                    type="button"
                    className="btn-secondary"
                    onClick={onClose}
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn-primary ml-2"
                    onClick={handleSaveClick}
                  >
                    Record Harvest
                  </button>
                </div>
              </HeadlessUI.Dialog.Panel>
            </HeadlessUI.Transition.Child>
          </div>
        </div>
      </HeadlessUI.Dialog>
    </HeadlessUI.Transition.Root>
  );
}
