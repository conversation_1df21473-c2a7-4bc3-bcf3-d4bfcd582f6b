export interface CobornsLabelItem {
  rowNumber: number;
  upc: string;
  description: string;
  productNumber: string;
  size: string;
  retail: string;
  packQuantity: number | null;
}

export interface CobornsLabelStoreItem extends CobornsLabelItem {
  quantity: number;
}

export interface CobornsLabelStore {
  name: string;
  storeNumber: number | null;
  orders: CobornsLabelStoreItem[];
}

export interface AlbrechtsLabelItem {
  rowNumber: number;
  upc: string;
  description: string;
  productNumber: string;
  cost: string;
  retail: string;
  packQuantity: number | null;
}

export interface AlbrechtsLabelStoreItem extends AlbrechtsLabelItem {
  quantity: number;
}

export interface AlbrechtsLabelStore {
  name: string;
  storeNumber: number | null;
  orders: AlbrechtsLabelStoreItem[];
}

export interface HeinensLabelItem {
  rowNumber: number;
  upc: string;
  description: string;
  productNumber: string;
  packQuantity: number | null;
  type: HeinensLabelType;
}

export interface HeinensLabelStoreItem extends HeinensLabelItem {
  quantity: number;
  skidQuantity: number | null;
}

export interface HeinensLabelStore {
  storeNumber: string;
  destination: HeinensLabelDestination;
  orders: HeinensLabelStoreItem[];
}

export type HeinensLabelType = 'Potted' | 'Cuts';

type HeinensLabelDestination = 'Chicago' | 'Cleveland';

export function heinensLabelType(description: string): HeinensLabelType {
  const cutsPrefixes = ['CB', 'CF', 'GB', 'BQT', 'ARRG'],
    isCuts = cutsPrefixes.some((p) => description.indexOf(p) === 0);

  return isCuts ? 'Cuts' : 'Potted';
}

export function heinensLabelDestination(
  storeNumber: string
): HeinensLabelDestination {
  const chicagoPrefixes = ['#4'],
    isChicago = chicagoPrefixes.some((p) => storeNumber.indexOf(p) === 0);

  return isChicago ? 'Chicago' : 'Cleveland';
}
