import { useAppSelector } from '@/services/hooks';
import { classNames } from '@/utils/class-names';
import { equals } from '@/utils/equals';
import { formatNumber } from '@/utils/format';
import { Week, weekFromDate } from '@/utils/weeks';
import {
  selectProductionOrders,
  selectPrebookItems,
  selectPlants,
} from './sales-slice';
import { boekCaseQuantity } from './boekestyn-sales-functions';

interface AvailableItemProps {
  week: Week;
  customer: string;
}

export function AvailableItem({ week, customer }: AvailableItemProps) {
  const productionOrders = useAppSelector(selectProductionOrders),
    prebookItems = useAppSelector(selectPrebookItems),
    plants = useAppSelector(selectPlants),
    salesQuantity = prebookItems
      .filter(
        (p) =>
          p.week === week.week &&
          p.year === week.year &&
          equals(p.boekestynCustomerAbbreviation, customer)
      )
      .reduce((total, p) => total + boekCaseQuantity(plants, p), 0),
    productionQuantity = productionOrders
      .filter(
        (p) =>
          weekFromDate(p.flowerDate)?.weekId === week.weekId &&
          equals(p.customer?.abbreviation, customer)
      )
      .reduce((total, p) => total + p.cases, 0),
    quantity = productionQuantity - salesQuantity,
    isNegative = quantity < 0;

  return (
    <th
      className={classNames(
        'border font-semibold',
        isNegative && 'text-red-600'
      )}
    >
      {formatNumber(quantity)}
    </th>
  );
}
