import { useMemo } from 'react';
import { formatNumber } from '@/utils/format';
import { ItemRequirementsFooterCell } from './item-requirements-footer-cell';

interface ItemRequirementsFooterDateTotalsProps {
  dates: string[];
  items: string[];
  itemDateMap: Map<string, number>;
}

export function ItemRequirementsFooter({
  dates,
  items,
  itemDateMap,
}: ItemRequirementsFooterDateTotalsProps) {
  const colTotals = useMemo(
    () =>
      items.reduce((acc, item) => {
        const rowTotal = dates.reduce((memo, date) => {
          const key = `${item}_${date}`,
            rowTotal = itemDateMap?.get(key) || 0;
          return memo + rowTotal;
        }, 0);
        return acc + rowTotal;
      }, 0),
    [dates, items, itemDateMap]
  );

  return (
    <tfoot className="sticky bottom-0 bg-white">
      <tr>
        <th className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">
          Total
        </th>
        {dates.map((date) => (
          <ItemRequirementsFooterCell
            key={date}
            date={date}
            items={items}
            itemDateMap={itemDateMap}
          />
        ))}
        <th className="px-3 py-3.5 text-center text-sm font-semibold text-gray-900">
          {formatNumber(colTotals)}
        </th>
      </tr>
    </tfoot>
  );
}
