import { useState, useRef } from 'react';
import { useDrag } from 'react-dnd';
import { useLazySchedulesForOrderQuery } from 'api/boekestyn-harvesting-service';
import * as models from 'api/models/boekestyns';
import { useAppDispatch } from '@/services/hooks';
import { classNames } from '@/utils/class-names';
import { formatDate, formatNumber } from '@/utils/format';
import { Icon } from '@/components/icon';
import { ScheduleHarvestingOrderType } from './harvesting-slice';
import { setSelectedOrder } from './harvesting-work-orders-slice';

interface HarvestingOrderProps {
  order: models.HarvestingAdminOrderItem;
}

const cellClassName = 'whitespace-nowrap px-2 py-1 text-gray-700 align-top';

export function HarvestingOrderRow({ order }: HarvestingOrderProps) {
  const dispatch = useAppDispatch(),
    [expanded, setExpanded] = useState(false),
    [schedulesForOrder] = useLazySchedulesForOrderQuery(),
    wasScheduled = !!order.rounds.length,
    ref = useRef<HTMLTableRowElement>(null),
    [, drag] = useDrag(() => ({
      type: ScheduleHarvestingOrderType,
      item: order,
    }));

  const handleExpand = () => {
    setExpanded(!expanded);
  };

  const handleLotNumberClick = async () => {
    const response = await schedulesForOrder({ orderId: order.orderId });
    if (response.data?.order) {
      dispatch(setSelectedOrder(response.data.order));
    }
  };

  if (!wasScheduled) {
    drag(ref);
  }

  return (
    <tr
      ref={ref}
      className={classNames(
        wasScheduled ? 'cursor-default' : 'cursor-pointer hover:bg-gray-100'
      )}
      style={{
        backgroundColor: wasScheduled ? '' : order.colour ?? '',
      }}
    >
      <td className={classNames(cellClassName, 'text-center')}>
        <span className="font-semibold">{formatDate(order.flowerDate)}</span>
        {order.rounds.map((round) => (
          <div key={round.roundNumber} className="ml-4 italic">
            {round.roundNumber}: {formatDate(round.date)}
          </div>
        ))}
      </td>
      <td className={cellClassName}>
        <div className="flex flex-row">
          <div className="flex-grow">
            {wasScheduled ? (
              <button
                type="button"
                className="btn-link"
                onClick={handleLotNumberClick}
              >
                {order.orderNumber}
              </button>
            ) : (
              <span className="font-semibold">{order.orderNumber}</span>
            )}
          </div>
          <div
            className={classNames(order.varieties.length ? '' : 'invisible')}
          >
            <button
              type="button"
              className="btn-secondary ml-2 px-2 py-1"
              onClick={handleExpand}
            >
              <Icon icon={expanded ? 'minus' : 'plus'} />
            </button>
          </div>
        </div>
      </td>
      <td className={cellClassName}>
        <span className="font-semibold">
          {order.plantSize}&nbsp;{order.plantCrop}
        </span>
        {expanded && (
          <div className="pl-4 italic">
            {order.varieties.map((variety) => (
              <div key={variety.name}>{variety.name}</div>
            ))}
          </div>
        )}
      </td>
      <td className={cellClassName}>
        <span className="font-semibold">{order.customer}</span>
      </td>
      <td className={classNames(cellClassName, 'text-right')}>
        <span className="font-semibold">{formatNumber(order.pots)}</span>
        {expanded && (
          <div className="italic">
            {order.varieties.map((variety) => (
              <div key={variety.name}>{formatNumber(variety.pots)}</div>
            ))}
          </div>
        )}
      </td>
      <td className={classNames(cellClassName, 'text-right')}>
        <span className="font-semibold">
          {formatNumber(potsHarvested(order, null))}
        </span>
        {expanded && (
          <div className="italic">
            {order.varieties.map((variety) => (
              <div key={variety.name}>
                {formatNumber(potsHarvested(order, variety.name))}
              </div>
            ))}
          </div>
        )}
      </td>
      <td className={classNames(cellClassName, 'text-right')}>
        <span className="font-semibold">
          {formatNumber(potsThrownOut(order, null))}
        </span>
        {expanded && (
          <div className="italic">
            {order.varieties.map((variety) => (
              <div key={variety.name}>
                {formatNumber(potsThrownOut(order, variety.name))}
              </div>
            ))}
          </div>
        )}
      </td>
      <td className={classNames(cellClassName, 'text-right')}>
        <span className="font-semibold">
          {formatNumber(potsRemaining(order, null))}
        </span>
        {expanded && (
          <div className="italic">
            {order.varieties.map((variety) => (
              <div key={variety.name}>
                {formatNumber(potsRemaining(order, variety.name))}
              </div>
            ))}
          </div>
        )}
      </td>
    </tr>
  );
}

function potsHarvested(
  order: models.HarvestingAdminOrderItem,
  variety: string | null
) {
  return order.rounds.reduce((total, round) => {
    return (
      total +
      round.varieties
        .filter((v) => !variety || variety === v.name)
        .reduce((sum, v) => v.harvested + sum, 0)
    );
  }, 0);
}

function potsRemaining(
  order: models.HarvestingAdminOrderItem,
  variety: string | null
) {
  return (
    ((variety ? order.varieties.find((v) => v.name === variety) : order)
      ?.pots || 0) -
    potsHarvested(order, variety) -
    potsThrownOut(order, variety)
  );
}

function potsThrownOut(
  order: models.HarvestingAdminOrderItem,
  variety: string | null
) {
  return order.rounds.reduce((total, round) => {
    return (
      total +
      round.varieties
        .filter((v) => !variety || variety === v.name)
        .reduce((sum, v) => v.thrownOut + sum, 0)
    );
  }, 0);
}
