import { Fragment, useEffect, useState } from 'react';
import * as HeadlessUI from '@headlessui/react';
import { apiUrl } from 'api/api-base';
import * as spire from 'api/models/spire';
import { useAppSelector } from '@/services/hooks';
import { useEmailTemplatesQuery } from 'api/spire-service';
import { selectFutureOrder } from '@/components/future-orders/future-order-detail-slice';
import { Icon } from '@/components/icon';
import { Error } from '@/components/error';
import { classNames } from '@/utils/class-names';
import { formatNumber, formatDate } from '@/utils/format';
import { Tiptap } from '../tip-tap';
import { FutureOrderDetail } from 'api/models/future-orders';
import { futureOrdersApi } from 'api/future-orders-service';

interface CustomerConfirmationEmailProps {
  open: boolean;
  close: () => void;
}

export function CustomerConfirmationEmail({
  open,
  close,
}: CustomerConfirmationEmailProps) {
  const [to, setTo] = useState(''),
    [cc, setCc] = useState<string | null>(null),
    [bcc, setBcc] = useState<string | null>(null),
    [subject, setSubject] = useState('Order Confirmation'),
    [body, setBody] = useState(''),
    [includeZeroQuantity, setIncludeZeroQuantity] = useState(true),
    [error, setError] = useState<string | null>(null),
    futureOrder = useAppSelector(selectFutureOrder),
    { data: templates, isLoading } = useEmailTemplatesQuery();

  useEffect(() => {
    if (templates?.length) {
      const template = templates[0],
        subject = replaceTokens(template.subject, futureOrder),
        body = replaceTokens(template.body, futureOrder).replaceAll(
          '\n',
          '<br />'
        );
      setSubject(subject);
      setBody(body);
    }
  }, [templates, futureOrder]);

  const handleDialogAfterEnter = () => {
    setTo('');
    setCc(null);
    setBcc(null);
    setSubject('Order Confirmation');
    setBody('');
    setError(null);

    if (templates?.length) {
      const template = templates[0],
        subject = replaceTokens(template.subject, futureOrder),
        body = replaceTokens(template.body, futureOrder).replaceAll(
          '\n',
          '<br />'
        );
      setSubject(subject);
      setBody(body);
    }
  };

  const handleToChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTo(e.target.value);
  };

  const handleCcChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCc(e.target.value || null);
  };

  const handleBccChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setBcc(e.target.value || null);
  };

  const handleSubjectChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSubject(e.target.value);
  };

  const handleBodyChange = (value: string) => {
    setBody(value);
  };

  const handleIncludeZeroQuantityChange = (value: boolean) => {
    setIncludeZeroQuantity(value);
  };

  const handleTemplateChange = (template: spire.EmailTemplate) => {
    if (template) {
      const subject = replaceTokens(template.subject, futureOrder),
        body = replaceTokens(template.body, futureOrder).replaceAll(
          '\n',
          '<br />'
        );
      setSubject(subject);
      setBody(body);
    }
  };

  const handleSendClick = async () => {
    if (!futureOrder) {
      return;
    }

    if (!to) {
      setError('Please enter the To Address.');
      return;
    } else if (!subject) {
      setError('Please enter the Subject.');
      return;
    } else {
      await futureOrdersApi.createEmail(
        futureOrder.id,
        to,
        cc,
        bcc,
        subject,
        body,
        includeZeroQuantity
      );
      close();
    }
  };

  const handleClearErrorClick = () => {
    setError(null);
  };

  if (!futureOrder) {
    return null;
  }

  return (
    <HeadlessUI.Transition.Root
      as={Fragment}
      show={open}
      afterEnter={handleDialogAfterEnter}
    >
      <HeadlessUI.Dialog as="div" onClose={close} className="relative z-30">
        <HeadlessUI.Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </HeadlessUI.Transition.Child>

        <div className="fixed inset-0 z-30 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <HeadlessUI.Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <HeadlessUI.Dialog.Panel className="relative h-screen w-screen transform overflow-hidden p-2 transition-all">
                <div className="flex h-full flex-col rounded-lg bg-white p-2 text-left shadow-xl">
                  <div className="relative flex justify-center border-b-2 pb-4">
                    <HeadlessUI.Dialog.Title as="div" className="flex">
                      <h3 className="flex-grow text-lg font-medium leading-6 text-gray-900">
                        <Icon
                          icon="paper-plane"
                          className="h-6 w-6"
                          aria-hidden="true"
                        />
                        &nbsp; Send Customer Confirmation{' '}
                        {futureOrder?.id
                          ? ` for Future Order ${formatNumber(
                              futureOrder.id,
                              '00000'
                            )}`
                          : ''}
                      </h3>
                      <button
                        className="btn-secondary absolute right-0 px-2 py-1"
                        onClick={close}
                      >
                        <Icon icon="x" />
                      </button>
                    </HeadlessUI.Dialog.Title>
                  </div>
                  <div className="flex flex-grow flex-col">
                    <div className="mt-4 flex flex-grow rounded border p-2">
                      <div className="flex h-full flex-grow flex-col bg-white text-left">
                        <Error
                          error={error}
                          clear={handleClearErrorClick}
                          containerClasses="w-full mb-4"
                        />
                        <div className="flex flex-grow flex-col">
                          <form className="grid w-full grid-cols-2 gap-4">
                            <div>
                              <div className="flex flex-row">
                                <label
                                  htmlFor="to"
                                  className="mr-4 mt-px block w-32 pt-2 text-right text-sm font-medium text-gray-700"
                                >
                                  To
                                </label>
                                <input
                                  type="text"
                                  name="to"
                                  id="to"
                                  tabIndex={0}
                                  autoComplete="off"
                                  value={to}
                                  onChange={handleToChange}
                                  className="block w-full max-w-lg flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                />
                              </div>
                              <div className="mt-2 flex flex-row">
                                <label
                                  htmlFor="cc"
                                  className="mr-4 mt-px block w-32 pt-2 text-right text-sm font-medium text-gray-700"
                                >
                                  CC
                                </label>
                                <input
                                  type="text"
                                  name="cc"
                                  id="cc"
                                  tabIndex={1}
                                  value={cc || ''}
                                  onChange={handleCcChange}
                                  className="block w-full max-w-lg flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                />
                              </div>
                              <div className="mt-2 flex flex-row">
                                <label
                                  htmlFor="bcc"
                                  className="mr-4 mt-px block w-32 pt-2 text-right text-sm font-medium text-gray-700"
                                >
                                  BCC
                                </label>
                                <input
                                  type="text"
                                  name="bcc"
                                  id="bcc"
                                  tabIndex={1}
                                  value={bcc || ''}
                                  onChange={handleBccChange}
                                  className="block w-full max-w-lg flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                />
                              </div>
                              <p className="ml-32 mt-2 pl-4 text-sm text-gray-500">
                                Separate multiple email addresses with
                                semicolons (;)
                              </p>
                              <div className="mt-2 flex flex-row">
                                <label
                                  htmlFor="subject"
                                  className="mr-4 mt-px block w-32 pt-2 text-right text-sm font-medium text-gray-700"
                                >
                                  Subject
                                </label>
                                <input
                                  type="text"
                                  name="subject"
                                  id="subject"
                                  tabIndex={2}
                                  autoComplete="off"
                                  value={subject}
                                  onChange={handleSubjectChange}
                                  className="block w-full max-w-lg flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                />
                                {!!templates?.length && (
                                  <div className="relative">
                                    <div className="absolute inset-y-0 right-0 flex">
                                      <HeadlessUI.Menu
                                        as="div"
                                        className="relative -ml-px block h-full"
                                      >
                                        <HeadlessUI.Menu.Button className="relative inline-flex h-full items-center rounded-md px-2 py-2 text-xs font-medium text-gray-500 focus:z-10 focus:outline-none focus:ring-0">
                                          <span>Templates</span>
                                          &nbsp;
                                          <Icon
                                            icon="chevron-down"
                                            className="h-5 w-5"
                                            aria-hidden="true"
                                          />
                                        </HeadlessUI.Menu.Button>
                                        <HeadlessUI.Transition
                                          as={Fragment}
                                          enter="transition ease-out duration-100"
                                          enterFrom="transform opacity-0 scale-95"
                                          enterTo="transform opacity-100 scale-100"
                                          leave="transition ease-in duration-75"
                                          leaveFrom="transform opacity-100 scale-100"
                                          leaveTo="transform opacity-0 scale-95"
                                        >
                                          <HeadlessUI.Menu.Items className="absolute right-0 z-10 -mr-1 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                                            <div className="py-1">
                                              {templates.map((template) => (
                                                <HeadlessUI.Menu.Item
                                                  key={template.id}
                                                >
                                                  {({ active }) => (
                                                    <button
                                                      type="button"
                                                      onClick={() =>
                                                        handleTemplateChange(
                                                          template
                                                        )
                                                      }
                                                      className={classNames(
                                                        active
                                                          ? 'bg-gray-100 text-gray-900'
                                                          : 'text-gray-700',
                                                        'block w-full px-4 py-2 text-left text-sm'
                                                      )}
                                                    >
                                                      {template.name}
                                                    </button>
                                                  )}
                                                </HeadlessUI.Menu.Item>
                                              ))}
                                            </div>
                                          </HeadlessUI.Menu.Items>
                                        </HeadlessUI.Transition>
                                      </HeadlessUI.Menu>
                                    </div>
                                  </div>
                                )}
                              </div>
                              <HeadlessUI.Switch.Group
                                as="div"
                                className="ml-36 mt-2"
                              >
                                <HeadlessUI.Switch.Label className="mr-2">
                                  Include Zero Quantity Items
                                </HeadlessUI.Switch.Label>
                                <HeadlessUI.Switch
                                  checked={includeZeroQuantity}
                                  onChange={handleIncludeZeroQuantityChange}
                                  className={classNames(
                                    includeZeroQuantity
                                      ? 'bg-blue-400 outline-none ring-2 ring-blue-500 ring-offset-2'
                                      : 'bg-gray-200',
                                    'relative inline-flex h-4 w-8 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out'
                                  )}
                                >
                                  <span
                                    aria-hidden="true"
                                    className={classNames(
                                      includeZeroQuantity
                                        ? 'translate-x-4'
                                        : 'translate-x-0',
                                      'pointer-events-none inline-block h-3 w-3 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                                    )}
                                  />
                                </HeadlessUI.Switch>
                              </HeadlessUI.Switch.Group>
                            </div>
                            <div className="flex flex-col">
                              <div className="flex flex-grow flex-col">
                                <label
                                  htmlFor="body"
                                  className="mt-px block text-sm font-medium text-gray-700"
                                >
                                  Body
                                </label>
                                <Tiptap
                                  content={body}
                                  onChange={handleBodyChange}
                                />
                              </div>
                            </div>
                          </form>
                        </div>
                        <div className="mt-4 grid h-full grid-cols-6 gap-4 text-right">
                          <div>
                            <a
                              href={apiUrl(
                                `reports/future-orders/${futureOrder.id}/customer-confirmation?download`
                              )}
                              className="btn-secondary inline-block"
                              tabIndex={-1}
                            >
                              <Icon icon="download"></Icon>
                              &nbsp; Download PDF
                            </a>
                          </div>
                          <iframe
                            title="Customer Confirmation preview"
                            src={apiUrl(
                              `reports/future-orders/${futureOrder.id}/customer-confirmation?includeZeroQuantity=${includeZeroQuantity}`
                            )}
                            className="col-span-5 col-start-2 h-full w-full"
                          ></iframe>
                        </div>
                        <div className="mt-2 flex justify-end border-t-2 pt-2">
                          <button
                            type="button"
                            className="btn-secondary ml-4 text-lg"
                            onClick={() => handleSendClick()}
                            disabled={isLoading}
                          >
                            Send
                            <Icon
                              icon={isLoading ? 'spinner' : 'send'}
                              className="ml-2"
                              spin={isLoading}
                            />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </HeadlessUI.Dialog.Panel>
            </HeadlessUI.Transition.Child>
          </div>
        </div>
      </HeadlessUI.Dialog>
    </HeadlessUI.Transition.Root>
  );
}

function replaceTokens(
  value: string,
  futureOrder: FutureOrderDetail | null
  //account: AccountInfo | null
) {
  if (!futureOrder) {
    return value;
  }

  const boxCode = futureOrder.boxCode || '',
    dateCode = futureOrder.items.filter((i) => i.dateCode)[0]?.dateCode || '',
    userName = ''; // account?.name || '';

  return value
    .replaceAll(
      '{RequiredDate}',
      formatDate(futureOrder.requiredDate, 'MMM d, yyyy') ||
        futureOrder.seasonName ||
        ''
    )
    .replaceAll('{BoxCode}', boxCode)
    .replaceAll('{DateCode}', dateCode)
    .replaceAll('{UserName}', userName)
    .replaceAll(
      '{{ order.customerPO }}',
      futureOrder.customerPurchaseOrderNumber || ''
    )
    .replaceAll(
      '{{ order.shipDate }}',
      formatDate(futureOrder.arrivalDate, 'MMM d, yyyy') || ''
    );
}
