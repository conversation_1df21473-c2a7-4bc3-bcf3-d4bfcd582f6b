import React, { Fragment, useState } from 'react';
import { useAppSelector } from '@/services/hooks';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { Popover, Transition } from '@headlessui/react';
import {
  selectFilter,
  setSelectedItems,
  unsetSelectedItems,
  selectSelectedItems,
} from './prebook-list-slice';
import { setGrowerConfirmed } from './prebook-detail-slice';
import * as models from 'api/models/prebooks';
import { usePermissions } from '@/services/auth';
import { useAppDispatch } from '@/services/hooks';
import { routes } from '@/services/routes';
import { Alert } from '@/components/alert';
import { Icon } from '@/components/icon';
import { formatNumber, formatDate } from '@/utils/format';
import { classNames } from '@/utils/class-names';

export interface ListItemProps {
  prebook: models.PrebookListItem;
  refresh: () => void;
}

export function ListItem({ prebook, refresh }: ListItemProps) {
  const dispatch = useAppDispatch(),
    router = useRouter(),
    { can } = usePermissions(),
    [tooltipOpen, setTooltipOpen] = useState(false),
    [showFutureOrderWarning, setShowFutureOrderWarning] = useState(false),
    { includeSpirePurchaseOrders, vendor } = useAppSelector(selectFilter),
    isSelected = useAppSelector(selectSelectedItems).some(
      (i) => i === prebook.id
    ),
    readonly = !can('Sales Team') || prebook.isDeleted;

  const handleTooltipMouseEnter = () => {
    setTooltipOpen(true);
  };

  const handleTooltipMouseLeave = () => {
    setTooltipOpen(false);
  };

  const handleSetGrowerConfirmedClick = async () => {
    await dispatch(setGrowerConfirmed(prebook.id));
    refresh();
  };

  const handleSelectItemChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.checked) {
      dispatch(setSelectedItems(prebook.id));
    } else {
      dispatch(unsetSelectedItems(prebook.id));
    }
  };

  const handleEditClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    if (!readonly && prebook.futureOrderId) {
      e.preventDefault();
      setShowFutureOrderWarning(true);
    }
  };

  const handleEditPrebook = () => {
    router.push(routes.prebooks.detail.to(prebook.id));
  };

  const handleEditFutureOrder = () => {
    if (prebook.futureOrderId) {
      router.push(routes.futureOrders.detail.to(prebook.futureOrderId));
    }
  };

  return (
    <tr
      className={classNames(
        'border-b-2 border-gray-100',
        prebook.isDeleted ? 'bg-gray-50' : prebook.isBlanket && 'bg-blue-100'
      )}
    >
      <td className="whitespace-nowrap px-2 py-4 text-center text-sm font-medium">
        {!!vendor && !prebook.isDeleted && (
          <input
            type="checkbox"
            checked={isSelected}
            onChange={handleSelectItemChange}
          />
        )}
      </td>
      <td className="whitespace-nowrap py-4 pl-3 pr-6 text-center text-sm font-medium">
        {prebook.isDeleted && (
          <div>
            <span className="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-inset ring-red-600/10">
              Deleted
            </span>
          </div>
        )}
        <Link
          href={routes.prebooks.detail.to(prebook.id)}
          onClick={handleEditClick}
        >
          {formatNumber(prebook.id, '00000')}
        </Link>
        <Alert
          open={showFutureOrderWarning}
          icon="question-circle"
          title="Edit Prebook"
          message="Making changes to this Prebook will not update the Future Order. What would you like to do?"
          colour="info"
          confirmButtonText="Continue to Prebook"
          confirm={handleEditPrebook}
          cancelButtonText="Edit Future Order"
          cancel={handleEditFutureOrder}
        />
      </td>
      <td className="whitespace-nowrap px-3 py-4 text-left text-sm text-gray-700">
        <Popover
          className="inline-block translate-y-0 cursor-pointer"
          onMouseEnter={handleTooltipMouseEnter}
          onMouseLeave={handleTooltipMouseLeave}
        >
          <>
            <Popover.Button as="div" className="font-lg  px-2 py-1">
              <Icon
                icon={
                  !prebook.sent
                    ? 'triangle-exclamation'
                    : !prebook.confirmed
                    ? 'question-circle'
                    : 'check-circle'
                }
                className={
                  !prebook.sent
                    ? 'text-red-600'
                    : !prebook.confirmed
                    ? 'text-yellow-600'
                    : 'text-green-600'
                }
              />
            </Popover.Button>
            <Transition
              as={Fragment}
              show={tooltipOpen}
              enter="transition ease-out duration-200"
              enterFrom="opacity-0 translate-y-1"
              enterTo="opacity-100 translate-y-0"
              leave="transition ease-in duration-150"
              leaveFrom="opacity-100 translate-y-0"
              leaveTo="opacity-0 translate-y-1"
            >
              <Popover.Panel
                static
                className="absolute left-[25px] z-10 -translate-y-1/2 transform bg-white"
              >
                <div className="rounded-lg border p-4 shadow-lg">
                  <p className="my-2 text-xs text-gray-500">
                    Created by{' '}
                    <span className="font-medium">{prebook.createdBy}</span> on{' '}
                    <span className="font-medium">
                      {formatDate(prebook.created)}
                    </span>{' '}
                    @{' '}
                    <span className="font-medium">
                      {formatDate(prebook.created, 'h:mm a')}
                    </span>
                  </p>
                  {prebook.created !== prebook.modified && (
                    <p className="my-2 text-xs text-gray-500">
                      Updated by{' '}
                      <span className="font-medium">{prebook.modifiedBy}</span>{' '}
                      on{' '}
                      <span className="font-medium">
                        {formatDate(prebook.modified)}
                      </span>{' '}
                      @{' '}
                      <span className="font-medium">
                        {formatDate(prebook.modified, 'h:mm a')}
                      </span>
                    </p>
                  )}
                  {!!prebook.sent && (
                    <p className="my-2 text-xs text-gray-500">
                      Last sent by{' '}
                      <span className="font-medium">{prebook.sentBy}</span> on{' '}
                      <span className="font-medium">
                        {formatDate(prebook.sent)}
                      </span>{' '}
                      @{' '}
                      <span className="font-medium">
                        {formatDate(prebook.sent, 'h:mm a')}
                      </span>
                    </p>
                  )}
                  {!prebook.sent && (
                    <p className="my-2 text-xs font-bold italic text-red-500">
                      Not sent to Grower
                    </p>
                  )}
                  {!!prebook.confirmed && (
                    <p className="my-2 text-xs text-gray-500">
                      Grower Confirmed by{' '}
                      <span className="font-medium">{prebook.confirmedBy}</span>{' '}
                      on{' '}
                      <span className="font-medium">
                        {formatDate(prebook.confirmed)}
                      </span>{' '}
                      @{' '}
                      <span className="font-medium">
                        {formatDate(prebook.confirmed, 'h:mm a')}
                      </span>
                    </p>
                  )}
                  {!!prebook.sent && !prebook.confirmed && (
                    <p className="my-2 text-xs font-bold italic text-yellow-500">
                      <span className="mr-4 inline-block">
                        Grower has not confirmed
                      </span>
                      {!readonly && (
                        <button
                          type="button"
                          onClick={handleSetGrowerConfirmedClick}
                          className="btn-secondary px-2 py-1 text-xs"
                        >
                          Confirm
                        </button>
                      )}
                    </p>
                  )}
                </div>
              </Popover.Panel>
            </Transition>
          </>
        </Popover>
        &nbsp;
        <div
          className={classNames('inline-block', prebook.isDeleted && 'italic')}
        >
          {!!prebook.blanketStartDate && (
            <>
              {`${formatDate(prebook.blanketStartDate, 'MMM d')} - `}
              <br />
            </>
          )}
          {formatDate(prebook.requiredDate, 'MMM d')}
        </div>
      </td>
      <td
        className={classNames(
          'whitespace-nowrap px-3 py-4 text-sm text-gray-700',
          prebook.isDeleted && 'italic'
        )}
      >
        {prebook.isBlanket && (
          <div>
            <span className="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-blue-500">
              <svg
                className="-ml-0.5 mr-1.5 h-2 w-2 text-indigo-400"
                fill="currentColor"
                viewBox="0 0 8 8"
              >
                <circle cx={4} cy={4} r={3} />
              </svg>
              Blanket
            </span>
          </div>
        )}
        {prebook.vendor}
      </td>
      <td
        className={classNames(
          'whitespace-nowrap px-3 py-4 text-sm text-gray-700',
          prebook.isDeleted && 'italic'
        )}
      >
        {prebook.customer}
        {!!prebook.shipTo && (
          <>
            <br />
            <span className="italic text-gray-400">{prebook.shipTo}</span>
          </>
        )}
      </td>
      <td
        className={classNames(
          'whitespace-nowrap px-3 py-4 text-sm text-gray-700',
          prebook.isDeleted && 'italic'
        )}
      >
        {prebook.salesperson}
      </td>
      <td
        className={classNames(
          'whitespace-nowrap px-3 py-4 text-sm text-gray-700',
          prebook.isDeleted && 'italic'
        )}
      >
        {prebook.season}
      </td>
      <td
        className={classNames(
          'whitespace-nowrap px-3 py-4 text-center text-sm text-gray-700',
          prebook.isDeleted && 'italic'
        )}
      >
        {formatNumber(prebook.caseCount)}
      </td>
      {includeSpirePurchaseOrders && (
        <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-700">
          {prebook.spirePurchaseOrderNumber}
        </td>
      )}
    </tr>
  );
}
