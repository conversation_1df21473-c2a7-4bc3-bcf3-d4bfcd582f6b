import {
  AsyncThunk,
  createAsyncThunk,
  createSelector,
  createSlice,
  PayloadAction,
} from '@reduxjs/toolkit';
import { DateTime } from 'luxon';
import { boekestynApi, boekestynListApi } from 'api/boekestyn-service';
import * as models from 'api/models/boekestyns';
import { RootState } from '@/services/store';
import { equals } from '@/utils/equals';
import { ProblemDetails } from '@/utils/problem-details';
import { sort, sortBy } from '@/utils/sort';
import { Week, weekFromDate, weekFromWeekAndYear } from '@/utils/weeks';

export const downloadSales: AsyncThunk<void, void, { state: RootState }> =
  createAsyncThunk(
    'boekestyn-list-downloadList',
    async (_, { rejectWithValue, getState }) => {
      try {
        const rootState = getState() as RootState,
          {
            plants,
            prebookItems,
            productionOrders,
            filterPlant: plant,
            productionCustomers: customers,
          } = rootState.boekestynSales,
          args = {
            plants,
            prebookItems,
            productionOrders,
            plant,
            customers,
          };

        return await boekestynApi.boekestynSalesDownload(args);
      } catch (e) {
        return rejectWithValue(e as ProblemDetails);
      }
    }
  );

interface BoekestynSalesState {
  startDate: string;
  endDate: string;
  filterPlant: string;
  productionCustomers: string[];
  plants: models.Plant[];
  prebookItems: models.BoekestynPrebookItem[];
  productionOrders: models.BoekestynProductionOrder[];
  isLoading: boolean;
  error: ProblemDetails | null;
}

const initialState: BoekestynSalesState = {
  startDate: DateTime.now().toFormat('yyyy-MM-dd'),
  endDate: DateTime.now().plus({ weeks: 4 }).toFormat('yyyy-MM-dd'),
  filterPlant: '',
  productionCustomers: [],
  plants: [],
  prebookItems: [],
  productionOrders: [],
  isLoading: false,
  error: null,
};

export const boekestynSalesSlice = createSlice({
  name: 'boekestyn-sales',
  initialState,
  reducers: {
    clearError(state) {
      state.error = null;
    },
    clearSearchAndFilters(state) {
      return {
        ...state,
        startDate: DateTime.now().toFormat('yyyy-MM-dd'),
        endDate: DateTime.now().plus({ weeks: 4 }).toFormat('yyyy-MM-dd'),
        filterPlant: '',
      };
    },
    setStartDate(state, { payload }: PayloadAction<string>) {
      state.startDate = payload;
    },
    setEndDate(state, { payload }: PayloadAction<string>) {
      state.endDate = payload;
    },
    setFilterPlant(state, { payload }: PayloadAction<string>) {
      state.filterPlant = payload;
      const { prebookItems, productionOrders, productionCustomers } = state,
        // this is a copy of selectCustomers
        customers = productionOrders
          .filter((o) => !payload || equals(o.plant._id, payload))
          .reduce((memo, o) => {
            if (!memo.some((m) => equals(m, o.customer.abbreviation))) {
              memo.push(o.customer.abbreviation);
            }
            return memo;
          }, [] as string[]);

      prebookItems
        .filter((i) => !payload || equals(i.boekestynPlantId, payload))
        .forEach((item) => {
          if (
            !customers.some((c) =>
              equals(c, item.boekestynCustomerAbbreviation)
            )
          ) {
            customers.push(item.boekestynCustomerAbbreviation);
          }
        });
      state.productionCustomers = productionCustomers.filter(
        (c) => customers.indexOf(c) !== -1
      );
    },
    setProductionCustomers(state, { payload }: PayloadAction<string[]>) {
      state.productionCustomers = payload;
    },
  },
  extraReducers: (builder) =>
    builder
      .addMatcher(boekestynListApi.endpoints.sales.matchPending, (state) => {
        state.isLoading = true;
      })
      .addMatcher(
        boekestynListApi.endpoints.sales.matchFulfilled,
        (state, { payload }) => {
          const productionCustomers = state.productionCustomers,
            { plants, prebookItems, productionOrders } = payload,
            // this is a copy of selectCustomers
            customers = productionOrders
              .filter(
                (o) =>
                  !state.filterPlant || equals(o.plant._id, state.filterPlant)
              )
              .reduce((memo, o) => {
                if (!memo.some((m) => equals(m, o.customer.abbreviation))) {
                  memo.push(o.customer.abbreviation);
                }
                return memo;
              }, [] as string[]);

          prebookItems
            .filter(
              (i) =>
                !state.filterPlant ||
                equals(i.boekestynPlantId, state.filterPlant)
            )
            .forEach((item) => {
              if (
                !customers.some((c) =>
                  equals(c, item.boekestynCustomerAbbreviation)
                )
              ) {
                customers.push(item.boekestynCustomerAbbreviation);
              }
            });

          state.isLoading = false;
          state.productionCustomers = productionCustomers.filter(
            (c) => customers.indexOf(c) !== -1
          );
          state.plants = plants;
          state.prebookItems = prebookItems;
          state.productionOrders = productionOrders;

          if (!state.filterPlant) {
            if (productionOrders.length) {
              state.filterPlant = productionOrders[0].plant._id;
            } else {
              state.filterPlant = plants[0]._id;
            }
          }
        }
      )
      .addMatcher(
        boekestynListApi.endpoints.itemList.matchRejected,
        (state, { payload }) => {
          state.isLoading = false;
          if (payload) {
            state.error = payload;
          }
        }
      ),
});

export const {
  clearError,
  clearSearchAndFilters,
  setStartDate,
  setEndDate,
  setFilterPlant,
  setProductionCustomers,
} = boekestynSalesSlice.actions;

export const selectError = (state: RootState) => state.boekestynSales.error;
export const selectStartDate = (state: RootState) =>
  state.boekestynSales.startDate;
export const selectEndDate = (state: RootState) => state.boekestynSales.endDate;
export const selectFilterPlant = (state: RootState) =>
  state.boekestynSales.filterPlant;
export const selectProductionCustomers = (state: RootState) =>
  state.boekestynSales.productionCustomers;
export const selectIsLoading = (state: RootState) =>
  state.boekestynSales.isLoading;
const selectAllPlants = (state: RootState) => state.boekestynSales.plants;
const selectAllPrebookItems = (state: RootState) =>
  state.boekestynSales.prebookItems;
const selectAllProductionOrders = (state: RootState) =>
  state.boekestynSales.productionOrders;

export const selectProductionOrders = createSelector(
  selectAllProductionOrders,
  selectProductionCustomers,
  selectFilterPlant,
  (productionOrders, productionCustomers, filterPlant) =>
    productionOrders.filter(
      (o) =>
        (!filterPlant || equals(o.plant._id, filterPlant)) &&
        (!productionCustomers.length ||
          productionCustomers.indexOf(o.customer.abbreviation || '') !== -1)
    )
);

export const selectPrebookItems = createSelector(
  selectAllPrebookItems,
  selectFilterPlant,
  (prebookItems, filterPlant) =>
    prebookItems.filter(
      (i) => !filterPlant || equals(i.boekestynPlantId, filterPlant)
    )
);

export const selectSingleProductItems = createSelector(
  selectPrebookItems,
  (prebookItems) =>
    prebookItems.filter((i) => !i.multipleProducts || i.ignoreOverrideQuantity)
);

export const selectMultipleProductItems = createSelector(
  selectPrebookItems,
  (prebookItems) =>
    prebookItems.filter((i) => i.multipleProducts && !i.ignoreOverrideQuantity)
);

export const selectPlants = createSelector(
  selectAllPlants,
  selectAllProductionOrders,
  selectAllPrebookItems,
  (plants, productionOrders, prebookItems) =>
    plants.filter(
      (p) =>
        productionOrders.some((o) => o.plant._id === p._id) ||
        prebookItems.some((i) => i.boekestynPlantId === p._id)
    )
);

export const selectCustomers = createSelector(
  selectProductionOrders,
  selectPrebookItems,
  (productionOrders, prebookItems) => {
    const customers = productionOrders.reduce((memo, o) => {
      if (!memo.some((m) => equals(m, o.customer.abbreviation))) {
        memo.push(o.customer.abbreviation);
      }
      return memo;
    }, [] as string[]);

    prebookItems.forEach((item) => {
      if (
        !customers.some((c) => equals(c, item.boekestynCustomerAbbreviation))
      ) {
        customers.push(item.boekestynCustomerAbbreviation);
      }
    });

    return customers.sort();
  }
);

export const selectSalesCustomers = createSelector(
  selectSingleProductItems,
  (prebookItems) =>
    prebookItems
      .map((o) => ({ ...o }))
      .sort(sortBy('couchCustomer'))
      .reduce((memo, o) => {
        const customer = o.customerName;
        if (!memo.some((m) => equals(m, customer))) {
          memo.push(customer);
        }
        return memo;
      }, [] as string[])
);

export const selectBloomingAssortedSalesCustomers = createSelector(
  selectMultipleProductItems,
  (prebookItems) =>
    prebookItems
      .map((o) => ({ ...o }))
      .sort(sortBy('couchCustomer'))
      .reduce((memo, o) => {
        const customer = o.customerName;
        if (!memo.some((m) => equals(m, customer))) {
          memo.push(customer);
        }
        return memo;
      }, [] as string[])
);

interface ProductionWeek {
  week: Week;
  customer: string;
}

const sortByProductionWeek = (a: ProductionWeek, b: ProductionWeek) =>
  sort(a.week.weekId, b.week.weekId) || sort(a.customer, b.customer);

export const selectWeeks = createSelector(
  selectProductionOrders,
  selectPrebookItems,
  selectProductionCustomers,
  (productionOrders, prebookItems, productionCustomers) => {
    const weeks = productionOrders.reduce((memo, o) => {
      const week = weekFromDate(o.flowerDate),
        customer = o.customer.abbreviation;
      if (week) {
        if (
          !memo.some(
            (m) => week.weekId === m.week.weekId && equals(m.customer, customer)
          )
        ) {
          memo.push({ week, customer });
        }
      }
      return memo;
    }, [] as ProductionWeek[]);

    prebookItems.forEach((item) => {
      const week = weekFromWeekAndYear(item.week, item.year),
        customer = item.boekestynCustomerAbbreviation;

      if (
        (!productionCustomers.length ||
          productionCustomers.indexOf(customer) !== -1) &&
        !weeks.some(
          (w) => w.week.weekId === week.weekId && equals(w.customer, customer)
        )
      ) {
        weeks.push({ week, customer });
      }
    });

    return weeks.sort(sortByProductionWeek);
  }
);

export default boekestynSalesSlice.reducer;
