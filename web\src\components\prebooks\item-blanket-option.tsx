import { PrebookBlanketItem } from 'api/models/prebooks';
import { formatDate, formatNumber } from '@/utils/format';
import { weekFromId } from '@/utils/weeks';

interface ItemBlanketOptionProps {
  blanketItem: PrebookBlanketItem;
  prebookVendorId: number | null | undefined;
}

export function ItemBlanketOption({
  blanketItem,
  prebookVendorId,
}: ItemBlanketOptionProps) {
  const week = weekFromId(blanketItem.blanketWeekId);

  return (
    <option value={blanketItem.id}>
      {!prebookVendorId && `${blanketItem.vendorName}: `}
      {!!blanketItem.blanketStartDate &&
        `${formatDate(blanketItem.blanketStartDate, 'MMM d')} - `}
      {formatDate(blanketItem.requiredDate, 'MMM d')}
      {!!week && ` Week ${week.week}`}
      {` (${formatNumber(blanketItem.blanketQuantity)})`}
    </option>
  );
}
