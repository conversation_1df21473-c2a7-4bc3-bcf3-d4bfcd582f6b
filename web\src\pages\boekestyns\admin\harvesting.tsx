import { useHarvestingQuery, useHarvestingSchedulesQuery } from 'api/boekestyn-harvesting-service';
import { HarvestingOrders } from '@/components/boekestyns/admin/harvesting-orders';
import { HarvestingSchedules } from '@/components/boekestyns/admin/harvesting-schedules';
import { setLastSelected } from '@/components/boekestyns/admin/admin-slice';
import { AdminLayout } from '@/components/boekestyns/admin/layout';
import { useAppDispatch } from '@/services/hooks';
import { routes } from '@/services/routes';

export default function Harvesting() {
  const dispatch = useAppDispatch();

  dispatch(setLastSelected(routes.boekestyns.admin.harvesting.to()));

  useHarvestingQuery();

  return (
    <div className="flex h-full flex-col overflow-y-auto bg-blue-500">
      <div className="m-4 flex flex-grow flex-col overflow-y-auto rounded bg-white">
        <div className="grid h-full grid-cols-2 gap-x-2 overflow-y-auto p-2">
          <div className="flex h-full flex-col overflow-y-auto rounded border">
            <HarvestingOrders />
          </div>
          <div className="flex h-full flex-col overflow-y-auto rounded border">
            <HarvestingSchedules />
          </div>
        </div>
      </div>
    </div>
  );
}

Harvesting.getLayout = function getLayout(page: React.ReactElement) {
  return <AdminLayout>{page}</AdminLayout>;
};
