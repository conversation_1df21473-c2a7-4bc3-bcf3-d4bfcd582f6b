import {
  FontAwesomeIcon,
  FontAwesomeIconProps,
} from '@fortawesome/react-fontawesome';
import { IconProp, SizeProp } from '@fortawesome/fontawesome-svg-core';

type LoadingProps = Omit<FontAwesomeIconProps, 'icon'>;

export function Loading({ ...props }: LoadingProps) {
  const icon: IconProp = ['fal', 'spinner'],
    size: SizeProp = '4x',
    className = 'text-blue-500 w-full mx-auto',
    allProps = { ...props, icon, spin: true, size, className };
  return <FontAwesomeIcon {...allProps} />;
}
