import React, { Fragment, useEffect, useState, useRef } from 'react';
import * as HeadlessUI from '@headlessui/react';
import { settingsService } from 'api/settings-service';
import { useInventoryItemsQuery } from 'api/spire-service';
import * as spire from 'api/models/spire';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import { classNames } from '@/utils/class-names';
import { contains } from '@/utils/equals';
import { ProblemDetails, createProblemDetails } from '@/utils/problem-details';

interface ProductDefaultNewProps {
  open: boolean;
  onClose: (item?: spire.InventoryItem) => void;
}

export function ProductDefaultNew({ open, onClose }: ProductDefaultNewProps) {
  const { data } = useInventoryItemsQuery(),
    inputRef = useRef<HTMLInputElement>(null),
    [item, setItem] = useState<spire.InventoryItem | null>(null),
    [inventoryItems, setInventoryItems] = useState<spire.InventoryItem[]>([]),
    [query, setQuery] = useState(''),
    [error, setError] = useState<ProblemDetails | null>(null),
    filteredItems = inventoryItems.filter(
      (i) =>
        !query || contains(i.description, query) || contains(i.partNo, query)
    ),
    collection = filteredItems.slice(0, 100);

  useEffect(() => {
    if (data) {
      const defaults =
          data?.productDefaults?.map((d) => d.spireInventoryId) || [],
        inventoryItems = data?.inventoryItems || [],
        availableItems = inventoryItems.filter(
          (i) => defaults.indexOf(i.id) === -1
        );
      setInventoryItems(availableItems);
    }
  }, [data]);

  const handleTransitionAfterEnter = () => {
    setItem(null);
    setQuery('');
    setError(null);
  };

  const handleItemChange = (item: spire.InventoryItem | null) => {
    setItem(item);
  };

  const handleQueryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(e.target.value);
  };

  const handleSaveClick = async () => {
    if (!item) {
      return setError(createProblemDetails('Please choose an item'));
    }

    try {
      const model = {
        spireInventoryId: item.id,
        boekestynPlantId: null,
        boekestynCustomerAbbreviation: null,
        upgradeLabourHours: null,
        quantityPerFinishedItem: null,
        isUpgrade: false,
        ignoreOverrideQuantity: false,
        products: [],
        overrides: [],
        updateExisting: false,
      };

      await settingsService.updateProductDefault(model);

      onClose(item);
    } catch (e) {
      setError(e as ProblemDetails);
    }
  };

  const handleCancelClick = () => {
    onClose();
  };

  const handleClearError = () => {
    setError(null);
  };

  return (
    <HeadlessUI.Transition.Root
      show={open}
      as={Fragment}
      afterEnter={handleTransitionAfterEnter}
    >
      <HeadlessUI.Dialog
        as="div"
        className="relative z-30"
        onClose={handleCancelClick}
        initialFocus={inputRef}
      >
        <HeadlessUI.Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </HeadlessUI.Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <HeadlessUI.Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <HeadlessUI.Dialog.Panel className="relative transform rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-sm sm:p-6">
                <div className="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
                  <button
                    type="button"
                    className="btn-secondary px-2 py-1"
                    onClick={handleCancelClick}
                  >
                    <span className="sr-only">Close</span>
                    <Icon icon="x" aria-hidden="true" />
                  </button>
                </div>
                <div>
                  <div className="mt-3 text-center sm:mt-5">
                    <HeadlessUI.Dialog.Title
                      as="h3"
                      className="text-lg font-medium leading-6 text-gray-900"
                    >
                      Add Product Default
                    </HeadlessUI.Dialog.Title>
                    <div className="mt-2">
                      <HeadlessUI.Combobox
                        as="div"
                        value={item}
                        onChange={handleItemChange}
                      >
                        <HeadlessUI.Combobox.Label className="block text-sm font-medium leading-6 text-gray-900">
                          Choose Item
                        </HeadlessUI.Combobox.Label>
                        <div className="relative mt-2">
                          <HeadlessUI.Combobox.Input
                            className="w-full rounded-md border-0 bg-white px-3 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                            type="search"
                            onChange={handleQueryChange}
                            displayValue={(i: spire.InventoryItem) =>
                              i ? `${i.description} (${i.partNo})` : ''
                            }
                            ref={inputRef}
                          />

                          {collection.length > 0 && (
                            <HeadlessUI.Combobox.Options className="absolute z-20 mt-1 max-h-60 w-full divide-y-2 overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                              {collection.map((availableItem) => (
                                <HeadlessUI.Combobox.Option
                                  key={availableItem.id}
                                  value={availableItem}
                                  className={({ active }) =>
                                    classNames(
                                      'relative cursor-default select-none py-2 pl-3 pr-9',
                                      active
                                        ? 'bg-blue-600 text-white'
                                        : 'text-gray-900'
                                    )
                                  }
                                >
                                  {({ active }) => (
                                    <div
                                      className={classNames(
                                        'cursor-pointer truncate text-left text-sm',
                                        active ? 'text-white' : 'text-gray-900'
                                      )}
                                    >
                                      <div
                                        className={classNames(
                                          'font-medium',
                                          active
                                            ? 'text-white'
                                            : 'text-gray-900'
                                        )}
                                      >
                                        {availableItem.partNo}
                                      </div>

                                      <div
                                        className={classNames(
                                          'cursor-pointer truncate text-sm',
                                          active
                                            ? 'text-white'
                                            : 'text-gray-500'
                                        )}
                                      >
                                        {availableItem.description}
                                      </div>
                                    </div>
                                  )}
                                </HeadlessUI.Combobox.Option>
                              ))}
                            </HeadlessUI.Combobox.Options>
                          )}
                        </div>
                      </HeadlessUI.Combobox>
                    </div>
                    <div className="mt-4 text-right">
                      <button
                        type="button"
                        onClick={handleCancelClick}
                        className="btn-secondary mr-2"
                      >
                        Cancel
                      </button>
                      <button
                        type="button"
                        onClick={handleSaveClick}
                        className="btn-primary"
                        disabled={!item}
                      >
                        Add Defaults &nbsp;
                        <Icon icon="chevron-right" />
                      </button>
                    </div>
                    <Error
                      error={error}
                      clear={handleClearError}
                      containerClasses="col-span-2"
                    />
                  </div>
                </div>
              </HeadlessUI.Dialog.Panel>
            </HeadlessUI.Transition.Child>
          </div>
        </div>
      </HeadlessUI.Dialog>
    </HeadlessUI.Transition.Root>
  );
}
