import { InventoryItem } from 'api/models/spire';
import { useAppSelector } from '@/services/hooks';
import { Icon } from '@/components/icon';
import {
  selectBlanketItems,
  selectVendor,
  selectCustomer,
  selectShipTo,
  selectIsBlanket,
  selectRequiredDate,
} from './prebook-detail-slice';
import { InventoryListItemBlanketItem } from './inventory-list-item-blanket-item';
import { classNames } from '@/utils/class-names';
import { isDateInWeek } from '@/utils/weeks';

export interface InventoryListItemProps {
  item: InventoryItem;
  onItemSelected: (
    value: InventoryItem,
    blanketItemId: number | null,
    hasBlanketItems: boolean
  ) => void;
}

export function InventoryListItem({
  item,
  onItemSelected,
}: InventoryListItemProps) {
  const allBlanketItems = useAppSelector(selectBlanketItems),
    vendor = useAppSelector(selectVendor),
    customer = useAppSelector(selectCustomer),
    shipTo = useAppSelector(selectShipTo),
    isBlanket = useAppSelector(selectIsBlanket),
    requiredDate = useAppSelector(selectRequiredDate),
    blanketItems = isBlanket
      ? []
      : allBlanketItems.filter(
          (i) =>
            i.spireInventoryId === item.id &&
            (!requiredDate ||
              (requiredDate <= i.requiredDate &&
                (!i.blanketStartDate || requiredDate >= i.blanketStartDate))) &&
            isDateInWeek(i.blanketWeekId, requiredDate) &&
            (!vendor || i.vendorId === vendor.id) &&
            (!customer || !i.customerId || i.customerId === customer.id) &&
            (!shipTo || !i.shipToId || i.shipToId === shipTo.id)
        );

  const handleSelectItem = (blanketItemId: number | null) => {
    onItemSelected(item, blanketItemId || null, !!blanketItems.length);
  };

  return (
    <li
      key={item.id}
      className="grid grid-cols-4 bg-white py-2 pl-4 pr-2 text-left focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-600 hover:bg-gray-50"
    >
      <div className="col-span-3 truncate text-sm font-medium text-gray-900">
        {item.partNo}
      </div>
      <div
        className={classNames(
          'row-span-2 flex flex-col border-gray-200 pl-4 align-middle',
          blanketItems.length ? 'border-b-2' : ''
        )}
      >
        <button
          type="button"
          className="btn-secondary my-auto flex w-24 justify-between py-1 text-xs text-blue-500 hover:border-blue-300 hover:text-blue-700"
          onClick={() => handleSelectItem(null)}
        >
          <div className="flex flex-grow">
            {blanketItems.length ? 'No Blanket' : 'Select'}
          </div>
          <Icon icon="chevron-right" />
        </button>
      </div>
      <div
        className={classNames(
          'col-span-3 col-start-1 truncate text-sm text-gray-500',
          blanketItems.length && 'border-b-2 border-gray-200 pb-2'
        )}
      >
        {item.description}
      </div>
      {!!blanketItems.length && (
        <div className="col-span-3 col-start-1 mt-2 italic text-gray-700">
          Open Blanket Prebooks{' '}
          {!!vendor && (
            <>
              <br />
              {` for ${vendor.name}`}
            </>
          )}
        </div>
      )}
      {blanketItems.map((blanketItem) => (
        <InventoryListItemBlanketItem
          key={blanketItem.id}
          blanketItem={blanketItem}
          vendor={vendor}
          onItemSelected={handleSelectItem}
        />
      ))}
    </li>
  );
}
