import React, { Fragment, useEffect, useRef, useState } from 'react';
import { useDrop } from 'react-dnd';
import { DateTime } from 'luxon';
import * as HeadlessUI from '@headlessui/react';
import { boekestynApi } from 'api/boekestyn-service';
import { useLazyItemPriceQuery } from 'api/future-orders-service';
import { usePriceDeviationWarningsQuery } from 'api/settings-service';
import { useVendorsQuery, useInventoryItemsQuery } from 'api/spire-service';
import { prebooksApi, useOpenBlanketItemsQuery } from 'api/prebooks-service';
import * as boeks from 'api/models/boekestyns';
import * as prebooks from 'api/models/prebooks';
import * as spire from 'api/models/spire';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { Alert } from '@/components/alert';
import { DropdownMenu } from '@/components/drop-down-menu';
import { Icon } from '@/components/icon';
import { ToastMessage, addToast } from '@/components/toaster-slice';
import { classNames } from '@/utils/class-names';
import { startsWith } from '@/utils/equals';
import {
  formatNumber,
  parseNullableCurrency,
  parseNullableQuantity,
  parseQuantity,
} from '@/utils/format';
import { handleFocus } from '@/utils/focus';
import { sortBy } from '@/utils/sort';
import { isDateInWeek } from '@/utils/weeks';
import {
  CreateItem,
  removeItem,
  setItemInventoryItem,
  setItemOrderQuantity,
  setItemHasPotCover,
  setItemPotCover,
  setItemDateCode,
  setItemUPC,
  setItemWeightsAndMeasures,
  setItemRetail,
  setItemComments,
  setItemIsApproximate,
  setItemVendor,
  setItemCreatePrebook,
  setItemIsBlanket,
  setItemBlanketItemId,
  setItemUnitPrice,
  setItemSpecialPrice,
  setItemGrowerItemNotes,
  setItemUseAvailabilityPricing,
  selectRequiredDate,
  selectSeasonName,
  selectCustomer,
  selectShipTo,
  selectCustomerItemCodeDefaults,
  setItemCustomerItemCode,
  setItemUpgradeSheet,
  setItemPhytoRequired,
  setItemExpanded,
  setItemBoekestynProductionOrder,
  duplicateItem,
  moveItem,
  selectPotCovers,
  selectSeasons,
  setItemUpgradeLabourHours,
  setItemQuantityPerFinishedItem,
  selectDefaultVendorOverrides,
} from './future-order-create-slice';
import { Inventory } from './inventory';
import {
  itemIsOverbooked,
  getUnitPrice,
  getCasePrice,
  getPackQuantity,
  showPriceWarning,
  colourLengthWarning,
  labelLengthWarning,
  findDefaultVendorOverride,
} from './item-functions';
import {
  ItemBoekestynOption,
  makeOptionKey,
  parseKey,
} from './item-boekestyn-option';
import { BlanketItemOption } from './blanket-item-option';
import { setFutureOrderItem } from './boekestyn-product-slice';
import { useDebounce } from 'use-debounce';

const sortByDescription = sortBy('description');

interface NewFutureOrderItemExpandedProps {
  item: CreateItem;
}

export function NewFutureOrderItemExpanded({
  item,
}: NewFutureOrderItemExpandedProps) {
  const dispatch = useAppDispatch(),
    requiredDate = useAppSelector(selectRequiredDate),
    seasonName = useAppSelector(selectSeasonName),
    customer = useAppSelector(selectCustomer),
    shipTo = useAppSelector(selectShipTo),
    customerItemCodeDefaults = useAppSelector(selectCustomerItemCodeDefaults),
    potCovers = useAppSelector(selectPotCovers),
    seasons = useAppSelector(selectSeasons),
    defaultVendorOverrides = useAppSelector(selectDefaultVendorOverrides),
    { data: openBlanketResponse } = useOpenBlanketItemsQuery(),
    { data: vendors } = useVendorsQuery(),
    { data: inventoryItemsData } = useInventoryItemsQuery(),
    [itemPriceQuery, { data: spirePrice }] = useLazyItemPriceQuery(),
    { data: priceDeviationWarnings } = usePriceDeviationWarningsQuery(),
    blanketItems = openBlanketResponse?.blanketItems || [],
    productDefaults = inventoryItemsData?.productDefaults || [],
    itemId = item.id,
    [vendor, setVendor] = useState<spire.Vendor | null>(null),
    [showDeleteAlert, setShowDeleteAlert] = useState(false),
    [orderQuantity, setOrderQuantity] = useState(
      formatNumber(item.orderQuantity)
    ),
    [unitPrice, setUnitPrice] = useState(
      item.unitPrice == null ? '' : formatNumber(item.unitPrice, '#,##0.00')
    ),
    [eachPrice, setEachPrice] = useState(
      item.unitPrice == null ? '' : formatNumber(getUnitPrice(item), '#,##0.00')
    ),
    [specialPrice, setSpecialPrice] = useState(
      item.specialPrice == null
        ? ''
        : formatNumber(item.specialPrice, '#,##0.00')
    ),
    [showEditDialog, setShowEditDialog] = useState(false),
    [vendorQuery, setVendorQuery] = useState(''),
    [boekestynOrders, setBoekestynOrders] = useState(
      [] as boeks.WeeklyProductionOrder[]
    ),
    [boekestynPlants, setBoekestynPlants] = useState([] as boeks.Plant[]),
    [hasSpecialPricing, setHasSpecialPricing] = useState(false),
    [showDuplicateTooltip, setShowDuplicateTooltip] = useState(false),
    [showRestoreDefaultsTooltip, setShowRestoreDefaultsTooltip] =
      useState(false),
    [showDeleteTooltip, setShowDeleteTooltip] = useState(false),
    [showPriceWarningTooltip, setShowPriceWarningTooltip] = useState(false),
    cellClassName = 'whitespace-nowrap px-1 py-1 text-gray-500 border-bottom-0',
    inputClassName =
      'w-full rounded-md border-gray-300 text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500',
    quantityRef = useRef<HTMLInputElement | null>(null),
    commentsRef = useRef<HTMLTextAreaElement | null>(null),
    potCoverRef = useRef<HTMLInputElement | null>(null),
    specialPricingRef = useRef<HTMLInputElement | null>(null),
    [{ isOver }, drop] = useDrop<CreateItem, void, { isOver: boolean }>(() => ({
      accept: 'FUTURE_ORDER_ITEM',
      collect: (monitor) => ({
        isOver: monitor.isOver(),
      }),
      drop(droppedItem) {
        dispatch(moveItem({ existingItem: item, movingItem: droppedItem }));
      },
    })),
    isBoekestyn = vendor?.id === boeks.BoekestynVendorId,
    season = seasons.find((s) => s.name === seasonName),
    availableBlanketItems = blanketItems.filter(
      (i) =>
        !isBoekestyn &&
        (!item.vendorId || i.vendorId === item.vendorId) &&
        i.spireInventoryId === item.spireInventoryId &&
        (!customer || !i.customerId || i.customerId === customer.id) &&
        (!shipTo || !i.shipToId || i.shipToId === shipTo.id) &&
        // no date at all - don't bother filtering
        ((!requiredDate && !season) ||
          // just a season date - use that
          (!requiredDate &&
            season &&
            season.seasonDate <= i.requiredDate &&
            (!i.blanketStartDate || season.seasonDate >= i.blanketStartDate)) ||
          // required date - use that
          (requiredDate &&
            requiredDate <= i.requiredDate &&
            (!i.blanketStartDate || requiredDate >= i.blanketStartDate))) &&
        isDateInWeek(i.blanketWeekId, requiredDate)
    ),
    lux = requiredDate ? DateTime.fromISO(requiredDate) : null,
    week = lux?.get('weekNumber'),
    year = lux?.get('weekYear'),
    availableBoekestynOrders = (
      isBoekestyn
        ? boeks.ordersWithAllPlants(
            requiredDate,
            item,
            boekestynOrders,
            boekestynPlants
          )
        : []
    ).sort(sortByDescription),
    isOverbooked = itemIsOverbooked(item, blanketItems),
    filteredVendors =
      (vendorQuery
        ? vendors?.filter(
            (item) =>
              startsWith(item.name, vendorQuery) ||
              startsWith(item.vendorNo, vendorQuery)
          )
        : vendors) || [],
    packQuantity = getPackQuantity(item),
    eachSpirePrice =
      !!spirePrice && !!packQuantity
        ? formatNumber(spirePrice / packQuantity, '0,0.00')
        : null,
    priceWarning = showPriceWarning(
      customer?.id,
      shipTo?.id,
      item,
      spirePrice,
      priceDeviationWarnings
    ),
    colourWarning = colourLengthWarning(item),
    labelWarning = labelLengthWarning(item);

  const [potCover, setPotCover] = useState(item.potCover);
  const [debouncedPotCover] = useDebounce(potCover, 500);

  useEffect(() => {
    dispatch(setItemPotCover({ itemId, value: debouncedPotCover }));
  }, [debouncedPotCover, dispatch, itemId]);

  useEffect(() => {
    window.setTimeout(() => {
      quantityRef.current?.focus();
    });
  }, []);

  useEffect(() => {
    const vendor = (vendors || []).find((v) => v.id === item.vendorId) || null;
    setVendor(vendor);
  }, [item, vendors]);

  useEffect(() => {
    boekestynApi
      .productionOrders()
      .then((orders) => setBoekestynOrders(orders));
    boekestynApi.plants().then((plants) => setBoekestynPlants(plants));
  }, []);

  useEffect(() => {
    if (item.useAvailabilityPricing && item.spireInventoryId) {
      itemPriceQuery({
        spireInventoryId: item.spireInventoryId,
        customerId: customer?.id,
        shipToId: shipTo?.id,
        requiredDate,
      });
    }
  }, [
    item.spireInventoryId,
    customer?.id,
    shipTo?.id,
    requiredDate,
    item.useAvailabilityPricing,
    itemPriceQuery,
  ]);

  useEffect(() => {
    setPotCover(item.potCover);
  }, [item.potCover]);

  const handleInventoryItemClick = () => {
    setShowEditDialog(true);
  };

  const handleInventoryItemCancel = () => {
    setShowEditDialog(false);
  };

  const handleInventoryItemConfirm = (
    value: spire.InventoryItem,
    blanketItemId: number | null,
    boekestynPlantId: string | null,
    boekestynCustomerAbbreviation: string | null,
    comments: string | null
  ) => {
    dispatch(setItemInventoryItem({ itemId, value }));
    if (blanketItemId && blanketItemId > 0) {
      dispatch(setItemBlanketItemId({ itemId, value: blanketItemId }));
    } else {
      dispatch(setItemBlanketItemId({ itemId, value: null }));
    }
    dispatch(setItemComments({ itemId, value: comments }));

    const override = findDefaultVendorOverride(
        value.partNo,
        requiredDate,
        defaultVendorOverrides
      ),
      overrideVendor = vendors?.find((v) => v.id === override?.vendorId);

    if (overrideVendor) {
      dispatch(setItemVendor({ itemId, value: vendor }));
    } else if (value.primaryVendor) {
      const vendor = vendors?.find(
        (v) => v.vendorNo === value.primaryVendor?.vendorNo
      );
      if (vendor) {
        dispatch(setItemVendor({ itemId, value: vendor }));
      }
    }

    const boekestynValue: {
      boekestynPlantId: string | null;
      boekestynCustomerAbbreviation: string | null;
    } = {
      boekestynPlantId: boekestynPlantId || null,
      boekestynCustomerAbbreviation: boekestynCustomerAbbreviation || null,
    };

    const productDefault = productDefaults.find(
      (d) => d.spireInventoryId === value?.id
    );

    if (productDefault) {
      if (productDefault.upgradeLabourHours) {
        dispatch(
          setItemUpgradeLabourHours({
            itemId,
            value: productDefault.upgradeLabourHours,
          })
        );
      }

      if (isBoekestyn) {
        boekestynValue.boekestynPlantId =
          boekestynPlantId || productDefault.boekestynPlantId;
        boekestynValue.boekestynCustomerAbbreviation =
          boekestynCustomerAbbreviation ||
          productDefault.boekestynCustomerAbbreviation ||
          boeks.WeeklyCustomerAbbreviation;
        dispatch(
          setItemQuantityPerFinishedItem({
            itemId,
            value: productDefault.quantityPerFinishedItem,
          })
        );
      }
    }

    dispatch(
      setItemBoekestynProductionOrder({ itemId, value: boekestynValue })
    );

    setShowEditDialog(false);
  };

  const handleHasPotCoverChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.checked;
    dispatch(setItemHasPotCover({ itemId, value }));
    if (value) {
      dispatch(setItemPotCover({ itemId, value: 'PC' }));
      window.setTimeout(() => potCoverRef.current?.focus(), 500);
    } else {
      dispatch(setItemPotCover({ itemId, value: null }));
    }
  };

  const handlePotCoverChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const value = e.target.value.toUpperCase() || null;
    setPotCover(value);
  };

  const handlePotCoverSelected = (value: string) => {
    setPotCover(value);
  };

  const handleDateCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.toUpperCase() || null;
    dispatch(setItemDateCode({ itemId, value }));
  };

  const handleUPCChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value || null;
    dispatch(setItemUPC({ itemId, value }));
  };

  const handleWeightsAndMeasuresChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = e.target.checked;
    dispatch(setItemWeightsAndMeasures({ itemId, value }));
  };

  const handleRetailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value || null;
    dispatch(setItemRetail({ itemId, value }));
  };

  const handleCommentsChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value || null;
    dispatch(setItemComments({ itemId, value }));
  };

  const handleGrowerItemNotesChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const value = e.target.value || null;
    dispatch(setItemGrowerItemNotes({ itemId, value }));
  };

  const handleOrderQuantityChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setOrderQuantity(e.target.value);
  };

  const handleUnitPriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUnitPrice(e.target.value);
  };

  const handleCustomerItemCodeChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = e.target.value.toUpperCase() || null;
    dispatch(setItemCustomerItemCode({ itemId, value }));
  };

  const handleUpgradeSheetChange = (value: boolean) => {
    dispatch(setItemUpgradeSheet({ itemId, value }));
  };

  const handlePhytoRequiredChange = (value: boolean) => {
    dispatch(setItemPhytoRequired({ itemId, value }));
  };

  const handleIsApproximateChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = e.target.checked;
    dispatch(setItemIsApproximate({ itemId, value }));
  };

  const handleOrderQuantityBlur = () => {
    const value = parseQuantity(orderQuantity);
    setOrderQuantity(formatNumber(value));
    dispatch(setItemOrderQuantity({ itemId, value }));
  };

  const handleUnitPriceBlur = () => {
    const value = parseNullableCurrency(unitPrice),
      price = value == null ? '' : formatNumber(value, '0,0.00'),
      eachPrice = getUnitPrice(item, value),
      formattedEachPrice =
        eachPrice == null ? '' : formatNumber(eachPrice, '0,0.00');

    setUnitPrice(price);
    setEachPrice(formattedEachPrice);
    dispatch(setItemUnitPrice({ itemId, value }));
  };

  const handleEachPriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEachPrice(e.target.value);
  };

  const handleEachPriceBlur = () => {
    const value = parseNullableCurrency(eachPrice || ''),
      casePrice = getCasePrice(item, value),
      price = value == null ? '' : formatNumber(value, '0,0.00'),
      formattedCasePrice =
        casePrice == null ? '' : formatNumber(casePrice, '0,0.00');

    setEachPrice(price);
    setUnitPrice(formattedCasePrice);

    dispatch(setItemUnitPrice({ itemId, value: casePrice }));
  };

  const handleUseAvailabilityPricingChange = (value: boolean) => {
    dispatch(setItemUseAvailabilityPricing({ itemId, value }));

    if (value) {
      setUnitPrice('');
    }
  };

  const handleVendorChange = async (value: spire.Vendor | null) => {
    dispatch(setItemVendor({ itemId, value }));

    const boekestynValue: {
        boekestynPlantId: string | null;
        boekestynCustomerAbbreviation: string | null;
      } = {
        boekestynPlantId: null,
        boekestynCustomerAbbreviation: null,
      },
      isBoekestyn = value?.id === boeks.BoekestynVendorId;

    if (isBoekestyn) {
      const productDefault = productDefaults.find(
        (d) => d.spireInventoryId === item.spireInventoryId
      );

      if (productDefault) {
        boekestynValue.boekestynPlantId = productDefault.boekestynPlantId;
        boekestynValue.boekestynCustomerAbbreviation =
          item.boekestynCustomerAbbreviation ||
          productDefault.boekestynCustomerAbbreviation ||
          boeks.WeeklyCustomerAbbreviation;
      }
    }

    dispatch(
      setItemBoekestynProductionOrder({ itemId, value: boekestynValue })
    );

    const blanketItemId =
      blanketItems.find(
        (i) =>
          i.vendorId === value?.id &&
          (!i.blanketStartDate ||
            !requiredDate ||
            i.blanketStartDate <= requiredDate) &&
          (!requiredDate ||
            !i.requiredDate ||
            i.requiredDate >= requiredDate) &&
          i.spireInventoryId === item.spireInventoryId &&
          (!customer || !i.customerId || customer.id === i.customerId) &&
          (!shipTo || !i.shipToId || shipTo.id === i.shipToId)
      )?.id || null;

    dispatch(setItemBlanketItemId({ itemId, value: blanketItemId }));
  };

  const handleVendorFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setVendorQuery(e.target.value);
  };

  const handleCreatePrebookChange = (value: boolean) => {
    dispatch(setItemCreatePrebook({ itemId, value }));
    if (!value) {
      dispatch(setItemIsBlanket({ itemId, value: false }));
    }
  };

  const handlePrebookIsBlanketChange = (value: boolean) => {
    dispatch(setItemIsBlanket({ itemId, value }));
  };

  const handleBlanketItemChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = parseNullableQuantity(e.target.value);
    dispatch(setItemBlanketItemId({ itemId, value }));

    if (
      !value &&
      !item.comments &&
      availableBlanketItems.length &&
      vendor?.id !== boeks.BoekestynVendorId
    ) {
      dispatch(
        setItemComments({
          itemId,
          value: prebooks.overAndAboveBlanketComment,
        })
      );
    } else if (value && item.comments === prebooks.overAndAboveBlanketComment) {
      dispatch(setItemComments({ itemId, value: null }));
    }
  };

  const handleCollapseClick = () => {
    dispatch(setItemExpanded({ itemId: item.id, value: false }));
  };

  const handleDuplicateTooltipMouseEnter = () => {
    setShowDuplicateTooltip(true);
  };

  const handleDuplicateTooltipMouseLeave = () => {
    setShowDuplicateTooltip(false);
  };

  const handleRestoreDefaultsTooltipMouseEnter = () => {
    if (customer && shipTo) {
      setShowRestoreDefaultsTooltip(true);
    }
  };

  const handleRestoreDefaultsTooltipMouseLeave = () => {
    setShowRestoreDefaultsTooltip(false);
  };

  const handleDeleteTooltipMouseEnter = () => {
    setShowDeleteTooltip(true);
  };

  const handleDeleteTooltipMouseLeave = () => {
    setShowDeleteTooltip(false);
  };

  const handlePriceWarningTooltipMouseEnter = () => {
    setShowPriceWarningTooltip(true);
  };

  const handlePriceWarningTooltipMouseLeave = () => {
    setShowPriceWarningTooltip(false);
  };

  const handleDuplicateClick = () => {
    dispatch(duplicateItem(itemId));
  };

  const handleBoekestynProductClick = () => {
    dispatch(setFutureOrderItem(item));
  };

  const handleBoekestynProductionOrderChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    const key = e.target.value,
      value = parseKey(key);
    dispatch(setItemBoekestynProductionOrder({ itemId, value }));
  };

  const handleDeleteClick = () => {
    setShowDeleteAlert(true);
  };

  const handleDeleteCancel = () => {
    setShowDeleteAlert(false);
  };

  const handleDeleteConfirm = () => {
    dispatch(removeItem(itemId));
    setShowDeleteAlert(false);
  };

  const handleRestoreDefaultsClick = async () => {
    if (customer && shipTo && item.spireInventoryId) {
      const { default: details } = await prebooksApi.productShipToDefaults(
        item.spireInventoryId,
        customer.id,
        shipTo.id
      );

      if (details.id) {
        const itemId = item.id,
          { customerItemCode, retail, upc, weightsAndMeasures } = details;
        dispatch(setItemCustomerItemCode({ itemId, value: customerItemCode }));
        dispatch(setItemRetail({ itemId, value: retail }));
        dispatch(setItemUPC({ itemId, value: upc }));
        dispatch(
          setItemWeightsAndMeasures({ itemId, value: weightsAndMeasures })
        );

        const message: ToastMessage = {
          type: 'success',
          title: 'Defaults restored',
          message: 'Default values were restored.',
        };
        dispatch(addToast(message));

        return true;
      }
    }

    const message: ToastMessage = {
      type: 'error',
      title: 'Defaults not found',
      message: 'No default values found.',
    };
    dispatch(addToast(message));
  };

  const handleSpecialPriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSpecialPrice(e.target.value);
  };

  const handleHasSpecialPricingChange = (value: boolean) => {
    setHasSpecialPricing(value);
    if (value) {
      window.setTimeout(() => specialPricingRef.current?.focus(), 100);
    } else {
      setSpecialPrice('');
      dispatch(setItemSpecialPrice({ itemId, value: null }));
    }
  };

  const handleSpecialPriceBlur = () => {
    const value = parseNullableCurrency(specialPrice),
      price = value == null ? '' : formatNumber(value, '0,0.00');

    setSpecialPrice(price);
    dispatch(setItemSpecialPrice({ itemId, value }));
  };

  return (
    <>
      <tr
        // @ts-ignore
        ref={drop}
        className={classNames(
          isOver ? 'border-t-4 border-t-gray-500' : 'border-transparent'
        )}
      >
        <td
          rowSpan={2}
          className={classNames(cellClassName, 'pl-4 align-top text-xs')}
        >
          <div className="flex flex-row">
            <button
              type="button"
              className="btn-secondary h-8 w-8 px-2 py-1"
              onClick={handleCollapseClick}
              tabIndex={-1}
            >
              <Icon icon="minus" />
            </button>
            <div className="ml-4">
              <label className="block text-xs italic text-gray-500">
                Product
              </label>
              <div className="flex flex-col">
                <button
                  type="button"
                  className="text-left text-sm font-medium text-gray-500 hover:text-blue-700 hover:underline"
                  onClick={handleInventoryItemClick}
                  tabIndex={-1}
                >
                  {item.spirePartNumber}
                </button>
                <div className="max-w-[10rem] cursor-default whitespace-normal">
                  {item.description}
                </div>
              </div>
            </div>
          </div>
          <Inventory
            open={showEditDialog}
            cancel={handleInventoryItemCancel}
            confirm={handleInventoryItemConfirm}
            customer={customer}
            shipTo={shipTo}
            requiredDate={requiredDate}
            seasonName={seasonName}
            customerItemCodeDefaults={customerItemCodeDefaults}
          />
        </td>
        <td className={cellClassName}>
          <label className="block text-left text-xs italic text-gray-500">
            Order Quantity
          </label>
          <input
            type="text"
            name={`item-order-quantity-${item.id}`}
            ref={quantityRef}
            autoFocus={item.id <= 0}
            autoComplete="off"
            value={orderQuantity}
            onChange={handleOrderQuantityChange}
            onBlur={handleOrderQuantityBlur}
            onFocus={handleFocus}
            className={classNames(
              inputClassName,
              'max-w-[100px] text-right',
              isOverbooked && 'border-red-500'
            )}
          />
        </td>
        <td className={classNames(cellClassName, 'text-center')}>
          <label className="block text-left text-xs italic text-gray-500">
            Pot Cover
          </label>
          <div className="flex max-w-[240px] flex-row rounded-md border border-gray-300 shadow-sm">
            <div className="flex items-center px-2">
              <input
                type="checkbox"
                checked={item.hasPotCover}
                onChange={handleHasPotCoverChange}
                className="h-4 w-4 rounded border-gray-300 text-blue-600 ring-0 focus:ring-0"
              />
            </div>
            <input
              type="text"
              name={`item-pot-cover-${item.id}`}
              autoComplete="off"
              value={potCover || ''}
              onChange={handlePotCoverChange}
              onFocus={handleFocus}
              className={classNames(
                'flex flex-grow rounded-md border-transparent text-center text-xs shadow-sm placeholder:italic placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500',
                item.hasPotCover ? '' : 'invisible'
              )}
              ref={potCoverRef}
              placeholder="PC"
            />
            <div
              className={classNames(
                'flex px-2 pt-1',
                item.hasPotCover ? '' : 'invisible'
              )}
            >
              <DropdownMenu
                items={potCovers.map((text) => ({
                  text,
                  selected: handlePotCoverSelected,
                }))}
              />
            </div>
          </div>
        </td>
        <td className={cellClassName}>
          <label className="block text-left text-xs italic text-gray-500">
            UPC
          </label>
          <input
            type="text"
            name={`item-upc-${item.id}`}
            autoComplete="off"
            value={item.upc || ''}
            onChange={handleUPCChange}
            className={classNames(inputClassName, '!w-[140px] text-center')}
          />
        </td>
        <td className={cellClassName}>
          <label className="block text-left text-xs italic text-gray-500">
            Date Code
          </label>
          <input
            type="text"
            name={`item-date-code-${item.id}`}
            autoComplete="off"
            value={item.dateCode || ''}
            onChange={handleDateCodeChange}
            className={classNames(inputClassName, '!w-[100px] text-center')}
          />
        </td>
        <td className={cellClassName}>
          <label className="block text-left text-xs italic text-gray-500">
            Retail
          </label>
          <input
            type="text"
            name={`item-retail-${item.id}`}
            autoComplete="off"
            value={item.retail || ''}
            onChange={handleRetailChange}
            className={classNames(inputClassName, '!w-[100px] text-center')}
          />
        </td>
        <td className={classNames(cellClassName, 'align-top')}>
          <label className="block text-left text-xs italic text-gray-500">
            W & M
          </label>
          <input
            type="checkbox"
            name={`item-weights-and-measures-${item.id}`}
            checked={item.weightsAndMeasures}
            onChange={handleWeightsAndMeasuresChange}
            className="h-4 w-4 rounded border-gray-300 text-blue-600 ring-0 focus:ring-0"
          />
        </td>
        <td className={classNames(cellClassName, 'pr-4 text-center')}>
          <div className="flex flex-col">
            <HeadlessUI.Popover
              className="relative inline-block cursor-pointer"
              onMouseEnter={handleDuplicateTooltipMouseEnter}
              onMouseLeave={handleDuplicateTooltipMouseLeave}
            >
              <>
                <button
                  type="button"
                  className="btn-secondary mt-2 border-blue-500 px-2 py-1 text-blue-500"
                  onClick={handleDuplicateClick}
                  tabIndex={-1}
                >
                  <Icon icon="copy" />
                </button>
                <HeadlessUI.Transition
                  as={Fragment}
                  show={showDuplicateTooltip}
                  enter="transition ease-out duration-200"
                  enterFrom="opacity-0"
                  enterTo="opacity-100"
                  leave="transition ease-in duration-150"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <HeadlessUI.Popover.Panel
                    static
                    className="absolute left-0 top-1/2 z-10 -translate-x-full -translate-y-1/2 transform bg-yellow-50"
                  >
                    <div className="w-auto whitespace-nowrap rounded-lg border p-4 shadow-lg">
                      Duplicate Item
                    </div>
                  </HeadlessUI.Popover.Panel>
                </HeadlessUI.Transition>
              </>
            </HeadlessUI.Popover>
            <HeadlessUI.Popover
              className="relative inline-block cursor-pointer"
              onMouseEnter={handleRestoreDefaultsTooltipMouseEnter}
              onMouseLeave={handleRestoreDefaultsTooltipMouseLeave}
            >
              <>
                <button
                  type="button"
                  className="btn-secondary px-2 py-1"
                  onClick={handleRestoreDefaultsClick}
                  disabled={!customer || !shipTo}
                  tabIndex={-1}
                >
                  <Icon icon="undo" />
                </button>
                <HeadlessUI.Transition
                  as={Fragment}
                  show={showRestoreDefaultsTooltip}
                  enter="transition ease-out duration-200"
                  enterFrom="opacity-0"
                  enterTo="opacity-100"
                  leave="transition ease-in duration-150"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <HeadlessUI.Popover.Panel
                    static
                    className="absolute left-0 top-1/2 z-10 -translate-x-full -translate-y-1/2 transform bg-yellow-50"
                  >
                    <div className="w-auto whitespace-nowrap rounded-lg border p-4 shadow-lg">
                      Restore Item Defaults
                      <div className="text-xs italic">
                        (UPC, Retail, W&M and Customer Item Code)
                      </div>
                    </div>
                  </HeadlessUI.Popover.Panel>
                </HeadlessUI.Transition>
              </>
            </HeadlessUI.Popover>
            <HeadlessUI.Popover
              className="relative inline-block cursor-pointer"
              onMouseEnter={handleDeleteTooltipMouseEnter}
              onMouseLeave={handleDeleteTooltipMouseLeave}
            >
              <>
                <button
                  type="button"
                  className="btn-delete px-2 py-1"
                  onClick={handleDeleteClick}
                  tabIndex={-1}
                >
                  <Icon icon="trash" />
                </button>
                <HeadlessUI.Transition
                  as={Fragment}
                  show={showDeleteTooltip}
                  enter="transition ease-out duration-200"
                  enterFrom="opacity-0"
                  enterTo="opacity-100"
                  leave="transition ease-in duration-150"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <HeadlessUI.Popover.Panel
                    static
                    className="absolute left-0 top-1/2 z-10 -translate-x-full -translate-y-1/2 transform bg-yellow-50"
                  >
                    <div className="w-auto whitespace-nowrap rounded-lg border p-4 shadow-lg">
                      Delete Item
                    </div>
                  </HeadlessUI.Popover.Panel>
                </HeadlessUI.Transition>
              </>
            </HeadlessUI.Popover>
            <Alert
              title="Remove Item"
              message="Are you sure you want to remove this item?"
              colour="danger"
              open={showDeleteAlert}
              cancel={handleDeleteCancel}
              confirm={handleDeleteConfirm}
            />
          </div>
        </td>
      </tr>
      <tr className="border-b-transparent border-t-transparent">
        <td className={classNames(cellClassName, 'align-top')}>
          <div className="relative flex items-center justify-center">
            <div className="flex h-5 items-center">
              <input
                id={`approximate-${item.id}`}
                name="approximate"
                type="checkbox"
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                checked={item.isApproximate}
                onChange={handleIsApproximateChange}
              />
            </div>
            <div className="ml-2 text-xs">
              <label
                htmlFor={`approximate-${item.id}`}
                className="text-gray-700"
              >
                <Icon icon="plus-minus" />
              </label>
            </div>
          </div>
        </td>
        <td className={classNames(cellClassName, 'align-top text-xs')}>
          <div className="flex">
            <div className="mr-2 flex flex-row">
              {!item.useAvailabilityPricing && (
                <>
                  {!!packQuantity && (
                    <div>
                      <label
                        htmlFor={`item-each-price-${item.id}`}
                        className="block text-xs italic text-gray-500"
                      >
                        Price / ea
                      </label>
                      <div className="relative col-start-1 rounded-md shadow-sm">
                        <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                          <span className="text-xs text-gray-500">$</span>
                        </div>
                        <input
                          type="text"
                          id={`item-each-price-${item.id}`}
                          name={`item-each-price-${item.id}`}
                          autoComplete="off"
                          value={eachPrice || ''}
                          onChange={handleEachPriceChange}
                          onBlur={handleEachPriceBlur}
                          onFocus={handleFocus}
                          className={classNames(
                            inputClassName,
                            'block w-[100px] rounded-md border-gray-300 pl-7 text-right'
                          )}
                        />
                      </div>
                    </div>
                  )}
                  <div className="mx-2 flex flex-col">
                    <label
                      htmlFor={`item-unit-price-${item.id}`}
                      className="block text-xs italic text-gray-500"
                    >
                      Case Price
                    </label>
                    <div className="relative col-start-1 rounded-md shadow-sm">
                      <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                        <span className="text-xs text-gray-500">$</span>
                      </div>
                      <input
                        type="text"
                        id={`item-unit-price-${item.id}`}
                        name={`item-unit-price-${item.id}`}
                        autoComplete="off"
                        value={unitPrice || ''}
                        onChange={handleUnitPriceChange}
                        onBlur={handleUnitPriceBlur}
                        onFocus={handleFocus}
                        className={classNames(
                          inputClassName,
                          'block w-[100px] rounded-md border-gray-300 pl-7 text-right'
                        )}
                      />
                    </div>
                  </div>
                </>
              )}
              {item.useAvailabilityPricing && (
                <>
                  {!!packQuantity && !!eachSpirePrice && (
                    <div>
                      <label
                        htmlFor={`item-each-spire-price-${item.id}`}
                        className="block text-xs italic text-gray-500"
                      >
                        Price / ea
                      </label>
                      <div className="relative col-start-1 rounded-md shadow-sm">
                        <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                          <span className="text-xs text-gray-500">$</span>
                        </div>
                        <input
                          type="text"
                          id={`item-each-spire-price-${item.id}`}
                          autoComplete="off"
                          value={eachSpirePrice}
                          disabled
                          readOnly
                          className={classNames(
                            inputClassName,
                            'block w-full max-w-[100px] rounded-md border-transparent bg-white pl-7 text-right'
                          )}
                        />
                      </div>
                    </div>
                  )}
                  <div className="mx-2 flex flex-col">
                    <label
                      htmlFor={`item-spire-price-${item.id}`}
                      className="block text-xs italic text-gray-500"
                    >
                      Spire Price
                    </label>
                    <div className="relative col-start-1 rounded-md shadow-sm">
                      <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                        <span className="text-xs text-gray-500">$</span>
                      </div>
                      <input
                        type="text"
                        id={`item-spire-price-${item.id}`}
                        autoComplete="off"
                        value={
                          spirePrice ? formatNumber(spirePrice, '0,0.00') : '-'
                        }
                        readOnly
                        disabled
                        className={classNames(
                          inputClassName,
                          'block w-full max-w-[100px] rounded-md border-transparent bg-white pl-7 text-right'
                        )}
                      />
                    </div>
                  </div>
                </>
              )}
            </div>
            <HeadlessUI.Switch.Group as="div">
              <HeadlessUI.Switch.Label className="block text-xs italic text-gray-500">
                Availability Pricing
              </HeadlessUI.Switch.Label>
              <HeadlessUI.Switch
                checked={item.useAvailabilityPricing}
                onChange={handleUseAvailabilityPricingChange}
                className={classNames(
                  item.useAvailabilityPricing ? 'bg-blue-400' : 'bg-gray-200',
                  'relative mt-2 inline-flex h-4 w-8 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
                )}
              >
                <span
                  aria-hidden="true"
                  className={classNames(
                    item.useAvailabilityPricing
                      ? 'translate-x-4'
                      : 'translate-x-0',
                    'pointer-events-none inline-block h-3 w-3 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                  )}
                />
              </HeadlessUI.Switch>
            </HeadlessUI.Switch.Group>
          </div>
          {!!priceWarning && (
            <div className="mt-1 rounded bg-yellow-50 p-1 text-center text-xs italic">
              That price is quite low, are you sure it&apos;s correct?
              <HeadlessUI.Popover
                className="relative inline-block cursor-pointer"
                onMouseEnter={handlePriceWarningTooltipMouseEnter}
                onMouseLeave={handlePriceWarningTooltipMouseLeave}
              >
                <>
                  <button
                    type="button"
                    className="btn-secondary mb-1 ml-1 rounded-full px-1 py-0 text-sm text-blue-500 focus:ring-0"
                    tabIndex={-1}
                  >
                    <Icon icon="question-circle" />
                  </button>
                  <HeadlessUI.Transition
                    as={Fragment}
                    show={showPriceWarningTooltip}
                    enter="transition ease-out duration-200"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="transition ease-in duration-150"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                  >
                    <HeadlessUI.Popover.Panel
                      static
                      className="absolute left-1/2 top-0 z-10 -translate-x-1/2 -translate-y-full transform bg-yellow-50"
                    >
                      <div className="w-auto whitespace-nowrap rounded-lg border p-4 shadow-lg">
                        {priceWarning.message}
                      </div>
                    </HeadlessUI.Popover.Panel>
                  </HeadlessUI.Transition>
                </>
              </HeadlessUI.Popover>
            </div>
          )}
        </td>
        <td colSpan={4} className={cellClassName}>
          {!!labelWarning && (
            <div className="rounded bg-yellow-50 p-1 text-center text-xs italic">
              {labelWarning}
            </div>
          )}
          {!!colourWarning && (
            <div className="rounded bg-yellow-50 p-1 text-center text-xs italic">
              {colourWarning}
            </div>
          )}
          <textarea
            rows={2}
            className={classNames(
              inputClassName,
              'w-full placeholder:italic placeholder:text-gray-400'
            )}
            value={item.comments || ''}
            onChange={handleCommentsChange}
            ref={commentsRef}
            placeholder="Item Comments"
          />
          <br />
          <label className="block text-left text-xs italic text-gray-500">
            Grower Item Notes&nbsp;
            <span className="font-normal text-gray-400">(Prebook / PO)</span>
            &nbsp;
            <HeadlessUI.Popover className="relative inline-block">
              <HeadlessUI.Popover.Button
                className="btn-secondary mb-1 rounded-full px-1 py-0 text-sm text-blue-500 focus:ring-0"
                tabIndex={-1}
              >
                <Icon icon="question-circle" />
              </HeadlessUI.Popover.Button>
              <HeadlessUI.Transition
                as={Fragment}
                enter="transition ease-out duration-200"
                enterFrom="opacity-0 translate-y-1"
                enterTo="opacity-100 translate-y-0"
                leave="transition ease-in duration-150"
                leaveFrom="opacity-100 translate-y-0"
                leaveTo="opacity-0 translate-y-1"
              >
                <HeadlessUI.Popover.Panel className="absolute bottom-10 z-10 -translate-x-1/2 transform px-4">
                  <div className="rounded-lg bg-yellow-50 p-4 font-normal shadow-lg ring-1 ring-black ring-opacity-5">
                    <p>
                      Grower Item notes will be included for this item on the
                      Prebook and the Spire PO.
                    </p>
                    <p>
                      It <span className="font-semibold italic">will not</span>{' '}
                      be displayed on the Customer&apos;s Invoice or Packing
                      Slip.
                    </p>
                  </div>
                </HeadlessUI.Popover.Panel>
              </HeadlessUI.Transition>
            </HeadlessUI.Popover>
          </label>
          <textarea
            rows={2}
            className={classNames(
              inputClassName,
              'w-full placeholder:italic placeholder:text-gray-400'
            )}
            value={item.growerItemNotes || ''}
            onChange={handleGrowerItemNotesChange}
            placeholder="Grower Item Notes"
          />
        </td>
      </tr>
      <tr className="border border-b-2 border-gray-200 border-t-transparent">
        <td className={classNames(cellClassName, 'align-top')}>
          <div className="ml-auto mr-2 w-32">
            <HeadlessUI.Switch.Group as="div" className="mt-2">
              <HeadlessUI.Switch
                checked={hasSpecialPricing}
                onChange={handleHasSpecialPricingChange}
                className={classNames(
                  hasSpecialPricing ? 'bg-blue-400' : 'bg-gray-200',
                  'relative inline-flex h-4 w-8 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
                )}
              >
                <span
                  aria-hidden="true"
                  className={classNames(
                    hasSpecialPricing ? 'translate-x-4' : 'translate-x-0',
                    'pointer-events-none inline-block h-3 w-3 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                  )}
                />
              </HeadlessUI.Switch>
              <HeadlessUI.Switch.Label className="ml-2">
                <span className="text-xs">Special Pricing</span>
              </HeadlessUI.Switch.Label>
            </HeadlessUI.Switch.Group>
            {hasSpecialPricing && (
              <div>
                <label
                  htmlFor={`item-special-price-${item.id}`}
                  className="block text-xs italic text-gray-500"
                >
                  Price
                </label>
                <div className="relative col-start-1 rounded-md shadow-sm">
                  <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                    <span className="text-xs text-gray-500">$</span>
                  </div>
                  <input
                    type="text"
                    id={`item-special-price-${item.id}`}
                    name={`item-special-price-${item.id}`}
                    autoComplete="off"
                    ref={specialPricingRef}
                    value={specialPrice || ''}
                    onChange={handleSpecialPriceChange}
                    onBlur={handleSpecialPriceBlur}
                    onFocus={handleFocus}
                    className={classNames(
                      inputClassName,
                      'block w-[100px] rounded-md border-gray-300 pl-7 text-right'
                    )}
                  />
                </div>
              </div>
            )}
          </div>
        </td>
        <td className={classNames(cellClassName, 'align-top')}>
          <div className="grid h-full w-32 grid-cols-1">
            <HeadlessUI.Switch.Group as="div" className="mt-2">
              <HeadlessUI.Switch
                checked={item.upgradeSheet}
                onChange={handleUpgradeSheetChange}
                className={classNames(
                  item.upgradeSheet ? 'bg-blue-400' : 'bg-gray-200',
                  'relative inline-flex h-4 w-8 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
                )}
              >
                <span
                  aria-hidden="true"
                  className={classNames(
                    item.upgradeSheet ? 'translate-x-4' : 'translate-x-0',
                    'pointer-events-none inline-block h-3 w-3 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                  )}
                />
              </HeadlessUI.Switch>
              <HeadlessUI.Switch.Label className="ml-2">
                <span className="text-xs">Upgrade Sheet</span>
              </HeadlessUI.Switch.Label>
            </HeadlessUI.Switch.Group>
            <div>
              <HeadlessUI.Switch.Group as="div" className="mt-2">
                <HeadlessUI.Switch
                  checked={item.phytoRequired}
                  onChange={handlePhytoRequiredChange}
                  className={classNames(
                    item.phytoRequired ? 'bg-blue-400' : 'bg-gray-200',
                    'relative inline-flex h-4 w-8 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
                  )}
                >
                  <span
                    aria-hidden="true"
                    className={classNames(
                      item.phytoRequired ? 'translate-x-4' : 'translate-x-0',
                      'pointer-events-none inline-block h-3 w-3 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                    )}
                  />
                </HeadlessUI.Switch>
                <HeadlessUI.Switch.Label className="ml-2">
                  <span className="text-xs">Requires Phyto</span>
                </HeadlessUI.Switch.Label>
              </HeadlessUI.Switch.Group>
            </div>
          </div>
        </td>

        <td colSpan={3} className={classNames(cellClassName, 'align-top')}>
          <div className="grid grid-cols-3">
            <label
              htmlFor={`vendor-${item.id}`}
              className="col-span-2 block text-xs italic text-gray-500"
            >
              Vendor
            </label>
            {!!availableBlanketItems.length && (
              <label
                htmlFor={`blanket-${item.id}`}
                className="block text-xs italic text-gray-500"
              >
                Blanket Prebook
              </label>
            )}
            {item.createPrebook &&
              !!availableBoekestynOrders.length &&
              !item.boekestynProducts.length && (
                <label
                  htmlFor={`boekestyn-order-${item.id}`}
                  className="block text-xs italic text-gray-500"
                >
                  Boekestyn Order
                </label>
              )}
            <HeadlessUI.Combobox
              as="div"
              value={vendor}
              onChange={handleVendorChange}
              nullable
              className="col-span-2"
            >
              <div className="relative">
                <HeadlessUI.Combobox.Input
                  id={`vendor-${item.id}`}
                  className="w-full rounded-md border border-gray-300 bg-white py-2 pl-3 pr-10 text-xs shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  autoComplete="off"
                  displayValue={(vendor: spire.Vendor | null) =>
                    vendor?.name || 'Choose Vendor'
                  }
                  onChange={handleVendorFilterChange}
                  onFocus={handleFocus}
                />
                <HeadlessUI.Combobox.Button className="absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none">
                  <Icon
                    icon="caret-down"
                    className="h-5 w-5 text-gray-400"
                    aria-hidden="true"
                  />
                </HeadlessUI.Combobox.Button>

                {filteredVendors.length > 0 && (
                  <HeadlessUI.Combobox.Options className="absolute z-40 mt-1 max-h-60 overflow-y-auto rounded-md bg-white py-1 text-xs shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                    <HeadlessUI.Combobox.Option
                      value={null}
                      className={({ active }) =>
                        classNames(
                          'relative cursor-default select-none py-2 pl-3 pr-9',
                          active ? 'bg-blue-600 text-white' : 'text-gray-900'
                        )
                      }
                    >
                      {({ selected }) => (
                        <span
                          className={classNames(
                            'truncate',
                            selected && 'font-semibold'
                          )}
                        >
                          No Vendor
                        </span>
                      )}
                    </HeadlessUI.Combobox.Option>
                    {filteredVendors.map((v) => (
                      <HeadlessUI.Combobox.Option
                        key={v.id}
                        value={v}
                        className={({ active }) =>
                          classNames(
                            'relative cursor-default select-none py-2 pl-3 pr-9',
                            active ? 'bg-blue-600 text-white' : 'text-gray-900'
                          )
                        }
                      >
                        {({ active, selected }) => (
                          <>
                            <span
                              className={classNames(
                                'truncate',
                                selected && 'font-semibold'
                              )}
                            >
                              {v.name}
                            </span>

                            {!!v.vendorNo && (
                              <span
                                className={classNames(
                                  'ml-2 truncate text-gray-500',
                                  active ? 'text-indigo-200' : 'text-gray-500'
                                )}
                              >
                                ({v.vendorNo})
                              </span>
                            )}
                            {!!vendor && selected && (
                              <span
                                className={classNames(
                                  'absolute inset-y-0 right-0 flex items-center pr-4',
                                  active ? 'text-white' : 'text-blue-600'
                                )}
                              >
                                <Icon
                                  icon="check"
                                  className="h-5 w-5"
                                  aria-hidden="true"
                                />
                              </span>
                            )}
                          </>
                        )}
                      </HeadlessUI.Combobox.Option>
                    ))}
                  </HeadlessUI.Combobox.Options>
                )}
              </div>
            </HeadlessUI.Combobox>

            {!!availableBlanketItems.length && (
              <select
                id={`blanket-${item.id}`}
                className={classNames(
                  'w-full rounded-md text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500',
                  isOverbooked ? 'border-red-500' : 'border-gray-300'
                )}
                value={item.blanketItemId || ''}
                onChange={handleBlanketItemChange}
              >
                <option value="">No Blanket</option>
                {availableBlanketItems.map((blanketItem) => (
                  <BlanketItemOption
                    key={blanketItem.id}
                    blanketItem={blanketItem}
                  />
                ))}
              </select>
            )}
            {item.createPrebook && !!availableBoekestynOrders.length && (
              <>
                {!!item.boekestynProducts.length && (
                  <button
                    type="button"
                    className="btn-secondary mx-2 px-2 py-1 !font-normal"
                    onClick={handleBoekestynProductClick}
                  >
                    Boek Products&nbsp;
                    <Icon icon="arrow-right" />
                  </button>
                )}
                {!item.boekestynProducts.length && (
                  <select
                    className={classNames(
                      'w-full rounded-md text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500'
                    )}
                    value={makeOptionKey(
                      item.boekestynPlantId,
                      item.boekestynCustomerAbbreviation
                    )}
                    onChange={handleBoekestynProductionOrderChange}
                  >
                    <option value="">No Production Order</option>
                    {availableBoekestynOrders.map((order) => (
                      <ItemBoekestynOption
                        key={`${order.plantId}-${order.customer}`}
                        order={order}
                      />
                    ))}
                  </select>
                )}
                <div className="pl-3 text-xs italic">
                  Week {week}, {year}
                </div>
              </>
            )}
            <HeadlessUI.Switch.Group
              as="div"
              className="col-start-1 text-center"
            >
              <HeadlessUI.Switch
                checked={item.createPrebook}
                onChange={handleCreatePrebookChange}
                className={classNames(
                  item.createPrebook ? 'bg-blue-400' : 'bg-gray-200',
                  'relative inline-flex h-4 w-8 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
                )}
              >
                <span
                  aria-hidden="true"
                  className={classNames(
                    item.createPrebook ? 'translate-x-4' : 'translate-x-0',
                    'pointer-events-none inline-block h-3 w-3 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                  )}
                />
              </HeadlessUI.Switch>
              <HeadlessUI.Switch.Label className="ml-2">
                <span className="text-xs">Create Prebook</span>
              </HeadlessUI.Switch.Label>
            </HeadlessUI.Switch.Group>
            {item.createPrebook && (
              <HeadlessUI.Switch.Group as="div" className="text-center">
                <HeadlessUI.Switch
                  checked={item.isBlanket}
                  onChange={handlePrebookIsBlanketChange}
                  className={classNames(
                    item.isBlanket ? 'bg-blue-400' : 'bg-gray-200',
                    'relative inline-flex h-4 w-8 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
                  )}
                >
                  <span
                    aria-hidden="true"
                    className={classNames(
                      item.isBlanket ? 'translate-x-4' : 'translate-x-0',
                      'pointer-events-none inline-block h-3 w-3 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                    )}
                  />
                </HeadlessUI.Switch>
                <HeadlessUI.Switch.Label className="ml-2">
                  <span className="text-xs">Prebook is Blanket</span>
                </HeadlessUI.Switch.Label>
              </HeadlessUI.Switch.Group>
            )}
            {isOverbooked && (
              <div className="col-start-3 text-center text-xs italic text-red-500">
                Order quantity will overbook blanket.
              </div>
            )}
          </div>
        </td>
        <td colSpan={2} className={classNames(cellClassName, 'align-top')}>
          <label className="block text-left text-xs italic text-gray-500">
            Customer Item Code
          </label>
          <input
            type="text"
            name={`item-customer-item-code-${item.id}`}
            maxLength={20}
            autoComplete="off"
            value={item.customerItemCode || ''}
            onChange={handleCustomerItemCodeChange}
            className={classNames(inputClassName, '!w-[150px]')}
          />
        </td>
        <td className={classNames(cellClassName, 'align-top')}></td>
      </tr>
    </>
  );
}
