import React, { useRef, useState } from 'react';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { Icon } from '@/components/icon';
import { potCovers as defaultPotCovers } from '@/components/pot-covers';
import {
  selectPotCovers,
  addPotCover,
  deletePotCover,
} from './customer-settings-slice';

export function CustomerPotCovers() {
  const dispatch = useAppDispatch(),
    potCovers = useAppSelector(selectPotCovers),
    [newPotCover, setNewPotCover] = useState(''),
    newRef = useRef<HTMLInputElement>(null);

  const handleNewPotCoverChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewPotCover(e.target.value);
  };

  const handleNewPotCoverKeyUp = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleAddClick();
    }
  };

  const handleAddClick = () => {
    if (newPotCover) {
      dispatch(addPotCover(newPotCover));
    }

    setNewPotCover('');
    window.setTimeout(() => newRef.current?.focus(), 100);
  };

  const handleDeleteClick = (potCover: string) => {
    dispatch(deletePotCover(potCover));
  };

  return (
    <div>
      <h1 className="m-4 border-b p-2 text-4xl">Pot Covers</h1>
      <p className="m-4 border-b italic">
        This list will be shown in the dropdown for Future Order and Prebook
        items. If the list is blank, the default will be shown:
        <ul className="p-4 text-xs">
          {defaultPotCovers.map((potCover) => (
            <li key={potCover} className="my-2">
              {potCover}
            </li>
          ))}
        </ul>
        NOTE: Removing items from this list will{' '}
        <strong className="font-semibold">not</strong> affect existing Future
        Orders or Prebooks, it will only change the options that are listed.
      </p>

      <ul className="flex w-96 flex-col pl-8">
        {potCovers.map((potCover) => (
          <li key={potCover} className="flex flex-row border-b py-2">
            <div className="flex-grow truncate">{potCover}</div>
            <div className="w-12">
              <button
                type="button"
                className="btn-delete px-2 py-1"
                onClick={() => handleDeleteClick(potCover)}
              >
                <Icon icon="trash" />
              </button>
            </div>
          </li>
        ))}
        <li className="flex flex-row border-b py-2">
          <div className="mr-8 flex-grow">
            <input
              type="text"
              className="w-full rounded-md border-gray-300 text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
              ref={newRef}
              value={newPotCover}
              onChange={handleNewPotCoverChange}
              onKeyUp={handleNewPotCoverKeyUp}
              placeholder="Add Pot Cover"
            />
          </div>
          <div className="w-12">
            <button
              type="button"
              className="btn-new px-2 py-1"
              onClick={handleAddClick}
            >
              <Icon icon="plus-square" />
            </button>
          </div>
        </li>
      </ul>
    </div>
  );
}
