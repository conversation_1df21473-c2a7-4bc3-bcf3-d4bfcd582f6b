import React, { Fragment, useEffect } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { useAccount } from '@azure/msal-react';
import { apiUrl } from 'api/api-base';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import {
  selectBcc,
  selectBody,
  selectSubject,
  selectTo,
  setTo,
  setCc,
  setBcc,
  setSubject,
  setBody,
  clearState,
  setAccount,
  clearError,
  setError,
  selectError,
  selectIsLoading,
  getPrebookBatchEmail,
  createPrebookBatchEmail,
  selectCc,
} from './email-batch-slice';
import {
  selectFilter,
  selectSelectedItems,
  selectSelectedPrebooks,
} from './prebook-list-slice';
import { useVendorsQuery } from 'api/spire-service';
import { formatNumber } from '@/utils/format';

export interface EmailBatchProps {
  open: boolean;
  close: () => void;
  cancel: () => void;
}

export function EmailBatch({ close, open, cancel }: EmailBatchProps) {
  const dispatch = useAppDispatch(),
    account = useAccount(),
    to = useAppSelector(selectTo),
    cc = useAppSelector(selectCc),
    bcc = useAppSelector(selectBcc),
    subject = useAppSelector(selectSubject),
    body = useAppSelector(selectBody),
    error = useAppSelector(selectError),
    isLoading = useAppSelector(selectIsLoading),
    { vendor } = useAppSelector(selectFilter),
    selectedItems = useAppSelector(selectSelectedItems),
    selectedPrebooks = useAppSelector(selectSelectedPrebooks),
    { data: vendors } = useVendorsQuery(),
    queryString = selectedItems.map((i) => `ids=${i}`).join('&');

  useEffect(() => {
    const selectedVendor = vendors?.find((v) => v.name === vendor);
    if (selectedVendor) {
      dispatch(getPrebookBatchEmail(selectedVendor.id));
    }
  }, [dispatch, vendor, vendors]);

  useEffect(() => {
    dispatch(setAccount(account));
  }, [dispatch, account]);

  useEffect(() => {
    if (open) {
      const prebookIds =
          selectedPrebooks.length > 3
            ? ''
            : ' ' +
              selectedPrebooks
                .map((p) => '#' + formatNumber(p.id, '00000'))
                .join(', '),
        uniqueBoxCodes = selectedPrebooks
          .map((p) => p.boxCode)
          .filter((v, i, a) => a.indexOf(v) === i),
        uniqueRequiredDates = selectedPrebooks
          .map((p) => p.requiredDate)
          .filter((v, i, a) => a.indexOf(v) === i),
        boxCode = uniqueBoxCodes.length === 1 ? ` ${uniqueBoxCodes[0]}` : '',
        requiredDate =
          uniqueRequiredDates.length === 1
            ? ` - ${uniqueRequiredDates[0]}`
            : '',
        subject = `Prebooks (${selectedPrebooks.length})${prebookIds}${boxCode}${requiredDate}`,
        prebookList = selectedPrebooks
          .map(
            (p) =>
              ` - Prebook #${formatNumber(p.id, '00000')},  Box Code: ${
                p.boxCode
              }, Required Date: ${p.requiredDate}`
          )
          .join('\n'),
        body = `Attached (as PDF files) are our PREBOOKS (please note there are ${selectedPrebooks.length}):\n\n${prebookList}\n\nPlease confirm all details at your earliest convenience.\n\nThank you,\n${account?.name}`;

      dispatch(setSubject(subject));
      dispatch(setBody(body));
    }
  }, [dispatch, open, selectedPrebooks, account]);

  const handleToChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setTo(e.target.value));
  };

  const handleCcChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setCc(e.target.value || null));
  };

  const handleBccChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setBcc(e.target.value || null));
  };

  const handleSubjectChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setSubject(e.target.value));
  };

  const handleBodyChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    dispatch(setBody(e.target.value));
  };

  const save = async () => {
    if (!to) {
      dispatch(setError('Please enter the To Address.'));
    } else if (!subject) {
      dispatch(setError('Please enter the Subject.'));
    } else if (!body) {
      dispatch(setError('Please enter the Body of the email.'));
    } else {
      const response = await dispatch(createPrebookBatchEmail(selectedItems));
      if (response.meta.requestStatus === 'fulfilled') {
        return true;
      }
    }

    return false;
  };

  const handleSaveClick = async () => {
    const saved = await save();
    if (saved) {
      dispatch(clearState());
      close();
    }
  };

  const handleClearErrorClick = () => {
    dispatch(clearError());
  };

  return (
    <Transition.Root as={Fragment} show={open}>
      <Dialog as="div" onClose={cancel} className="relative z-30">
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-30 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative h-screen w-screen transform overflow-hidden p-6 transition-all">
                <div className="flex h-full flex-col rounded-lg bg-white p-6 text-left shadow-xl">
                  <div className="mb-4 flex justify-center border-b-2 pb-4">
                    <Dialog.Title
                      as="h3"
                      className="text-lg font-medium leading-6 text-gray-900"
                    >
                      <Icon
                        icon="envelope"
                        className="h-6 w-6"
                        aria-hidden="true"
                      />
                      &nbsp; Send Prebook Email
                    </Dialog.Title>
                  </div>
                  <Error
                    error={error}
                    clear={handleClearErrorClick}
                    containerClasses="w-full mb-4"
                  />
                  <div className="flex flex-grow flex-col">
                    <form className="flex w-full">
                      <div className="grid w-full grid-cols-6 items-start gap-4">
                        <label
                          htmlFor="to"
                          className="col-start-1 mt-px block grid-cols-2 pt-2 text-right text-sm font-medium text-gray-700"
                        >
                          To
                        </label>
                        <input
                          type="text"
                          name="to"
                          id="to"
                          tabIndex={0}
                          value={to}
                          onChange={handleToChange}
                          className="col-span-2 block w-full min-w-0 flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        />

                        <div className="col-span-3 row-span-4">
                          <label
                            htmlFor="body"
                            className="mt-px block pt-2 text-sm font-medium text-gray-700"
                          >
                            Body
                          </label>
                          <textarea
                            name="body"
                            id="body"
                            tabIndex={3}
                            rows={7}
                            value={body}
                            onChange={handleBodyChange}
                            className="col-span-3 block w-full min-w-0 flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                          />
                        </div>

                        <label
                          htmlFor="cc"
                          className="col-start-1 mt-px block grid-cols-2 pt-2 text-right text-sm font-medium text-gray-700"
                        >
                          CC
                        </label>
                        <div className="col-span-2">
                          <div className="flex max-w-lg rounded-md shadow-sm">
                            <input
                              type="text"
                              name="cc"
                              id="cc"
                              tabIndex={1}
                              value={cc || ''}
                              onChange={handleCcChange}
                              className="block w-full min-w-0 flex-1 rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                            />
                          </div>
                        </div>
                        <label
                          htmlFor="bcc"
                          className="col-start-1 mt-px block grid-cols-2 pt-2 text-right text-sm font-medium text-gray-700"
                        >
                          BCC
                        </label>
                        <div className="col-span-2">
                          <div className="flex max-w-lg rounded-md shadow-sm">
                            <input
                              type="text"
                              name="bcc"
                              id="bcc"
                              tabIndex={1}
                              value={bcc || ''}
                              onChange={handleBccChange}
                              className="block w-full min-w-0 flex-1 rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                            />
                          </div>
                        </div>
                        <label
                          htmlFor="subject"
                          className="col-start-1 mt-px block grid-cols-2 pt-2 text-right text-sm font-medium text-gray-700"
                        >
                          Subject
                        </label>
                        <div className="relative col-span-2 mt-1 rounded-md shadow-sm">
                          <input
                            type="text"
                            name="subject"
                            id="subject"
                            tabIndex={2}
                            value={subject}
                            onChange={handleSubjectChange}
                            className="block w-full min-w-0 flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                          />
                        </div>
                        <p className="col-span-2 col-start-2 mt-2 text-sm text-gray-500">
                          Separate multiple email addresses with semicolons (;)
                        </p>
                      </div>
                    </form>
                  </div>
                  <div className="mt-4 grid h-full grid-cols-6 gap-4 text-right">
                    <div>
                      <a
                        href={apiUrl(
                          `reports/prebooks/batch?download&${queryString}`
                        )}
                        className="btn-secondary inline-block"
                        tabIndex={-1}
                      >
                        <Icon icon="download"></Icon>
                        &nbsp; Download PDF
                      </a>
                    </div>
                    <iframe
                      title="Prebook preview"
                      src={apiUrl(`reports/prebooks/batch?${queryString}`)}
                      className="col-span-5 col-start-2 h-full w-full"
                    ></iframe>
                  </div>
                  <div className="mt-4 flex justify-end border-t-2 pt-4">
                    <button
                      type="button"
                      className="btn-secondary text-lg"
                      onClick={cancel}
                    >
                      Cancel
                    </button>
                    <button
                      type="button"
                      className="btn-secondary ml-4 text-lg"
                      onClick={handleSaveClick}
                      disabled={isLoading}
                    >
                      Send
                      <Icon
                        icon={isLoading ? 'spinner' : 'send'}
                        className="ml-2"
                        spin={isLoading}
                      />
                    </button>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
}
