import { useState } from 'react';
import { attachmentUrl } from 'api/api-base';
import { prebooksApi } from 'api/prebooks-service';
import * as futureOrders from 'api/models/future-orders';
import * as prebooks from 'api/models/prebooks';
import { Icon } from '@/components/icon';
import { classNames } from '@/utils/class-names';
import { FutureOrderPrebookEmailImageAddButton } from './future-order-prebook-email-image-add-button';

export interface FutureOrderPrebookEmailImageProps {
  upgrade: prebooks.PrebookDetailItem;
  attachments: futureOrders.UpgradeItemAttachment[];
  onAdded: (attachment: futureOrders.UpgradeItemAttachment) => void;
  onDeleted: (id: number) => void;
}

export function FutureOrderPrebookEmailImage({
  upgrade,
  attachments,
  onAdded,
  onDeleted,
}: FutureOrderPrebookEmailImageProps) {
  const [itemAttachments, setItemAttachments] = useState(
      attachments.reduce((memo, a) => {
        if (
          !memo.some((m) => m.filename === a.filename) &&
          a.prebookItemId === upgrade.id
        ) {
          memo.push(a);
        }
        return memo;
      }, [] as futureOrders.UpgradeItemAttachment[])
    ),
    [isOver, setIsOver] = useState(false);

  const handleAddedClick = (attachment: futureOrders.UpgradeItemAttachment) => {
    setItemAttachments([...itemAttachments, attachment]);
    onAdded(attachment);
  };

  const handleDeleteClick = async (id: number) => {
    await prebooksApi.deleteUpgradeAttachment(id);
    setItemAttachments(itemAttachments.filter((a) => a.id !== id));
    onDeleted(id);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsOver(true);
  };

  const handleDragLeave = () => {
    setIsOver(false);
  };

  const handleDrop = async (e: React.DragEvent<HTMLTableRowElement>) => {
    e.preventDefault();

    const file = e.dataTransfer.files?.[0];

    if (file) {
      const args = { file, prebookItemIds: [upgrade.id] },
        response = await prebooksApi.addUpgradeAttachment(args);

      if ('data' in response) {
        for (const attachment of response.data.attachments) {
          setItemAttachments([...itemAttachments, attachment]);
          onAdded(attachment);
        }
      }
    }

    setIsOver(false);
  };

  return (
    <tr
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      className={classNames(isOver ? 'border border-green-700' : '')}
    >
      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
        {upgrade.spirePartNumber}
      </td>
      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
        {upgrade.description}
      </td>
      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
        <FutureOrderPrebookEmailImageAddButton
          id={upgrade.id}
          onAdded={handleAddedClick}
        />
      </td>
      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
        <div className="flex flex-row flex-wrap justify-center gap-2 bg-white">
          {itemAttachments.map((attachment) => (
            <div key={attachment.filename} className="relative">
              <img
                src={attachmentUrl(attachment.filename)}
                alt={attachment.filename}
                className="h-32 w-auto"
              />
              <button
                type="button"
                className="absolute right-0 top-0"
                onClick={() => handleDeleteClick(attachment.id)}
              >
                <Icon icon="x" className="text-red-700" />
              </button>
            </div>
          ))}
        </div>
      </td>
    </tr>
  );
}
