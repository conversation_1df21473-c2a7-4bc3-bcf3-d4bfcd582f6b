import * as prebooks from 'api/models/prebooks';
import { formatDate, formatNumber } from '@/utils/format';
import { weekFromId } from '@/utils/weeks';

interface BlanketItemOptionProps {
  blanketItem: prebooks.PrebookBlanketItem;
}

export function BlanketItemOption({ blanketItem }: BlanketItemOptionProps) {
  const week = weekFromId(blanketItem.blanketWeekId);
  return (
    <option key={blanketItem.id} value={blanketItem.id}>
      {!!blanketItem.blanketStartDate &&
        `${formatDate(blanketItem.blanketStartDate, 'MMM d')} - `}
      {formatDate(blanketItem.requiredDate, 'MMM d')}
      {!!week && ` Week ${week.week}`}
      {` (${formatNumber(blanketItem.blanketQuantity)})`}
    </option>
  );
}
