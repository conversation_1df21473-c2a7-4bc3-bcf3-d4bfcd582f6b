import {
  AsyncThunk,
  createAction,
  createAsyncThunk,
  createSelector,
  createSlice,
  PayloadAction,
} from '@reduxjs/toolkit';
import { DateTime } from 'luxon';
import {
  DateGrouping,
  prebookListApi,
  PrebookListDownloadArgs,
  ItemSummaryDownloadArgs,
  BlanketItemDownloadArgs,
  prebooksApi,
  OverAndAboveDownloadArgs,
} from 'api/prebooks-service';
import * as models from 'api/models/prebooks';
import { RootState } from '@/services/store';
import { ProblemDetails } from '@/utils/problem-details';
import { contains, equals, startsWith } from '@/utils/equals';
import { sortBy, sort } from '@/utils/sort';
import { formatNumber } from '@/utils/format';

export type BlanketFilter = '' | 'Blanket' | 'Non-Blanket';
export const blankSalespersonFilter = 'No Salesperson';
export const blankShipToFilter = 'No Ship To';
export const blankCustomerFilter = 'No Customer';

const sortById = sortBy('id');

interface PrebookFilters {
  vendor: string | null;
  customer: string | null;
  shipTo: string | null;
  salesperson: string | null;
  season: string | null;
  partNumber: string | null;
  blanket: BlanketFilter;
  includeSpirePurchaseOrders: boolean;
  includeDeleted: boolean;
}

interface ItemOptions {
  groupByVendor: boolean;
  groupByCustomer: boolean;
  groupByShipTo: boolean;
  groupBySalesperson: boolean;
  groupBySeason: boolean;
  groupByIsBlanket: boolean;
  dateGrouping: DateGrouping;
}

interface PrebookListState {
  startDate: string;
  endDate: string;
  search: string;
  prebookSort: keyof models.PrebookListItem;
  sortPrebooksDescending: boolean;
  itemSearch: string;
  itemsSort: keyof models.PrebookSummaryItem;
  blanketItemsSort: keyof models.BlanketItemListItem;
  sortItemsDescending: boolean;
  filter: PrebookFilters;
  itemOptions: ItemOptions;
  prebooks: models.PrebookListItem[];
  items: models.PrebookSummaryItem[];
  blanketItems: models.BlanketItemListItem[];
  selectedItems: number[];
  isLoading: boolean;
  error: ProblemDetails | null;
}

const initialState: PrebookListState = {
  startDate: DateTime.now().toFormat('yyyy-MM-dd'),
  endDate: '',
  search: '',
  prebookSort: 'requiredDate',
  sortPrebooksDescending: false,
  itemSearch: '',
  itemsSort: 'spirePartNumber',
  blanketItemsSort: 'spirePartNumber',
  sortItemsDescending: false,
  filter: {
    vendor: null,
    customer: null,
    shipTo: null,
    salesperson: null,
    season: null,
    partNumber: null,
    blanket: '',
    includeSpirePurchaseOrders: false,
    includeDeleted: false,
  },
  itemOptions: {
    groupByVendor: true,
    groupByCustomer: true,
    groupByShipTo: true,
    groupBySalesperson: true,
    groupBySeason: true,
    groupByIsBlanket: true,
    dateGrouping: 'Date',
  },
  prebooks: [],
  items: [],
  blanketItems: [],
  selectedItems: [],
  isLoading: false,
  error: null,
};

export const downloadList: AsyncThunk<
  void,
  PrebookListDownloadArgs,
  { state: RootState }
> = createAsyncThunk(
  'prebook-list-downloadList',
  async (args, { rejectWithValue }) => {
    try {
      return await prebooksApi.prebookListDownload(args);
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const downloadItemSummary: AsyncThunk<
  void,
  ItemSummaryDownloadArgs,
  { state: RootState }
> = createAsyncThunk(
  'prebook-list-downloadItemSummary',
  async (args, { rejectWithValue }) => {
    try {
      return await prebooksApi.itemSummaryDownload(args);
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const downloadBlanketItems: AsyncThunk<
  void,
  BlanketItemDownloadArgs,
  { state: RootState }
> = createAsyncThunk(
  'prebook-list-downloadBlanketItems',
  async (args, { rejectWithValue }) => {
    try {
      return await prebooksApi.blanketItemDownload(args);
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const downloadOverAndAboveReport: AsyncThunk<
  void,
  OverAndAboveDownloadArgs,
  { state: RootState }
> = createAsyncThunk(
  'prebook-list-downloadOverAndAboveReport',
  async (args, { rejectWithValue }) => {
    try {
      return await prebooksApi.overAndAboveDownload(args);
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

const downloadListPending = createAction(downloadList.pending.type),
  downloadListFulfilled = createAction(downloadList.fulfilled.type),
  downloadListRejected = createAction<ProblemDetails>(
    downloadList.rejected.type
  ),
  downloadItemSummaryPending = createAction(downloadItemSummary.pending.type),
  downloadItemSummaryFulfilled = createAction(
    downloadItemSummary.fulfilled.type
  ),
  downloadItemSummaryRejected = createAction<ProblemDetails>(
    downloadItemSummary.rejected.type
  ),
  downloadBlanketItemsPending = createAction(downloadBlanketItems.pending.type),
  downloadBlanketItemsFulfilled = createAction(
    downloadBlanketItems.fulfilled.type
  ),
  downloadBlanketItemsRejected = createAction<ProblemDetails>(
    downloadBlanketItems.rejected.type
  ),
  downloadOverAndAbovePending = createAction(
    downloadOverAndAboveReport.pending.type
  ),
  downloadOverAndAboveFulfilled = createAction(
    downloadOverAndAboveReport.fulfilled.type
  ),
  downloadOverAndAboveRejected = createAction<ProblemDetails>(
    downloadOverAndAboveReport.rejected.type
  );

export interface PrebookSortArgs {
  sort: keyof models.PrebookListItem;
  sortDescending: boolean;
}

export interface ItemSortArgs {
  sort: keyof models.PrebookSummaryItem;
  sortDescending: boolean;
}

export interface BlanketItemSortArgs {
  sort: keyof models.BlanketItemListItem;
  sortDescending: boolean;
}

export const prebookListSlice = createSlice({
  name: 'prebook-list',
  initialState,
  reducers: {
    clearError(state) {
      state.error = null;
    },
    clearSearchAndFilters(state) {
      return {
        ...state,
        startDate: DateTime.now().toFormat('yyyy-MM-dd'),
        endDate: '',
        search: '',
        prebookSort: 'requiredDate',
        sortPrebooksDescending: false,
        filter: {
          vendor: null,
          customer: null,
          shipTo: null,
          salesperson: null,
          season: null,
          partNumber: null,
          blanket: '',
          includeSpirePurchaseOrders: false,
          includeDeleted: false,
        },
        itemOptions: {
          groupByVendor: true,
          groupByCustomer: true,
          groupByShipTo: true,
          groupBySalesperson: true,
          groupBySeason: true,
          groupByIsBlanket: true,
          dateGrouping: 'Date',
        },
      };
    },
    clearItemSearchAndFilters(state) {
      return {
        ...state,
        startDate: DateTime.now().toFormat('yyyy-MM-dd'),
        endDate: '',
        itemSearch: '',
        itemsSort: 'spirePartNumber',
        sortItemsDescending: false,
        filter: {
          vendor: null,
          customer: null,
          shipTo: null,
          salesperson: null,
          season: null,
          partNumber: null,
          blanket: '',
          includeSpirePurchaseOrders: false,
          includeDeleted: false,
        },
        itemOptions: {
          groupByVendor: true,
          groupByCustomer: true,
          groupByShipTo: true,
          groupBySalesperson: true,
          groupBySeason: true,
          groupByIsBlanket: true,
          dateGrouping: 'Date',
        },
      };
    },
    clearBlanketSearchAndFilters(state) {
      return {
        ...state,
        startDate: DateTime.now().toFormat('yyyy-MM-dd'),
        endDate: '',
        itemSearch: '',
        blanketItemsSort: 'spirePartNumber',
        sortItemsDescending: false,
        filter: {
          vendor: null,
          customer: null,
          shipTo: null,
          salesperson: null,
          season: null,
          partNumber: null,
          blanket: '',
          includeSpirePurchaseOrders: false,
          includeDeleted: false,
        },
        itemOptions: {
          groupByVendor: true,
          groupByCustomer: true,
          groupByShipTo: true,
          groupBySalesperson: true,
          groupBySeason: true,
          groupByIsBlanket: true,
          dateGrouping: 'Date',
        },
      };
    },
    setStartDate(state, { payload }: PayloadAction<string>) {
      state.startDate = payload;
      state.selectedItems = [];
    },
    setEndDate(state, { payload }: PayloadAction<string>) {
      state.endDate = payload;
      state.selectedItems = [];
    },
    setSearch(state, { payload }: PayloadAction<string>) {
      state.search = payload;
      state.selectedItems = [];
    },
    setPrebookSort(state, { payload }: PayloadAction<PrebookSortArgs>) {
      state.prebookSort = payload.sort;
      state.sortPrebooksDescending = payload.sortDescending;
    },
    setItemSort(state, { payload }: PayloadAction<ItemSortArgs>) {
      state.itemsSort = payload.sort;
      state.sortItemsDescending = payload.sortDescending;
    },
    setBlanketItemSort(state, { payload }: PayloadAction<BlanketItemSortArgs>) {
      state.blanketItemsSort = payload.sort;
      state.sortItemsDescending = payload.sortDescending;
    },
    setVendorFilter(state, { payload }: PayloadAction<string | null>) {
      const filter = { ...state.filter, vendor: payload };
      state.filter = filter;
      state.selectedItems = [];
    },
    setCustomerFilter(state, { payload }: PayloadAction<string | null>) {
      const filter = { ...state.filter, customer: payload };
      state.filter = filter;
      state.selectedItems = [];
    },
    setShipToFilter(state, { payload }: PayloadAction<string | null>) {
      const filter = { ...state.filter, shipTo: payload };
      state.filter = filter;
      state.selectedItems = [];
    },
    setSalespersonFilter(state, { payload }: PayloadAction<string | null>) {
      const filter = { ...state.filter, salesperson: payload };
      state.filter = filter;
      state.selectedItems = [];
    },
    setSeasonFilter(state, { payload }: PayloadAction<string | null>) {
      const filter = { ...state.filter, season: payload };
      state.filter = filter;
      state.selectedItems = [];
    },
    setBlanketFilter(state, { payload }: PayloadAction<BlanketFilter>) {
      const filter = { ...state.filter, blanket: payload };
      state.filter = filter;
      state.selectedItems = [];
    },
    setIncludeSpirePurchaseOrdersFilter(
      state,
      { payload }: PayloadAction<boolean>
    ) {
      const filter = { ...state.filter, includeSpirePurchaseOrders: payload };
      state.filter = filter;
      state.selectedItems = [];
    },
    setIncludeDeletedFilter(state, { payload }: PayloadAction<boolean>) {
      const filter = { ...state.filter, includeDeleted: payload };
      state.filter = filter;
      state.selectedItems = [];
    },
    setItemSearch(state, { payload }: PayloadAction<string>) {
      state.itemSearch = payload;
    },
    setPartNumberFilter(state, { payload }: PayloadAction<string | null>) {
      const filter = { ...state.filter, partNumber: payload };
      state.filter = filter;
    },
    setGroupByVendorItemOption(state, { payload }: PayloadAction<boolean>) {
      const options = { ...state.itemOptions, groupByVendor: payload };
      state.itemOptions = options;
    },
    setGroupByCustomerItemOption(state, { payload }: PayloadAction<boolean>) {
      const options = { ...state.itemOptions, groupByCustomer: payload };
      state.itemOptions = options;
    },
    setGroupByShipToItemOption(state, { payload }: PayloadAction<boolean>) {
      const options = { ...state.itemOptions, groupByShipTo: payload };
      state.itemOptions = options;
    },
    setGroupBySalespersonItemOption(
      state,
      { payload }: PayloadAction<boolean>
    ) {
      const options = { ...state.itemOptions, groupBySalesperson: payload };
      state.itemOptions = options;
    },
    setGroupBySeasonItemOption(state, { payload }: PayloadAction<boolean>) {
      const options = { ...state.itemOptions, groupBySeason: payload };
      state.itemOptions = options;
    },
    setGroupByIsBlanketItemOption(state, { payload }: PayloadAction<boolean>) {
      const options = { ...state.itemOptions, groupByIsBlanket: payload };
      state.itemOptions = options;
    },
    setDateGroupingItemOption(state, { payload }: PayloadAction<DateGrouping>) {
      const options = { ...state.itemOptions, dateGrouping: payload };
      state.itemsSort = payload === 'None' ? 'customer' : 'date';
      state.sortItemsDescending = false;

      state.itemOptions = options;
    },
    setSelectedItems(state, { payload }: PayloadAction<number | number[]>) {
      const selectedItems = Array.isArray(payload)
        ? payload
        : [...state.selectedItems, payload];
      state.selectedItems = selectedItems;
    },
    unsetSelectedItems(state, { payload }: PayloadAction<number | number[]>) {
      const selectedItems = Array.isArray(payload)
        ? []
        : state.selectedItems.filter((i) => i !== payload);
      state.selectedItems = selectedItems;
    },
  },
  extraReducers: (builder) =>
    builder
      .addCase(downloadListPending, (state) => {
        state.isLoading = true;
      })
      .addCase(downloadListFulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(downloadListRejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      .addCase(downloadItemSummaryPending, (state) => {
        state.isLoading = true;
      })
      .addCase(downloadItemSummaryFulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(downloadItemSummaryRejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      .addCase(downloadBlanketItemsPending, (state) => {
        state.isLoading = true;
      })
      .addCase(downloadBlanketItemsFulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(downloadBlanketItemsRejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      .addCase(downloadOverAndAbovePending, (state) => {
        state.isLoading = true;
      })
      .addCase(downloadOverAndAboveFulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(downloadOverAndAboveRejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      .addMatcher(prebookListApi.endpoints.list.matchPending, (state) => {
        state.isLoading = true;
      })
      .addMatcher(
        prebookListApi.endpoints.list.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.prebooks = payload.prebooks;
        }
      )
      .addMatcher(
        prebookListApi.endpoints.list.matchRejected,
        (state, { payload }: PayloadAction<ProblemDetails | undefined>) => {
          state.isLoading = false;
          if (payload) {
            state.error = payload;
          }
        }
      )
      .addMatcher(
        prebookListApi.endpoints.itemSummary.matchPending,
        (state) => {
          state.isLoading = true;
        }
      )
      .addMatcher(
        prebookListApi.endpoints.itemSummary.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.items = payload.items;
        }
      )
      .addMatcher(
        prebookListApi.endpoints.itemSummary.matchRejected,
        (state, { payload }: PayloadAction<ProblemDetails | undefined>) => {
          state.isLoading = false;
          if (payload) {
            state.error = payload;
          }
        }
      )
      .addMatcher(
        prebookListApi.endpoints.blanketItems.matchPending,
        (state) => {
          state.isLoading = true;
        }
      )
      .addMatcher(
        prebookListApi.endpoints.blanketItems.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.blanketItems = payload.items;
        }
      )
      .addMatcher(
        prebookListApi.endpoints.blanketItems.matchRejected,
        (state, { payload }: PayloadAction<ProblemDetails | undefined>) => {
          state.isLoading = false;
          if (payload) {
            state.error = payload;
          }
        }
      ),
});

export const {
  clearError,
  clearSearchAndFilters,
  clearItemSearchAndFilters,
  clearBlanketSearchAndFilters,
  setStartDate,
  setEndDate,
  setSearch,
  setPrebookSort,
  setItemSort,
  setBlanketItemSort,
  setVendorFilter,
  setCustomerFilter,
  setShipToFilter,
  setSalespersonFilter,
  setItemSearch,
  setSeasonFilter,
  setBlanketFilter,
  setIncludeSpirePurchaseOrdersFilter,
  setIncludeDeletedFilter,
  setPartNumberFilter,
  setGroupByCustomerItemOption,
  setGroupByShipToItemOption,
  setGroupByVendorItemOption,
  setGroupBySalespersonItemOption,
  setGroupBySeasonItemOption,
  setGroupByIsBlanketItemOption,
  setDateGroupingItemOption,
  setSelectedItems,
  unsetSelectedItems,
} = prebookListSlice.actions;

export const selectStartDate = (state: RootState) =>
  state.prebookList.startDate;
export const selectEndDate = (state: RootState) => state.prebookList.endDate;
export const selectSearch = (state: RootState) => state.prebookList.search;
export const selectPrebookSort = (state: RootState) =>
  state.prebookList.prebookSort;
export const selectSortPrebooksDescending = (state: RootState) =>
  state.prebookList.sortPrebooksDescending;
export const selectItemSearch = (state: RootState) =>
  state.prebookList.itemSearch;
export const selectItemsSort = (state: RootState) =>
  state.prebookList.itemsSort;
export const selectBlanketItemsSort = (state: RootState) =>
  state.prebookList.blanketItemsSort;
export const selectSortItemsDescending = (state: RootState) =>
  state.prebookList.sortItemsDescending;
export const selectError = (state: RootState) => state.prebookList.error;
export const selectIsLoading = (state: RootState) =>
  state.prebookList.isLoading;
export const selectFilter = (state: RootState) => state.prebookList.filter;
export const selectItemOptions = (state: RootState) =>
  state.prebookList.itemOptions;
export const selectSelectedItems = (state: RootState) =>
  state.prebookList.selectedItems;

const selectAllPrebooks = (state: RootState) => state.prebookList.prebooks;
const selectAllItems = (state: RootState) => state.prebookList.items;
const selectAllBlanketItems = (state: RootState) =>
  state.prebookList.blanketItems;

export const selectSelectedPrebooks = createSelector(
  selectAllPrebooks,
  selectSelectedItems,
  (prebooks, selectedItems) =>
    prebooks.filter((p) => selectedItems.indexOf(p.id) !== -1).sort(sortById)
);

export const selectPrebookVendors = createSelector(
  selectAllPrebooks,
  (prebooks) =>
    prebooks
      .reduce(
        (memo, p) =>
          p.vendor && memo.indexOf(p.vendor) === -1
            ? memo.concat([p.vendor])
            : memo,
        [] as string[]
      )
      .sort()
);
export const selectPrebookCustomers = createSelector(
  selectAllPrebooks,
  (prebooks) => {
    const customers = prebooks
      .reduce(
        (memo, p) =>
          p.customer && memo.indexOf(p.customer) === -1
            ? memo.concat([p.customer])
            : memo,
        [] as string[]
      )
      .sort();

    if (prebooks.some((p) => !p.customer)) {
      customers.unshift(blankCustomerFilter);
    }

    return customers;
  }
);
export const selectPrebookShipTos = createSelector(
  selectAllPrebooks,
  (prebooks) => {
    const shipTos = prebooks
      .reduce(
        (memo, p) =>
          p.shipTo && memo.indexOf(p.shipTo) === -1
            ? memo.concat([p.shipTo])
            : memo,
        [] as string[]
      )
      .sort();

    if (prebooks.some((p) => !p.shipTo)) {
      shipTos.unshift(blankShipToFilter);
    }

    return shipTos;
  }
);
export const selectPrebookSalespeople = createSelector(
  selectAllPrebooks,
  (prebooks) => {
    const salespeople = prebooks
      .reduce(
        (memo, p) =>
          p.salesperson && memo.indexOf(p.salesperson) === -1
            ? memo.concat([p.salesperson])
            : memo,
        [] as string[]
      )
      .sort();

    if (prebooks.some((p) => !p.salesperson)) {
      salespeople.unshift(blankSalespersonFilter);
    }
    return salespeople;
  }
);
export const selectPrebookSeasons = createSelector(
  selectAllPrebooks,
  (prebooks) =>
    prebooks
      .reduce(
        (memo, p) =>
          p.season && memo.indexOf(p.season) === -1
            ? memo.concat([p.season])
            : memo,
        [] as string[]
      )
      .sort()
);

export const selectItemVendors = createSelector(selectAllItems, (items) =>
  items
    .reduce(
      (memo, p) =>
        p.vendor && memo.indexOf(p.vendor) === -1
          ? memo.concat([p.vendor])
          : memo,
      [] as string[]
    )
    .sort()
);
export const selectItemCustomers = createSelector(selectAllItems, (items) => {
  const customers = items
    .reduce(
      (memo, p) =>
        p.customer && memo.indexOf(p.customer) === -1
          ? memo.concat([p.customer])
          : memo,
      [] as string[]
    )
    .sort();

  if (items.some((i) => !i.customer)) {
    customers.unshift(blankCustomerFilter);
  }

  return customers;
});
export const selectItemShipTos = createSelector(selectAllItems, (items) => {
  const shipTos = items
    .reduce(
      (memo, p) =>
        p.shipTo && memo.indexOf(p.shipTo) === -1
          ? memo.concat([p.shipTo])
          : memo,
      [] as string[]
    )
    .sort();

  if (items.some((i) => !i.shipTo)) {
    shipTos.unshift(blankShipToFilter);
  }

  return shipTos;
});
export const selectItemSalespeople = createSelector(selectAllItems, (items) => {
  const salespeople = items
    .reduce(
      (memo, p) =>
        p.salesperson && memo.indexOf(p.salesperson) === -1
          ? memo.concat([p.salesperson])
          : memo,
      [] as string[]
    )
    .sort();

  if (items.some((i) => !i.salesperson)) {
    salespeople.unshift(blankSalespersonFilter);
  }

  return salespeople;
});
export const selectItemSeasons = createSelector(selectAllItems, (items) =>
  items
    .reduce(
      (memo, p) =>
        p.season && memo.indexOf(p.season) === -1
          ? memo.concat([p.season])
          : memo,
      [] as string[]
    )
    .sort()
);

export interface FilterItem {
  partNumber: string;
  description: string;
}
export const selectItemInventoryItems = createSelector(
  selectAllItems,
  (items) =>
    items
      .reduce(
        (memo, p) =>
          memo.findIndex((i) => i.partNumber === p.spirePartNumber) === -1
            ? memo.concat([
                { partNumber: p.spirePartNumber, description: p.description },
              ])
            : memo,
        [] as FilterItem[]
      )
      .sort()
);

export const selectBlanketItemVendors = createSelector(
  selectAllBlanketItems,
  (items) =>
    items
      .reduce(
        (memo, p) =>
          p.vendor && memo.indexOf(p.vendor) === -1
            ? memo.concat([p.vendor])
            : memo,
        [] as string[]
      )
      .sort()
);
export const selectBlanketItemCustomers = createSelector(
  selectAllBlanketItems,
  (items) => {
    const customers = items
      .reduce(
        (memo, p) =>
          p.customer && memo.indexOf(p.customer) === -1
            ? memo.concat([p.customer])
            : memo,
        [] as string[]
      )
      .sort();

    if (items.some((i) => !i.customer)) {
      customers.unshift(blankCustomerFilter);
    }

    return customers;
  }
);
export const selectBlanketItemShipTos = createSelector(
  selectAllBlanketItems,
  (items) => {
    const shipTos = items
      .reduce(
        (memo, p) =>
          p.shipTo && memo.indexOf(p.shipTo) === -1
            ? memo.concat([p.shipTo])
            : memo,
        [] as string[]
      )
      .sort();

    if (items.some((i) => !i.shipTo)) {
      shipTos.unshift(blankShipToFilter);
    }

    return shipTos;
  }
);
export const selectBlanketItemSalespeople = createSelector(
  selectAllBlanketItems,
  (items) => {
    const salespeople = items
      .reduce(
        (memo, p) =>
          p.salesperson && memo.indexOf(p.salesperson) === -1
            ? memo.concat([p.salesperson])
            : memo,
        [] as string[]
      )
      .sort();

    if (items.some((i) => !i.salesperson)) {
      salespeople.unshift(blankSalespersonFilter);
    }

    return salespeople;
  }
);
export const selectBlanketItemSeasons = createSelector(
  selectAllBlanketItems,
  (items) =>
    items
      .reduce(
        (memo, p) =>
          p.season && memo.indexOf(p.season) === -1
            ? memo.concat([p.season])
            : memo,
        [] as string[]
      )
      .sort()
);

export const selectBlanketItemInventoryItems = createSelector(
  selectAllBlanketItems,
  (items) =>
    items
      .reduce(
        (memo, p) =>
          memo.findIndex((i) => i.partNumber === p.spirePartNumber) === -1
            ? memo.concat([
                { partNumber: p.spirePartNumber, description: p.description },
              ])
            : memo,
        [] as FilterItem[]
      )
      .sort()
);

const sortByWithBlanketStartDate = (
  propertyName: string,
  direction?: string
) => {
  if (propertyName === 'requiredDate') {
    return function (
      a: models.PrebookListItem | models.BlanketItemListItem,
      b: models.PrebookListItem | models.BlanketItemListItem
    ) {
      return (
        sort(
          a.blanketStartDate || a.requiredDate || a.seasonDate,
          b.blanketStartDate || b.requiredDate || a.seasonDate,
          direction
        ) ||
        sort(
          (a as models.BlanketItemListItem).blanketWeekId,
          (b as models.BlanketItemListItem).blanketWeekId,
          direction
        )
      );
    };
  }

  return sortBy(propertyName, direction);
};

export const selectPrebooks = createSelector(
  selectAllPrebooks,
  selectSearch,
  selectPrebookSort,
  selectSortPrebooksDescending,
  selectFilter,
  (prebooks, search, sortField, sortDescending, filter) => {
    const sort = sortByWithBlanketStartDate(
        sortField,
        sortDescending ? 'descending' : ''
      ),
      list = prebooks
        .filter(
          (p) =>
            (!search ||
              startsWith(p.name, search) ||
              startsWith(p.customer, search) ||
              startsWith(p.vendor, search) ||
              contains(formatNumber(p.id, '00000'), search)) &&
            (!filter.vendor || equals(p.vendor, filter.vendor)) &&
            (!filter.customer ||
              equals(p.customer, filter.customer) ||
              (filter.customer === blankCustomerFilter && !p.customer)) &&
            (!filter.shipTo ||
              equals(p.shipTo, filter.shipTo) ||
              (filter.shipTo === blankShipToFilter && !p.shipTo)) &&
            (!filter.salesperson ||
              equals(p.salesperson, filter.salesperson) ||
              (filter.salesperson === blankSalespersonFilter &&
                !p.salesperson)) &&
            (!filter.season || equals(p.season, filter.season)) &&
            (!filter.blanket ||
              (filter.blanket === 'Blanket' && p.isBlanket) ||
              (filter.blanket === 'Non-Blanket' && !p.isBlanket)) &&
            (filter.includeSpirePurchaseOrders ||
              !p.spirePurchaseOrderNumber) &&
            (filter.includeDeleted || !p.isDeleted)
        )
        .map((l) => ({ ...l } as models.PrebookListItem))
        .sort(sort);

    return list;
  }
);

export const selectItemSummary = createSelector(
  selectAllItems,
  selectItemSearch,
  selectItemsSort,
  selectSortItemsDescending,
  selectItemOptions,
  selectFilter,
  (items, search, sortField, sortDescending, { dateGrouping }, filter) => {
    const direction = sortDescending ? 'descending' : '',
      sortDate = sortBy('date', direction),
      sortWeek = sortBy('week', direction),
      sortWeekYear = sortBy('weekYear', direction),
      sortMonth = sortBy('month', direction),
      sortYear = sortBy('year', direction),
      sort =
        sortField === 'date'
          ? (a: models.PrebookSummaryItem, b: models.PrebookSummaryItem) => {
              switch (dateGrouping) {
                case 'Week':
                  return sortWeekYear(a, b) || sortWeek(a, b);
                case 'Month':
                  return sortYear(a, b) || sortMonth(a, b);
                case 'Year':
                  return sortYear(a, b);
              }
              return sortDate(a, b);
            }
          : sortBy(sortField, direction),
      list = items
        .filter(
          (p) =>
            (!search ||
              contains(p.spirePartNumber, search) ||
              contains(p.description, search)) &&
            (!filter.vendor || equals(p.vendor, filter.vendor)) &&
            (!filter.customer ||
              equals(p.customer, filter.customer) ||
              (filter.customer === blankCustomerFilter && !p.customer)) &&
            (!filter.shipTo ||
              equals(p.shipTo, filter.shipTo) ||
              (filter.shipTo === blankShipToFilter && !p.shipTo)) &&
            (!filter.salesperson ||
              equals(p.salesperson, filter.salesperson) ||
              (filter.salesperson === blankSalespersonFilter &&
                !p.salesperson)) &&
            (!filter.season || equals(p.season, filter.season)) &&
            (!filter.blanket ||
              (filter.blanket === 'Blanket' && p.isBlanket) ||
              (filter.blanket === 'Non-Blanket' && !p.isBlanket)) &&
            (!filter.partNumber || equals(p.spirePartNumber, filter.partNumber))
        )
        .map((l) => ({ ...l }))
        .sort(sort);

    return list;
  }
);

export const selectBlanketItems = createSelector(
  selectAllBlanketItems,
  selectItemSearch,
  selectItemsSort,
  selectSortItemsDescending,
  selectFilter,
  (blanketItems, search, sortField, sortDescending, filter) => {
    const sort = sortByWithBlanketStartDate(
        sortField,
        sortDescending ? 'descending' : ''
      ),
      list = blanketItems
        .filter(
          (p) =>
            (!search ||
              contains(p.spirePartNumber, search) ||
              contains(p.description, search)) &&
            (!filter.vendor || equals(p.vendor, filter.vendor)) &&
            (!filter.customer ||
              equals(p.customer, filter.customer) ||
              (filter.customer === blankCustomerFilter && !p.customer)) &&
            (!filter.shipTo ||
              equals(p.shipTo, filter.shipTo) ||
              (filter.shipTo === blankShipToFilter && !p.shipTo)) &&
            (!filter.salesperson ||
              equals(p.salesperson, filter.salesperson) ||
              (filter.salesperson === blankSalespersonFilter &&
                !p.salesperson)) &&
            (!filter.season || equals(p.season, filter.season)) &&
            (!filter.partNumber || equals(p.spirePartNumber, filter.partNumber))
        )
        .map((l) => ({ ...l } as models.BlanketItemListItem))
        .sort(sort);

    return list;
  }
);

export default prebookListSlice.reducer;
