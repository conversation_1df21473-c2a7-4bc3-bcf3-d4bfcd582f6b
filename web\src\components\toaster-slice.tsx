import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '@/services/store';

export interface ToastMessage {
  type: 'success' | 'error';
  title?: string;
  message: string;
  timeout?: number | null;
}

interface ToasterState {
  toasts: ToastMessage[];
}

const initialState: ToasterState = {
  toasts: [],
};

export const toasterSlice = createSlice({
  name: 'toaster',
  initialState,
  reducers: {
    removeToast(state, { payload }: PayloadAction<string>) {
      state.toasts = state.toasts.filter((t) => t.message !== payload);
    },
    addToast(state, { payload }: PayloadAction<ToastMessage>) {
      const toast = { timeout: 3, ...payload };
      state.toasts = state.toasts.concat([toast]);
    },
  },
});

export const { removeToast, addToast } = toasterSlice.actions;

export const selectToasts = ({ toaster }: RootState) => toaster.toasts;

export default toasterSlice.reducer;
