import React, { Fragment, useEffect, useState } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { apiUrl } from 'api/api-base';
import * as models from 'api/models/prebooks';
import { Icon } from '@/components/icon';
import { classNames } from '@/utils/class-names';
import { formatDate } from '@/utils/format';

export interface EmailReviewProps {
  open: boolean;
  close: () => void;
  emails: models.PrebookEmail[];
}

export function EmailReview({ open, close, emails }: EmailReviewProps) {
  const [selectedIndex, setSelectedIndex] = useState(0),
    selectedEmail = emails[selectedIndex];

  useEffect(() => {
    setSelectedIndex(emails.length - 1);
  }, [emails.length, selectedEmail?.prebookId]);

  const handleTabClick = (index: number) => {
    setSelectedIndex(index);
  };

  return (
    <Transition.Root as={Fragment} show={open}>
      <Dialog as="div" onClose={close} className="relative z-30">
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-30 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative h-screen w-screen transform overflow-hidden p-6 transition-all">
                <div className="flex h-full flex-col rounded-lg bg-white p-6 text-left shadow-xl">
                  <div className="mb-4 flex justify-center border-b-2 pb-4">
                    <Dialog.Title
                      as="h3"
                      className="text-lg font-medium leading-6 text-gray-900"
                    >
                      <Icon
                        icon="glasses"
                        className="h-6 w-6"
                        aria-hidden="true"
                      />
                      &nbsp; Review Emails
                    </Dialog.Title>
                  </div>
                  <div className="flex flex-grow flex-col">
                    <div className="m-r-auto flex w-auto">
                      <nav
                        className="isolate flex divide-x divide-gray-200 rounded-lg shadow"
                        aria-label="Tabs"
                      >
                        {emails.map((email, index) => (
                          <button
                            type="button"
                            key={email.id}
                            onClick={() => handleTabClick(index)}
                            className={classNames(
                              index === selectedIndex
                                ? 'text-gray-900'
                                : 'text-gray-500 hover:text-gray-700',
                              index === 0 ? 'rounded-l-lg' : '',
                              index === emails.length - 1 ? 'rounded-r-lg' : '',
                              'group relative min-w-0 flex-1 overflow-hidden bg-white px-4 py-4 text-center text-sm font-medium hover:bg-gray-50 focus:z-10'
                            )}
                            aria-current={
                              index === selectedIndex ? 'page' : undefined
                            }
                          >
                            <span>{formatDate(email.created, 'DD')}</span>
                            <span
                              aria-hidden="true"
                              className={classNames(
                                index === selectedIndex
                                  ? 'bg-blue-500'
                                  : 'bg-transparent',
                                'absolute inset-x-0 bottom-0 h-0.5'
                              )}
                            />
                          </button>
                        ))}
                      </nav>
                    </div>
                    <div className="flex flex-grow flex-col border-t">
                      {!!selectedEmail && (
                        <form className="mt-4 flex w-full">
                          <div className="grid w-full grid-cols-6 items-start gap-4">
                            <p className="col-start-1 mt-px block grid-cols-2 pt-2 text-right text-sm font-medium text-gray-700">
                              To:
                            </p>
                            <p className="col-span-2 truncate pt-2">
                              {selectedEmail.to}
                            </p>
                            <div
                              className={classNames(
                                'col-span-3',
                                selectedEmail.cc && selectedEmail.bcc
                                  ? 'row-span-5'
                                  : selectedEmail.cc || selectedEmail.bcc
                                  ? 'row-span-4'
                                  : 'row-span-3'
                              )}
                            >
                              <p className="mt-px inline-block pt-2 text-right text-sm font-medium text-gray-700">
                                Body:
                              </p>
                              <p className="font whitespace-pre pt-2">
                                {selectedEmail.body}
                              </p>
                            </div>
                            {!!selectedEmail.cc && (
                              <>
                                <p className="col-start-1 mt-px block grid-cols-2 pt-2 text-right text-sm font-medium text-gray-700">
                                  CC:
                                </p>
                                <p className="col-span-2 pt-2">
                                  {selectedEmail.cc}
                                </p>
                              </>
                            )}
                            {!!selectedEmail.bcc && (
                              <>
                                <p className="col-start-1 mt-px block grid-cols-2 pt-2 text-right text-sm font-medium text-gray-700">
                                  BCC:
                                </p>
                                <p className="col-span-2 pt-2">
                                  {selectedEmail.bcc}
                                </p>
                              </>
                            )}
                            <p className="col-start-1 mt-px block grid-cols-2 pt-2 text-right text-sm font-medium text-gray-700">
                              Subject:
                            </p>
                            <p className="col-span-2 pt-2">
                              {selectedEmail.subject}
                            </p>

                            <p className="col-span-6 col-start-2 border-t text-xs italic">
                              Sent to supplier by {selectedEmail.createdBy} on{' '}
                              {formatDate(selectedEmail.created, 'MMM d, yyyy')}{' '}
                              @ {formatDate(selectedEmail.created, 'h:mm a')}
                            </p>
                          </div>
                        </form>
                      )}
                    </div>
                    {!!selectedEmail && (
                      <div className="mt-4 grid h-full grid-cols-6 gap-4 text-right">
                        <div>
                          <a
                            href={apiUrl(
                              `reports/prebooks/${selectedEmail.prebookId}/email/${selectedEmail.id}?download`
                            )}
                            className="btn-secondary inline-block"
                          >
                            <Icon icon="download"></Icon>
                            &nbsp; Download PDF
                          </a>
                        </div>
                        <iframe
                          title="Prebook preview"
                          src={apiUrl(
                            `reports/prebooks/${selectedEmail.prebookId}/email/${selectedEmail.id}`
                          )}
                          className="col-span-5 col-start-2 h-full w-full"
                        ></iframe>
                      </div>
                    )}
                  </div>
                  <div className="mt-4 flex justify-end border-t-2 pt-4">
                    <button
                      type="button"
                      className="btn-secondary text-lg"
                      onClick={close}
                    >
                      Close
                    </button>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
}
