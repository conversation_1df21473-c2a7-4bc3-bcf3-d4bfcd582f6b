import { useState, Fragment } from 'react';
import * as HeadlessUI from '@headlessui/react';
import * as boeks from 'api/models/boekestyns';
import { handleFocus } from '@/utils/focus';

interface StopLabourDialogProps {
  open: boolean;
  onClose: () => void;
  onStop: (args: { varieties: { id: number; quantity: number }[] }) => void;
  order: boeks.StickingWorkOrderItem;
}

export function StopLabourDialog({
  open,
  onClose,
  onStop,
  order,
}: StopLabourDialogProps) {
  const [varietyActualQuantites, setVarietyActualQuantites] = useState<
    { id: number; quantity: number }[]
  >([]);

  const handleTransitionAfterEnter = () => {
    setVarietyActualQuantites(
      order.varieties.map((v) => ({ id: v.id, quantity: v.pots }))
    );
  };

  const handleStopClick = () => {
    onStop({ varieties: varietyActualQuantites });
  };

  const setVarietyQuantity = (id: number, quantity: number) => {
    setVarietyActualQuantites(
      varietyActualQuantites.map((v) => (v.id === id ? { ...v, quantity } : v))
    );
  };

  return (
    <HeadlessUI.Transition.Root
      show={open}
      as={Fragment}
      afterEnter={handleTransitionAfterEnter}
    >
      <HeadlessUI.Dialog as="div" className="relative z-10" onClose={onClose}>
        <HeadlessUI.Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </HeadlessUI.Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex max-h-full min-h-full justify-center overflow-y-auto p-0 text-center">
            <HeadlessUI.Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-0 scale-95"
              enterTo="opacity-100 translate-y-0 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 scale-100"
              leaveTo="opacity-0 translate-y-0 scale-95"
            >
              <HeadlessUI.Dialog.Panel className="relative my-8 flex w-full max-w-lg transform flex-col overflow-y-auto rounded-lg bg-white p-6 text-left shadow-xl transition-all">
                <div>
                  <div className="mt-3 text-center">
                    <HeadlessUI.Dialog.Title
                      as="h3"
                      className="text-base font-semibold leading-6 text-gray-900"
                    >
                      Stop Time
                    </HeadlessUI.Dialog.Title>
                  </div>
                </div>

                <div className="mt-5 flex-grow overflow-y-auto">
                  <table className="min-w-full divide-y divide-gray-300 text-sm">
                    <thead>
                      <tr className="sticky top-0 z-10 border-b bg-white shadow">
                        <th className="px-2 py-1 text-left">Variety</th>
                        <th className="px-2 py-1 text-left">
                          Actual Pots Stuck
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {order.varieties.map((variety) => (
                        <tr key={variety.name}>
                          <td className="px-2 py-1">{variety.name}</td>
                          <td className="px-2 py-1">
                            <input
                              onFocus={handleFocus}
                              type="number"
                              value={
                                varietyActualQuantites.find(
                                  (v) => v.id === variety.id
                                )?.quantity ?? variety.pots
                              }
                              onChange={(e) =>
                                setVarietyQuantity(
                                  variety.id,
                                  parseInt(e.target.value)
                                )
                              }
                              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            />
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                <div className="mt-6 text-right">
                  <button
                    type="button"
                    className="btn-secondary"
                    onClick={onClose}
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn-primary ml-2"
                    onClick={handleStopClick}
                  >
                    Stop
                  </button>
                </div>
              </HeadlessUI.Dialog.Panel>
            </HeadlessUI.Transition.Child>
          </div>
        </div>
      </HeadlessUI.Dialog>
    </HeadlessUI.Transition.Root>
  );
}
