import React, { useEffect, useRef, useState } from 'react';
import { DateTime } from 'luxon';
import * as Headless<PERSON> from '@headlessui/react';
import { boekestynApi } from 'api/boekestyn-service';
import * as boeks from 'api/models/boekestyns';
import { NewPrebookDetailItem, PrebookDetailItem } from 'api/models/prebooks';
import { usePermissions } from '@/services/auth';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import {
  selectPreviousItems,
  selectEmails,
  setItemIsApproximate,
  selectIsBlanket,
  selectBlanketItems,
  selectBlanketStartDate,
  selectRequiredDate,
  setItemUpgradeSheet,
  setItemSpecialPrice,
  selectPotCovers,
  setItemGrowerItemNotes,
} from './prebook-detail-slice';
import { Alert } from '@/components/alert';
import { DropdownMenu } from '@/components/drop-down-menu';
import { Icon } from '@/components/icon';
import { Inventory } from '@/components/prebooks/inventory';
import { classNames } from '@/utils/class-names';
import {
  formatNumber,
  parseNullableCurrency,
  parseQuantity,
} from '@/utils/format';
import { handleFocus } from '@/utils/focus';
import { isDateInWeek, weeksBetweenDates } from '@/utils/weeks';
import {
  duplicateItem,
  removeItem,
  setItemInventoryItem,
  setItemOrderQuantity,
  setItemHasPotCover,
  setItemPotCover,
  setItemDateCode,
  setItemUPC,
  setItemWeightsAndMeasures,
  setItemRetail,
  setItemComments,
  setItemBlanketItemId,
  setItemBlanketWeekId,
  setItemBoekestynProductionOrder,
  selectVendor,
  selectIsBoekestyn,
} from '@/components/prebooks/prebook-detail-slice';
import * as prebooks from 'api/models/prebooks';
import { InventoryItem } from 'api/models/spire';
import { ItemBlanketOption } from './item-blanket-option';
import { ItemBlanketOptions } from './item-blanket-options';
import {
  ItemBoekestynOption,
  makeOptionKey,
  parseKey,
} from './item-boekestyn-option';
import { itemIsOverbooked } from './item-functions';
import {
  setShowDialog,
  selectBoekestynProducts,
} from './boekestyn-product-slice';
import { BoekestynProducts } from './boekestyn-products';

interface ItemProps {
  item: PrebookDetailItem | NewPrebookDetailItem;
}

export function Item({ item }: ItemProps) {
  const dispatch = useAppDispatch(),
    { can } = usePermissions(),
    hasEmails = !!useAppSelector(selectEmails).length,
    isBlanket = useAppSelector(selectIsBlanket),
    isBoekestyn = useAppSelector(selectIsBoekestyn),
    blanketStartDate = useAppSelector(selectBlanketStartDate),
    requiredDate = useAppSelector(selectRequiredDate),
    blanketItems = useAppSelector(selectBlanketItems),
    vendor = useAppSelector(selectVendor),
    potCovers = useAppSelector(selectPotCovers),
    boekestynProducts = useAppSelector(selectBoekestynProducts),
    [boekestynOrders, setBoekestynOrders] = useState(
      [] as boeks.WeeklyProductionOrder[]
    ),
    [boekestynPlants, setBoekestynPlants] = useState([] as boeks.Plant[]),
    [specialPrice, setSpecialPrice] = useState(
      item.specialPrice == null
        ? ''
        : formatNumber(item.specialPrice, '#,##0.00')
    ),
    [hasSpecialPricing, setHasSpecialPricing] = useState(!!item.specialPrice),
    specialPricingRef = useRef<HTMLInputElement | null>(null),
    bookedQuantity = blanketItems.find((i) => i.id === item.id)?.bookedQuantity,
    availableBlanketItems = blanketItems.filter(
      (i) =>
        i.spireInventoryId === item.spireInventoryId &&
        (!vendor || i.vendorId === vendor.id) &&
        (!i.blanketStartDate ||
          !requiredDate ||
          i.blanketStartDate <= requiredDate) &&
        i.requiredDate >= (blanketStartDate || requiredDate || '') &&
        isDateInWeek(i.blanketWeekId, requiredDate)
    ),
    lux = requiredDate ? DateTime.fromISO(requiredDate) : null,
    week = lux?.get('weekNumber'),
    year = lux?.get('weekYear'),
    availableBoekestynOrders = boeks.ordersWithAllPlants(
      requiredDate,
      item,
      boekestynOrders,
      boekestynPlants
    ),
    previousItem = useAppSelector(selectPreviousItems).find(
      (i) => i.prebookItemId === item.id
    ),
    isNew = hasEmails && !previousItem,
    quantityChanged =
      !isNew &&
      previousItem?.orderQuantity &&
      item.orderQuantity &&
      previousItem.orderQuantity !== item.orderQuantity,
    wasRemoved = previousItem?.orderQuantity && !item.orderQuantity,
    itemId = item.id,
    [showDeleteAlert, setShowDeleteAlert] = useState(false),
    [orderQuantity, setOrderQuantity] = useState(
      formatNumber(item.orderQuantity)
    ),
    [showEditDialog, setShowEditDialog] = useState(false),
    [showBlanketOptions, setShowBlanketOptions] = useState(false),
    cellClassName = 'whitespace-nowrap px-1 py-1 text-gray-500 border-bottom-0',
    inputClassName =
      'w-full rounded-md border-gray-300 text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500',
    commentsRef = useRef<HTMLTextAreaElement | null>(null),
    potCoverRef = useRef<HTMLInputElement | null>(null),
    isBlanketOverbooked =
      // if this is a blanket
      (bookedQuantity != null && bookedQuantity > item.orderQuantity) ||
      // if this is connected to a blanket & pushes it over
      itemIsOverbooked(item, blanketItems),
    hasBookings =
      !isBlanketOverbooked && bookedQuantity != null && bookedQuantity > 0,
    readonly = !can('Sales Team'),
    weeks =
      isBlanket && blanketStartDate && requiredDate
        ? weeksBetweenDates(blanketStartDate, requiredDate)
        : [];

  useEffect(() => {
    boekestynApi
      .productionOrders()
      .then((orders) => setBoekestynOrders(orders));
    boekestynApi.plants().then((plants) => setBoekestynPlants(plants));
  }, []);

  const handleInventoryItemClick = () => {
    setShowEditDialog(true);
  };

  const handleInventoryItemCancel = () => {
    setShowEditDialog(false);
  };

  const handleInventoryItemConfirm = (
    value: InventoryItem,
    blanketItemId: number | null,
    comments: string | null
  ) => {
    dispatch(setItemInventoryItem({ itemId, value }));
    dispatch(setItemBlanketItemId({ itemId, value: blanketItemId }));
    dispatch(setItemComments({ itemId, value: comments }));
    setShowEditDialog(false);
  };

  const handleHasPotCoverChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.checked;
    dispatch(setItemHasPotCover({ itemId, value }));
    if (value) {
      window.setTimeout(() => potCoverRef.current?.focus(), 500);
    } else {
      dispatch(setItemPotCover({ itemId, value: null }));
    }
  };

  const handlePotCoverChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const value = e.target.value.toUpperCase() || null;
    dispatch(setItemPotCover({ itemId, value }));
  };

  const handlePotCoverSelected = (value: string) => {
    dispatch(setItemPotCover({ itemId, value }));
  };

  const handleDateCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.toUpperCase() || null;
    dispatch(setItemDateCode({ itemId, value }));
  };

  const handleUPCChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value || null;
    dispatch(setItemUPC({ itemId, value }));
  };

  const handleWeightsAndMeasuresChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = e.target.checked;
    dispatch(setItemWeightsAndMeasures({ itemId, value }));
  };

  const handleRetailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value || null;
    dispatch(setItemRetail({ itemId, value }));
  };

  const handleCommentsChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value || null;
    dispatch(setItemComments({ itemId, value }));
  };

  const handleGrowerItemNotesChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const value = e.target.value || null;
    dispatch(setItemGrowerItemNotes({ itemId, value }));
  };

  const handleOrderQuantityChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setOrderQuantity(e.target.value);
  };

  const handleIsApproximateChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = e.target.checked;
    dispatch(setItemIsApproximate({ itemId, value }));
  };

  const handleOrderQuantityBlur = () => {
    const value = parseQuantity(orderQuantity);
    setOrderQuantity(formatNumber(value));
    dispatch(setItemOrderQuantity({ itemId, value }));
  };

  const handleBlanketItemChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = parseQuantity(e.target.value) || null;
    dispatch(setItemBlanketItemId({ itemId, value }));

    if (
      !value &&
      !item.comments &&
      availableBlanketItems.length &&
      vendor?.id !== boeks.BoekestynVendorId
    ) {
      dispatch(
        setItemComments({
          itemId,
          value: prebooks.overAndAboveBlanketComment,
        })
      );
    } else if (value && item.comments === prebooks.overAndAboveBlanketComment) {
      dispatch(setItemComments({ itemId, value: null }));
    }
  };

  const handleBlanketWeekIdChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    const value = e.target.value || null;
    dispatch(setItemBlanketWeekId({ itemId, value }));
  };

  const handleUpgradeSheetChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.checked;
    dispatch(setItemUpgradeSheet({ itemId, value }));
  };

  const handleBoekestynProductClick = () => {
    dispatch(setShowDialog(item));
  };

  const handleBoekestynSalesOrderChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    const key = e.target.value,
      value = parseKey(key);
    dispatch(setItemBoekestynProductionOrder({ itemId, value }));
  };

  const handleBlanketOptionsClick = () => {
    setShowBlanketOptions(true);
  };

  const handleBlanketOptionsClose = () => {
    setShowBlanketOptions(false);
  };

  const handleDeleteClick = () => {
    setShowDeleteAlert(true);
  };

  const handleDeleteCancel = () => {
    setShowDeleteAlert(false);
  };

  const handleDeleteConfirm = () => {
    dispatch(removeItem(itemId));
    setShowDeleteAlert(false);
  };

  const handleDuplicateClick = () => {
    dispatch(duplicateItem(itemId));
  };

  const handleSpecialPriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSpecialPrice(e.target.value);
  };

  const handleHasSpecialPricingChange = (value: boolean) => {
    setHasSpecialPricing(value);
    if (value) {
      window.setTimeout(() => specialPricingRef.current?.focus(), 100);
    } else {
      setSpecialPrice('');
      dispatch(setItemSpecialPrice({ itemId, value: null }));
    }
  };

  const handleSpecialPriceBlur = () => {
    const value = parseNullableCurrency(specialPrice),
      price = value == null ? '' : formatNumber(value, '0,0.00');

    setSpecialPrice(price);
    dispatch(setItemSpecialPrice({ itemId, value }));
  };

  return (
    <>
      <tr className="border-transparent">
        <td
          className={classNames(cellClassName, 'pl-4 pt-4 align-top text-xs')}
        >
          <div className="flex flex-col">
            <button
              type="button"
              className={classNames(
                'text-left text-sm font-medium text-gray-500',
                !readonly && 'hover:text-blue-700 hover:underline'
              )}
              onClick={handleInventoryItemClick}
              disabled={readonly}
              tabIndex={-1}
            >
              {item.spirePartNumber}
            </button>
            <div
              className="max-w-[10rem] cursor-default truncate"
              title={item.description || ''}
            >
              {item.description}
            </div>
          </div>
          {isBlanket && (
            <>
              <div
                className={classNames(
                  'mb-1 mt-2 flex border-gray-200 pb-1',
                  item.blanketOptions?.length && 'border-b-2'
                )}
              >
                {!!item.blanketOptions?.length && (
                  <div className="flex w-full justify-between align-bottom font-bold">
                    Options&nbsp;
                    {!readonly && (
                      <button
                        type="button"
                        onClick={handleBlanketOptionsClick}
                        className="flex px-2 py-1 align-bottom text-xs font-normal text-blue-600 hover:underline"
                      >
                        Edit Options
                      </button>
                    )}
                  </div>
                )}
                {!readonly && !item.blanketOptions?.length && (
                  <button
                    type="button"
                    onClick={handleBlanketOptionsClick}
                    className="bold flex px-2 py-1 align-bottom text-xs text-blue-600 hover:underline"
                  >
                    Add Options
                  </button>
                )}
              </div>
              {item.blanketOptions?.map((option) => (
                <div key={option.id} className="mt-1 flex">
                  <div className="mr-1 flex w-20 truncate text-gray-500">
                    {option.spirePartNumber}
                  </div>
                  <div className="flex flex-grow truncate italic">
                    {option.description}
                  </div>
                </div>
              ))}
            </>
          )}
          <Inventory
            open={showEditDialog}
            cancel={handleInventoryItemCancel}
            confirm={handleInventoryItemConfirm}
            vendor={vendor}
          />
        </td>
        {hasEmails && (
          <td
            rowSpan={2}
            className={classNames(
              'border-bottom-0 whitespace-nowrap px-1 py-3 text-center align-top',
              isNew
                ? 'text-xs font-medium text-green-700'
                : quantityChanged
                ? 'font-medium text-blue-600'
                : wasRemoved
                ? 'font-medium text-red-600'
                : 'text-xs text-gray-500'
            )}
          >
            {previousItem?.orderQuantity}
            {isNew && 'ADDED'}
          </td>
        )}
        <td className={classNames(cellClassName, 'text-center')}>
          <input
            type="text"
            name={`item-order-quantity-${item.id}`}
            autoComplete="off"
            autoFocus={item.id <= 0}
            value={orderQuantity}
            onChange={handleOrderQuantityChange}
            onBlur={handleOrderQuantityBlur}
            onFocus={handleFocus}
            disabled={readonly}
            className={classNames(
              inputClassName,
              'max-w-[100px] text-right',
              isBlanketOverbooked && 'border-red-500'
            )}
          />
        </td>
        <td className={classNames(cellClassName, 'text-center')}>
          <div className="flex max-w-[190px] flex-row rounded-md border border-gray-300 shadow-sm">
            <div className="flex items-center px-2">
              <input
                type="checkbox"
                checked={item.hasPotCover}
                onChange={handleHasPotCoverChange}
                disabled={readonly}
                className="h-4 w-4 rounded border-gray-300 text-blue-600 ring-0 focus:ring-0"
              />
            </div>
            <input
              type="text"
              name={`item-pot-cover-${item.id}`}
              autoComplete="off"
              value={item.potCover || ''}
              onChange={handlePotCoverChange}
              onFocus={handleFocus}
              disabled={readonly}
              className={classNames(
                'flex max-w-[115px] flex-grow rounded-md border-transparent text-center text-xs shadow-sm placeholder:italic placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500',
                item.hasPotCover ? '' : 'invisible'
              )}
              ref={potCoverRef}
              placeholder="PC"
            />
            <div
              className={classNames(
                'flex px-2 pt-1',
                !readonly && item.hasPotCover ? '' : 'invisible'
              )}
            >
              <DropdownMenu
                items={potCovers.map((text) => ({
                  text,
                  selected: handlePotCoverSelected,
                }))}
              />
            </div>
          </div>
        </td>
        <td className={classNames(cellClassName, 'text-center')}>
          <input
            type="text"
            name={`item-upc-${item.id}`}
            autoComplete="off"
            value={item.upc || ''}
            onChange={handleUPCChange}
            disabled={readonly}
            className={classNames(inputClassName, 'w-[125px] text-center')}
          />
        </td>
        <td className={classNames(cellClassName, 'text-center')}>
          <input
            type="text"
            name={`item-date-code-${item.id}`}
            autoComplete="off"
            value={item.dateCode || ''}
            onChange={handleDateCodeChange}
            disabled={readonly}
            className={classNames(inputClassName, 'max-w-[125px] text-center')}
          />
        </td>
        <td className={classNames(cellClassName, 'text-center')}>
          <input
            type="text"
            name={`item-retail-${item.id}`}
            autoComplete="off"
            value={item.retail || ''}
            onChange={handleRetailChange}
            disabled={readonly}
            className={classNames(inputClassName, 'max-w-[125px] text-center')}
          />
        </td>
        <td className={classNames(cellClassName, 'text-center')}>
          <input
            type="checkbox"
            name={`item-weights-and-measures-${item.id}`}
            checked={item.weightsAndMeasures}
            onChange={handleWeightsAndMeasuresChange}
            disabled={readonly}
            className="h-4 w-4 rounded border-gray-300 text-blue-600 ring-0 focus:ring-0"
          />
        </td>
        <td className={classNames(cellClassName, 'pr-4 text-center')}>
          {!readonly && (
            <>
              <button
                type="button"
                className="btn-delete px-2 py-1"
                onClick={handleDeleteClick}
                tabIndex={-1}
              >
                <Icon icon="trash" />
              </button>
              <button
                type="button"
                className="btn-secondary ml-1 mt-2 border-blue-500 px-2 py-1 text-blue-500"
                onClick={handleDuplicateClick}
                tabIndex={-1}
              >
                <Icon icon="copy" />
              </button>
            </>
          )}
          <Alert
            title="Remove Item"
            message="Are you sure you want to remove this item?"
            colour="danger"
            open={showDeleteAlert}
            cancel={handleDeleteCancel}
            confirm={handleDeleteConfirm}
          />
        </td>
      </tr>
      <tr className="border border-b-2 border-t-0 border-gray-200">
        <td className={classNames(cellClassName, 'align-top')}>
          <div className="ml-auto mr-2 w-32">
            <HeadlessUI.Switch.Group as="div" className="mt-2">
              <HeadlessUI.Switch
                checked={hasSpecialPricing}
                onChange={handleHasSpecialPricingChange}
                className={classNames(
                  hasSpecialPricing ? 'bg-blue-400' : 'bg-gray-200',
                  'relative inline-flex h-4 w-8 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
                )}
              >
                <span
                  aria-hidden="true"
                  className={classNames(
                    hasSpecialPricing ? 'translate-x-4' : 'translate-x-0',
                    'pointer-events-none inline-block h-3 w-3 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                  )}
                />
              </HeadlessUI.Switch>
              <HeadlessUI.Switch.Label className="ml-2">
                <span className="text-xs">Special Pricing</span>
              </HeadlessUI.Switch.Label>
            </HeadlessUI.Switch.Group>
            {hasSpecialPricing && (
              <div>
                <label
                  htmlFor={`item-special-price-${item.id}`}
                  className="block text-xs italic text-gray-500"
                >
                  Price
                </label>
                <div className="relative col-start-1 rounded-md shadow-sm">
                  <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                    <span className="text-xs text-gray-500">$</span>
                  </div>
                  <input
                    type="text"
                    id={`item-special-price-${item.id}`}
                    name={`item-special-price-${item.id}`}
                    autoComplete="off"
                    ref={specialPricingRef}
                    value={specialPrice || ''}
                    onChange={handleSpecialPriceChange}
                    onBlur={handleSpecialPriceBlur}
                    onFocus={handleFocus}
                    className={classNames(
                      inputClassName,
                      'block w-[100px] rounded-md border-gray-300 pl-7 text-right'
                    )}
                  />
                </div>
              </div>
            )}
          </div>
        </td>
        <td className={classNames(cellClassName, 'align-top')}>
          <div className="flex flex-col">
            <div className="relative flex items-center justify-center">
              <div className="flex h-5 items-center">
                <input
                  id={`approximate-${item.id}`}
                  name="approximate"
                  type="checkbox"
                  className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  checked={item.isApproximate}
                  onChange={handleIsApproximateChange}
                  disabled={readonly}
                />
              </div>
              <div className="ml-2 text-xs">
                <label
                  htmlFor={`approximate-${item.id}`}
                  className="text-gray-700"
                >
                  <Icon icon="plus-minus" />
                </label>
              </div>
            </div>

            {isBlanket && bookedQuantity != null && (
              <div
                className={classNames(
                  'mt-2 text-xs',
                  isBlanketOverbooked && 'font-bold text-red-600',
                  hasBookings && 'font-bold text-blue-600',
                  !hasBookings && !isBlanketOverbooked && 'text-gray-500'
                )}
              >
                Booked: &nbsp;
                {formatNumber(bookedQuantity)}
              </div>
            )}
          </div>
        </td>
        <td colSpan={2} className={cellClassName}>
          <textarea
            rows={2}
            className={classNames(
              inputClassName,
              'w-full placeholder:italic placeholder:text-gray-400'
            )}
            value={item.comments || ''}
            onChange={handleCommentsChange}
            disabled={readonly}
            ref={commentsRef}
            placeholder="Item Comments"
          />
          <br />
          <textarea
            rows={2}
            className={classNames(
              inputClassName,
              'w-full placeholder:italic placeholder:text-gray-400'
            )}
            value={item.growerItemNotes || ''}
            onChange={handleGrowerItemNotesChange}
            disabled={readonly}
            placeholder="Grower Item Notes"
          />
        </td>
        <td colSpan={2} className={classNames(cellClassName, 'align-top')}>
          {!!availableBlanketItems.length && (
            <select
              className={classNames(
                'w-auto rounded-md text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500',
                isBlanketOverbooked ? 'border-red-500' : 'border-gray-300'
              )}
              value={item.blanketItemId || ''}
              onChange={handleBlanketItemChange}
              disabled={readonly}
            >
              <option value="">No Blanket</option>
              {availableBlanketItems.map((item) => (
                <ItemBlanketOption
                  key={item.id}
                  blanketItem={item}
                  prebookVendorId={vendor?.id}
                />
              ))}
            </select>
          )}
          {isBlanketOverbooked && (
            <div className="text-center text-xs italic text-red-500">
              Order quantity will overbook blanket.
            </div>
          )}
          {!!weeks.length && (
            <select
              className="w-auto rounded-md text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
              value={item.blanketWeekId || ''}
              onChange={handleBlanketWeekIdChange}
            >
              <option value="">All Weeks</option>
              {weeks.map((w) => (
                <option key={w.weekId} value={w.weekId}>
                  Week {w.week}
                </option>
              ))}
            </select>
          )}
        </td>
        {!isBlanket && isBoekestyn && (
          <td
            colSpan={3}
            className={classNames(cellClassName, 'pr-4 align-top')}
          >
            {!!item.boekestynProducts?.length && (
              <button
                type="button"
                className="btn-secondary mx-2 px-2 py-1 !font-normal"
                onClick={handleBoekestynProductClick}
              >
                Boek Products&nbsp;
                <Icon icon="arrow-right" />
              </button>
            )}
            {!item.boekestynProducts?.length && (
              <select
                className="w-full rounded-md text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
                value={makeOptionKey(
                  item.boekestynPlantId,
                  item.boekestynCustomerAbbreviation
                )}
                onChange={handleBoekestynSalesOrderChange}
                disabled={readonly}
              >
                <option value="">No Production Order</option>
                {availableBoekestynOrders.map((order) => (
                  <ItemBoekestynOption
                    key={`${order.plantId}-${order.customer}`}
                    order={order}
                  />
                ))}
              </select>
            )}
            <div className="pl-3 text-xs italic">
              Week {week}, {year}
            </div>
          </td>
        )}
        <td
          colSpan={2}
          className={classNames(cellClassName, 'text-center align-top')}
        >
          <label
            htmlFor={`upgrade-sheet-${item.id}`}
            className="block text-xs italic text-gray-500"
          >
            Include Upgrade Sheet
          </label>
          <input
            type="checkbox"
            id={`upgrade-sheet-${item.id}`}
            checked={item.upgradeSheet}
            onChange={handleUpgradeSheetChange}
            disabled={readonly}
            className="h-4 w-4 rounded border-gray-300 text-blue-600 ring-0 focus:ring-0"
          />
        </td>
      </tr>
      <ItemBlanketOptions
        item={item}
        open={showBlanketOptions}
        close={handleBlanketOptionsClose}
      />
    </>
  );
}
