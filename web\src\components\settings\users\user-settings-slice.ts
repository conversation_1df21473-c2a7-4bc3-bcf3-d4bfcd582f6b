import {
  createAction,
  createAsyncThunk,
  createSlice,
  AsyncThunk,
  PayloadAction,
} from '@reduxjs/toolkit';
import { ProblemDetails } from '@/utils/problem-details';
import { securityApi, SecurityDataResponse } from 'api/security-service';
import * as security from 'api/models/security';
import { RootState } from '@/services/store';

export interface UserSettingsState {
  isLoading: boolean;
  error: ProblemDetails | null;
  users: string[];
  groups: security.Group[];
  groupUsers: security.GroupUser[];
}

const initialState: UserSettingsState = {
  isLoading: false,
  error: null,
  users: [],
  groups: [],
  groupUsers: [],
};

export const getData: AsyncThunk<
  SecurityDataResponse,
  void,
  { state: RootState }
> = createAsyncThunk(
  'user-settings-getData',
  async (_, { rejectWithValue }) => {
    try {
      return await securityApi.data();
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const saveGroups: AsyncThunk<void, void, { state: RootState }> =
  createAsyncThunk(
    'user-settings-saveGroups',
    async (_, { rejectWithValue, getState }) => {
      try {
        const rootState = getState() as RootState,
          groupUsers = rootState.userSettings.groupUsers;

        return await securityApi.updateGroupUsers(groupUsers);
      } catch (e) {
        return rejectWithValue(e as ProblemDetails);
      }
    }
  );

const getDataPending = createAction(getData.pending.type),
  getDataFulfilled = createAction<SecurityDataResponse>(getData.fulfilled.type),
  getDataRejected = createAction<ProblemDetails>(getData.rejected.type),
  saveGroupsPending = createAction(saveGroups.pending.type),
  saveGroupsFulfilled = createAction(saveGroups.fulfilled.type),
  saveGroupsRejected = createAction<ProblemDetails>(saveGroups.rejected.type);

interface UserGroupArgs {
  groupId: number;
  userName: string;
}

export const userSettingsSlice = createSlice({
  name: 'user-settings',
  initialState,
  reducers: {
    clearState(state) {
      Object.assign(state, { ...initialState });
    },
    clearError(state) {
      state.error = null;
    },
    removeUserFromGroup(state, { payload }: PayloadAction<UserGroupArgs>) {
      const groupUsers = state.groupUsers.filter(
        (gu) =>
          gu.userName !== payload.userName || gu.groupId !== payload.groupId
      );
      state.groupUsers = groupUsers;
    },
    addUserToGroup(state, { payload }: PayloadAction<UserGroupArgs>) {
      const { groupId, userName } = payload,
        id = state.groupUsers.reduce((min, gu) => Math.min(min, gu.id), 0) - 1,
        newUser = { id, groupId, userName },
        groupUsers = state.groupUsers
          .map((gu) => ({ ...gu }))
          .concat([newUser]);

      state.groupUsers = groupUsers;
    },
  },
  extraReducers: (builder) =>
    builder
      .addCase(getDataPending, (state) => {
        state.isLoading = true;
      })
      .addCase(getDataFulfilled, (state, { payload }) => {
        state.isLoading = false;
        state.groups = payload.groups;
        state.groupUsers = payload.groupUsers;
        state.users = payload.users;
      })
      .addCase(getDataRejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      .addCase(saveGroupsPending, (state) => {
        state.isLoading = true;
      })
      .addCase(saveGroupsFulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(saveGroupsRejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      }),
});

export const { clearError, clearState, addUserToGroup, removeUserFromGroup } =
  userSettingsSlice.actions;

export const selectIsLoading = (state: RootState) =>
  state.userSettings.isLoading;
export const selectError = (state: RootState) => state.userSettings.error;
export const selectUsers = (state: RootState) => state.userSettings.users;
export const selectGroups = (state: RootState) => state.userSettings.groups;
export const selectGroupUsers = (state: RootState) =>
  state.userSettings.groupUsers;

export default userSettingsSlice.reducer;
