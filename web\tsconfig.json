{"compilerOptions": {"target": "ESNext", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "incremental": true, "plugins": [{"name": "next"}]}, "include": ["src", "api", ".next/types/**/*.ts", "../../Deploy/Web/types/**/*.ts"], "exclude": ["node_modules"]}