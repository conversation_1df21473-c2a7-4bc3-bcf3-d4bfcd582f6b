import { Fragment, useRef, useState } from 'react';
import { Combobox, Dialog, Transition } from '@headlessui/react';
import {
  addItem,
  getCustomerDetail,
  selectRequiredDate,
  setRequiredDate,
} from './future-order-create-slice';
import { useOpenBlanketItemsQuery } from 'api/prebooks-service';
import { useCustomersQuery } from 'api/spire-service';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { Icon } from '@/components/icon';
import { startsWith } from '@/utils/equals';
import { classNames } from '@/utils/class-names';
import { Customer } from 'api/models/spire';
import { PrebookBlanketItem } from 'api/models/prebooks';
import { formatDate, formatNumber } from '@/utils/format';

export interface BlankStateProps {
  close: () => void;
}

export function BlankSlate({ close }: BlankStateProps) {
  const dispatch = useAppDispatch(),
    [rawQuery, setRawQuery] = useState(''),
    { data: customers } = useCustomersQuery(),
    { data: openBlanketItemsResponse } = useOpenBlanketItemsQuery(),
    requiredDate = useAppSelector(selectRequiredDate),
    requiredDateRef = useRef<HTMLInputElement | null>(null),
    query = rawQuery.toLowerCase().replace(/^[cb]/, ''),
    blanketItems = openBlanketItemsResponse?.blanketItems || [],
    filteredCustomers =
      rawQuery === 'c'
        ? customers || []
        : query === '' || rawQuery.startsWith('b')
        ? []
        : (customers || []).filter((v) => startsWith(v.name, query)),
    filteredBlanketItems =
      rawQuery === '' || rawQuery.startsWith('c')
        ? []
        : blanketItems.filter(
            (i) =>
              (!requiredDate ||
                (requiredDate <= i.requiredDate &&
                  (!i.blanketStartDate ||
                    requiredDate >= i.blanketStartDate))) &&
              (startsWith(i.vendorName, query) ||
                startsWith(i.description, query) ||
                startsWith(i.spirePartNumber, query))
          );

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setRawQuery(e.target.value);
  };

  const handleComboboxChange = async (item: Customer | PrebookBlanketItem) => {
    const customerId = isCustomer(item)
      ? item?.id || null
      : item.customerId || null;

    dispatch(getCustomerDetail(customerId));

    if (!isCustomer(item)) {
      const inventory = {
        id: item.spireInventoryId,
        partNo: item.spirePartNumber,
        description: item.description,
        blanketItemId: item.id,
        vendorId: item.vendorId,
        vendorName: item.vendorName,
        phytoRequired: false,
        comments: null,
        boekestynPlantId: null,
        boekestynCustomerAbbreviation: null,
        useAvailabilityPricing: false,
        upgradeLabourHours: 0,
        quantityPerFinishedItem: null,
      };
      dispatch(addItem(inventory));
    }

    close();
  };

  const handleRequiredDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setRequiredDate(e.target.value || null));
  };

  const handleRequiredDateKeyUp = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      close();
    }
  };

  return (
    <Transition.Root
      show={true}
      as={Fragment}
      afterLeave={() => setRawQuery('')}
      appear
    >
      <Dialog
        as="div"
        className="relative z-40"
        onClose={close}
        initialFocus={requiredDateRef}
      >
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-25 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-40 overflow-y-auto p-20">
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0 scale-95"
            enterTo="opacity-100 scale-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100 scale-100"
            leaveTo="opacity-0 scale-95"
          >
            <Dialog.Panel className="mx-auto max-w-lg transform divide-y divide-gray-100 overflow-hidden rounded-xl bg-white shadow-2xl ring-1 ring-black ring-opacity-5 transition-all">
              <div className="flex justify-center">
                <label
                  htmlFor="required-date"
                  className="block p-4 text-sm font-medium text-gray-500"
                >
                  Required Date
                </label>
                <div className="mt-2 px-4">
                  <input
                    type="date"
                    max="2050-01-01"
                    name="requiredDate"
                    id="required-date"
                    value={requiredDate || ''}
                    onChange={handleRequiredDateChange}
                    onKeyUp={handleRequiredDateKeyUp}
                    ref={requiredDateRef}
                    className="block w-full rounded-md border-gray-300 text-sm shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  />
                </div>
              </div>
              <Combobox onChange={handleComboboxChange}>
                <div className="relative">
                  <Icon
                    icon="magnifying-glass"
                    className="pointer-events-none absolute left-4 top-3.5 h-5 w-5 text-gray-400"
                    aria-hidden="true"
                  />
                  <Combobox.Input
                    type="search"
                    className="h-12 w-full border-0 bg-transparent pl-11 pr-4 text-sm text-gray-800 placeholder-gray-400 focus:ring-0"
                    placeholder="Search..."
                    autoComplete="off"
                    value={rawQuery}
                    onChange={handleSearchChange}
                  />
                </div>

                {(filteredCustomers.length > 0 ||
                  filteredBlanketItems.length > 0) && (
                  <Combobox.Options
                    static
                    className="max-h-80 scroll-py-10 scroll-pb-2 space-y-4 overflow-y-auto p-4 pb-2"
                  >
                    {filteredCustomers.length > 0 && (
                      <li>
                        <h2 className="text-xs font-semibold text-gray-900">
                          Customers
                        </h2>
                        <ul className="-mx-4 mt-2 text-sm text-gray-700">
                          {filteredCustomers.map((customer) => (
                            <Combobox.Option
                              key={customer.id}
                              value={customer}
                              className={({ active }) =>
                                classNames(
                                  'flex cursor-default select-none items-center px-4 py-2',
                                  active
                                    ? 'bg-blue-600 text-white'
                                    : 'bg-white text-gray-700'
                                )
                              }
                            >
                              {customer.name}&nbsp;
                              <span className="text-gray-400">
                                ({customer.customerNo})
                              </span>
                            </Combobox.Option>
                          ))}
                        </ul>
                      </li>
                    )}
                    {filteredBlanketItems.length > 0 && (
                      <li>
                        <h2 className="text-xs font-semibold text-gray-900">
                          Blanket Items
                        </h2>
                        <ul className="-mx-4 mt-2 text-sm text-gray-700">
                          {filteredBlanketItems.map((blanketItem) => (
                            <Combobox.Option
                              key={blanketItem.id}
                              value={blanketItem}
                              className={({ active }) =>
                                classNames(
                                  'grid cursor-default select-none grid-cols-4 items-center border-b-2 px-12 py-2 ',
                                  active
                                    ? 'bg-blue-600 text-white'
                                    : 'bg-white text-gray-400'
                                )
                              }
                            >
                              {({ active }) => (
                                <>
                                  <div
                                    className={classNames(
                                      'col-span-4 truncate text-center text-lg',
                                      active ? 'text-white' : 'text-gray-900'
                                    )}
                                  >
                                    {blanketItem.spirePartNumber}
                                  </div>
                                  <div
                                    className={classNames(
                                      'col-span-3 truncate',
                                      active ? 'text-white' : 'text-gray-900'
                                    )}
                                  >
                                    <div
                                      className={classNames(
                                        'text-sm font-medium',
                                        active ? 'text-white' : 'text-gray-900'
                                      )}
                                    >
                                      {blanketItem.description}
                                    </div>
                                    <div
                                      className={classNames(
                                        'text-sm',
                                        active ? 'text-white' : 'text-gray-900'
                                      )}
                                    >
                                      {blanketItem.vendorName}
                                    </div>
                                    <div
                                      className={classNames(
                                        'truncate text-xs italic',
                                        active ? 'text-white' : 'text-gray-500'
                                      )}
                                    >
                                      {!!blanketItem.blanketStartDate &&
                                        `${formatDate(
                                          blanketItem.blanketStartDate,
                                          'MMM d, yyyy'
                                        )} - `}
                                      {formatDate(
                                        blanketItem.requiredDate,
                                        'MMM d, yyyy'
                                      )}
                                    </div>
                                    {!!blanketItem.customerName && (
                                      <div
                                        className={classNames(
                                          'mt-1 font-medium italic',
                                          active
                                            ? 'text-white'
                                            : 'text-gray-500'
                                        )}
                                      >
                                        {blanketItem.customerName}
                                      </div>
                                    )}
                                  </div>
                                  <div>
                                    {!!blanketItem.bookedQuantity && (
                                      <div
                                        className={classNames(
                                          'text-right',
                                          active
                                            ? 'text-white'
                                            : 'text-gray-500'
                                        )}
                                      >
                                        {formatNumber(
                                          blanketItem.blanketQuantity
                                        )}
                                      </div>
                                    )}
                                    {!!blanketItem.bookedQuantity && (
                                      <div
                                        className={classNames(
                                          'text-right',
                                          active
                                            ? 'text-white'
                                            : 'text-gray-500'
                                        )}
                                      >
                                        {`- ${formatNumber(
                                          blanketItem.bookedQuantity
                                        )}`}
                                      </div>
                                    )}
                                    <div
                                      className={classNames(
                                        'text-md text-right font-bold',
                                        blanketItem.blanketQuantity >=
                                          blanketItem.bookedQuantity
                                          ? 'text-green-600'
                                          : 'text-red-500'
                                      )}
                                    >
                                      {!!blanketItem.bookedQuantity && '= '}
                                      {formatNumber(
                                        blanketItem.blanketQuantity -
                                          blanketItem.bookedQuantity
                                      )}
                                    </div>
                                  </div>
                                </>
                              )}
                            </Combobox.Option>
                          ))}
                        </ul>
                      </li>
                    )}
                  </Combobox.Options>
                )}

                {rawQuery === '?' && (
                  <div className="px-14 py-14 text-center text-sm">
                    <Icon
                      icon="life-ring"
                      className="mx-auto h-6 w-6 text-gray-400"
                      aria-hidden="true"
                    />
                    <p className="mt-4 font-semibold text-gray-900">
                      Help with searching
                    </p>
                    <p className="mt-2 text-gray-500">
                      Use this tool to quickly search for Customers and Blanket
                      Items across the entire platform. You can also use the
                      search modifiers found in the footer below to limit the
                      results to just customers or blanket items.
                    </p>
                  </div>
                )}

                {query !== '' &&
                  rawQuery !== '?' &&
                  filteredCustomers.length === 0 &&
                  filteredBlanketItems.length === 0 && (
                    <div className="px-14 py-14 text-center text-sm">
                      <Icon
                        icon="exclamation-triangle"
                        className="mx-auto h-6 w-6 text-gray-400"
                        aria-hidden="true"
                      />
                      <p className="mt-4 font-semibold text-gray-900">
                        No results found
                      </p>
                      <p className="mt-2 text-gray-500">
                        We couldn’t find anything with that term. Please try
                        again.
                      </p>
                    </div>
                  )}

                <div className="flex flex-wrap items-center bg-gray-50 px-4 py-2.5 text-xs text-gray-700">
                  Type{' '}
                  <kbd
                    className={classNames(
                      'mx-2 flex h-5 w-5 items-center justify-center rounded border bg-white font-semibold',
                      rawQuery.startsWith('c')
                        ? 'border-blue-600 text-blue-600'
                        : 'border-gray-400 text-gray-900'
                    )}
                  >
                    c
                  </kbd>{' '}
                  <span className="inline">to access customers,</span>
                  <kbd
                    className={classNames(
                      'mx-2 flex h-5 w-5 items-center justify-center rounded border bg-white font-semibold',
                      rawQuery.startsWith('b')
                        ? 'border-blue-600 text-blue-600'
                        : 'border-gray-400 text-gray-900'
                    )}
                  >
                    b
                  </kbd>{' '}
                  for blanket items, and{' '}
                  <kbd
                    className={classNames(
                      'mx-2 flex h-5 w-5 items-center justify-center rounded border bg-white font-semibold',
                      rawQuery === '?'
                        ? 'border-blue-600 text-blue-600'
                        : 'border-gray-400 text-gray-900'
                    )}
                  >
                    ?
                  </kbd>{' '}
                  for help.
                </div>
              </Combobox>
            </Dialog.Panel>
          </Transition.Child>
        </div>
      </Dialog>
    </Transition.Root>
  );
}

function isCustomer(item: Customer | PrebookBlanketItem): item is Customer {
  return (
    typeof item['id'] === 'number' &&
    'name' in item &&
    typeof item['name'] === 'string'
  );
}
