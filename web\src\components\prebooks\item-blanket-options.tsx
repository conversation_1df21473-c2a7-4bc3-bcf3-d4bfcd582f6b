import React, { Fragment, useEffect, useRef, useState } from 'react';
import * as HeadlessUI from '@headlessui/react';
import { setItemBlanketOptions } from './prebook-detail-slice';
import { useLazyInventoryItemsQuery } from 'api/spire-service';
import {
  NewPrebookDetailItem,
  PrebookDetailItem,
  PrebookDetailItemBlanketOption,
} from 'api/models/prebooks';
import { InventoryItem } from 'api/models/spire';
import { useAppDispatch } from '@/services/hooks';
import { Icon } from '@/components/icon';

export interface ItemBlanketOptionsProps {
  open: boolean;
  item: PrebookDetailItem | NewPrebookDetailItem;
  close: () => void;
}

export function ItemBlanketOptions({
  open,
  item,
  close,
}: ItemBlanketOptionsProps) {
  const dispatch = useAppDispatch(),
    [query, setQuery] = useState(item.spirePartNumber || ''),
    [blanketOptions, setBlanketOptions] = useState<
      PrebookDetailItemBlanketOption[]
    >([]),
    [inventoryItemsQuery, { data: inventoryItemsData }] =
      useLazyInventoryItemsQuery(),
    searchRef = useRef<HTMLInputElement>(null),
    blanketIds = blanketOptions.map((b) => b.spireInventoryId),
    inventoryItems = inventoryItemsData?.inventoryItems || [],
    collection =
      query.length >= 3
        ? inventoryItems.filter(
            (i) =>
              i.id !== item.spireInventoryId && blanketIds.indexOf(i.id) === -1
          ) || []
        : [];

  useEffect(() => {
    if (query.length >= 3) {
      inventoryItemsQuery(query);
    }
  }, [query, inventoryItemsQuery]);

  const handleQueryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(e.target.value);
  };

  const handleAddItemClick = (inventory: InventoryItem) => {
    const options = blanketOptions.map((o) => ({ ...o })),
      id = options.reduce((min, o) => Math.min(min, o.id), 0) - 1,
      blanket = {
        id,
        prebookItemId: item.id,
        spireInventoryId: inventory.id,
        spirePartNumber: inventory.partNo,
        description: inventory.description,
      };
    options.push(blanket);

    setBlanketOptions(options);
  };

  const handleAddAllItemsClick = () => {
    const options = blanketOptions.map((o) => ({ ...o })),
      id = options.reduce((min, o) => Math.min(min, o.id), 0) - 1,
      allOptions = options.concat(
        collection.map((i, index) => ({
          id: id - index,
          prebookItemId: item.id,
          spireInventoryId: i.id,
          spirePartNumber: i.partNo,
          description: i.description,
        }))
      );

    setBlanketOptions(allOptions);
  };

  const handleRemoveItemClick = (blanket: PrebookDetailItemBlanketOption) => {
    const options = blanketOptions.filter(
      (o) => o.spireInventoryId !== blanket.spireInventoryId
    );
    setBlanketOptions(options);
  };

  const handleSaveClick = () => {
    const args = { itemId: item.id, value: blanketOptions };
    dispatch(setItemBlanketOptions(args));
    close();
  };

  const handleBeforeEnter = () => {
    const query = (item.spirePartNumber || '').slice(0, -2);
    setQuery(query);
    setBlanketOptions(item.blanketOptions || []);
  };

  const handleAfterLeave = () => {
    close();
  };

  return (
    <HeadlessUI.Transition.Root
      show={open}
      as={Fragment}
      afterLeave={handleAfterLeave}
      beforeEnter={handleBeforeEnter}
    >
      <HeadlessUI.Dialog
        as="div"
        className="relative z-30"
        initialFocus={searchRef}
        onClose={close}
      >
        <HeadlessUI.Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </HeadlessUI.Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-0 text-center">
            <HeadlessUI.Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-0 scale-95"
              enterTo="opacity-100 translate-y-0 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 scale-100"
              leaveTo="opacity-0 translate-y-0 scale-95"
            >
              <HeadlessUI.Dialog.Panel className="relative my-8 w-full max-w-2xl transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all">
                <div className="h-[75vh] bg-white p-6 px-4 pb-4 pt-5">
                  <div className="ml-4 mt-0 flex h-full flex-col text-left">
                    <div className="flex flex-row">
                      <div className="mx-0 mr-5 flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-blue-100">
                        <Icon
                          icon="search"
                          className="h-6 w-6 text-blue-600"
                          aria-hidden="true"
                        />
                      </div>
                      <div className="flex flex-col">
                        <HeadlessUI.Dialog.Title
                          as="h3"
                          className="text-lg font-medium leading-6 text-gray-900"
                        >
                          Blanket Item Options
                        </HeadlessUI.Dialog.Title>
                        <p className="italic text-gray-500">
                          Blanket Item options can be used to draw down on
                          blanket quantities with a different item (e.g. a
                          blanket order for NF, with Prebooks for PC).
                        </p>
                      </div>
                    </div>
                    <div className="mx-8 flex flex-col overflow-auto text-center text-sm">
                      <div className="flex flex-col pt-2">
                        <input
                          type="search"
                          autoComplete="off"
                          className="mx-[1px] rounded-md border border-gray-300 bg-white px-3 py-2 text-xs shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                          value={query}
                          onChange={handleQueryChange}
                          ref={searchRef}
                        />
                        <p className="mt-2 text-left italic text-gray-500">
                          Search for items by Spire Part Number or Description.
                        </p>
                      </div>
                      <div className="mt-3 overflow-auto">
                        <h5 className="text-md mx-4 border-b-2 border-gray-200 text-left font-medium">
                          Options
                        </h5>
                        {!blanketOptions.length && (
                          <div className="mx-4 mb-4 bg-yellow-100 p-4 text-lg italic text-yellow-700">
                            No Blanket Item Options Selected
                          </div>
                        )}
                        <ul>
                          {blanketOptions.map((option) => (
                            <li
                              key={option.id}
                              className="mx-4 mb-2 grid grid-cols-4 border-b-2 border-gray-200 bg-blue-100 p-2 text-left"
                            >
                              <div className="col-span-3 truncate text-sm font-medium text-gray-900">
                                {option.spirePartNumber}
                              </div>
                              <div className="row-span-2 flex h-full items-center justify-center border-gray-200 align-middle">
                                <input
                                  type="checkbox"
                                  checked
                                  onClick={() => handleRemoveItemClick(option)}
                                />
                              </div>
                              <div className="col-span-3 col-start-1 truncate text-sm text-gray-500">
                                {option.description}
                              </div>
                            </li>
                          ))}
                        </ul>

                        <div className="mx-4 grid grid-cols-4 border-b-2 border-gray-200 p-2">
                          <h5 className="text-md col-span-3 text-left font-medium">
                            Inventory Items
                          </h5>
                          <div className="flex h-full items-center justify-center align-middle">
                            <input
                              type="checkbox"
                              checked={false}
                              onClick={handleAddAllItemsClick}
                            />
                          </div>
                        </div>

                        {query.length >= 3 && !collection.length && (
                          <div className="mx-4 mb-4 bg-yellow-100 p-4 text-lg italic text-yellow-700">
                            No Items found
                          </div>
                        )}
                        <ul className="divide-y divide-gray-200 rounded-md">
                          {collection.map((item) => (
                            <li
                              key={item.id}
                              className="mx-4 grid grid-cols-4 bg-white p-2 text-left hover:bg-gray-50"
                            >
                              <div className="col-span-3 truncate text-sm font-medium text-gray-900">
                                {item.partNo}
                              </div>
                              <div className="row-span-2 flex h-full items-center justify-center border-gray-200 align-middle">
                                <input
                                  type="checkbox"
                                  onClick={() => handleAddItemClick(item)}
                                />
                              </div>
                              <div className="col-span-3 col-start-1 truncate text-sm text-gray-500">
                                {item.description}
                              </div>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex flex-row justify-end bg-gray-50 px-6 py-3">
                  <button
                    type="button"
                    className="btn-secondary"
                    onClick={close}
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn-primary ml-3"
                    onClick={handleSaveClick}
                  >
                    Save &nbsp;
                    <Icon icon="save" />
                  </button>
                </div>
              </HeadlessUI.Dialog.Panel>
            </HeadlessUI.Transition.Child>
          </div>
        </div>
      </HeadlessUI.Dialog>
    </HeadlessUI.Transition.Root>
  );
}
