import {
  createAction,
  createAsyncThunk,
  createSlice,
  AsyncThunk,
  PayloadAction,
} from '@reduxjs/toolkit';
import { boekestynApi } from 'api/boekestyn-service';
import * as boeks from 'api/models/boekestyns';
import * as prebooks from 'api/models/prebooks';
import { RootState } from '@/services/store';
import { ProblemDetails } from '@/utils/problem-details';
import { sortBy } from '@/utils/sort';

const sortByName = sortBy('name');

export interface BoekestynProduct extends prebooks.PrebookItemBoekestynProduct {
  id: number;
}

export interface ProductSettingsState {
  showDialog: prebooks.PrebookDetailItem | prebooks.NewPrebookDetailItem | null;
  boekestynProducts: BoekestynProduct[];
  error: ProblemDetails | null;
  plants: boeks.Plant[];
  customers: boeks.Customer[];
}

const initialState: ProductSettingsState = {
  showDialog: null,
  boekestynProducts: [],
  error: null,
  plants: [],
  customers: [],
};

interface GetDataResponse {
  plants: boeks.Plant[];
  customers: boeks.Customer[];
}

export const getData: AsyncThunk<GetDataResponse, void, { state: RootState }> =
  createAsyncThunk(
    'boekestyn-product-getData',
    async (_, { rejectWithValue }) => {
      try {
        const [plants, customers] = await Promise.all([
          boekestynApi.plants(),
          boekestynApi.customers(),
        ]);

        return { plants, customers };
      } catch (e) {
        return rejectWithValue(e as ProblemDetails);
      }
    }
  );

const getDataFulfilled = createAction<GetDataResponse>(getData.fulfilled.type),
  getDataRejected = createAction<ProblemDetails>(getData.rejected.type);

export const boekestynPrebookProductSlice = createSlice({
  name: 'boekestyn-prebook-product-slice',
  initialState,
  reducers: {
    setError(state, { payload }: PayloadAction<ProblemDetails | null>) {
      state.error = payload;
    },
    setBoekestynProducts(
      state,
      { payload }: PayloadAction<BoekestynProduct[]>
    ) {
      state.boekestynProducts = payload;
    },
    setShowDialog(
      state,
      {
        payload,
      }: PayloadAction<
        prebooks.PrebookDetailItem | prebooks.NewPrebookDetailItem | null
      >
    ) {
      state.boekestynProducts = (payload?.boekestynProducts || []).map(
        (p, id) => ({
          ...p,
          id,
        })
      );
      state.showDialog = payload;
    },
  },
  extraReducers: (builder) =>
    builder
      .addCase(getDataFulfilled, (state, { payload }) => {
        state.plants = payload.plants.map((p) => ({ ...p })).sort(sortByName);
        state.customers = payload.customers
          .map((c) => ({ ...c }))
          .sort(sortByName);
      })
      .addCase(getDataRejected, (state, { payload }) => {
        state.error = payload;
      }),
});

export const { setError, setBoekestynProducts, setShowDialog } =
  boekestynPrebookProductSlice.actions;

export const selectShowDialog = ({ boekestynPrebookProducts }: RootState) =>
  boekestynPrebookProducts.showDialog;
export const selectError = ({ boekestynPrebookProducts }: RootState) =>
  boekestynPrebookProducts.error;
export const selectPlants = ({ boekestynPrebookProducts }: RootState) =>
  boekestynPrebookProducts.plants;
export const selectCustomers = ({ boekestynPrebookProducts }: RootState) =>
  boekestynPrebookProducts.customers;
export const selectBoekestynProducts = ({
  boekestynPrebookProducts,
}: RootState) => boekestynPrebookProducts.boekestynProducts;

export default boekestynPrebookProductSlice.reducer;
