import { useMemo, useRef, useState, Fragment } from 'react';
import { useDrop } from 'react-dnd';
import * as HeadlessUI from '@headlessui/react';
import {
  useAddSpacingOrderToSpacingScheduleMutation,
  useSpacingOrdersQuery,
} from 'api/boekestyn-spacing-service';
import * as boeks from 'api/models/boekestyns';
import * as models from 'api/models/boekestyns';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import { useAppSelector } from '@/services/hooks';
import { classNames } from '@/utils/class-names';
import { formatNumber } from '@/utils/format';
import { createProblemDetails, ProblemDetails } from '@/utils/problem-details';
import { selectStartDate, selectEndDate } from './admin-slice';
import { selectSchedules, ScheduleSpacingOrderType } from './spacing-slice';
import { SpacingScheduleWorkOrder } from './spacing-schedule-work-order';

interface SpacingScheduleProps {
  line: models.SpacingLine;
  date: string;
}

export function SpacingSchedule({ line, date }: SpacingScheduleProps) {
  const [addOrderToSchedule] = useAddSpacingOrderToSpacingScheduleMutation(),
    [showDetailDialog, setShowDetailDialog] =
      useState<models.SpacingOrder | null>(null),
    [potsToSpace, setPotsToSpace] = useState<number>(0),
    [fromSpaceType, setFromSpaceType] = useState<models.SpacingTypes>('Tight'),
    [toSpaceType, setToSpaceType] = useState<models.SpacingTypes>('Full'),
    [requiresPinching, setRequiresPinching] = useState<boolean>(true),
    [robotProgram, setRobotProgram] = useState(''),
    [comments, setComments] = useState<string | null>(null),
    [error, setError] = useState<ProblemDetails | null>(null),
    schedules = useAppSelector(selectSchedules),
    startDate = useAppSelector(selectStartDate),
    endDate = useAppSelector(selectEndDate),
    { refetch: refetchOrders } = useSpacingOrdersQuery({
      startDate,
      endDate,
    }),
    schedule = useMemo(
      () =>
        schedules.find((s) => s.lineId === line.id && s.date === date) ?? {
          id: 0,
          lineId: line.id,
          date,
          workOrders: [],
        },
      [schedules, line.id, date]
    ),
    totalPots = useMemo(
      () =>
        schedule.workOrders.reduce((total, o) => total + o.potsToSpace, 0) ?? 0,
      [schedule]
    ),
    estimatedHours = useMemo(
      () =>
        schedule.workOrders.reduce((total, o) => total + o.estimatedHours, 0) ??
        0,
      [schedule]
    ),
    [{ isOver }, drop] = useDrop<
      models.SpacingOrder,
      void,
      { isOver: boolean }
    >(() => ({
      accept: ScheduleSpacingOrderType,
      collect: (monitor) => ({
        isOver: monitor.isOver(),
      }),
      drop(order) {
        setError(null);
        setShowDetailDialog(order);
      },
    })),
    ref = useRef<HTMLDivElement>(null);

  const handleDetailCancel = () => {
    setShowDetailDialog(null);
  };

  const handleDetailConfirm = async () => {
    if (showDetailDialog) {
      if (!potsToSpace) {
        setError(
          createProblemDetails('Please enter the number of pots to space.')
        );
        return;
      }
      if (!robotProgram) {
        setError(createProblemDetails('Please select a robot program.'));
        return;
      }

      try {
        const order = { ...showDetailDialog };
        await addOrderToSchedule({
          crewSize: 1,
          potsToSpace,
          fromSpaceType,
          toSpaceType,
          requiresPinching,
          robotProgram,
          comments,
          schedule,
          order,
        }).unwrap();

        refetchOrders();

        setShowDetailDialog(null);
      } catch (error) {
        setError(error as ProblemDetails);
      }
    }
  };

  const handleDialogAfterEnter = () => {
    var orderPots = showDetailDialog?.pots ?? 0,
      partiallySpaced = showDetailDialog?.potsPartiallySpaced ?? 0,
      fullySpaced = showDetailDialog?.potsFullySpaced ?? 0,
      potsToSpace = Math.max(orderPots - partiallySpaced - fullySpaced, 0),
      from: models.SpacingTypes = partiallySpaced > 0 ? 'Partial' : 'Tight',
      to: models.SpacingTypes =
        showDetailDialog?.hasPartialSpace &&
        partiallySpaced == 0 &&
        from !== 'Partial'
          ? 'Partial'
          : 'Full';
    setPotsToSpace(potsToSpace);
    setFromSpaceType(from);
    setToSpaceType(to);
    setRequiresPinching(!!showDetailDialog?.hasPinching);
    if (showDetailDialog?.plant) {
      setRobotProgram(
        boeks.findSpacingRobotProgram(showDetailDialog.plant, to)
      );
    } else {
      setRobotProgram('');
    }
    setComments(null);
  };

  const handleFromSpaceTypeChange = (
    event: React.ChangeEvent<HTMLSelectElement>
  ) => {
    setFromSpaceType(event.target.value as models.SpacingTypes);
  };

  const handleToSpaceTypeChange = (
    event: React.ChangeEvent<HTMLSelectElement>
  ) => {
    setToSpaceType(event.target.value as models.SpacingTypes);
  };

  const handleRobotProgramChange = (
    event: React.ChangeEvent<HTMLSelectElement>
  ) => {
    setRobotProgram(event.target.value);
  };

  drop(ref);
  return (
    <div
      ref={ref}
      className={classNames(
        'm-2 rounded border p-2',
        isOver && 'border-green-600'
      )}
    >
      <h2 className="text-lg font-semibold">{line.name}</h2>
      <div className="flex-grow">
        {!!schedule.id && (
          <table className="min-w-full divide-y divide-gray-300 text-sm">
            <thead>
              <tr className="sticky top-0 z-10">
                <th className="whitespace-nowrap bg-gray-100 p-2 text-left">
                  Lot #
                </th>
                <th className="whitespace-nowrap bg-gray-100 p-2 text-left">
                  Size / Plant
                </th>
                <th className="whitespace-nowrap bg-gray-100 p-2 text-center">
                  Spacing
                </th>
                <th className="whitespace-nowrap bg-gray-100 p-2 text-center">
                  Pinching
                </th>
                <th className="whitespace-nowrap bg-gray-100 p-2 text-right">
                  Pots
                </th>
                <th className="whitespace-nowrap bg-gray-100 p-2 text-right">
                  Hours
                </th>
                <th className="whitespace-nowrap bg-gray-100 p-2 text-right">
                  &nbsp;
                </th>
              </tr>
            </thead>
            <tbody>
              {schedule.workOrders.map((order) => (
                <SpacingScheduleWorkOrder
                  key={order.id}
                  order={order}
                  scheduleId={schedule.id}
                />
              ))}
            </tbody>
            <tfoot>
              <tr>
                <th colSpan={4} className="p-2 text-right">
                  Total Hours:
                </th>
                <th className="p-2 text-right">{formatNumber(totalPots)}</th>
                <th className="p-2 text-right">
                  {formatNumber(estimatedHours, '0,0.0')}
                </th>
                <th className="p-2 text-right">&nbsp;</th>
              </tr>
            </tfoot>
          </table>
        )}
        {!schedule.id && (
          <div className="flex h-24 items-center justify-center text-gray-500">
            <span className="text-sm italic">Drag to schedule orders.</span>
          </div>
        )}
      </div>
      <HeadlessUI.Transition.Root
        show={!!showDetailDialog}
        as={Fragment}
        afterEnter={handleDialogAfterEnter}
      >
        <HeadlessUI.Dialog
          as="div"
          className="relative z-30"
          onClose={() => setShowDetailDialog(null)}
        >
          <HeadlessUI.Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
          </HeadlessUI.Transition.Child>

          <div className="fixed inset-0 z-10 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-0 text-center">
              <HeadlessUI.Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 translate-y-0 scale-95"
                enterTo="opacity-100 translate-y-0 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 translate-y-0 scale-100"
                leaveTo="opacity-0 translate-y-0 scale-95"
              >
                <HeadlessUI.Dialog.Panel className="relative my-8 w-full max-w-lg transform overflow-hidden rounded-lg bg-white p-4 text-left shadow-xl transition-all">
                  <div className="rounded border p-8">
                    <h3 className="text-lg font-semibold text-gray-900">
                      {showDetailDialog?.orderNumber}
                      <div className="italic">
                        {showDetailDialog?.plant.name}
                      </div>
                    </h3>
                    <div className="mt-4 grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-500">
                          Pots to Space
                        </label>
                        <input
                          type="number"
                          value={potsToSpace}
                          onChange={(e) =>
                            setPotsToSpace(parseInt(e.target.value, 10))
                          }
                          className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-500">
                          Requires Pinching?
                        </label>
                        <HeadlessUI.Switch
                          checked={requiresPinching}
                          onChange={setRequiresPinching}
                          className={classNames(
                            requiresPinching ? 'bg-blue-400' : 'bg-gray-200',
                            'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
                          )}
                        >
                          <span
                            aria-hidden="true"
                            className={classNames(
                              requiresPinching
                                ? 'translate-x-5'
                                : 'translate-x-0',
                              'pointer-events-none inline-block size-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                            )}
                          />
                        </HeadlessUI.Switch>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-500">
                          From Space Type
                        </label>
                        <select
                          id="blanket-filter"
                          value={fromSpaceType}
                          onChange={handleFromSpaceTypeChange}
                          className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                        >
                          <option value="Tight">Tight</option>
                          <option value="Partial">Partial</option>
                          <option value="Full">Full</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-500">
                          To Space Type
                        </label>
                        <select
                          id="blanket-filter"
                          value={toSpaceType}
                          onChange={handleToSpaceTypeChange}
                          className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                        >
                          <option value="Full">Full</option>
                          <option value="Partial">Partial</option>
                          <option value="Tight">None (leave tight)</option>
                        </select>
                      </div>
                      <div className="col-span-2">
                        <label className="block text-sm font-medium text-gray-500">
                          Robot Program
                        </label>
                        <select
                          id="robot-program"
                          value={robotProgram}
                          onChange={handleRobotProgramChange}
                          className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                        >
                          <option value="">Choose Robot Program</option>
                          {boeks.spacingRobotPrograms.map((program) => (
                            <option key={program} value={program}>
                              {program}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div className="col-span-2">
                        <label className="block text-sm font-medium text-gray-500">
                          Comments
                        </label>
                        <textarea
                          rows={3}
                          value={comments ?? ''}
                          onChange={(e) => setComments(e.target.value)}
                          className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        />
                      </div>
                    </div>
                  </div>
                  <Error error={error} clear={() => setError(null)} />
                  <div className="mt-4 text-right">
                    <button
                      type="button"
                      className="btn-secondary"
                      onClick={handleDetailCancel}
                    >
                      Cancel
                    </button>
                    <button
                      type="button"
                      className="btn-primary ml-2"
                      onClick={handleDetailConfirm}
                    >
                      Save &nbsp;
                      <Icon icon="save" />
                    </button>
                  </div>
                </HeadlessUI.Dialog.Panel>
              </HeadlessUI.Transition.Child>
            </div>
          </div>
        </HeadlessUI.Dialog>
      </HeadlessUI.Transition.Root>
    </div>
  );
}
