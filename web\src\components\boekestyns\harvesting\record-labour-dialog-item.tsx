import { useMemo, useState, useEffect } from 'react';
import * as models from 'api/models/boekestyns';
import { handleFocus } from '@/utils/focus';
import { formatNumber } from '@/utils/format';

interface RecordLabourDialogItemProps {
  harvestingOrder: models.HarvestingAdminOrderItem | null;
  variety: models.HarvestingWorkOrderVariety;
  history: models.HarvestingWorkOrderLabourVarietyItem[];
  onChange: (
    varietyName: string,
    property: 'harvested' | 'thrownOut' | 'numberTwos',
    value: number
  ) => void;
}

export function RecordLabourDialogItem({
  variety,
  harvestingOrder,
  history,
  onChange,
}: RecordLabourDialogItemProps) {
  const [harvested, setHarvested] = useState(''),
    [thrownOut, setThrownOut] = useState(''),
    [numberTwos, setNumberTwos] = useState(''),
    previousHarvest = useMemo(
      () =>
        history
          .filter((h) => h.varietyName === variety.name)
          .reduce((total, h) => total + h.harvested + h.thrownOut, 0),
      [history, variety.name]
    ),
    remaining = useMemo(
      () =>
        Math.max(
          (harvestingOrder?.varieties.find((v) => v.name === variety.name)
            ?.pots || 0) - previousHarvest,
          0
        ),
      [harvestingOrder, variety.name, previousHarvest]
    );

  useEffect(() => {
    setHarvested('');
    setThrownOut('');
  }, [variety]);

  const handleHarvestedBlur = () => {
    const value = parseInt(harvested, 10);
    onChange(variety.name, 'harvested', isNaN(value) ? 0 : value);
  };

  const handleHarvestedChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setHarvested(e.target.value);
  };

  const handleThrownOutBlur = () => {
    const value = parseInt(thrownOut, 10);
    onChange(variety.name, 'thrownOut', isNaN(value) ? 0 : value);
  };

  const handleThrownOutChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setThrownOut(e.target.value);
  };

  const handleNumberTwosBlur = () => {
    const value = parseInt(numberTwos, 10);
    onChange(variety.name, 'numberTwos', isNaN(value) ? 0 : value);
  };

  const handleNumberTwosChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNumberTwos(e.target.value);
  };

  return (
    <tr>
      <td className="w-1 whitespace-nowrap p-2 text-left">{variety.name}</td>
      <td className="w-1 p-2 text-right">
        {formatNumber(variety.pots, '0,0')}
      </td>
      <td className="w-1">{formatNumber(previousHarvest, '0,0')}</td>
      <td className="w-1 p-2 text-right">{formatNumber(remaining, '0,0')}</td>
      <td className="w-1 text-center">
        <input
          value={harvested}
          onChange={handleHarvestedChange}
          onBlur={handleHarvestedBlur}
          onFocus={handleFocus}
          className="block w-32 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
        />
      </td>
      <td className="w-1 text-center">
        <input
          type="number"
          value={thrownOut}
          onChange={handleThrownOutChange}
          onBlur={handleThrownOutBlur}
          onFocus={handleFocus}
          className="block w-32 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
        />
      </td>
      <td>
        <input
          type="number"
          value={numberTwos}
          onChange={handleNumberTwosChange}
          onBlur={handleNumberTwosBlur}
          onFocus={handleFocus}
          className="block w-32 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
        />
      </td>
      <td>&nbsp;</td>
    </tr>
  );
}
