import { useEffect, useState } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useDebounce } from 'use-debounce';
import {
  useGetStickingWorkOrdersByDateQuery,
  useStickingQuery,
} from 'api/boekestyn-sticking-service';
import { Error } from '@/components/error';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { routes } from '@/services/routes';
import {
  selectDate,
  selectWorkOrders,
  selectError,
  setError,
  setDate,
  selectLines,
  setLine,
  selectLine,
} from '@/components/boekestyns/sticking/sticking-slice';
import { StickingItem } from '@/components/boekestyns/sticking/sticking-item';
import { formatDate } from '@/utils/format';

export default function Sticking() {
  const dispatch = useAppDispatch(),
    date = useAppSelector(selectDate),
    line = useAppSelector(selectLine),
    lines = useAppSelector(selectLines),
    workOrders = useAppSelector(selectWorkOrders),
    error = useAppSelector(selectError),
    [dateValue, setDateValue] = useState<string | null>(
      formatDate(new Date(), 'yyyy-MM-dd')
    ),
    [debouncedDateValue] = useDebounce(dateValue, 500);

  useStickingQuery();
  useGetStickingWorkOrdersByDateQuery(date);

  useEffect(() => {
    if (debouncedDateValue) {
      dispatch(setDate(debouncedDateValue));
    }
  }, [debouncedDateValue, dispatch]);

  const handleClearError = () => {
    dispatch(setError(null));
  };

  const handleDateChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    setDateValue(e.target.value || null);
  };

  const handleLineChange = async (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = parseInt(e.target.value),
      line = isNaN(value) ? null : value;
    dispatch(setLine(line));
  };

  return (
    <div className="flex h-full flex-col overflow-y-auto">
      <Head>
        <title>Boekestyn Sticking</title>
      </Head>
      <header className="bg-white shadow">
        <div className="grid grid-cols-1">
          <div className="mb-4 border-b shadow">
            <div className="mx-auto flex max-w-7xl items-center justify-between px-8">
              <div className="flex">
                <nav className="ml-24 flex flex-row">
                  <Link
                    href={routes.boekestyns.list.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Boekestyn Item List
                  </Link>
                  <Link
                    href={routes.boekestyns.sales.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Boekestyn Sales
                  </Link>
                  <div className="group relative my-2 text-nowrap rounded-lg bg-blue-500 p-2 text-center text-sm font-medium text-white">
                    Sticking
                  </div>
                  <Link
                    href={routes.boekestyns.spacing.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Spacing
                  </Link>
                  <Link
                    href={routes.boekestyns.harvesting.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Harvesting
                  </Link>
                  <Link
                    href={routes.boekestyns.upcs.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    UPCs
                  </Link>
                  <Link
                    href={routes.boekestyns.prep.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Order Prep
                  </Link>
                  <Link
                    href={routes.boekestyns.packing.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Order Packing
                  </Link>
                  <Link
                    href={routes.boekestyns.admin.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Admin
                  </Link>
                </nav>
              </div>
            </div>
          </div>
          <div className="bg-gray-100 py-2">
            <div className="mx-auto flex max-w-7xl items-center justify-between px-8">
              <div className="flex flex-grow items-center align-top">
                <div className="flex w-full flex-col rounded p-2">
                  <div className="flex w-full flex-row justify-center gap-2 rounded-sm">
                    <div>
                      <label htmlFor="date">Date</label>
                      <input
                        type="date"
                        max="2050-01-01"
                        id="date"
                        value={date}
                        onChange={handleDateChange}
                        className="w-full !max-w-none text-xs"
                      />
                    </div>
                    <div>
                      <label htmlFor="line">Line</label>
                      <select
                        id="line"
                        value={line || ''}
                        onChange={handleLineChange}
                        className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                      >
                        <option value="">All Lines</option>
                        {lines.map((l) => (
                          <option key={l.id} value={l.id}>
                            {l.name}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>
      <Error error={error} clear={handleClearError} />
      <main className="flex-grow">
        <div className="mx-auto h-full px-8">
          <div className="mt-8 flex h-full flex-col">
            <div className="-mx-8 -my-2 h-full">
              <table className="text-normal min-w-full divide-y divide-gray-300 lg:text-xl">
                <thead>
                  <tr className="sticky top-0 z-10 bg-white">
                    <th className="whitespace-nowrap px-8 py-2 text-left font-semibold text-gray-900">
                      &nbsp;
                    </th>
                    <th className="whitespace-nowrap px-8 py-2 text-left font-semibold text-gray-900">
                      Lot #
                    </th>
                    <th className="whitespace-nowrap px-8 py-2 text-left font-semibold text-gray-900">
                      Product
                    </th>
                    <th className="px-8 py-2 text-right font-semibold text-gray-900">
                      Cuttings
                    </th>
                    <th className="px-8 py-2 text-right font-semibold text-gray-900">
                      Sticking Hours
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {workOrders.map((workOrder) => (
                    <StickingItem key={workOrder.id} workOrder={workOrder} />
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
