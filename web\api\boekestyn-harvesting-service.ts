import { createApi } from '@reduxjs/toolkit/query/react';
import { axiosBaseQuery } from './api-base';
import * as models from './models/boekestyns';

export const boekestynHarvestingApi = createApi({
  reducerPath: 'boekestyn-harvesting-api',
  baseQuery: axiosBaseQuery('boekestyns/harvesting/'),
  refetchOnMountOrArgChange: true,
  tagTypes: [
    'HarvestingSchedules',
    'HarvestingWorkOrdersByDate',
    'HarvestingLabour',
  ],
  endpoints: (builder) => ({
    harvesting: builder.query<BoekestynHarvestingResponse, void>({
      query: () => ({
        url: '',
      }),
    }),
    harvestingOrders: builder.query<
      BoekestynHarvestingOrdersResponse,
      BoekestynHarvestingOrdersArgs
    >({
      query: ({ startDate, endDate }) => ({
        url: `orders?startDate=${startDate}&endDate=${endDate}`,
      }),
    }),
    harvestingSchedules: builder.query<
      BoekestynHarvestingSchedulesResponse,
      BoekestynHarvestingSchedulesArgs
    >({
      query: ({ date }) => ({
        url: `schedules?date=${date}`,
      }),
      providesTags: ['HarvestingSchedules'],
    }),
    schedulesForOrder: builder.query<
      BoekestynHarvestingSchedulesForOrderResponse,
      SchedulesForOrderArgs
    >({
      query: ({ orderId }) => ({
        url: `schedules/${orderId}`,
      }),
    }),
    createWorkOrders: builder.mutation<void, CreateWorkOrdersArgs>({
      query: (data) => ({
        url: `workOrders`,
        method: 'POST',
        data,
      }),
      invalidatesTags: ['HarvestingSchedules'],
    }),
    sortHarvestingWorkOrders: builder.mutation<
      void,
      SortHarvestingWorkOrdersArgs
    >({
      query: (data) => ({
        url: `workOrders/sort`,
        method: 'POST',
        data,
      }),
      invalidatesTags: ['HarvestingSchedules'],
    }),
    updateHarvestingWorkOrderComment: builder.mutation<
      void,
      UpdateHarvestingWorkOrderCommentArgs
    >({
      query: ({ id, comment }) => ({
        url: `workOrders/${id}/comment`,
        method: 'POST',
        data: { comment },
      }),
      invalidatesTags: ['HarvestingSchedules'],
    }),
    getHarvestingWorkOrdersByDate: builder.query<
      HarvestingWorkOrdersByDateResponse,
      string
    >({
      query: (date) => ({
        url: `workOrders?date=${date}`,
      }),
      providesTags: ['HarvestingWorkOrdersByDate'],
    }),
    harvestingLabour: builder.query<HarvestingLabourResponse, string>({
      query: (orderId) => ({
        url: `labour?orderId=${orderId}`,
      }),
      providesTags: ['HarvestingLabour'],
    }),
    startHarvestingWorkOrderLabour: builder.mutation<
      void,
      StartHarvestingWorkOrderLabourArgs
    >({
      query: (data) => ({
        url: `workOrders/${data.workOrderId}/labour/start`,
        method: 'POST',
        data,
      }),
      invalidatesTags: ['HarvestingWorkOrdersByDate', 'HarvestingLabour'],
    }),
    pauseHarvestingWorkOrderLabour: builder.mutation<
      void,
      PauseHarvestingWorkOrderLabourArgs
    >({
      query: (data) => ({
        url: `workOrders/${data.workOrderId}/labour/pause`,
        method: 'POST',
        data,
      }),
      invalidatesTags: ['HarvestingWorkOrdersByDate', 'HarvestingLabour'],
    }),
    stopHarvestingWorkOrderLabour: builder.mutation<
      void,
      StopHarvestingWorkOrderLabourArgs
    >({
      query: (data) => ({
        url: `workOrders/${data.workOrderId}/labour/stop`,
        method: 'POST',
        data,
      }),
      invalidatesTags: ['HarvestingWorkOrdersByDate', 'HarvestingLabour'],
    }),
  }),
});

export interface BoekestynHarvestingResponse {
  lines: models.HarvestingLine[];
}

interface BoekestynHarvestingOrdersArgs {
  startDate: string;
  endDate: string;
}

export interface BoekestynHarvestingOrdersResponse {
  orders: models.HarvestingAdminOrderItem[];
}

interface BoekestynHarvestingSchedulesArgs {
  date: string;
}

interface SchedulesForOrderArgs {
  orderId: string;
}

export interface BoekestynHarvestingSchedulesResponse {
  schedules: models.HarvestingSchedule[];
}

export interface BoekestynHarvestingSchedulesForOrderResponse {
  schedules: models.HarvestingSchedule[];
  order: models.HarvestingAdminOrderItem;
}

export interface BoekestynHarvestingLinesResponse {
  lines: models.HarvestingLine[];
}

export interface CreateWorkOrdersArgs {
  orderId: string;
  schedules: {
    id: number;
    lineId: number;
    date: string;
    workOrders: {
      id: number;
      order: models.HarvestingAdminOrderItem;
      crewSize: number;
      harvestingComments: string | null;
      defaultExpectedHarvestPercentage: number;
      finalRound: boolean;
      stickingWorkOrderId: number | null;
      varieties: {
        name: string;
        pots: number;
        beginningQuantity: number;
        expectedHarvestPercentage: number;
        comment: string | null;
      }[];
    }[];
  }[];
}

export interface SortHarvestingWorkOrdersArgs {
  workOrders: { workOrderId: number; sortOrder: number }[];
}

export interface UpdateHarvestingWorkOrderCommentArgs {
  id: number;
  comment: string | null;
}

export interface HarvestingWorkOrdersByDateResponse {
  orders: models.HarvestingWorkOrderItem[];
}

export interface HarvestingLabourResponse {
  labour: models.HarvestingWorkOrderLabourVarietyItem[];
}

export interface StartHarvestingWorkOrderLabourArgs {
  workOrderId: number;
  crewSize: number;
}

export interface PauseHarvestingWorkOrderLabourArgs {
  workOrderId: number;
  crewSize: number;
  comments: string | null;
}

export interface HarvestingLabourVariety {
  varietyName: string;
  harvested: number;
  thrownOut: number;
  numberTwos: number;
}

export interface StopHarvestingWorkOrderLabourArgs {
  workOrderId: number;
  crewSize: number;
  comments: string | null;
  labourVarieties: HarvestingLabourVariety[];
  harvestComplete: boolean;
  remainderThrownOut: boolean;
  remainderSentBack: boolean;
  remainderNumberTwos: boolean;
}

export const {
  useHarvestingQuery,
  useHarvestingOrdersQuery,
  useHarvestingSchedulesQuery,
  useLazySchedulesForOrderQuery,
  useCreateWorkOrdersMutation,
  useSortHarvestingWorkOrdersMutation,
  useUpdateHarvestingWorkOrderCommentMutation,
  useGetHarvestingWorkOrdersByDateQuery,
  useStartHarvestingWorkOrderLabourMutation,
  usePauseHarvestingWorkOrderLabourMutation,
  useStopHarvestingWorkOrderLabourMutation,
  useHarvestingLabourQuery,
} = boekestynHarvestingApi;
