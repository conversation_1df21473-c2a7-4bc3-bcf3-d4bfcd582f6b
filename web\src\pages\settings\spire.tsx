import { useEffect, useState } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { settingsService } from 'api/settings-service';
import * as settings from 'api/models/settings';
import { routes } from '@/services/routes';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import { Loading } from '@/components/loading';
import { ProblemDetails } from '@/utils/problem-details';

export default function Spire() {
  const [refreshed, setRefreshed] = useState<settings.SpireRefreshItem | null>(
      null
    ),
    [isLoading, setIsLoading] = useState(false),
    [error, setError] = useState<ProblemDetails | null>(null),
    [date, setDate] = useState('');

  useEffect(() => {
    setError(null);
    setIsLoading(false);
    setRefreshed(null);
  }, []);

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setDate(e.target.value);
  };

  const handleRefreshClick = async () => {
    try {
      setIsLoading(true);
      const refreshed = await settingsService.refreshSpireItems(date);
      setRefreshed(refreshed);
    } catch (e) {
      setError(e as ProblemDetails);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearError = () => {
    setError(null);
  };

  return (
    <>
      <Head>
        <title>Settings: Spire</title>
      </Head>
      <header className="flex-wrap bg-white shadow">
        <div className="mx-auto flex max-w-7xl items-center justify-between px-8 py-6">
          <h2 className="flex-shrink-0 text-2xl font-bold leading-7 text-gray-900">
            <Icon icon="s" />
            &nbsp; Spire Settings
          </h2>
          <div className="flex">
            <Link
              href={routes.settings.home.to()}
              className="btn-secondary inline-flex"
            >
              Close
            </Link>
          </div>
        </div>
      </header>
      <main className="mx-auto max-w-xl">
        <Error error={error} clear={handleClearError} />
        {isLoading && <Loading />}
        <div className="mt-4">
          <div>
            <h2 className="text-3xl text-gray-900">
              Refreshing Spire Products
            </h2>
            <p className="mt-2 italic text-gray-700">
              This will refresh the Part Numbers and Descriptions in the FP
              Drive app, based on the latest values in Spire.
            </p>
            <p className="mt-2 italic text-gray-700">
              You can optionally specify a date, and items prior to that date
              will not be affected.
            </p>
          </div>
          <div className="mt-4 text-center">
            <label className="block" htmlFor="date">
              Date (optional)
            </label>
            <input type="date" value={date} onChange={handleDateChange} />
          </div>
          <div className="mt-8 text-center">
            <button
              type="button"
              className="btn-primary p-4 text-base"
              onClick={handleRefreshClick}
              disabled={isLoading}
            >
              <Icon icon="refresh" spin={isLoading} />
              &nbsp; Refresh Part Numbers
            </button>
          </div>
          {!!refreshed && (
            <div className="mt-4 text-green-700">
              <h1 className="text-center text-xl">Products were refreshed</h1>
              <p className="mt-2 text-center">
                {refreshed.prebooksRefreshed} prebooks were refreshed, and{' '}
                {refreshed.futureOrdersRefreshed} future orders were refreshed.
              </p>
            </div>
          )}
        </div>
      </main>
    </>
  );
}
