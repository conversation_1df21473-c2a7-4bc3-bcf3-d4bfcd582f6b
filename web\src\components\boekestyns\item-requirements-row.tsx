import { Fragment, useState, useMemo } from 'react';
import Link from 'next/link';
import * as models from 'api/models/boekestyns';
import { Icon } from '@/components/icon';
import { classNames } from '@/utils/class-names';
import { formatDate, formatNumber } from '@/utils/format';
import { ItemRequirementsRowCell } from './item-requirements-row-cell';

interface ItemRequirementsRowProps {
  item: string;
  dates: string[];
  items: string[];
  itemDateMap: Map<string, number>;
  itemDetails: Map<string, models.ItemListRequirementItem[]>;
}

export function ItemRequirementsRow({
  item,
  dates,
  itemDateMap,
  itemDetails,
}: ItemRequirementsRowProps) {
  const [isExpanded, setIsExpanded] = useState(false),
    itemDetailsList = useMemo(
      () => itemDetails.get(item) || [],
      [item, itemDetails]
    ),
    dateRequirements = useMemo(
      () =>
        itemDetailsList.filter((detail) => dates.includes(detail.requiredDate)),
      [itemDetailsList, dates]
    ),
    rowTotal = useMemo(
      () =>
        dates.reduce((memo, date) => {
          const key = `${item}_${date}`,
            rowTotal = itemDateMap?.get(key) || 0;
          return memo + rowTotal;
        }, 0),
      [item, dates, itemDateMap]
    ),
    hasDetails = itemDetailsList.length > 0;

  const handleToggle = () => {
    if (!hasDetails) return;
    setIsExpanded((prev) => !prev);
  };

  return (
    <Fragment>
      <tr
        className={classNames(
          isExpanded ? 'bg-gray-50' : '',
          hasDetails ? 'cursor-pointer hover:bg-gray-50' : ''
        )}
        onClick={handleToggle}
      >
        <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900">
          <div className="flex">
            <Icon
              className={classNames('mr-2', hasDetails ? '' : 'invisible')}
              icon={isExpanded ? 'chevron-down' : 'chevron-right'}
            />
            {item}
          </div>
        </td>
        {dates.map((date) => (
          <ItemRequirementsRowCell
            key={date}
            item={item}
            date={date}
            itemDateMap={itemDateMap}
            handleToggle={handleToggle}
          />
        ))}
        <td
          className="whitespace-nowrap px-3 py-4 text-center text-sm font-medium text-gray-900"
          onClick={handleToggle}
        >
          {formatNumber(rowTotal)}
        </td>
      </tr>

      {isExpanded && hasDetails && (
        <tr>
          <td colSpan={dates.length + 2} className="bg-gray-50 px-4 py-2">
            <div className="border-t border-gray-200 pt-2">
              <table className="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr className="bg-gray-100">
                    <th
                      scope="col"
                      className="px-3 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                    >
                      Prebook
                    </th>
                    <th
                      scope="col"
                      className="px-3 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                    >
                      Required Date
                    </th>
                    <th
                      scope="col"
                      className="px-3 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                    >
                      Box Code
                    </th>
                    <th
                      scope="col"
                      className="px-3 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                    >
                      Pot Cover
                    </th>
                    <th
                      scope="col"
                      className="px-3 py-2 text-right text-xs font-medium uppercase tracking-wider text-gray-500"
                    >
                      Quantity
                    </th>
                    <th
                      scope="col"
                      className="px-3 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                    >
                      Comments
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">
                  {dateRequirements.map((detail) => (
                    <tr key={detail.id} className="hover:bg-gray-50">
                      <td className="px-3 py-2 text-xs text-gray-500">
                        <Link
                          href={`/prebooks/${detail.prebookId}`}
                          className="text-blue-600 hover:text-blue-800 hover:underline"
                        >
                          {detail.prebookId}
                        </Link>
                      </td>
                      <td className="px-3 py-2 text-xs text-gray-500">
                        {formatDate(detail.requiredDate)}
                      </td>
                      <td className="px-3 py-2 text-xs text-gray-500">
                        {detail.boxCode || '-'}
                      </td>
                      <td className="px-3 py-2 text-xs text-gray-500">
                        {detail.potCover || '-'}
                      </td>
                      <td className="px-3 py-2 text-right text-xs text-gray-500">
                        {formatNumber(detail.orderQuantity)}
                      </td>
                      <td className="px-3 py-2 text-xs text-gray-500">
                        {detail.comments || '-'}
                      </td>
                    </tr>
                  ))}
                  {dateRequirements.length === 0 && (
                    <tr>
                      <td
                        colSpan={6}
                        className="px-3 py-4 text-center text-sm italic text-gray-500"
                      >
                        No items to display for the selected date range.
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </td>
        </tr>
      )}
    </Fragment>
  );
}
