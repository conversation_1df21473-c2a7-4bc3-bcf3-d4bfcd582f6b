import { useAppSelector } from '@/services/hooks';
import { selectLines } from './harvesting-slice';
import { HarvestingSchedule } from './harvesting-schedule';

interface HarvestingScheduleDayProps {
  date: string;
}

export function HarvestingScheduleDay({ date }: HarvestingScheduleDayProps) {
  const lines = useAppSelector(selectLines);

  return (
    <div className="mb-2 flex-grow overflow-y-auto px-2">
      {lines.map((line) => (
        <HarvestingSchedule key={line.id} line={line} date={date} />
      ))}
    </div>
  );
}
