import { Fragment } from 'react';
import { Menu, Transition } from '@headlessui/react';
import { Icon } from './icon';

export interface DropdownMenuItem {
  text: string;
  value?: string;
  selected: (value: string) => void;
}

interface DropdownMenuProps {
  items: DropdownMenuItem[];
}

export function DropdownMenu({ items }: DropdownMenuProps) {
  const handleItemClick = (item: DropdownMenuItem) => {
    item.selected(item.value || item.text);
  };

  return (
    <Menu as="div" className="relative inline-block">
      <div>
        <Menu.Button
          className="inline-flex justify-center rounded-md bg-white text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none"
          tabIndex={-1}
        >
          &nbsp;
          <Icon icon="chevron-down" className="h-5 w-5" aria-hidden="true" />
        </Menu.Button>
      </div>

      <Transition
        as={Fragment}
        enter="transition ease-out duration-100"
        enterFrom="transform opacity-0 scale-95"
        enterTo="transform opacity-100 scale-100"
        leave="transition ease-in duration-75"
        leaveFrom="transform opacity-100 scale-100"
        leaveTo="transform opacity-0 scale-95"
      >
        <Menu.Items className="absolute right-0 z-10 mt-2 origin-top-right rounded-md bg-white shadow-lg focus:outline-none">
          <div className="divide-y py-1">
            {items.map((item) => (
              <Menu.Item key={item.text}>
                <button
                  type="button"
                  className="block w-full whitespace-nowrap px-2 py-2 text-left text-sm"
                  onClick={() => handleItemClick(item)}
                >
                  {item.text}
                </button>
              </Menu.Item>
            ))}
          </div>
        </Menu.Items>
      </Transition>
    </Menu>
  );
}
