import React, { useState, useRef, useEffect } from 'react';
import { settingsService } from 'api/settings-service';
import * as futureOrders from 'api/models/future-orders';
import { useAppDispatch } from '@/services/hooks';
import { Icon } from '@/components/icon';
import { classNames } from '@/utils/class-names';
import { handleFocus } from '@/utils/focus';
import { SetItemArgs, setItemPropertyValue } from './upgrade-item-list-slice';
import { formatNumber, parseCurrency } from '@/utils/format';

interface UpgradeItemLabourHoursProps {
  items: futureOrders.UpgradeItem[];
}

export function UpgradeItemLabourHours({ items }: UpgradeItemLabourHoursProps) {
  const dispatch = useAppDispatch(),
    [editing, setEditing] = useState(false),
    [editValue, setEditValue] = useState(''),
    editRef = useRef<HTMLInputElement>(null),
    [item, setItem] = useState(items[0]),
    [totalLabourHours, setTotalLabourHours] = useState(0);

  useEffect(() => {
    const maxLabourHours = Math.max(
        ...items.map(({ labourHours }) => labourHours || 0)
      ),
      totalOrderQuantity = items.reduce(
        (total, item) => total + item.orderQuantity,
        0
      ),
      rawHours = maxLabourHours === 0 ? 0 : totalOrderQuantity / maxLabourHours,
      totalLabourHours = Math.round(
        rawHours > 0 && rawHours < 1 ? 1 : rawHours
      );

    setItem(items[0]);
    setTotalLabourHours(totalLabourHours);
  }, [items]);

  const handleEditClick = () => {
    setEditing(true);
    setEditValue(item.labourHours.toString());

    window.setTimeout(() => {
      editRef.current?.focus();
    }, 100);
  };

  const handleCancelClick = () => {
    setEditing(false);
  };

  const handleSaveClick = async () => {
    const value = parseCurrency(editValue);
    await Promise.all(
      items.map(({ id }) => {
        const args: SetItemArgs = { id, propName: 'labourHours', value };
        return dispatch(setItemPropertyValue(args));
      })
    );
    setEditing(false);

    if (item.spireInventoryId && typeof value === 'number' && value) {
      await settingsService.updateUpgradeLabourHours(
        item.spireInventoryId,
        value
      );
    }
  };

  const handleEditChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setEditValue(e.target.value);
  };

  const handleEditKeyUp = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSaveClick();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancelClick();
    }
  };

  if (editing) {
    return (
      <>
        <div className="whitespace-nowrap text-left">Cases / hour</div>
        <div className="height-[25px] flex flex-grow rounded-md">
          <div className="relative flex flex-grow focus-within:z-10">
            <input
              type="text"
              ref={editRef}
              value={editValue}
              onChange={handleEditChange}
              onKeyUp={handleEditKeyUp}
              onFocus={handleFocus}
              className="flex w-10 flex-grow rounded border-0 py-0 text-center text-xs text-gray-900 ring-1 ring-inset ring-gray-300 focus:border-blue-500"
            />
          </div>
          <button
            type="button"
            className="btn-secondary px-2 py-0"
            onClick={handleCancelClick}
          >
            <Icon icon="undo" />
          </button>
          <button
            type="button"
            className="btn-secondary border-green-700 px-2 py-0"
          >
            <Icon
              icon="check"
              className="text-green-900"
              onClick={handleSaveClick}
            />
          </button>
        </div>
      </>
    );
  }

  return (
    <>
      <div
        className={classNames(
          !!totalLabourHours && 'block border-b-2 border-dotted',
          'block max-w-xs cursor-pointer truncate border-gray-300 text-center hover:border-gray-500 hover:bg-yellow-100'
        )}
        onClick={handleEditClick}
      >
        {totalLabourHours ? `${formatNumber(totalLabourHours)}h` : ''}
        {!totalLabourHours && (
          <div className="mx-auto inline-block border-b-2 border-dotted">
            <Icon icon="plus" />
          </div>
        )}
      </div>
    </>
  );
}
