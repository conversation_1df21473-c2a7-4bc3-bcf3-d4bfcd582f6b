import { createSelector, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '@/services/store';
import * as boeks from 'api/models/boekestyns';
import { formatDate } from '@/utils/format';
import { boekestynStickingApi } from 'api/boekestyn-sticking-service';
import { ProblemDetails } from '@/utils/problem-details';

interface StickingState {
  date: string;
  line: number | null;
  workOrders: boeks.StickingWorkOrderItem[];
  lines: boeks.StickingLine[];
  error: ProblemDetails | null;
}

const initialState: StickingState = {
  date: formatDate(new Date(), 'yyyy-MM-dd'),
  line: null,
  workOrders: [],
  lines: [],
  error: null,
};

const stickingSlice = createSlice({
  name: 'sticking',
  initialState,
  reducers: {
    setError(state, { payload }: PayloadAction<ProblemDetails | null>) {
      state.error = payload;
    },
    setDate(state, { payload }: PayloadAction<string>) {
      state.date = payload;
    },
    setLine(state, { payload }: PayloadAction<number | null>) {
      state.line = payload;
    },
  },
  extraReducers: (builder) =>
    builder
      .addMatcher(
        boekestynStickingApi.endpoints.sticking.matchFulfilled,
        (state, { payload }) => {
          state.lines = payload.lines;
        }
      )
      .addMatcher(
        boekestynStickingApi.endpoints.getStickingWorkOrdersByDate
          .matchFulfilled,
        (state, { payload }) => {
          state.workOrders = payload.orders;
        }
      ),
});

export const { setDate, setLine, setError } = stickingSlice.actions;

const selectAllWorkOrders = ({ boekestynSticking }: RootState) =>
  boekestynSticking.workOrders;
export const selectDate = ({ boekestynSticking }: RootState) =>
  boekestynSticking.date;
export const selectLine = ({ boekestynSticking }: RootState) =>
  boekestynSticking.line;
export const selectLines = ({ boekestynSticking }: RootState) =>
  boekestynSticking.lines;
export const selectError = ({ boekestynSticking }: RootState) =>
  boekestynSticking.error;

export const selectWorkOrders = createSelector(
  selectAllWorkOrders,
  selectLines,
  selectLine,
  (workOrders, lines, line) =>
    workOrders.filter(
      (wo) => !line || wo.lineName === lines.find((l) => l.id === line)?.name
    )
);

export default stickingSlice.reducer;
