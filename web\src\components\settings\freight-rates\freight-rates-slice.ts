import {
  createAction,
  createAsyncThunk,
  createSlice,
  AsyncThunk,
} from '@reduxjs/toolkit';
import { settingsApi } from 'api/settings-service';
import * as settings from 'api/models/settings';
import * as spire from 'api/models/spire';
import { RootState } from '@/services/store';
import { ProblemDetails } from '@/utils/problem-details';

export interface FreightRateSettingsState {
  isLoading: boolean;
  error: ProblemDetails | null;
  freightRates: settings.PriceLevelFreightRate[];
  priceLevels: spire.PriceLevel[];
}

const initialState: FreightRateSettingsState = {
  isLoading: false,
  error: null,
  freightRates: [],
  priceLevels: [],
};

export const freightRateSettingsSlice = createSlice({
  name: 'freight-rate-settings',
  initialState,
  reducers: {
    clearError(state) {
      state.error = null;
    },
  },
  extraReducers: (builder) =>
    builder
      .addMatcher(
        settingsApi.endpoints.getFreightRates.matchPending,
        (state) => {
          state.isLoading = true;
          state.error = null;
        }
      )
      .addMatcher(
        settingsApi.endpoints.getFreightRates.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.error = null;
          state.freightRates = payload.freightRates;
          state.priceLevels = payload.priceLevels;
        }
      )
      .addMatcher(
        settingsApi.endpoints.getFreightRates.matchRejected,
        (state, { payload }) => {
          state.isLoading = false;
          state.error = payload as ProblemDetails;
        }
      )
      .addMatcher(
        settingsApi.endpoints.updateFreightRate.matchRejected,
        (state, { payload }) => {
          state.error = payload as ProblemDetails;
        }
      ),
});

export const { clearError } = freightRateSettingsSlice.actions;

export const selectIsLoading = ({ freightRateSettings }: RootState) =>
  freightRateSettings.isLoading;
export const selectError = ({ freightRateSettings }: RootState) =>
  freightRateSettings.error;
export const selectFreightRates = ({ freightRateSettings }: RootState) =>
  freightRateSettings.freightRates;
export const selectPriceLevels = ({ freightRateSettings }: RootState) =>
  freightRateSettings.priceLevels;

export default freightRateSettingsSlice.reducer;
