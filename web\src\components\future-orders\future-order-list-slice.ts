import {
  createAction,
  createSelector,
  createSlice,
  PayloadAction,
  AsyncThunk,
  createAsyncThunk,
} from '@reduxjs/toolkit';
import { DateTime } from 'luxon';
import {
  FutureOrderListDownloadArgs,
  futureOrdersListApi,
  futureOrdersApi,
  FutureOrderItemsListDownloadArgs,
} from 'api/future-orders-service';
import * as models from 'api/models/future-orders';
import { RootState } from '@/services/store';
import { ProblemDetails } from '@/utils/problem-details';
import { contains, equals, startsWith } from '@/utils/equals';
import { sort, sortBy } from '@/utils/sort';
import { formatNumber } from '@/utils/format';

export const blankSalespersonFilter = 'No Salesperson';
export const blankShipToFilter = 'No Ship To';
export const blankCustomerFilter = 'No Customer';
export const blankVendorFilter = 'No Vendor';
export const blankTruckFilter = 'No Truck';
export const blankSeasonFilter = 'No Season';

export const downloadList: AsyncThunk<
  void,
  FutureOrderListDownloadArgs,
  { state: RootState }
> = createAsyncThunk(
  'future-item-list-downloadList',
  async (args, { rejectWithValue }) => {
    try {
      return await futureOrdersApi.futureOrderListDownload(args);
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const downloadItemsList: AsyncThunk<
  void,
  FutureOrderItemsListDownloadArgs,
  { state: RootState }
> = createAsyncThunk(
  'future-item-list-downloadItemsList',
  async (args, { rejectWithValue }) => {
    try {
      return await futureOrdersApi.futureOrderItemsListDownload(args);
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

const downloadListPending = createAction(downloadList.pending.type),
  downloadListFulfilled = createAction(downloadList.fulfilled.type),
  downloadListRejected = createAction<ProblemDetails>(
    downloadList.rejected.type
  ),
  downloadItemsListPending = createAction(downloadItemsList.pending.type),
  downloadItemsListFulfilled = createAction(downloadItemsList.fulfilled.type),
  downloadItemsListRejected = createAction<ProblemDetails>(
    downloadItemsList.rejected.type
  );

interface FutureOrderFilters {
  customer: string | null;
  shipTo: string | null;
  vendor: string | null;
  salesperson: string | null;
  truck: string | null;
  season: string | null;
  includeSpireSalesOrders: boolean;
  phytosOnly: boolean;
}

interface FutureOrderListState {
  startDate: string;
  endDate: string;
  search: string;
  itemSearch: string;
  futureOrdersSort: keyof models.FutureOrderListItem;
  sortFutureOrdersDescending: boolean;
  futureOrderItemsSort: keyof models.FutureOrderSummaryItem;
  sortFutureOrderItemsDescending: boolean;
  filter: FutureOrderFilters;
  futureOrders: models.FutureOrderListItem[];
  items: models.FutureOrderSummaryItem[];
  isLoading: boolean;
  error: ProblemDetails | null;
}

const initialState: FutureOrderListState = {
  startDate: DateTime.now().toFormat('yyyy-MM-dd'),
  endDate: '',
  search: '',
  itemSearch: '',
  futureOrdersSort: 'requiredDate',
  sortFutureOrdersDescending: false,
  futureOrderItemsSort: 'date',
  sortFutureOrderItemsDescending: false,
  filter: {
    customer: null,
    shipTo: null,
    vendor: null,
    salesperson: null,
    truck: null,
    season: null,
    includeSpireSalesOrders: true,
    phytosOnly: false,
  },
  futureOrders: [],
  items: [],
  isLoading: false,
  error: null,
};

export interface FutureOrderSortArgs {
  sort: keyof models.FutureOrderListItem;
  sortDescending: boolean;
}

export interface FutureOrderItemSortArgs {
  sort: keyof models.FutureOrderSummaryItem;
  sortDescending: boolean;
}

export const futureOrderListSlice = createSlice({
  name: 'future-order-list',
  initialState,
  reducers: {
    clearError(state) {
      state.error = null;
    },
    clearSearchAndFilters(state) {
      return {
        ...state,
        startDate: DateTime.now().toFormat('yyyy-MM-dd'),
        endDate: '',
        search: '',
        futureOrdersSort: 'requiredDate',
        sortFutureOrdersDescending: false,
        filter: {
          customer: null,
          shipTo: null,
          vendor: null,
          salesperson: null,
          truck: null,
          season: null,
          includeSpireSalesOrders: true,
          phytosOnly: false,
        },
      };
    },
    clearItemSearchAndFilters(state) {
      return {
        ...state,
        startDate: DateTime.now().toFormat('yyyy-MM-dd'),
        endDate: '',
        itemSearch: '',
        futureOrderItemsSort: 'date',
        sortFutureOrderItemsDescending: false,
        filter: {
          customer: null,
          shipTo: null,
          vendor: null,
          salesperson: null,
          truck: null,
          season: null,
          includeSpireSalesOrders: true,
          phytosOnly: false,
        },
      };
    },
    setStartDate(state, { payload }: PayloadAction<string>) {
      state.startDate = payload;
    },
    setEndDate(state, { payload }: PayloadAction<string>) {
      state.endDate = payload;
    },
    setSearch(state, { payload }: PayloadAction<string>) {
      state.search = payload;
    },
    setItemSearch(state, { payload }: PayloadAction<string>) {
      state.itemSearch = payload;
    },
    setFutureOrderSort(state, { payload }: PayloadAction<FutureOrderSortArgs>) {
      state.futureOrdersSort = payload.sort;
      state.sortFutureOrdersDescending = payload.sortDescending;
    },
    setFutureOrderItemsSort(
      state,
      { payload }: PayloadAction<FutureOrderItemSortArgs>
    ) {
      state.futureOrderItemsSort = payload.sort;
      state.sortFutureOrderItemsDescending = payload.sortDescending;
    },
    setCustomerFilter(state, { payload }: PayloadAction<string | null>) {
      const filter = { ...state.filter, customer: payload };
      state.filter = filter;
    },
    setShipToFilter(state, { payload }: PayloadAction<string | null>) {
      const filter = { ...state.filter, shipTo: payload };
      state.filter = filter;
    },
    setVendorFilter(state, { payload }: PayloadAction<string | null>) {
      const filter = { ...state.filter, vendor: payload };
      state.filter = filter;
    },
    setSalespersonFilter(state, { payload }: PayloadAction<string | null>) {
      const filter = { ...state.filter, salesperson: payload };
      state.filter = filter;
    },
    setTruckFilter(state, { payload }: PayloadAction<string | null>) {
      const filter = { ...state.filter, truck: payload };
      state.filter = filter;
    },
    setSeasonFilter(state, { payload }: PayloadAction<string | null>) {
      const filter = { ...state.filter, season: payload };
      state.filter = filter;
    },
    setIncludeSpireSalesOrdersFilter(
      state,
      { payload }: PayloadAction<boolean>
    ) {
      const filter = {
        ...state.filter,
        includeSpireSalesOrders: payload,
      };
      state.filter = filter;
    },
    setPhytosOnlyFilter(state, { payload }: PayloadAction<boolean>) {
      const filter = {
        ...state.filter,
        phytosOnly: payload,
      };
      state.filter = filter;
    },
  },
  extraReducers: (builder) =>
    builder
      .addCase(downloadListPending, (state) => {
        state.isLoading = true;
      })
      .addCase(downloadListFulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(downloadListRejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      .addCase(downloadItemsListPending, (state) => {
        state.isLoading = true;
      })
      .addCase(downloadItemsListFulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(downloadItemsListRejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      .addMatcher(futureOrdersListApi.endpoints.list.matchPending, (state) => {
        state.isLoading = true;
      })
      .addMatcher(
        futureOrdersListApi.endpoints.list.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.futureOrders = payload.futureOrders;
        }
      )
      .addMatcher(
        futureOrdersListApi.endpoints.list.matchRejected,
        (state, { payload }) => {
          state.isLoading = false;
          if (payload) {
            state.error = payload;
          }
        }
      )
      .addMatcher(
        futureOrdersListApi.endpoints.itemSummary.matchPending,
        (state) => {
          state.isLoading = true;
        }
      )
      .addMatcher(
        futureOrdersListApi.endpoints.itemSummary.matchFulfilled,
        (state, { payload }) => {
          state.isLoading = false;
          state.items = payload.items;
        }
      )
      .addMatcher(
        futureOrdersListApi.endpoints.itemSummary.matchRejected,
        (state, { payload }) => {
          state.isLoading = false;
          if (payload) {
            state.error = payload;
          }
        }
      ),
});

export const {
  clearError,
  clearSearchAndFilters,
  clearItemSearchAndFilters,
  setStartDate,
  setEndDate,
  setSearch,
  setItemSearch,
  setFutureOrderSort,
  setFutureOrderItemsSort,
  setCustomerFilter,
  setShipToFilter,
  setVendorFilter,
  setSalespersonFilter,
  setTruckFilter,
  setSeasonFilter,
  setIncludeSpireSalesOrdersFilter,
  setPhytosOnlyFilter,
} = futureOrderListSlice.actions;

export const selectError = (state: RootState) => state.futureOrderList.error;
export const selectStartDate = (state: RootState) =>
  state.futureOrderList.startDate;
export const selectEndDate = (state: RootState) =>
  state.futureOrderList.endDate;
export const selectSearch = (state: RootState) => state.futureOrderList.search;
export const selectItemSearch = (state: RootState) =>
  state.futureOrderList.itemSearch;
export const selectFutureOrdersSort = (state: RootState) =>
  state.futureOrderList.futureOrdersSort;
export const selectSortFutureOrdersDescending = (state: RootState) =>
  state.futureOrderList.sortFutureOrdersDescending;
export const selectFutureOrderItemsSort = (state: RootState) =>
  state.futureOrderList.futureOrderItemsSort;
export const selectSortFutureOrderItemsDescending = (state: RootState) =>
  state.futureOrderList.sortFutureOrderItemsDescending;
export const selectFilter = (state: RootState) => state.futureOrderList.filter;
export const selectIsLoading = (state: RootState) =>
  state.futureOrderList.isLoading;
const selectAllFutureOrders = (state: RootState) =>
  state.futureOrderList.futureOrders;

export const selectFutureOrderCustomers = createSelector(
  selectAllFutureOrders,
  (futureOrders) => {
    const customers = futureOrders
      .reduce(
        (memo, p) =>
          p.customer && memo.indexOf(p.customer) === -1
            ? memo.concat([p.customer])
            : memo,
        [] as string[]
      )
      .sort();

    if (futureOrders.some((o) => !o.customer)) {
      customers.unshift(blankCustomerFilter);
    }

    return customers;
  }
);
export const selectFutureOrderShipTos = createSelector(
  selectAllFutureOrders,
  selectFilter,
  (futureOrders, { customer }) => {
    const shipTos = futureOrders
      // if there's a customer filter, only show those ones
      .filter(
        (o) =>
          !customer ||
          equals(customer, o.customer) ||
          (customer === blankCustomerFilter && !o.customer)
      )
      .reduce(
        (memo, p) =>
          p.shipTo && memo.indexOf(p.shipTo) === -1
            ? memo.concat([p.shipTo])
            : memo,
        [] as string[]
      )
      .sort();

    if (futureOrders.some((o) => !o.shipTo)) {
      shipTos.unshift(blankShipToFilter);
    }

    return shipTos;
  }
);
export const selectFutureOrderSalespeople = createSelector(
  selectAllFutureOrders,
  (futureOrders) => {
    const salespeople = futureOrders
      .reduce(
        (memo, p) =>
          p.salesperson && memo.indexOf(p.salesperson) === -1
            ? memo.concat([p.salesperson])
            : memo,
        [] as string[]
      )
      .sort();

    if (futureOrders.some((o) => !o.salesperson)) {
      salespeople.unshift(blankSalespersonFilter);
    }

    return salespeople;
  }
);
export const selectFutureOrderTrucks = createSelector(
  selectAllFutureOrders,
  (futureOrders) => {
    const trucks = futureOrders
      .reduce(
        (memo, p) =>
          p.truck && memo.indexOf(p.truck) === -1
            ? memo.concat([p.truck])
            : memo,
        [] as string[]
      )
      .sort();

    if (futureOrders.some((o) => !o.truck)) {
      trucks.unshift(blankTruckFilter);
    }

    return trucks;
  }
);
export const selectFutureOrderSeasons = createSelector(
  selectAllFutureOrders,
  (futureOrders) => {
    const seasons = futureOrders
      .reduce(
        (memo, p) =>
          p.season && memo.indexOf(p.season) === -1
            ? memo.concat([p.season])
            : memo,
        [] as string[]
      )
      .sort();

    if (futureOrders.some((o) => !o.season)) {
      seasons.unshift(blankSeasonFilter);
    }

    return seasons;
  }
);

export const selectFutureOrders = createSelector(
  selectAllFutureOrders,
  selectSearch,
  selectFutureOrdersSort,
  selectSortFutureOrdersDescending,
  selectFilter,
  (futureOrders, search, sortField, sortDescending, filter) => {
    const sortFn = (
        a: models.FutureOrderListItem,
        b: models.FutureOrderListItem
      ) => {
        const direction = sortDescending ? 'descending' : '';

        if (sortField === 'requiredDate') {
          return sort(
            a.requiredDate || a.seasonDate,
            b.requiredDate || b.seasonDate,
            direction
          );
        } else {
          return sortBy(sortField, direction)(a, b);
        }
      },
      list = futureOrders
        .filter(
          (o) =>
            (!search ||
              contains(formatNumber(o.id, '00000'), search) ||
              startsWith(o.customer, search) ||
              startsWith(o.shipTo, search) ||
              startsWith(o.salesperson, search) ||
              startsWith(o.truck, search) ||
              contains(o.customerPurchaseOrderNumber, search) ||
              contains(o.season, search)) &&
            (!filter.customer ||
              equals(filter.customer, o.customer) ||
              (filter.customer === blankCustomerFilter && !o.customer)) &&
            (!filter.shipTo ||
              equals(filter.shipTo, o.shipTo) ||
              (filter.shipTo === blankShipToFilter && !o.shipTo)) &&
            (!filter.salesperson ||
              equals(filter.salesperson, o.salesperson) ||
              (filter.salesperson === blankSalespersonFilter &&
                !o.salesperson)) &&
            (!filter.truck ||
              equals(filter.truck, o.truck) ||
              (filter.truck === blankTruckFilter && !o.truck)) &&
            (!filter.season ||
              equals(filter.season, o.season) ||
              (filter.season === blankSeasonFilter && !o.season)) &&
            (filter.includeSpireSalesOrders || !o.spireSalesOrderNumber) &&
            (!filter.phytosOnly || o.phytoRequired)
        )
        .map((o) => ({ ...o } as models.FutureOrderListItem))
        .sort(sortFn);

    return list;
  }
);

const selectAllFutureOrderItems = (state: RootState) =>
  state.futureOrderList.items;

export const selectFutureOrderItemCustomers = createSelector(
  selectAllFutureOrderItems,
  (items) => {
    const customers = items
      .reduce(
        (memo, p) =>
          p.customer && memo.indexOf(p.customer) === -1
            ? memo.concat([p.customer])
            : memo,
        [] as string[]
      )
      .sort();

    if (items.some((o) => !o.customer)) {
      customers.unshift(blankCustomerFilter);
    }

    return customers;
  }
);
export const selectFutureOrderItemShipTos = createSelector(
  selectAllFutureOrderItems,
  (items) => {
    const shipTos = items
      .reduce(
        (memo, p) =>
          p.shipTo && memo.indexOf(p.shipTo) === -1
            ? memo.concat([p.shipTo])
            : memo,
        [] as string[]
      )
      .sort();

    if (items.some((o) => !o.shipTo)) {
      shipTos.unshift(blankShipToFilter);
    }

    return shipTos;
  }
);
export const selectFutureOrderItemVendors = createSelector(
  selectAllFutureOrderItems,
  (items) => {
    const vendors = items
      .reduce(
        (memo, p) =>
          p.vendor && memo.indexOf(p.vendor) === -1
            ? memo.concat([p.vendor])
            : memo,
        [] as string[]
      )
      .sort();

    if (items.some((o) => !o.vendor)) {
      vendors.unshift(blankVendorFilter);
    }

    return vendors;
  }
);
export const selectFutureOrderItemSalespeople = createSelector(
  selectAllFutureOrderItems,
  (items) => {
    const salespeople = items
      .reduce(
        (memo, p) =>
          p.salesperson && memo.indexOf(p.salesperson) === -1
            ? memo.concat([p.salesperson])
            : memo,
        [] as string[]
      )
      .sort();

    if (items.some((o) => !o.salesperson)) {
      salespeople.unshift(blankSalespersonFilter);
    }

    return salespeople;
  }
);
export const selectFutureOrderItemSeasons = createSelector(
  selectAllFutureOrderItems,
  (items) => {
    const seasons = items
      .reduce(
        (memo, p) =>
          p.season && memo.indexOf(p.season) === -1
            ? memo.concat([p.season])
            : memo,
        [] as string[]
      )
      .sort();

    if (items.some((o) => !o.season)) {
      seasons.unshift(blankSeasonFilter);
    }

    return seasons;
  }
);

export const selectFutureOrderItems = createSelector(
  selectAllFutureOrderItems,
  selectItemSearch,
  selectFutureOrderItemsSort,
  selectSortFutureOrderItemsDescending,
  selectFilter,
  (items, search, sortField, sortDescending, filter) => {
    const sortFn = (
        a: models.FutureOrderSummaryItem,
        b: models.FutureOrderSummaryItem
      ) => {
        const direction = sortDescending ? 'descending' : '';

        if (sortField === 'date') {
          return sort(
            a.date || a.seasonDate,
            b.date || b.seasonDate,
            direction
          );
        } else {
          return sortBy(sortField, direction)(a, b);
        }
      },
      list = items
        .filter(
          (o) =>
            (!search ||
              contains(formatNumber(o.futureOrderId, '00000'), search) ||
              startsWith(o.customer, search) ||
              startsWith(o.shipTo, search) ||
              startsWith(o.salesperson, search) ||
              contains(o.customerItemCode, search) ||
              contains(o.spirePartNumber, search) ||
              contains(o.description, search)) &&
            (!filter.customer ||
              equals(filter.customer, o.customer) ||
              (filter.customer === blankCustomerFilter && !o.customer)) &&
            (!filter.shipTo ||
              equals(filter.shipTo, o.shipTo) ||
              (filter.shipTo === blankShipToFilter && !o.shipTo)) &&
            (!filter.vendor ||
              equals(filter.vendor, o.vendor) ||
              (filter.vendor === blankVendorFilter && !o.vendor)) &&
            (!filter.salesperson ||
              equals(filter.salesperson, o.salesperson) ||
              (filter.salesperson === blankSalespersonFilter &&
                !o.salesperson)) &&
            (!filter.season ||
              equals(filter.season, o.season) ||
              (filter.season === blankSeasonFilter && !o.season))
        )
        .map((o) => ({ ...o } as models.FutureOrderSummaryItem))
        .sort(sortFn);

    return list;
  }
);

export default futureOrderListSlice.reducer;
