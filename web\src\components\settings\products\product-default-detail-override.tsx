import React from 'react';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { Icon } from '@/components/icon';
import { selectSelectedProductDefault } from './product-default-settings-slice';
import { selectOverrides, setOverrides } from './product-default-detail-slice';
import { ProductDefaultDetailPlantOverride } from './product-default-detail-override-plant';

interface ProductDefaultDetailOverrideProps {
  startWeek: number;
  endWeek: number;
}

export function ProductDefaultDetailOverride({
  startWeek,
  endWeek,
}: ProductDefaultDetailOverrideProps) {
  const dispatch = useAppDispatch(),
    productDefault = useAppSelector(selectSelectedProductDefault),
    overrides = useAppSelector(selectOverrides),
    weekOverrides = overrides.filter(
      (o) => o.startWeek === startWeek && o.endWeek === endWeek
    ),
    first = weekOverrides[0],
    weeks = Array(52)
      .fill(null)
      .map((_, i) => i + 1);

  const handleAddOverridePlantClick = () => {
    if (productDefault) {
      const spireInventoryId = productDefault.spireInventoryId,
        updated = overrides.map((m) => ({ ...m })),
        id = updated.reduce((min, o) => Math.min(min, o.id), 0) - 1,
        override = {
          id,
          spireInventoryId,
          startWeek,
          endWeek,
          boekestynPlantId: '',
          boekestynCustomerAbbreviation: null,
          quantityPerFinishedItem: 1,
        };

      updated.push(override);

      dispatch(setOverrides(updated));
    }
  };

  const removeOverride = () => {
    const updated = overrides.filter(
      (m) => m.startWeek !== startWeek || m.endWeek !== endWeek
    );
    dispatch(setOverrides(updated));
  };

  const handleStartWeekChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const week = parseInt(e.target.value);

    if (week) {
      const updated = overrides.map((m) => ({ ...m }));

      updated
        .filter((m) => m.startWeek === startWeek && m.endWeek === endWeek)
        .forEach((u) => (u.startWeek = week));

      dispatch(setOverrides(updated));
    }
  };

  const handleEndWeekChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const week = parseInt(e.target.value);

    if (week) {
      const updated = overrides.map((m) => ({ ...m }));

      updated
        .filter((m) => m.startWeek === startWeek && m.endWeek === endWeek)
        .forEach((u) => (u.endWeek = week));

      dispatch(setOverrides(updated));
    }
  };

  if (!productDefault || !first) {
    return null;
  }

  return (
    <tbody className="rounded-md border">
      <tr className="">
        <td colSpan={3} className="p-2">
          <label className="block text-xs italic">Effective dates</label>
          <select
            className="rounded-md border-gray-300 text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
            value={first.startWeek}
            onChange={handleStartWeekChange}
          >
            {weeks.map((w) => (
              <option key={w} value={w}>
                Week {w}
              </option>
            ))}
          </select>
          &nbsp;&ndash;&nbsp;
          <select
            className="rounded-md border-gray-300 text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
            value={first.endWeek}
            onChange={handleEndWeekChange}
          >
            {weeks.map((w) => (
              <option key={w} value={w}>
                Week {w}
              </option>
            ))}
          </select>
        </td>
        <td className="p-2 text-right">
          <button
            type="button"
            onClick={removeOverride}
            className="btn-delete px-2 py-1 text-xs"
          >
            <Icon icon="trash" />
          </button>
        </td>
      </tr>
      <tr>
        <th className="p-2">Plant</th>
        <th className="p-2">Customer</th>
        <th className="p-2">Qty</th>
        <th className="p-2">&nbsp;</th>
        <th>&nbsp;</th>
      </tr>
      {weekOverrides.map((plant) => (
        <ProductDefaultDetailPlantOverride key={plant.id} plant={plant} />
      ))}
      <tr>
        <td colSpan={3} className="p-2">
          <button
            type="button"
            className="btn-new px-2 py-1 text-xs"
            onClick={handleAddOverridePlantClick}
          >
            Add Plant
          </button>
        </td>
      </tr>
    </tbody>
  );
}
