import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Icon } from '@/components/icon';
import { useAppSelector, useAppDispatch } from '@/services/hooks';
import { classNames } from '@/utils/class-names';
import { isUpc } from '@/utils/equals';
import { formatDate } from '@/utils/format';
import {
  selectTaskItems,
  setPriority,
  startPrep,
  completePrep,
} from './task-slice';

const today = formatDate(new Date(), 'yyyy-MM-dd');

interface PrepItemProps {
  date: string;
}

export function PrepItem({ date }: PrepItemProps) {
  const dispatch = useAppDispatch(),
    tasks = useAppSelector(selectTaskItems),
    dateTasks = tasks.filter(
      (t) =>
        t.requiredDate === date &&
        (!t.prepStart || !t.prepComplete || !isUpc(t.comment))
    ),
    isToday = date === today;

  const handleChangePriority = (id: number, priority: boolean) => {
    dispatch(setPriority({ id, priority }));
  };

  const handlePrepStart = (id: number) => {
    dispatch(startPrep(id));
  };

  const handlePrepComplete = (id: number) => {
    dispatch(completePrep(id));
  };

  if (!dateTasks.length) {
    return null;
  }

  return (
    <tbody
      key={date}
      className={classNames(
        'divide-y',
        isToday ? 'divide-green-700' : 'divide-gray-200'
      )}
    >
      <tr
        className={classNames(
          'sticky top-[40px] border-b',
          isToday ? 'bg-green-700' : 'bg-gray-200'
        )}
      >
        <td
          colSpan={6}
          className={classNames(
            'p-2 align-top font-semibold',
            isToday ? 'text-white' : 'text-gray-900'
          )}
        >
          {date}
        </td>
      </tr>
      {dateTasks.map((task) => (
        <tr key={task.id}>
          <td className="w-1 p-2 text-center align-top">
            <button
              type="button"
              className="small secondary m-2 rounded px-2"
              onClick={() => handleChangePriority(task.id, !task.priority)}
            >
              <FontAwesomeIcon
                icon={[task.priority ? 'fas' : 'fal', 'star']}
                className={classNames(
                  task.priority ? 'text-yellow-500' : 'text-gray-300'
                )}
              />
            </button>
          </td>
          <td className="w-1 p-2 text-center align-top">{task.boxCode}</td>
          <td className="w-1 p-2 text-center align-top">{task.orderQty}</td>
          <td className="p-2 align-top">{task.description}</td>
          <td className="w-1 p-2 text-center align-top">
            {!task.prepStart && (
              <button
                type="button"
                className="small secondary m-2 rounded border px-2"
                onClick={() => handlePrepStart(task.id)}
              >
                <Icon icon="check" />
              </button>
            )}
            {!!task.prepStart && (
              <div className="text-center text-green-700">
                <Icon icon="check-circle" />
              </div>
            )}
          </td>
          <td className="w-1 p-2 text-center align-top">
            {!task.prepComplete && (
              <button
                type="button"
                className={classNames(
                  'small secondary m-2 rounded border px-2',
                  task.prepStart ? '' : 'invisible'
                )}
                onClick={() => handlePrepComplete(task.id)}
              >
                <Icon icon="check" />
              </button>
            )}
            {!!task.prepComplete && (
              <div className="text-center text-green-700">
                <Icon icon="check-circle" />
              </div>
            )}
          </td>
        </tr>
      ))}
    </tbody>
  );
}
