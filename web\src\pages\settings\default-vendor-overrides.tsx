import React, { useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useDefaultVendorOverridesQuery } from 'api/settings-service';
import { useVendorsQuery } from 'api/spire-service';
import * as settings from 'api/models/settings';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { routes } from '@/services/routes';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import { Loading } from '@/components/loading';
import {
  clearError,
  clearState,
  selectError,
  selectIsLoading,
  selectDefaultVendorOverrides,
  selectSearch,
  setSearch,
  setSelectedOverride,
  addOverride,
} from '@/components/settings/default-vendor-overrides/default-vendor-overrides-slice';
import { DefaultVendorOverrideDetail } from '@/components/settings/default-vendor-overrides/default-vendor-override-detail';
import { DefaultVendorOverrideRow } from '@/components/settings/default-vendor-overrides/default-vendor-override-row';

export default function DefaultVendorOverrides() {
  const dispatch = useAppDispatch(),
    search = useAppSelector(selectSearch),
    isLoading = useAppSelector(selectIsLoading),
    error = useAppSelector(selectError),
    overrides = useAppSelector(selectDefaultVendorOverrides),
    { refetch, isFetching } = useDefaultVendorOverridesQuery();

  useVendorsQuery();

  useEffect(() => {
    dispatch(clearState());
  }, [dispatch]);

  const handleClearError = () => {
    dispatch(clearError());
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setSearch(e.target.value));
  };

  const handleRefreshClick = () => {
    refetch();
  };

  const handleAddNewClick = () => {
    dispatch(addOverride());
  };

  return (
    <>
      <Head>
        <title>Settings: Default Vendor Overrides</title>
      </Head>
      <header className="sticky top-0 z-20 flex-wrap bg-white shadow">
        <div className="mx-auto flex max-w-7xl items-center justify-between px-8 py-6">
          <h2 className="flex-shrink-0 text-2xl font-bold leading-7 text-gray-900">
            <Icon icon="calendar-circle-user" />
            &nbsp; Default Vendor Overrides
          </h2>
          <div className="flex flex-grow px-10">
            <input
              type="search"
              value={search}
              onChange={handleSearchChange}
              className="w-full text-xs"
              placeholder="Search for Products"
            />
            <button
              type="button"
              className="btn-secondary"
              onClick={handleRefreshClick}
              disabled={isFetching}
            >
              <Icon icon="refresh" spin={isFetching} />
            </button>
          </div>
          <Link href={routes.settings.home.to()} className="btn-secondary">
            Close
          </Link>
          <button
            type="button"
            className="btn-new ml-2"
            onClick={handleAddNewClick}
          >
            Add Default Vendor Override &nbsp;
            <Icon icon="plus" />
          </button>
        </div>
      </header>
      <main className="flex-grow overflow-auto">
        <div className="mx-auto h-full px-8">
          <Error error={error} clear={handleClearError} />
          {isLoading ? (
            <Loading />
          ) : (
            <div className="mt-8 flex h-full flex-col">
              <div className="-mx-8 -my-2 h-full">
                <div className="inline-block min-w-full px-8 py-2 align-middle">
                  <div className="rounded-lg shadow ring-1 ring-black ring-opacity-5">
                    <table className="min-w-full divide-y divide-gray-300">
                      <thead className="text-sm">
                        <tr className="sticky top-0">
                          <th className="w-1 bg-gray-100 px-2 py-3.5 text-left text-gray-900">
                            &nbsp;
                          </th>
                          <th className="whitespace-nowrap bg-gray-100 px-2 py-3.5 text-left text-gray-900">
                            Part Number
                          </th>
                          <th className="bg-gray-100 px-2 py-3 text-left text-gray-900">
                            Overrides
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white">
                        {overrides.map((override) => (
                          <DefaultVendorOverrideRow
                            key={override.id}
                            override={override}
                          />
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>
      <DefaultVendorOverrideDetail />
    </>
  );
}
