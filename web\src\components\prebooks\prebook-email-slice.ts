import { AccountInfo } from '@azure/msal-browser';
import {
  AsyncThunk,
  createAction,
  createAsyncThunk,
  createSlice,
  PayloadAction,
} from '@reduxjs/toolkit';
import {
  prebooksApi,
  PendingEmailResponse,
  CreateEmailResponse,
} from 'api/prebooks-service';
import { replaceTokens } from 'api/models/prebook-email-template';
import * as models from 'api/models/prebooks';
import * as spire from 'api/models/spire';
import { RootState } from '@/services/store';
import { ProblemDetails, createProblemDetails } from '@/utils/problem-details';
import { contains } from '@/utils/equals';

interface PrebookEmailState {
  to: string;
  cc: string | null;
  bcc: string | null;
  subject: string;
  body: string;
  upgradesBody: string;
  upgradesCc: string | null;
  account: AccountInfo | null;
  prebookEmail: models.PendingPrebookEmail | null;
  templates: spire.EmailTemplate[];
  isLoading: boolean;
  error: ProblemDetails | null;
}

const initialState: PrebookEmailState = {
  to: '',
  cc: null,
  bcc: null,
  subject: '',
  body: '',
  upgradesBody: '',
  upgradesCc: null,
  account: null,
  prebookEmail: null,
  templates: [],
  isLoading: false,
  error: null,
};

export const getPrebookEmail: AsyncThunk<
  PendingEmailResponse,
  number,
  { state: RootState }
> = createAsyncThunk(
  'prebook-email-getPrebookEmail',
  async (id, { rejectWithValue }) => {
    try {
      return await prebooksApi.pendingEmail(id);
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const createPrebookEmail: AsyncThunk<
  CreateEmailResponse,
  number,
  { state: RootState }
> = createAsyncThunk(
  'prebook-email-createPrebookEmail',
  async (id, { rejectWithValue, getState }) => {
    try {
      const state = getState() as RootState,
        { to, cc, bcc, subject, body, upgradesBody, upgradesCc } =
          state.prebookEmail;
      return await prebooksApi.createEmail(
        id,
        to,
        cc,
        bcc,
        subject,
        body,
        upgradesBody,
        upgradesCc
      );
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

const getPrebookEmailPending = createAction(getPrebookEmail.pending.type),
  getPrebookEmailFulfilled = createAction<PendingEmailResponse>(
    getPrebookEmail.fulfilled.type
  ),
  getPrebookEmailRejected = createAction<ProblemDetails>(
    getPrebookEmail.rejected.type
  ),
  createPrebookEmailPending = createAction(createPrebookEmail.pending.type),
  createPrebookEmailFulfilled = createAction<CreateEmailResponse>(
    createPrebookEmail.fulfilled.type
  ),
  createPrebookEmailRejected = createAction<ProblemDetails>(
    createPrebookEmail.rejected.type
  );

export const prebookEmailSlice = createSlice({
  name: 'prebook-email',
  initialState,
  reducers: {
    clearState(state) {
      Object.assign(state, { ...initialState });
    },
    clearError(state) {
      state.error = null;
    },
    setError(state, { payload }: PayloadAction<string>) {
      state.error = createProblemDetails(payload);
    },
    setTo(state, { payload }: PayloadAction<string>) {
      state.to = payload;
    },
    setCc(state, { payload }: PayloadAction<string | null>) {
      state.cc = payload;
    },
    setBcc(state, { payload }: PayloadAction<string | null>) {
      state.bcc = payload;
    },
    setSubject(state, { payload }: PayloadAction<string>) {
      state.subject = payload;
    },
    setBody(state, { payload }: PayloadAction<string>) {
      state.body = payload;
    },
    setUpgradesBody(state, { payload }: PayloadAction<string>) {
      state.upgradesBody = payload;
    },
    setUpgradesCc(state, { payload }: PayloadAction<string | null>) {
      state.upgradesCc = payload;
    },
    setAccount(state, { payload }: PayloadAction<AccountInfo | null>) {
      state.account = payload;
    },
    setTemplate(state, { payload }: PayloadAction<spire.EmailTemplate>) {
      const { prebookEmail, account } = state,
        subject = replaceTokens(payload.subject, prebookEmail, account),
        body = replaceTokens(payload.body, prebookEmail, account).replaceAll(
          '\n',
          '<br />'
        );

      state.subject = subject;
      state.body = body;
    },
  },
  extraReducers: (builder) =>
    builder
      .addCase(getPrebookEmailPending, (state) => {
        state.isLoading = true;
      })
      .addCase(getPrebookEmailFulfilled, (state, { payload }) => {
        state.isLoading = false;
        state.prebookEmail = payload.prebook;
        state.templates = payload.templates;
        state.to = payload.toAddresses.join(';');
        state.cc = null;
        state.bcc = null;
        state.upgradesCc = null;
        state.upgradesBody = '';

        if (payload.templates.length > 0) {
          const { prebookEmail, account } = state,
            template =
              (prebookEmail.previousPrebookDate &&
                payload.templates.find((t) =>
                  contains(t.subject, 'revised')
                )) ||
              payload.templates[0],
            subject = replaceTokens(template.subject, prebookEmail, account),
            body = replaceTokens(
              template.body,
              prebookEmail,
              account
            ).replaceAll('\n', '<br />');

          state.subject = subject;
          state.body = body;
        }
      })
      .addCase(getPrebookEmailRejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      .addCase(createPrebookEmailPending, (state) => {
        state.isLoading = true;
      })
      .addCase(createPrebookEmailFulfilled, (state, { payload }) => {
        state.isLoading = false;
      })
      .addCase(createPrebookEmailRejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      }),
});

export const {
  clearState,
  setTo,
  setCc,
  setBcc,
  setSubject,
  setBody,
  setUpgradesBody,
  setUpgradesCc,
  setAccount,
  setTemplate,
  clearError,
  setError,
} = prebookEmailSlice.actions;

export const selectTo = (state: RootState) => state.prebookEmail.to;
export const selectCc = (state: RootState) => state.prebookEmail.cc;
export const selectBcc = (state: RootState) => state.prebookEmail.bcc;
export const selectSubject = (state: RootState) => state.prebookEmail.subject;
export const selectBody = (state: RootState) => state.prebookEmail.body;
export const selectUpgradesBody = (state: RootState) =>
  state.prebookEmail.upgradesBody;
export const selectUpgradesCc = (state: RootState) =>
  state.prebookEmail.upgradesCc;
export const selectError = (state: RootState) => state.prebookEmail.error;
export const selectIsLoading = (state: RootState) =>
  state.prebookEmail.isLoading;
export const selectTemplates = (state: RootState) =>
  state.prebookEmail.templates;
export const selectPrebookEmail = (state: RootState) =>
  state.prebookEmail.prebookEmail;

export default prebookEmailSlice.reducer;
