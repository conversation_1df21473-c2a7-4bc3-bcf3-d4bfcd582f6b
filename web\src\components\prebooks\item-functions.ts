import {
  NewPrebookDetailItem,
  PrebookBlanketItem,
  PrebookDetailItem,
} from 'api/models/prebooks';

export function itemIsOverbooked(
  item: PrebookDetailItem | NewPrebookDetailItem,
  blanketItems: PrebookBlanketItem[]
) {
  const blanketItem = blanketItems.find((i) => i.id === item.blanketItemId);
  if (!blanketItem) {
    return false;
  }

  return (
    blanketItem.blanketQuantity - blanketItem.bookedQuantity >= 0 &&
    blanketItem.blanketQuantity - blanketItem.bookedQuantity <
      item.orderQuantity
  );
}
