import { useState, useRef, useMemo } from 'react';
import { useDrag } from 'react-dnd';
import * as models from 'api/models/boekestyns';
import { classNames } from '@/utils/class-names';
import { formatDate, formatNumber } from '@/utils/format';
import { Icon } from '@/components/icon';
import { ScheduleSpacingOrderType } from './spacing-slice';

interface SpacingOrderProps {
  order: models.SpacingOrder;
}

const cellClassName = 'whitespace-nowrap px-2 py-1 text-gray-700 align-top';

export function SpacingOrderRow({ order }: SpacingOrderProps) {
  const [expanded, setExpanded] = useState(false),
    ref = useRef<HTMLTableRowElement>(null),
    [, drag] = useDrag(() => ({
      type: ScheduleSpacingOrderType,
      item: order,
    })),
    fullyScheduled = useMemo(
      () => order.potsFullySpaced >= order.pots,
      [order.pots, order.potsFullySpaced]
    );

  const handleExpand = () => {
    setExpanded(!expanded);
  };

  if (!fullyScheduled) {
    drag(ref);
  }

  return (
    <tr
      ref={ref}
      className={classNames(
        fullyScheduled ? 'cursor-default' : 'cursor-pointer hover:bg-gray-100'
      )}
      style={{
        backgroundColor: fullyScheduled ? '' : order.plant.colour,
      }}
    >
      <td className={classNames(cellClassName, 'text-center')}>
        {formatDate(order.fullSpaceDate)}
      </td>
      <td className={classNames(cellClassName, 'text-center')}>
        {order.fullSpaceZone?.name}
      </td>
      <td className={cellClassName}>
        <div className="flex flex-row">
          <div className="flex-grow">
            <span className="font-semibold">{order.orderNumber}</span>
            {!!order.notes && <div className="italic">{order.notes}</div>}
          </div>
          <div
            className={classNames(order.varieties.length ? '' : 'invisible')}
          >
            <button
              type="button"
              className="btn-secondary px-2 py-1"
              onClick={handleExpand}
            >
              <Icon icon={expanded ? 'minus' : 'plus'} />
            </button>
          </div>
        </div>
      </td>
      <td className={cellClassName}>
        <span className="font-semibold">
          {order.plant.size}&nbsp;{order.plant.crop}
        </span>
        {expanded && (
          <div className="pl-4 italic">
            {order.varieties.map((variety) => (
              <div key={variety.name}>{variety.name}</div>
            ))}
          </div>
        )}
      </td>
      <td className={cellClassName}>
        <span className="font-semibold">{order.customer.name}</span>
      </td>
      <td className={classNames(cellClassName, 'text-right')}>
        <span className="font-semibold">{formatNumber(order.pots)}</span>
        {expanded && (
          <div className="italic">
            {order.varieties.map((variety) => (
              <div key={variety.name}>{formatNumber(variety.pots)}</div>
            ))}
          </div>
        )}
        {!expanded && (
          <>
            {!!order.potsPartiallySpaced && (
              <div className="italic">
                {formatNumber(order.potsPartiallySpaced)} partially spaced
              </div>
            )}
            {!!order.potsFullySpaced && (
              <div className="italic">
                {formatNumber(order.potsFullySpaced)} fully spaced
              </div>
            )}
          </>
        )}
      </td>
      <td className={classNames(cellClassName, 'text-right')}>
        {formatNumber(order.spacingHours, '0,0.0')}
      </td>
    </tr>
  );
}
