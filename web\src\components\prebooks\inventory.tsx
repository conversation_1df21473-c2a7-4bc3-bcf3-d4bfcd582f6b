import React, { Fragment, useEffect, useState } from 'react';
import * as HeadlessUI from '@headlessui/react';
import { Icon } from '@/components/icon';
import { useLazyInventoryItemsQuery } from 'api/spire-service';
import * as boeks from 'api/models/boekestyns';
import * as prebooks from 'api/models/prebooks';
import * as spire from 'api/models/spire';
import { InventoryListItem } from './inventory-list-item';

export interface InventoryProps {
  vendor: spire.Vendor | null;
  open: boolean;
  confirm: (
    value: spire.InventoryItem,
    blanketItemId: number | null,
    comment: string | null
  ) => void;
  cancel: () => void;
}

export function Inventory({ open, confirm, cancel, vendor }: InventoryProps) {
  const [query, setQuery] = useState(''),
    [blanketItemId, setBlanketItemId] = useState<number | null>(null),
    [comments, setComments] = useState<string | null>(null),
    [inventoryItemsQuery, { data: inventoryItemsData }] =
      useLazyInventoryItemsQuery(),
    [item, setItem] = useState<spire.InventoryItem | null>(null),
    inventoryItems = inventoryItemsData?.inventoryItems || [],
    collection = query.length >= 3 ? inventoryItems : [];

  useEffect(() => {
    if (query.length >= 3) {
      inventoryItemsQuery(query);
    }
  }, [query, inventoryItemsQuery]);

  const handleQueryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(e.target.value);
  };

  const handleSelectItem = (
    item: spire.InventoryItem,
    blanketItemId: number | null,
    hasBlanketItems: boolean
  ) => {
    setItem(item);
    setBlanketItemId(blanketItemId);
    const comments =
      hasBlanketItems &&
      !blanketItemId &&
      vendor?.id !== boeks.BoekestynVendorId
        ? prebooks.overAndAboveBlanketComment
        : null;
    setComments(comments);
    cancel();
  };

  const handleClose = () => {
    cancel();
  };

  const handleAfterLeave = () => {
    if (item) {
      confirm(item, blanketItemId, comments);
    } else {
      cancel();
    }

    setQuery('');
    setItem(null);
    setBlanketItemId(null);
  };

  return (
    <HeadlessUI.Transition.Root
      show={open}
      as={Fragment}
      afterLeave={handleAfterLeave}
    >
      <HeadlessUI.Dialog
        as="div"
        className="relative z-30"
        onClose={handleClose}
      >
        <HeadlessUI.Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </HeadlessUI.Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-0 text-center">
            <HeadlessUI.Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-0 scale-95"
              enterTo="opacity-100 translate-y-0 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 scale-100"
              leaveTo="opacity-0 translate-y-0 scale-95"
            >
              <HeadlessUI.Dialog.Panel className="relative my-8 w-full max-w-2xl transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all">
                <div className="h-[75vh] bg-white p-6 px-4 pb-4 pt-5">
                  <div className="ml-4 mt-0 flex h-full flex-col text-left">
                    <div className="flex flex-row">
                      <div className="mx-0 mr-5 flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-blue-100">
                        <Icon
                          icon="search"
                          className="h-6 w-6 text-blue-600"
                          aria-hidden="true"
                        />
                      </div>
                      <div className="flex flex-col">
                        <HeadlessUI.Dialog.Title
                          as="h3"
                          className="text-lg font-medium leading-6 text-gray-900"
                        >
                          Find Inventory Item
                        </HeadlessUI.Dialog.Title>
                        <p>
                          Search for a Spire Inventory Item by Part Number or
                          Description
                        </p>
                      </div>
                    </div>
                    <div className="mx-8 flex flex-col overflow-auto text-center text-sm">
                      <div className="flex pt-2">
                        <input
                          type="search"
                          autoComplete="off"
                          className="mx-[1px] w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-xs shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                          onChange={handleQueryChange}
                        />
                      </div>
                      <div className="mt-3 overflow-auto rounded-md border">
                        <ul className="divide-y divide-gray-200">
                          {query.length > 3 && !inventoryItems?.length && (
                            <li className="relative bg-white px-4 py-5">
                              No Items found
                            </li>
                          )}
                          {collection.map((item) => (
                            <InventoryListItem
                              key={item.id}
                              onItemSelected={handleSelectItem}
                              item={item}
                            />
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex flex-row-reverse bg-gray-50 px-6 py-3">
                  <button
                    type="button"
                    className="ml-3 mt-0 inline-flex w-auto justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    onClick={handleClose}
                  >
                    Cancel
                  </button>
                </div>
              </HeadlessUI.Dialog.Panel>
            </HeadlessUI.Transition.Child>
          </div>
        </div>
      </HeadlessUI.Dialog>
    </HeadlessUI.Transition.Root>
  );
}
