import React, { useState } from 'react';
import * as HeadlessUI from '@headlessui/react';
import * as security from 'api/models/security';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { Alert } from '@/components/alert';
import { Icon } from '@/components/icon';
import { classNames } from '@/utils/class-names';
import { contains } from '@/utils/equals';
import {
  selectGroupUsers,
  removeUserFromGroup,
  selectUsers,
  addUserToGroup,
} from './user-settings-slice';

interface GroupProps {
  group: security.Group;
}

export function Group({ group }: GroupProps) {
  const dispatch = useAppDispatch(),
    allGroupUsers = useAppSelector(selectGroupUsers),
    allUsers = useAppSelector(selectUsers),
    [showDeleteAlert, setShowDeleteAlert] = useState(''),
    [newUserQuery, setNewUserQuery] = useState(''),
    [newUser, setNewUser] = useState<string | null>(null),
    groupUsers = allGroupUsers.filter((gu) => gu.groupId === group.id),
    groupUserEmails = groupUsers
      .filter((gu) => gu.userName)
      .map((gu) => gu.userName.toLowerCase()),
    usersNotInGroup = allUsers.filter(
      (u) => u && groupUserEmails.indexOf(u.toLowerCase()) === -1
    ),
    filteredUsers = newUserQuery
      ? usersNotInGroup.filter((u) => contains(u, newUserQuery))
      : usersNotInGroup;

  const handleDeleteClick = (userName: string) => {
    setShowDeleteAlert(userName);
  };

  const handleDeleteConfirm = () => {
    const args = { groupId: group.id, userName: showDeleteAlert };
    dispatch(removeUserFromGroup(args));
    setShowDeleteAlert('');
  };

  const handleDeleteCancel = () => {
    setShowDeleteAlert('');
  };

  const handleUserQueryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewUserQuery(e.target.value);
  };

  const handleNewUserSelected = (userName: string) => {
    dispatch(addUserToGroup({ groupId: group.id, userName }));
    setNewUser(null);
    setNewUserQuery('');
  };

  return (
    <div>
      <h2 className="text-3xl text-gray-900">{group.name}</h2>
      <p className="mb-8 italic text-gray-700">{group.description}</p>
      <h3 className="text-2xl">Users</h3>
      <ul className="flex w-full flex-col pl-8 lg:w-[50%]">
        {groupUsers.map((groupUser) => (
          <li key={groupUser.id} className="flex flex-row border-b py-2">
            <div className="flex-grow truncate">{groupUser.userName}</div>
            <div className="w-12">
              <button
                type="button"
                className="btn-delete px-2 py-1"
                onClick={() => handleDeleteClick(groupUser.userName)}
              >
                <Icon icon="trash" />
              </button>
            </div>
          </li>
        ))}
        <HeadlessUI.Combobox
          as="li"
          className="flex flex-col py-2"
          value={newUser}
          onChange={handleNewUserSelected}
          nullable
        >
          <HeadlessUI.Combobox.Label className="text-xs text-gray-700">
            Add User
          </HeadlessUI.Combobox.Label>
          <div className="relative mr-12 mt-1 flex-grow">
            <HeadlessUI.Combobox.Input
              type="search"
              className="w-full rounded-md border border-gray-300 bg-white py-2 pl-3 pr-10 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
              onChange={handleUserQueryChange}
              autoComplete="off"
            ></HeadlessUI.Combobox.Input>
            <HeadlessUI.Combobox.Button className="absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none">
              <Icon
                icon="caret-down"
                className="h-5 w-5 text-gray-400"
                aria-hidden="true"
              />
            </HeadlessUI.Combobox.Button>
            {filteredUsers.length > 0 && (
              <HeadlessUI.Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                {filteredUsers.map((user) => (
                  <HeadlessUI.Combobox.Option
                    key={user}
                    value={user}
                    className={({ active }) =>
                      classNames(
                        'relative cursor-default select-none py-2 pl-3 pr-9',
                        active ? 'bg-blue-600 text-white' : 'text-gray-900'
                      )
                    }
                  >
                    {({ active, selected }) => (
                      <>
                        <span
                          className={classNames(
                            'truncate',
                            selected && 'font-semibold'
                          )}
                        >
                          {user}
                        </span>

                        {selected && (
                          <span
                            className={classNames(
                              'absolute inset-y-0 right-0 flex items-center pr-4',
                              active ? 'text-white' : 'text-indigo-600'
                            )}
                          >
                            <Icon
                              icon="check"
                              className="h-5 w-5"
                              aria-hidden="true"
                            />
                          </span>
                        )}
                      </>
                    )}
                  </HeadlessUI.Combobox.Option>
                ))}
              </HeadlessUI.Combobox.Options>
            )}
          </div>
        </HeadlessUI.Combobox>
      </ul>
      <Alert
        title="Remove User from Group?"
        message={`Are you sure you want to remove ${showDeleteAlert} from the ${group.name} group?`}
        colour="danger"
        open={!!showDeleteAlert}
        cancel={handleDeleteCancel}
        confirm={handleDeleteConfirm}
        cancelButtonText="No"
        confirmButtonText="Yes"
      />
    </div>
  );
}
