import { Fragment } from 'react';
import * as HeadlessUI from '@headlessui/react';
import { useAppSelector } from '@/services/hooks';
import {
  selectPrebooks,
  selectDirtyPrebooks,
} from './future-order-detail-slice';
import { FutureOrderDetailPrebook } from './future-order-detail-prebook';
import { Icon } from '@/components/icon';
import { classNames } from '@/utils/class-names';

export function FutureOrderDetailPrebooks() {
  const prebooks = useAppSelector(selectPrebooks),
    { dirty } = useAppSelector(selectDirtyPrebooks),
    dirtyPrebookIds = dirty.map((p) => p.id);

  return (
    <div className="mt-4">
      <HeadlessUI.Tab.Group>
        <HeadlessUI.Tab.List
          as="nav"
          className="flex w-full flex-row overflow-x-auto border-b border-t"
        >
          {prebooks.map((prebook) => (
            <HeadlessUI.Tab key={prebook.id} as={Fragment}>
              {({ selected }) => (
                <button
                  type="button"
                  className={classNames(
                    'group relative my-2 mr-4 whitespace-nowrap rounded-lg p-2 text-center text-sm font-medium',
                    selected
                      ? 'bg-blue-500 text-white'
                      : 'bg-white text-gray-500 hover:text-blue-500'
                  )}
                >
                  {prebook.vendorName}
                  {!!prebook.deleted && (
                    <span className="ml-2 italic">**Deleted**</span>
                  )}
                  {dirtyPrebookIds.indexOf(prebook.id) !== -1 && (
                    <Icon
                      icon="exclamation-triangle"
                      title="Prebook does not match the Future Order"
                      className="-my-1 ml-4 mr-2 font-bold text-red-500"
                      size="2x"
                    />
                  )}
                </button>
              )}
            </HeadlessUI.Tab>
          ))}
        </HeadlessUI.Tab.List>
        <HeadlessUI.Tab.Panels>
          {prebooks.map((prebook) => (
            <HeadlessUI.Tab.Panel
              key={prebook.id}
              className="m-2 rounded-lg border p-2"
            >
              <FutureOrderDetailPrebook prebook={prebook} />
            </HeadlessUI.Tab.Panel>
          ))}
        </HeadlessUI.Tab.Panels>
      </HeadlessUI.Tab.Group>
    </div>
  );
}
