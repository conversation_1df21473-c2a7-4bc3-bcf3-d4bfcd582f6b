import { useAppSelector } from '@/services/hooks';
import { equals } from '@/utils/equals';
import { Week } from '@/utils/weeks';
import {
  selectSingleProductItems,
  selectMultipleProductItems,
} from './sales-slice';
import { CustomerShiptoWeekItem } from './customer-ship-to-week-item';

interface CustomerShipToItemProps {
  salesCustomer: string;
  shipTo: string;
  week: Week;
  customer: string;
  multiple: boolean;
}

export function CustomerShipToItem({
  salesCustomer,
  shipTo,
  week,
  customer,
  multiple,
}: CustomerShipToItemProps) {
  const singleProductItems = useAppSelector(selectSingleProductItems),
    multipleProductItems = useAppSelector(selectMultipleProductItems),
    prebookItems = multiple ? multipleProductItems : singleProductItems,
    weekItems = prebookItems.filter(
      (p) =>
        equals(p.customerName, salesCustomer) &&
        ((!shipTo && !p.shipToName) ||
          p.shipToName === shipTo ||
          p.boxCode === shipTo) &&
        p.week === week.week &&
        p.year === week.year &&
        (!customer || equals(p.boekestynCustomerAbbreviation, customer))
    );

  return (
    <td className="whitespace-nowrap border-l border-r border-white bg-blue-100 p-2 text-center">
      {weekItems.map((item) => (
        <CustomerShiptoWeekItem key={item.id} item={item} />
      ))}
    </td>
  );
}
