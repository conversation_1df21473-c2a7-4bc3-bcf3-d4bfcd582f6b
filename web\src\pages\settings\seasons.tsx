import { useEffect, useState } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import * as prebooks from 'api/models/prebooks';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { routes } from '@/services/routes';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import { Loading } from '@/components/loading';
import {
  getData,
  clearError,
  clearState,
  selectError,
  selectIsLoading,
  selectSeasons,
} from '@/components/settings/seasons/season-settings-slice';
import { SeasonDetail } from '@/components/settings/seasons/season-detail';
import { formatDate } from '@/utils/format';

export default function Seasons() {
  const dispatch = useAppDispatch(),
    isLoading = useAppSelector(selectIsLoading),
    error = useAppSelector(selectError),
    seasons = useAppSelector(selectSeasons),
    [selectedSeason, setSelectedSeason] = useState<prebooks.Season | null>(
      null
    );

  useEffect(() => {
    dispatch(clearState());
    dispatch(getData());
  }, [dispatch]);

  const handleClearError = () => {
    dispatch(clearError());
  };

  const handleEditSeasonClick = (season: prebooks.Season) => {
    setSelectedSeason(season);
  };

  const handleSeasonDetailClose = (reload?: boolean) => {
    setSelectedSeason(null);
    if (reload) {
      dispatch(getData());
    }
  };

  return (
    <>
      <Head>
        <title>Settings: Seasons</title>
      </Head>
      <header className="sticky top-0 z-20 flex-wrap bg-white shadow">
        <div className="mx-auto flex max-w-7xl items-center justify-between px-8 py-6">
          <h2 className="flex-shrink-0 text-2xl font-bold leading-7 text-gray-900">
            <Icon icon="calendar-day" />
            &nbsp; Season Settings
          </h2>
          <div className="flex">
            <Link
              href={routes.settings.home.to()}
              className="btn-secondary inline-flex"
            >
              Close
            </Link>
          </div>
        </div>
      </header>
      <main className="flex-grow overflow-auto">
        <div className="mx-auto h-full px-8">
          <Error error={error} clear={handleClearError} />
          {isLoading && <Loading />}
          <div className="mt-8 flex h-full flex-col">
            <div className="-mx-8 -my-2 h-full">
              <div className="inline-block min-w-full px-8 py-2 align-middle">
                <div className="rounded-lg shadow ring-1 ring-black ring-opacity-5">
                  <table className="min-w-full divide-y divide-gray-300">
                    <thead>
                      <tr className="sticky top-0">
                        <th className="bg-gray-100 px-2 py-3.5 text-left text-gray-900">
                          &nbsp;
                        </th>
                        <th className="bg-gray-100 px-2 py-3.5 text-left text-gray-900">
                          Name
                        </th>
                        <th className="bg-gray-100 px-2 py-3.5 text-center text-gray-900">
                          Date
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white">
                      {seasons.map((season) => (
                        <tr key={season.name} className="border-b">
                          <td className="whitespace-nowrap p-4 text-left">
                            <button
                              type="button"
                              onClick={() => handleEditSeasonClick(season)}
                              className="secondary text-blue-600"
                            >
                              <Icon icon="edit" />
                            </button>
                          </td>
                          <td className="whitespace-nowrap px-2 py-4 text-left">
                            {season.name}
                          </td>
                          <td className="whitespace-nowrap px-2 py-4 text-center">
                            {formatDate(season.seasonDate)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
        {!!selectedSeason && (
          <SeasonDetail
            season={selectedSeason}
            close={handleSeasonDetailClose}
          />
        )}
      </main>
    </>
  );
}
