import React, { Fragment, useState } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import * as HeadlessUI from '@headlessui/react';
import { useListQuery } from 'api/future-orders-service';
import * as models from 'api/models/future-orders';
import { usePermissions } from '@/services/auth';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { routes } from '@/services/routes';
import {
  clearError,
  clearSearchAndFilters,
  setStartDate,
  setEndDate,
  setSearch,
  setCustomerFilter,
  setShipToFilter,
  setSalespersonFilter,
  selectStartDate,
  selectEndDate,
  selectSearch,
  selectFutureOrdersSort,
  selectSortFutureOrdersDescending,
  selectIsLoading,
  selectError,
  selectFutureOrders,
  selectFutureOrderCustomers,
  selectFutureOrderShipTos,
  selectFutureOrderSalespeople,
  selectFutureOrderTrucks,
  selectFutureOrderSeasons,
  selectFilter,
  setTruckFilter,
  setSeasonFilter,
  setIncludeSpireSalesOrdersFilter,
  setPhytosOnlyFilter,
  setFutureOrderSort,
  downloadList,
} from '@/components/future-orders/future-order-list-slice';
import { ListItem } from '@/components/future-orders/list-item';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import { Loading } from '@/components/loading';
import { classNames } from '@/utils/class-names';
import { DeletedFutureOrders } from '@/components/future-orders/deleted-future-orders';
import { formatCurrency, formatNumber } from '@/utils/format';

export default function FutureOrders() {
  const dispatch = useAppDispatch(),
    { can } = usePermissions(),
    futureOrders = useAppSelector(selectFutureOrders),
    customers = useAppSelector(selectFutureOrderCustomers),
    shipTos = useAppSelector(selectFutureOrderShipTos),
    salespeople = useAppSelector(selectFutureOrderSalespeople),
    trucks = useAppSelector(selectFutureOrderTrucks),
    seasons = useAppSelector(selectFutureOrderSeasons),
    startDate = useAppSelector(selectStartDate),
    endDate = useAppSelector(selectEndDate),
    search = useAppSelector(selectSearch),
    sort = useAppSelector(selectFutureOrdersSort),
    sortDescending = useAppSelector(selectSortFutureOrdersDescending),
    isLoading = useAppSelector(selectIsLoading),
    error = useAppSelector(selectError),
    filter = useAppSelector(selectFilter),
    { refetch } = useListQuery({ startDate, endDate }),
    [showAdvanced, setShowAdvanced] = useState(
      !!filter.customer ||
        !!filter.shipTo ||
        !!filter.salesperson ||
        !!filter.truck
    ),
    [showDeletedDialog, setShowDeletedDialog] = useState(false),
    hasSpireOrders = futureOrders.some((o) => o.spireSalesOrderNumber),
    readonly = !can('Sales Team'),
    totalCases = futureOrders.reduce((acc, o) => acc + o.caseCount, 0),
    totalDollars = futureOrders.reduce((acc, o) => acc + o.dollarValue, 0);

  const handleDownloadClick = () => {
    const args = {
      items: futureOrders,
    };
    dispatch(downloadList(args));
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setSearch(e.target.value));
  };

  const handleStartDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setStartDate(e.target.value || ''));
  };

  const handleEndDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setEndDate(e.target.value || ''));
  };

  const handleRefreshClick = () => {
    refetch();
  };

  const handleClearError = () => {
    dispatch(clearError());
  };

  const handleColumnSort = (sortProp: keyof models.FutureOrderListItem) => {
    const descending = sortProp === sort ? !sortDescending : false;
    dispatch(
      setFutureOrderSort({ sort: sortProp, sortDescending: descending })
    );
  };

  const handleToggleAdvanced = () => {
    setShowAdvanced(!showAdvanced);
    if (showAdvanced) {
      dispatch(setCustomerFilter(null));
      dispatch(setShipToFilter(null));
      dispatch(setSalespersonFilter(null));
      dispatch(setTruckFilter(null));
    }
  };

  const handleResetSearchClick = async () => {
    await dispatch(clearSearchAndFilters());
    window.setTimeout(refetch);
  };

  const handleCustomerFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setCustomerFilter(e.target.value || null));
  };

  const handleShipToFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setShipToFilter(e.target.value || null));
  };

  const handleSalespersonFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setSalespersonFilter(e.target.value || null));
  };

  const handleTruckFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    dispatch(setTruckFilter(e.target.value || null));
  };

  const handleSeasonFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setSeasonFilter(e.target.value || null));
  };

  const handleIncludeSpireSalesOrdersChange = (include: boolean) => {
    dispatch(setIncludeSpireSalesOrdersFilter(include));
  };

  const handlePhytosOnlyChange = (only: boolean) => {
    dispatch(setPhytosOnlyFilter(only));
  };

  const handleUndeleteClick = () => {
    setShowDeletedDialog(true);
  };

  const handleDeletedFutureOrdersClose = () => {
    setShowDeletedDialog(false);
  };

  const handleDeletedFutureOrdersConfirm = () => {
    setShowDeletedDialog(false);
    refetch();
  };

  interface HeaderButtonProps {
    text: string;
    propName: keyof models.FutureOrderListItem;
    total?: string;
  }
  const HeaderButton = ({ text, propName, total }: HeaderButtonProps) => (
    <button
      type="button"
      className="group inline-flex"
      onClick={() => handleColumnSort(propName)}
    >
      <div>
        {text}
        {!!total && <div className="block text-xs italic">{total}</div>}
      </div>
      <span
        className={classNames(
          'ml-2 flex-none rounded text-gray-400 group-hover:visible group-focus:visible',
          sort !== propName && 'invisible'
        )}
      >
        <Icon
          icon={sortDescending ? 'chevron-down' : 'chevron-up'}
          className="h-5 w-5"
          aria-hidden="true"
        />
      </span>
    </button>
  );

  return (
    <>
      <Head>
        <title>Future Order List</title>
      </Head>
      <header className="bg-white shadow">
        <div className="grid grid-cols-1">
          <div className="mb-4 border-b shadow">
            <div className="mx-auto flex max-w-7xl items-center justify-between px-8">
              <div className="flex">
                <nav className="ml-24 flex flex-row">
                  <div className="group relative my-2 text-nowrap rounded-lg bg-blue-500 p-2 text-center text-sm font-medium text-white">
                    Future Order List
                  </div>
                  <Link
                    href={routes.futureOrders.items.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Future Order Items
                  </Link>
                </nav>
              </div>
            </div>
          </div>
          <div className="bg-gray-100 py-2">
            <div className="mx-auto flex max-w-7xl items-center justify-between px-8">
              <div className="flex flex-grow items-center align-top">
                <div className="flex w-full flex-col rounded p-2">
                  <div className="grid w-full grid-cols-8 gap-2 rounded-sm text-xs">
                    <div>
                      <label htmlFor="start-date">From Date</label>
                      <input
                        type="date"
                        max="2050-01-01"
                        id="start-date"
                        value={startDate}
                        onChange={handleStartDateChange}
                        className="w-full !max-w-none text-xs"
                      />
                    </div>
                    <div>
                      <label htmlFor="end-date">To Date</label>
                      <input
                        type="date"
                        max="2050-01-01"
                        id="end-date"
                        value={endDate}
                        onChange={handleEndDateChange}
                        className="w-full !max-w-none text-xs"
                      />
                    </div>
                    <div className="col-span-4">
                      <label htmlFor="end-date">Search</label>
                      <div className="flex">
                        <input
                          type="search"
                          id="search"
                          name="search"
                          value={search}
                          onChange={handleSearchChange}
                          className="flex-grow text-xs"
                          placeholder="Search"
                          autoComplete="off"
                        />
                        <button
                          type="button"
                          className="btn-secondary flex px-1 focus:ring-0"
                          onClick={handleToggleAdvanced}
                        >
                          <Icon
                            icon={showAdvanced ? 'chevron-up' : 'chevron-down'}
                            className="h-5 w-5"
                          />
                        </button>
                        <button
                          type="button"
                          className="btn-secondary flex px-1 focus:ring-0"
                          title="Reset Search Filters"
                          onClick={handleResetSearchClick}
                        >
                          <Icon
                            icon="magnifying-glass-arrows-rotate"
                            className="h-5 w-5"
                          />
                        </button>
                      </div>
                    </div>
                    <div className="flex items-start justify-center pt-3">
                      <button
                        type="button"
                        onClick={handleRefreshClick}
                        className="btn-secondary flex p-3 text-blue-700 "
                      >
                        <Icon icon="refresh" spin={isLoading} />
                      </button>
                      <button
                        type="button"
                        onClick={handleDownloadClick}
                        className="btn-secondary flex p-3 text-green-700"
                      >
                        <Icon icon="file-excel" />
                      </button>
                    </div>
                    {!readonly && (
                      <div className="flex items-start justify-center pt-3">
                        <Link
                          href={routes.futureOrders.new.to()}
                          className="btn-new flex flex-nowrap rounded-r-none"
                        >
                          <Icon icon="plus-circle" className="flex" />
                          &nbsp;
                          <div className="flex whitespace-nowrap">
                            New Future Order
                          </div>
                        </Link>
                        <HeadlessUI.Menu
                          as="div"
                          className="relative -ml-px block"
                        >
                          <HeadlessUI.Menu.Button className="btn-secondary relative rounded-l-none px-2 focus:outline-none focus:ring-0">
                            <span className="sr-only">Open options</span>
                            <Icon
                              icon="chevron-down"
                              className="h-5 w-5"
                              aria-hidden="true"
                            />
                          </HeadlessUI.Menu.Button>
                          <HeadlessUI.Transition
                            as={Fragment}
                            enter="transition ease-out duration-100"
                            enterFrom="transform opacity-0 scale-95"
                            enterTo="transform opacity-100 scale-100"
                            leave="transition ease-in duration-75"
                            leaveFrom="transform opacity-100 scale-100"
                            leaveTo="transform opacity-0 scale-95"
                          >
                            <HeadlessUI.Menu.Items className="absolute right-0 z-10 -mr-1 mt-2 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                              <div className="py-1">
                                <HeadlessUI.Menu.Item>
                                  {({ active }) => (
                                    <button
                                      type="button"
                                      className={classNames(
                                        active
                                          ? 'bg-gray-100 text-gray-900'
                                          : 'text-gray-700',
                                        'block w-full px-4 py-2 text-left text-sm'
                                      )}
                                      onClick={handleUndeleteClick}
                                    >
                                      <Icon icon="trash-undo" fixedWidth />
                                      &nbsp; Undelete Future Order
                                    </button>
                                  )}
                                </HeadlessUI.Menu.Item>
                              </div>
                            </HeadlessUI.Menu.Items>
                          </HeadlessUI.Transition>
                        </HeadlessUI.Menu>
                      </div>
                    )}
                    {showAdvanced && (
                      <>
                        <div className="col-start-1">
                          <label htmlFor="customer-filter">Customer</label>
                          <select
                            id="customer-filter"
                            value={filter.customer || ''}
                            onChange={handleCustomerFilterChange}
                            className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                          >
                            <option value="">All Customers</option>
                            {customers.map((customer) => (
                              <option key={customer}>{customer}</option>
                            ))}
                          </select>
                        </div>
                        <div className="">
                          <label htmlFor="ship-to-filter">Ship To</label>
                          <select
                            id="ship-to-filter"
                            value={filter.shipTo || ''}
                            onChange={handleShipToFilterChange}
                            className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                          >
                            <option value="">All Ship Tos</option>
                            {shipTos.map((shipTo) => (
                              <option key={shipTo}>{shipTo}</option>
                            ))}
                          </select>
                        </div>
                        <div className="">
                          <label htmlFor="salesperson-filter">
                            Salesperson
                          </label>
                          <select
                            id="salesperson-filter"
                            value={filter.salesperson || ''}
                            onChange={handleSalespersonFilterChange}
                            className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                          >
                            <option value="">All Salespeople</option>
                            {salespeople.map((salesperson) => (
                              <option key={salesperson}>{salesperson}</option>
                            ))}
                          </select>
                        </div>
                        <div>
                          <label htmlFor="truck-filter">Truck</label>
                          <select
                            id="truck-filter"
                            value={filter.truck || ''}
                            onChange={handleTruckFilterChange}
                            className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                          >
                            <option value="">All Trucks</option>
                            {trucks.map((truck) => (
                              <option key={truck}>{truck}</option>
                            ))}
                          </select>
                        </div>
                        <div>
                          <label htmlFor="season-filter">Season</label>
                          <select
                            id="season-filter"
                            value={filter.season || ''}
                            onChange={handleSeasonFilterChange}
                            className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                          >
                            <option value="">All Seasons</option>
                            {seasons.map((season) => (
                              <option key={season}>{season}</option>
                            ))}
                          </select>
                        </div>
                        <div>
                          <label
                            htmlFor="phytos-only"
                            className="block w-full text-center"
                          >
                            Orders with Phytos
                          </label>
                          <div className="mt-3 text-center align-middle">
                            <HeadlessUI.Switch
                              id="phytos-only"
                              checked={filter.phytosOnly}
                              onChange={handlePhytosOnlyChange}
                              className={`${
                                filter.phytosOnly
                                  ? 'bg-blue-600'
                                  : 'bg-gray-200'
                              } relative inline-flex h-6 w-11 items-center rounded-full`}
                            >
                              <span className="sr-only">
                                Orders with Phytos
                              </span>
                              <span
                                className={`${
                                  filter.phytosOnly
                                    ? 'translate-x-6'
                                    : 'translate-x-1'
                                } inline-block h-4 w-4 transform rounded-full bg-white transition`}
                              />
                            </HeadlessUI.Switch>
                          </div>
                        </div>
                        <div>
                          <div>
                            <label
                              htmlFor="spire-filter"
                              className="block w-full text-center"
                            >
                              Include Spire Orders
                            </label>
                            <div className="mt-3 text-center align-middle">
                              <HeadlessUI.Switch
                                id="spire-filter"
                                checked={filter.includeSpireSalesOrders}
                                onChange={handleIncludeSpireSalesOrdersChange}
                                className={`${
                                  filter.includeSpireSalesOrders
                                    ? 'bg-blue-600'
                                    : 'bg-gray-200'
                                } relative inline-flex h-6 w-11 items-center rounded-full`}
                              >
                                <span className="sr-only">
                                  Include Spire Orders
                                </span>
                                <span
                                  className={`${
                                    filter.includeSpireSalesOrders
                                      ? 'translate-x-6'
                                      : 'translate-x-1'
                                  } inline-block h-4 w-4 transform rounded-full bg-white transition`}
                                />
                              </HeadlessUI.Switch>
                            </div>
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>
      <main className="flex-grow overflow-auto">
        <div className="mx-auto h-full px-8">
          <Error error={error} clear={handleClearError} />
          {isLoading && <Loading />}
          <div className="mt-8 flex h-full flex-col">
            <div className="-mx-8 -my-2 h-full">
              <div className="inline-block min-w-full px-8 py-2 align-middle">
                <div className="rounded-lg shadow ring-1 ring-black ring-opacity-5">
                  <table className="min-w-full divide-y divide-gray-300">
                    <thead>
                      <tr className="sticky top-0 z-10">
                        <th
                          scope="col"
                          className="bg-gray-100 py-3.5 pl-3 pr-6 align-top"
                        >
                          &nbsp;
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-left align-top text-sm font-semibold text-gray-900"
                        >
                          <HeaderButton text="Id" propName="id" />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-left align-top text-sm font-semibold text-gray-900"
                        >
                          &nbsp;
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-left align-top text-sm font-semibold text-gray-900"
                        >
                          <HeaderButton text="Date" propName="requiredDate" />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-left align-top text-sm font-semibold text-gray-900"
                        >
                          <HeaderButton text="Season" propName="season" />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-left align-top text-sm font-semibold text-gray-900"
                        >
                          <HeaderButton text="Customer" propName="customer" />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-left align-top text-sm font-semibold text-gray-900"
                        >
                          <HeaderButton
                            text="Salesperson"
                            propName="salesperson"
                          />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-left align-top text-sm font-semibold text-gray-900"
                        >
                          <HeaderButton
                            text="Customer PO"
                            propName="customerPurchaseOrderNumber"
                          />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-left align-top text-sm font-semibold text-gray-900"
                        >
                          <HeaderButton text="Truck" propName="truck" />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-center align-top text-sm font-semibold text-gray-900"
                        >
                          <HeaderButton
                            text="$"
                            propName="dollarValue"
                            total={formatCurrency(totalDollars)}
                          />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-center align-top text-sm font-semibold text-gray-900"
                        >
                          <HeaderButton
                            text="Cases"
                            propName="caseCount"
                            total={formatNumber(totalCases)}
                          />
                        </th>
                        {hasSpireOrders && (
                          <th
                            scope="col"
                            className="bg-gray-100 px-3 py-3.5 text-center text-sm font-semibold text-gray-900"
                          >
                            <HeaderButton
                              text="Spire Order #"
                              propName="spireSalesOrderNumber"
                            />
                          </th>
                        )}
                      </tr>
                    </thead>
                    <tbody className="bg-white">
                      {futureOrders.map((futureOrder) => (
                        <ListItem
                          key={futureOrder.id}
                          futureOrder={futureOrder}
                          refresh={refetch}
                        />
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
        {showDeletedDialog && (
          <DeletedFutureOrders
            close={handleDeletedFutureOrdersClose}
            confirm={handleDeletedFutureOrdersConfirm}
          />
        )}
      </main>
    </>
  );
}
