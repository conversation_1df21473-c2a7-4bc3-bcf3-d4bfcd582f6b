import React from 'react';
import { Icon } from './icon';
import { classNames } from '@/utils/class-names';
import { createProblemDetails, ProblemDetails } from '@/utils/problem-details';

export type ErrorType = 'error' | 'warning' | 'information';

interface ErrorProps {
  error: ProblemDetails | string | null | undefined;
  type?: ErrorType;
  clear?: () => void;
  containerClasses?: string;
  children?: React.ReactNode;
}

export function Error({
  error,
  clear,
  children,
  containerClasses = '',
  type = 'error',
}: ErrorProps) {
  const problemDetails =
      typeof error === 'string' ? createProblemDetails(error) : error,
    validationErrors = problemDetails?.errors
      ? Object.values(problemDetails.errors).flatMap((e) => e)
      : [];

  if (!problemDetails) {
    return null;
  }

  return (
    <div className={containerClasses}>
      <div
        className={classNames(
          'relative rounded-md p-4',
          type === 'error' && 'bg-red-50',
          type === 'warning' && 'bg-yellow-50',
          type === 'information' && 'bg-blue-50'
        )}
      >
        <div className="flex">
          <div className="flex-shrink-0">
            <Icon
              icon="exclamation-triangle"
              className={classNames(
                'h-5 w-5',
                type === 'error' && 'text-red-400',
                type === 'warning' && 'text-yellow-400',
                type === 'information' && 'text-blue-400'
              )}
              aria-hidden="true"
            />
          </div>
          <div className="ml-3">
            <h3
              className={classNames(
                'text-sm font-medium',
                type === 'error' && 'text-red-500',
                type === 'warning' && 'text-yellow-800',
                type === 'information' && 'text-blue-500'
              )}
            >
              {problemDetails.title}
            </h3>
            {!!problemDetails.detail && (
              <div
                className={classNames(
                  'mt-2 text-sm',
                  type === 'error' && 'text-red-700',
                  type === 'warning' && 'text-yellow-700',
                  type === 'information' && 'text-blue-700'
                )}
              >
                <p>{problemDetails.detail}</p>
              </div>
            )}
            {!!validationErrors.length && (
              <ul
                className={classNames(
                  'text-xs',
                  type === 'error' && 'text-red-500',
                  type === 'warning' && 'text-yellow-500',
                  type === 'information' && 'text-blue-500'
                )}
              >
                {validationErrors.map((v) => (
                  <li key={v}>{v}</li>
                ))}
              </ul>
            )}
            {!!clear && (
              <div className="absolute right-2 top-2">
                <button
                  type="button"
                  className={classNames(
                    'rounded-md px-2 py-1.5 text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2',
                    type === 'error' &&
                      'bg-red-50 text-red-800 hover:bg-red-100 focus:ring-red-600 focus:ring-offset-red-50',
                    type === 'warning' &&
                      'bg-yellow-50 text-yellow-800 hover:bg-yellow-100 focus:ring-yellow-600 focus:ring-offset-yellow-50',
                    type === 'information' &&
                      'bg-blue-50 text-blue-800 hover:bg-blue-100 focus:ring-blue-600 focus:ring-offset-blue-50'
                  )}
                  onClick={clear}
                >
                  <Icon icon="x" />
                </button>
              </div>
            )}
          </div>
          {children}
        </div>
      </div>
    </div>
  );
}
