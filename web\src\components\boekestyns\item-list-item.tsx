import { useState } from 'react';
import Link from 'next/link';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import * as models from 'api/models/boekestyns';
import { Alert } from '@/components/alert';
import { Icon } from '@/components/icon';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { routes } from '@/services/routes';
import { classNames } from '@/utils/class-names';
import { formatNumber, formatDate } from '@/utils/format';
import {
  setPriority,
  setUpcPrinted,
  setUpcUnprinted,
  selectHighlightPrintedUPCs,
} from './boekestyn-list-slice';

export interface ItemListItemProps {
  item: models.ItemListItem;
}

export function ListItem({ item }: ItemListItemProps) {
  const dispatch = useAppDispatch(),
    highlightPrintedUPCs = useAppSelector(selectHighlightPrintedUPCs),
    [showUpcReprintWarning, setShowUpcReprintWarning] = useState<number | null>(
      null
    ),
    [showUpcUnprintWarning, setShowUpcUnprintWarning] = useState<number | null>(
      null
    ),
    highlightRow = item.upcPrinted && highlightPrintedUPCs;

  const handleChangePriority = (id: number, priority: boolean) => {
    dispatch(setPriority({ id, priority }));
  };

  const handleUpcPrinted = (id: number) => {
    if (item.upcPrinted) {
      setShowUpcUnprintWarning(id);
    } else {
      if (item.upcPrintedPrev) {
        setShowUpcReprintWarning(id);
      } else {
        dispatch(setUpcPrinted(id));
      }
    }
  };

  const handleUpcReprintCancel = () => {
    setShowUpcReprintWarning(null);
  };

  const handleUpcReprintConfirm = () => {
    if (showUpcReprintWarning) {
      dispatch(setUpcPrinted(showUpcReprintWarning));
      setShowUpcReprintWarning(null);
    }
  };

  const handleUpcUnprintCancel = () => {
    setShowUpcUnprintWarning(null);
  };

  const handleUpcUnprintConfirm = () => {
    if (showUpcUnprintWarning) {
      dispatch(setUpcUnprinted(showUpcUnprintWarning));
      setShowUpcUnprintWarning(null);
    }
  };

  return (
    <tr className={classNames('border-b', highlightRow ? 'bg-yellow-100' : '')}>
      <td className="whitespace-nowrap px-1 py-2 text-center align-top text-xs font-medium">
        <button
          type="button"
          className="small secondary rounded px-2"
          onClick={() => handleChangePriority(item.id, !item.priority)}
        >
          <FontAwesomeIcon
            icon={[item.priority ? 'fas' : 'fal', 'star']}
            className={classNames(
              item.priority ? 'text-yellow-500' : 'text-gray-300'
            )}
          />
        </button>
        <button
          type="button"
          className="small secondary rounded px-2"
          onClick={() => handleUpcPrinted(item.id)}
        >
          <Icon
            icon="barcode"
            className={classNames(item.upcPrinted ? '' : 'text-gray-300')}
          />
        </button>
        <Link href={routes.prebooks.detail.to(item.prebookId)}>
          {formatNumber(item.prebookId, '00000')}
        </Link>
      </td>
      <td className="whitespace-nowrap px-1 py-2 text-left align-top text-xs text-gray-700">
        <div className="inline-block">
          {item.season || formatDate(item.requiredDate, 'MMM d')}
        </div>
      </td>
      <td className="whitespace-nowrap px-1 py-2 align-top text-xs text-gray-700">
        <div className="w-56">{item.customer}</div>
        {!!item.shipTo && (
          <div className="w-56 text-xs italic text-gray-400">{item.shipTo}</div>
        )}
      </td>
      <td className="whitespace-nowrap px-1 py-2 text-center align-top text-xs text-gray-700">
        {formatNumber(item.caseCount)}
        {item.isApproximate && (
          <span title="Quantity is approximate" className="cursor-pointer">
            &nbsp;
            <Icon icon="plus-minus" />
          </span>
        )}
      </td>
      <td className="whitespace-nowrap px-1 py-2 text-center align-top text-xs text-gray-700">
        {formatNumber(item.packQuantity)}
      </td>
      <td className="whitespace-nowrap px-1 py-2 text-left align-top text-xs text-gray-700">
        <div className="w-64">{item.description}</div>
        {!!item.itemComments && (
          <div className="w-64 text-xs italic text-gray-400">
            {item.itemComments}
          </div>
        )}
        {!!item.growerItemNotes && (
          <div className="w-64 text-xs italic text-gray-400">
            {item.growerItemNotes}
          </div>
        )}
        {!!item.itemGrowerItemNotes && (
          <div className="w-64 text-xs italic text-gray-400">
            {item.itemGrowerItemNotes}
          </div>
        )}
      </td>
      <td className="whitespace-nowrap px-1 py-2 text-center align-top text-xs text-gray-700">
        {item.potCover}
      </td>
      <td className="whitespace-nowrap px-1 py-2 text-center align-top text-xs text-gray-700">
        {item.boxCode}
      </td>
      <td className="whitespace-nowrap px-1 py-2 text-center align-top text-xs text-gray-700">
        {item.upc}
      </td>
      <td className="whitespace-nowrap px-1 py-2 text-center align-top text-xs text-gray-700">
        {item.dateCode}
      </td>
      <td className="whitespace-nowrap px-1 py-2 text-center align-top text-xs text-gray-700">
        {item.retail}
      </td>
      <td className="whitespace-nowrap px-1 py-2 text-center align-top text-xs text-gray-700">
        {item.weightsAndMeasures ? 'W&M' : ''}
      </td>
      <Alert
        open={!!showUpcReprintWarning}
        colour="danger"
        title="UPC Already Printed"
        message="The UPCs for this item had already been printed prior to the most-recent change, please be sure to discard of the original UPC’s."
        confirm={handleUpcReprintConfirm}
        cancel={handleUpcReprintCancel}
      />
      <Alert
        open={!!showUpcUnprintWarning}
        colour="danger"
        title="Unprint UPCs?"
        message="Are you sure you want to unprint the UPCs for this item?"
        confirm={handleUpcUnprintConfirm}
        cancel={handleUpcUnprintCancel}
      />
    </tr>
  );
}
