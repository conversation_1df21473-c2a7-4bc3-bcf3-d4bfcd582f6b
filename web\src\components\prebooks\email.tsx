import React, { Fragment, useEffect } from 'react';
import { useRouter } from 'next/router';
import { Dialog, Menu, Transition } from '@headlessui/react';
import { useAccount } from '@azure/msal-react';
import { apiUrl } from 'api/api-base';
import * as models from 'api/models/prebooks';
import * as spire from 'api/models/spire';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import { classNames } from '@/utils/class-names';
import {
  selectBcc,
  selectBody,
  selectUpgradesBody,
  selectSubject,
  selectTemplates,
  selectTo,
  setTo,
  setCc,
  setBcc,
  setSubject,
  setBody,
  setUpgradesBody,
  clearState,
  setTemplate,
  getPrebookEmail,
  setAccount,
  clearError,
  setError,
  selectError,
  selectIsLoading,
  createPrebookEmail,
  selectCc,
} from './prebook-email-slice';
import { Tiptap } from '../tip-tap';

export interface EmailProps {
  prebook: models.PrebookDetail;
  open: boolean;
  close: (email: models.PrebookEmail) => void;
  cancel: () => void;
  saveAndNewLink?: string;
  saveAndCloseLink?: string;
}

export function Email({
  prebook,
  close,
  open,
  cancel,
  saveAndNewLink,
  saveAndCloseLink,
}: EmailProps) {
  const dispatch = useAppDispatch(),
    router = useRouter(),
    account = useAccount(),
    to = useAppSelector(selectTo),
    cc = useAppSelector(selectCc),
    bcc = useAppSelector(selectBcc),
    subject = useAppSelector(selectSubject),
    body = useAppSelector(selectBody),
    upgradesBody = useAppSelector(selectUpgradesBody),
    templates = useAppSelector(selectTemplates),
    error = useAppSelector(selectError),
    isLoading = useAppSelector(selectIsLoading),
    hasUpgrades = prebook.items.some((i) => i.upgradeSheet);

  useEffect(() => {
    dispatch(getPrebookEmail(prebook.id));

    return function cleanup() {
      dispatch(clearState());
    };
  }, [dispatch, prebook.id]);

  useEffect(() => {
    dispatch(setAccount(account));
  }, [dispatch, account]);

  const handleToChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setTo(e.target.value));
  };

  const handleCcChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setCc(e.target.value || null));
  };

  const handleBccChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setBcc(e.target.value || null));
  };

  const handleSubjectChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setSubject(e.target.value));
  };

  const handleBodyChange = (value: string) => {
    dispatch(setBody(value));
  };

  const handleUpgradesBodyChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    dispatch(setUpgradesBody(e.target.value));
  };

  const handleTemplateChange = (template: spire.EmailTemplate) => {
    dispatch(setTemplate(template));
  };

  const save = async () => {
    if (!to) {
      dispatch(setError('Please enter the To Address.'));
    } else if (!subject) {
      dispatch(setError('Please enter the Subject.'));
    } else {
      const response = await dispatch(createPrebookEmail(prebook.id));
      if (response.meta.requestStatus === 'fulfilled') {
        const email = response.payload as models.PrebookEmail;
        return email;
      }
    }

    return null;
  };

  const handleSaveClick = async () => {
    const saved = await save();
    if (saved) {
      close(saved);
    }
  };

  const handleSaveAndCloseClick = async () => {
    const saved = await save();
    if (saved) {
      close(saved);
      if (saveAndCloseLink) {
        router.push(saveAndCloseLink);
      }
    }
  };

  const handleSaveAndNewClick = async () => {
    const saved = await save();
    if (saved) {
      close(saved);
      if (saveAndNewLink) {
        router.push(saveAndNewLink);
      }
    }
  };

  const handleClearErrorClick = () => {
    dispatch(clearError());
  };

  return (
    <Transition.Root as={Fragment} show={open}>
      <Dialog as="div" onClose={cancel} className="relative z-30">
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-30 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative h-screen w-screen transform overflow-hidden p-6 transition-all">
                <div className="flex h-full flex-col rounded-lg bg-white p-6 text-left shadow-xl">
                  <div className="mb-4 flex justify-center border-b-2 pb-4">
                    <Dialog.Title
                      as="h3"
                      className="text-lg font-medium leading-6 text-gray-900"
                    >
                      <Icon
                        icon="envelope"
                        className="h-6 w-6"
                        aria-hidden="true"
                      />
                      &nbsp; Send Prebook Email
                    </Dialog.Title>
                  </div>
                  <Error
                    error={error}
                    clear={handleClearErrorClick}
                    containerClasses="flex"
                  />
                  <div className="flex flex-grow flex-col">
                    <form className="flex w-full">
                      <div className="grid w-full grid-cols-6 items-start gap-4">
                        <label
                          htmlFor="to"
                          className="col-start-1 mt-px block grid-cols-2 pt-2 text-right text-sm font-medium text-gray-700"
                        >
                          To
                        </label>
                        <input
                          type="text"
                          name="to"
                          id="to"
                          tabIndex={0}
                          value={to}
                          onChange={handleToChange}
                          className="col-span-2 block w-full min-w-0 flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        />

                        <div
                          className={classNames(
                            'col-span-3',
                            hasUpgrades ? 'row-span-2' : 'row-span-4'
                          )}
                        >
                          <label
                            htmlFor="body"
                            className="mt-px block pt-2 text-sm font-medium text-gray-700"
                          >
                            Body
                          </label>
                          <Tiptap
                            content={body}
                            onChange={handleBodyChange}
                            className="h-32"
                          />
                        </div>

                        <label
                          htmlFor="cc"
                          className="col-start-1 mt-px block grid-cols-2 pt-2 text-right text-sm font-medium text-gray-700"
                        >
                          CC
                        </label>
                        <div className="col-span-2">
                          <div className="flex max-w-lg rounded-md shadow-sm">
                            <input
                              type="text"
                              name="cc"
                              id="cc"
                              tabIndex={1}
                              value={cc || ''}
                              onChange={handleCcChange}
                              className="block w-full min-w-0 flex-1 rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                            />
                          </div>
                        </div>
                        <label
                          htmlFor="bcc"
                          className="col-start-1 mt-px block grid-cols-2 pt-2 text-right text-sm font-medium text-gray-700"
                        >
                          BCC
                        </label>
                        <div className="col-span-2">
                          <div className="flex max-w-lg rounded-md shadow-sm">
                            <input
                              type="text"
                              name="bcc"
                              id="bcc"
                              tabIndex={1}
                              value={bcc || ''}
                              onChange={handleBccChange}
                              className="block w-full min-w-0 flex-1 rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                            />
                          </div>
                        </div>
                        {hasUpgrades && (
                          <div className="col-span-3 row-span-2">
                            <label
                              htmlFor="upgrades-body"
                              className="mt-px block pt-2 text-sm font-medium text-gray-700"
                            >
                              Upgrades Email Body
                            </label>
                            <textarea
                              name="upgrades-body"
                              id="upgrades-body"
                              tabIndex={3}
                              rows={7}
                              value={upgradesBody}
                              onChange={handleUpgradesBodyChange}
                              className="block w-full min-w-0 flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            />
                          </div>
                        )}
                        <label
                          htmlFor="subject"
                          className="col-start-1 mt-px block grid-cols-2 pt-2 text-right text-sm font-medium text-gray-700"
                        >
                          Subject
                        </label>
                        <div className="relative col-span-2 mt-1 rounded-md shadow-sm">
                          <input
                            type="text"
                            name="subject"
                            id="subject"
                            tabIndex={2}
                            value={subject}
                            onChange={handleSubjectChange}
                            className="block w-full min-w-0 flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                          />
                          {!!templates.length && (
                            <div className="absolute inset-y-0 right-0 flex">
                              <Menu
                                as="div"
                                className="relative -ml-px block h-full"
                              >
                                <Menu.Button className="relative inline-flex h-full items-center rounded-md px-2 py-2 text-xs font-medium text-gray-500 focus:z-10 focus:outline-none focus:ring-0">
                                  <span>Templates</span>
                                  &nbsp;
                                  <Icon
                                    icon="chevron-down"
                                    className="h-5 w-5"
                                    aria-hidden="true"
                                  />
                                </Menu.Button>
                                <Transition
                                  as={Fragment}
                                  enter="transition ease-out duration-100"
                                  enterFrom="transform opacity-0 scale-95"
                                  enterTo="transform opacity-100 scale-100"
                                  leave="transition ease-in duration-75"
                                  leaveFrom="transform opacity-100 scale-100"
                                  leaveTo="transform opacity-0 scale-95"
                                >
                                  <Menu.Items className="absolute right-0 z-10 -mr-1 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                                    <div className="py-1">
                                      {templates.map((template) => (
                                        <Menu.Item key={template.id}>
                                          {({ active }) => (
                                            <button
                                              type="button"
                                              onClick={() =>
                                                handleTemplateChange(template)
                                              }
                                              className={classNames(
                                                active
                                                  ? 'bg-gray-100 text-gray-900'
                                                  : 'text-gray-700',
                                                'block w-full px-4 py-2 text-left text-sm'
                                              )}
                                            >
                                              {template.name}
                                            </button>
                                          )}
                                        </Menu.Item>
                                      ))}
                                    </div>
                                  </Menu.Items>
                                </Transition>
                              </Menu>
                            </div>
                          )}
                        </div>
                        <p className="col-span-2 col-start-2 mt-2 text-sm text-gray-500">
                          Separate multiple email addresses with semicolons (;)
                        </p>
                      </div>
                    </form>
                  </div>
                  <div className="mt-4 grid h-full grid-cols-6 gap-4 text-right">
                    <div>
                      <a
                        href={apiUrl(`reports/prebooks/${prebook.id}?download`)}
                        className="btn-secondary inline-block"
                        tabIndex={-1}
                      >
                        <Icon icon="download"></Icon>
                        &nbsp; Download PDF
                      </a>
                    </div>
                    <iframe
                      title="Prebook preview"
                      src={apiUrl(`reports/prebooks/${prebook.id}`)}
                      className="col-span-5 col-start-2 h-full w-full"
                    ></iframe>
                  </div>
                  <div className="mt-4 flex justify-end border-t-2 pt-4">
                    <button
                      type="button"
                      className="btn-secondary text-lg"
                      onClick={cancel}
                    >
                      Cancel
                    </button>
                    <button
                      type="button"
                      className="btn-secondary ml-4 text-lg"
                      onClick={handleSaveClick}
                      disabled={isLoading}
                    >
                      Send
                      <Icon
                        icon={isLoading ? 'spinner' : 'send'}
                        className="ml-2"
                        spin={isLoading}
                      />
                    </button>
                    {saveAndCloseLink && (
                      <button
                        type="button"
                        className="btn-primary ml-4 text-lg"
                        onClick={handleSaveAndCloseClick}
                        disabled={isLoading}
                      >
                        Send &nbsp;&amp;&nbsp;Close
                        <Icon
                          icon={isLoading ? 'spinner' : 'send'}
                          className="ml-2"
                          spin={isLoading}
                        />
                      </button>
                    )}
                    {saveAndNewLink && (
                      <button
                        type="button"
                        className="btn-secondary ml-4 border-green-600 text-lg text-green-600"
                        onClick={handleSaveAndNewClick}
                        disabled={isLoading}
                      >
                        Send &nbsp;&amp;&nbsp;New
                        <Icon
                          icon={isLoading ? 'spinner' : 'send'}
                          className="ml-2"
                          spin={isLoading}
                        />
                      </button>
                    )}
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
}
