{"version": "0.2.0", "configurations": [{"name": "Next.js: debug server-side", "type": "node-terminal", "request": "launch", "command": "npm run dev"}, {"name": "Next.js: debug client-side", "type": "chrome", "request": "launch", "url": "http://localhost:3050"}, {"name": "Next.js: debug full stack", "type": "node-terminal", "request": "launch", "command": "npm run dev", "serverReadyAction": {"pattern": "Local:.+(https?://.+)", "uriFormat": "%s", "action": "debugWithChrome"}}]}