import * as settings from 'api/models/settings';
import { Icon } from '@/components/icon';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { handleFocus } from '@/utils/focus';
import {
  deleteOverrideItem,
  setOverrideEndWeek,
  setOverrideStartWeek,
  setOverrideVendorId,
  selectVendors,
} from './default-vendor-overrides-slice';

interface DefaultVendorOverrideDetailItemProps {
  item: settings.DefaultVendorOverrideItem;
}

export function DefaultVendorOverrideDetailItem({
  item,
}: DefaultVendorOverrideDetailItemProps) {
  const dispatch = useAppDispatch(),
    vendors = useAppSelector(selectVendors),
    id = item.id;

  const handleStartWeekChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    if (value) {
      dispatch(setOverrideStartWeek({ id, value }));
    }
  };

  const handleEndWeekChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    if (value) {
      dispatch(setOverrideEndWeek({ id, value }));
    }
  };

  const handleVendorIdChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = parseInt(e.target.value);
    if (value) {
      dispatch(setOverrideVendorId({ id, value }));
    }
  };

  const handleDeleteClick = async () => {
    dispatch(deleteOverrideItem(id));
  };

  return (
    <tr>
      <td className="px-1">
        <input
          type="text"
          value={item.startWeek}
          onChange={handleStartWeekChange}
          onFocus={handleFocus}
          className="w-24 rounded-md border-gray-300 text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
        />
      </td>
      <td className="px-1">
        <input
          type="text"
          value={item.endWeek}
          onChange={handleEndWeekChange}
          onFocus={handleFocus}
          className="w-24 rounded-md border-gray-300 text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
        />
      </td>
      <td className="px-1">
        <select
          className="rounded-md border-gray-300 text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
          value={item.vendorId}
          onChange={handleVendorIdChange}
        >
          {vendors.map((v) => (
            <option key={v.id} value={v.id}>
              {v.name}
            </option>
          ))}
        </select>
      </td>
      <td className="px-1">
        <button
          type="button"
          className="btn-delete px-2 py-1"
          onClick={handleDeleteClick}
        >
          <Icon icon="trash" />
        </button>
      </td>
    </tr>
  );
}
