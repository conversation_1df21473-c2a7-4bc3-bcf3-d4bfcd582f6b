import { createSlice, PayloadAction, createSelector } from '@reduxjs/toolkit';
import { settingsApi } from 'api/settings-service';
import { spireApi } from 'api/spire-service';
import * as spire from 'api/models/spire';
import * as settings from 'api/models/settings';
import { RootState } from '@/services/store';
import { ProblemDetails } from '@/utils/problem-details';
import { contains } from '@/utils/equals';
import { sort } from '@/utils/sort';

export interface ProductSettingsState {
  search: string;
  isLoading: boolean;
  error: ProblemDetails | null;
  selectedOverride: settings.DefaultVendorOverride | null;
  defaultVendorOverrides: settings.DefaultVendorOverride[];
  vendors: spire.Vendor[];
  items: spire.InventoryItem[];
}

const initialState: ProductSettingsState = {
  search: '',
  isLoading: false,
  error: null,
  selectedOverride: null,
  defaultVendorOverrides: [],
  vendors: [],
  items: [],
};

interface UpdateOverrideItemValue {
  id: number;
  value: number;
}

export const defaultVendorOverridesSlice = createSlice({
  name: 'default-vendor-overrides-settings',
  initialState,
  reducers: {
    clearState(state) {
      Object.assign(state, { ...initialState });
    },
    clearError(state) {
      state.error = null;
    },
    setSelectedOverride(
      state,
      { payload }: PayloadAction<settings.DefaultVendorOverride | null>
    ) {
      state.selectedOverride = payload;
    },
    setSearch(state, { payload }: PayloadAction<string>) {
      state.search = payload;
    },
    setOverrideStartWeek(
      state,
      { payload }: PayloadAction<UpdateOverrideItemValue>
    ) {
      if (state.selectedOverride) {
        const override = { ...state.selectedOverride },
          items = state.selectedOverride.items.map((i) => ({ ...i })),
          item = items.find((i) => i.id === payload.id);

        if (item) {
          item.startWeek = payload.value;
        }

        override.items = items;
        state.selectedOverride = override;
      }
    },
    setOverrideEndWeek(
      state,
      { payload }: PayloadAction<UpdateOverrideItemValue>
    ) {
      if (state.selectedOverride) {
        const override = { ...state.selectedOverride },
          items = state.selectedOverride.items.map((i) => ({ ...i })),
          item = items.find((i) => i.id === payload.id);

        if (item) {
          item.endWeek = payload.value;
        }

        override.items = items;
        state.selectedOverride = override;
      }
    },
    setOverrideVendorId(
      state,
      { payload }: PayloadAction<UpdateOverrideItemValue>
    ) {
      if (state.selectedOverride) {
        const override = { ...state.selectedOverride },
          items = state.selectedOverride.items.map((i) => ({ ...i })),
          item = items.find((i) => i.id === payload.id);

        if (item) {
          item.vendorId = payload.value;
        }

        override.items = items;
        state.selectedOverride = override;
      }
    },
    addOverridePartNumber(state, { payload }: PayloadAction<string>) {
      if (
        state.selectedOverride &&
        state.selectedOverride.spirePartNumbers.indexOf(payload) === -1
      ) {
        const override = { ...state.selectedOverride };
        override.spirePartNumbers = override.spirePartNumbers.concat(payload);
        state.selectedOverride = override;
      }
    },
    removeOverridePartNumber(state, { payload }: PayloadAction<string>) {
      if (state.selectedOverride) {
        const override = { ...state.selectedOverride };
        override.spirePartNumbers = override.spirePartNumbers.filter(
          (p) => p !== payload
        );
        state.selectedOverride = override;
      }
    },
    deleteOverrideItem(state, { payload }: PayloadAction<number>) {
      if (state.selectedOverride) {
        const override = { ...state.selectedOverride };
        override.items = override.items.filter((i) => i.id !== payload);
        state.selectedOverride = override;
      }
    },
    addOverride(state) {
      const id =
          state.defaultVendorOverrides.reduce((m, o) => Math.min(o.id, m), 0) -
          1,
        override = {
          id,
          spirePartNumbers: [],
          items: [
            {
              id: 0,
              defaultVendorOverrideId: id,
              startWeek: 1,
              endWeek: 2,
              vendorId: 0,
            },
          ],
        };
      state.selectedOverride = override;
    },
    addOverrideItem(state) {
      if (state.selectedOverride) {
        const id =
            state.selectedOverride.items.reduce(
              (m2, i) => Math.min(i.id, m2),
              0
            ) - 1,
          startWeek =
            (state.selectedOverride.items.reduce(
              (max, m) => Math.max(max, m.endWeek),
              0
            ) || 0) + 1,
          endWeek = startWeek + 1,
          vendorId = state.selectedOverride.items[0]?.vendorId ?? 0,
          items = state.selectedOverride.items.concat({
            id,
            defaultVendorOverrideId: state.selectedOverride.id,
            startWeek,
            endWeek,
            vendorId,
          }),
          override = { ...state.selectedOverride, items };
        state.selectedOverride = override;
      }
    },
  },
  extraReducers: (builder) =>
    builder
      .addMatcher(
        settingsApi.endpoints.defaultVendorOverrides.matchPending,
        (state) => {
          state.isLoading = true;
        }
      )
      .addMatcher(
        settingsApi.endpoints.defaultVendorOverrides.matchFulfilled,
        (state, { payload }) => {
          state.defaultVendorOverrides = payload;
          state.isLoading = false;
        }
      )
      .addMatcher(
        settingsApi.endpoints.defaultVendorOverrides.matchRejected,
        (state, { payload }) => {
          state.isLoading = false;
          if (payload) {
            state.error = payload;
          }
        }
      )
      .addMatcher(
        settingsApi.endpoints.updateDefaultVendorOverride.matchPending,
        (state) => {
          state.isLoading = true;
        }
      )
      .addMatcher(
        settingsApi.endpoints.updateDefaultVendorOverride.matchFulfilled,
        (state) => {
          state.selectedOverride = null;
          state.isLoading = false;
        }
      )
      .addMatcher(
        settingsApi.endpoints.updateDefaultVendorOverride.matchRejected,
        (state, { payload }) => {
          state.isLoading = false;
          if (payload) {
            state.error = payload;
          }
        }
      )
      .addMatcher(
        spireApi.endpoints.vendors.matchFulfilled,
        (state, { payload }) => {
          state.vendors = payload;
        }
      )
      .addMatcher(
        spireApi.endpoints.vendors.matchRejected,
        (state, { payload }) => {
          if (payload) {
            state.error = payload;
          }
        }
      )
      .addMatcher(
        spireApi.endpoints.inventoryItems.matchFulfilled,
        (state, { payload }) => {
          state.items = payload.inventoryItems;
        }
      )
      .addMatcher(
        spireApi.endpoints.inventoryItems.matchRejected,
        (state, { payload }) => {
          if (payload) {
            state.error = payload;
          }
        }
      ),
});

export interface ProductDefaultItem {
  id: number;
  partNo: string;
  description: string;
  plantName: string | null;
  customerAbbreviation: string | null;
  labourHours: string;
  quantityPerFinishedItem: string;
  isUpgrade: boolean;
}

export const {
  clearError,
  clearState,
  setSelectedOverride,
  setSearch,
  setOverrideStartWeek,
  setOverrideEndWeek,
  setOverrideVendorId,
  addOverride,
  addOverrideItem,
  addOverridePartNumber,
  removeOverridePartNumber,
  deleteOverrideItem,
} = defaultVendorOverridesSlice.actions;

export const selectSearch = ({ defaultVendorOverrides }: RootState) =>
  defaultVendorOverrides.search;
export const selectIsLoading = ({ defaultVendorOverrides }: RootState) =>
  defaultVendorOverrides.isLoading;
export const selectError = ({ defaultVendorOverrides }: RootState) =>
  defaultVendorOverrides.error;

export const selectSelectedOverride = ({ defaultVendorOverrides }: RootState) =>
  defaultVendorOverrides.selectedOverride;

export const selectVendors = ({ defaultVendorOverrides }: RootState) =>
  defaultVendorOverrides.vendors;

export const selectAllItems = ({ defaultVendorOverrides }: RootState) =>
  defaultVendorOverrides.items;

const selectAllDefaultVendorOverrides = ({
  defaultVendorOverrides,
}: RootState) => defaultVendorOverrides.defaultVendorOverrides;

export const selectDefaultVendorOverrides = createSelector(
  selectAllDefaultVendorOverrides,
  selectSearch,
  (overrides, search) =>
    overrides
      .filter(
        (dvo) =>
          !search || dvo.spirePartNumbers.some((i) => contains(i, search))
      )
      .sort((a, b) => sort(a.spirePartNumbers[0], b.spirePartNumbers[0]))
);

export default defaultVendorOverridesSlice.reducer;
