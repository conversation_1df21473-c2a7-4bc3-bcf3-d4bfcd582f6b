import { Fragment, useEffect, useState } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import * as HeadlessUI from '@headlessui/react';
import { useCustomersQuery } from 'api/spire-service';
import * as spire from 'api/models/spire';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { routes } from '@/services/routes';
import {
  selectIsLoading,
  selectError,
  clearState,
  clearError,
  setCustomer,
  setCustomerItemCodeByShipTo,
  selectCustomer,
  selectCustomerItemCodeByShipTo,
  selectShipTos,
  selectProductCustomerDefaults,
  selectQuery,
  setQuery,
} from '@/components/settings/customers/customer-settings-slice';
import { CustomerPotCovers } from '@/components/settings/customers/customer-pot-covers';
import { ProductCustomerDefault } from '@/components/settings/customers/product-customer-default';
import { ProductShipToDefaults } from '@/components/settings/customers/product-ship-to-defaults';
import { Alert } from '@/components/alert';
import { Combobox } from '@/components/combo-box';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import { Loading } from '@/components/loading';
import { classNames } from '@/utils/class-names';
import { startsWith } from '@/utils/equals';

export default function Settings() {
  const dispatch = useAppDispatch(),
    isLoading = useAppSelector(selectIsLoading),
    error = useAppSelector(selectError),
    customer = useAppSelector(selectCustomer),
    query = useAppSelector(selectQuery),
    customerItemCodeByShipTo = useAppSelector(selectCustomerItemCodeByShipTo),
    shipTos = useAppSelector(selectShipTos),
    productCustomerDefaults = useAppSelector(selectProductCustomerDefaults),
    { data: customers } = useCustomersQuery(),
    [showDeleteCustomerSettingsDialog, setShowDeleteCustomerSettingsDialog] =
      useState(false);

  useEffect(() => {
    dispatch(clearState());
    setShowDeleteCustomerSettingsDialog(false);
  }, [dispatch]);

  const handleClearError = () => {
    dispatch(clearError());
  };

  const handleQueryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setQuery(e.target.value));
  };

  const handleCustomerChange = (customer: spire.Customer | null) => {
    dispatch(setCustomer(customer));
  };

  const handleCustomerFilter = (query: string, value: spire.Customer) =>
    startsWith(value.name, query) || startsWith(value.customerNo, query);

  const handleCustomerItemCodeByShipToChange = (value: boolean) => {
    if (customer) {
      if (value || !productCustomerDefaults.length) {
        dispatch(
          setCustomerItemCodeByShipTo({
            customerId: customer.id,
            customerItemCodeByShipTo: value,
          })
        );
      } else {
        setShowDeleteCustomerSettingsDialog(true);
      }
    }
  };

  const handleDeleteCustomerSettingsCancel = () =>
    setShowDeleteCustomerSettingsDialog(false);

  const handleDeleteCustomerSettingsConfirm = () => {
    setShowDeleteCustomerSettingsDialog(false);
    if (customer) {
      dispatch(
        setCustomerItemCodeByShipTo({
          customerId: customer.id,
          customerItemCodeByShipTo: false,
        })
      );
    }
  };

  return (
    <>
      <Head>
        <title>Settings: Customers</title>
      </Head>
      <header className="sticky top-0 z-20 flex-wrap bg-white shadow">
        <div className="mx-auto flex max-w-7xl items-center justify-between px-8 py-6">
          <h2 className="flex-shrink-0 text-2xl font-bold leading-7 text-gray-900">
            <Icon icon="user-tie" />
            &nbsp; Customer Settings
          </h2>
          <div className="w-96">
            <Combobox
              label="Choose Customer"
              collection={customers}
              filter={handleCustomerFilter}
              onChange={handleCustomerChange}
              value={customer}
              nullDisplayText="Choose Customer"
              autofocus
            ></Combobox>
          </div>
          <div className="flex">
            <Link
              href={routes.settings.home.to()}
              className="btn-secondary inline-flex"
            >
              Close
            </Link>
          </div>
        </div>
      </header>
      <main className="flex flex-grow flex-col overflow-y-auto">
        <Error error={error} clear={handleClearError} />
        {isLoading && <Loading />}
        <div className="flex h-full w-full">
          <div className="flex h-full w-full flex-col">
            <div className="flex h-full w-full">
              {!!customer && (
                <div className="flex h-full w-full flex-col px-8 py-2">
                  <div className="flex justify-center gap-8 py-2">
                    <HeadlessUI.Switch.Group as="div">
                      <HeadlessUI.Switch
                        checked={customerItemCodeByShipTo}
                        onChange={handleCustomerItemCodeByShipToChange}
                        className={classNames(
                          customerItemCodeByShipTo
                            ? 'bg-blue-400'
                            : 'bg-gray-200',
                          'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
                        )}
                      >
                        <span
                          aria-hidden="true"
                          className={classNames(
                            customerItemCodeByShipTo
                              ? 'translate-x-5'
                              : 'translate-x-0',
                            'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                          )}
                        />
                      </HeadlessUI.Switch>
                      <HeadlessUI.Switch.Label className="ml-2">
                        <span className="font-normal">
                          Save Customer Item Codes per Ship To
                        </span>
                      </HeadlessUI.Switch.Label>
                    </HeadlessUI.Switch.Group>
                  </div>
                  <HeadlessUI.Tab.Group vertical>
                    <div className="flex h-full border-b border-t">
                      <HeadlessUI.Tab.List
                        as="nav"
                        className="flex w-64 flex-col overflow-y-auto border-2 bg-gray-200"
                      >
                        <HeadlessUI.Tab as={Fragment}>
                          {({ selected }) => (
                            <button
                              type="button"
                              className={classNames(
                                'group relative my-2 mr-4 text-nowrap rounded-lg rounded-l-none border-2 p-2 text-left text-sm font-medium',
                                selected
                                  ? 'border-transparent bg-gray-500 text-white'
                                  : 'border-gray-500 border-l-transparent bg-white text-gray-500 hover:text-gray-500'
                              )}
                            >
                              Pot Covers
                            </button>
                          )}
                        </HeadlessUI.Tab>
                        {!customerItemCodeByShipTo && (
                          <HeadlessUI.Tab as={Fragment}>
                            {({ selected }) => (
                              <button
                                type="button"
                                className={classNames(
                                  'group relative my-2 mr-4 text-nowrap rounded-lg rounded-l-none p-2 text-left text-sm font-medium',
                                  selected
                                    ? 'bg-blue-500 text-white'
                                    : 'bg-white text-gray-500 hover:text-blue-500'
                                )}
                              >
                                Customer Item Codes
                              </button>
                            )}
                          </HeadlessUI.Tab>
                        )}
                        {shipTos.map((shipTo) => (
                          <HeadlessUI.Tab key={shipTo.id} as={Fragment}>
                            {({ selected }) => (
                              <button
                                type="button"
                                className={classNames(
                                  'group relative my-2 mr-4 text-nowrap rounded-lg rounded-l-none p-2 text-left text-sm font-medium',
                                  selected
                                    ? 'bg-blue-500 text-white'
                                    : 'bg-white text-gray-500 hover:text-blue-500'
                                )}
                              >
                                {shipTo.shipId}
                              </button>
                            )}
                          </HeadlessUI.Tab>
                        ))}
                      </HeadlessUI.Tab.List>
                      <HeadlessUI.Tab.Panels className="m-2 h-full w-full overflow-y-auto">
                        <HeadlessUI.Tab.Panel className="w-full rounded-lg border p-2">
                          <CustomerPotCovers />
                        </HeadlessUI.Tab.Panel>
                        {!customerItemCodeByShipTo && (
                          <HeadlessUI.Tab.Panel className="w-full rounded-lg border p-2">
                            <div className="mb-2 flex items-center gap-4">
                              <label htmlFor="item-search" className="text-sm">
                                Search for Products
                              </label>
                              <input
                                id="item-search"
                                type="search"
                                value={query}
                                className="block text-xs"
                                onChange={handleQueryChange}
                              />
                            </div>
                            <table className="min-w-full divide-y divide-gray-300">
                              <thead>
                                <tr className="sticky top-0 z-10">
                                  <th className="bg-gray-100 px-2 py-3.5 pl-3 text-left font-semibold text-gray-900">
                                    Product Code
                                  </th>
                                  <th className="bg-gray-100 px-2 py-3.5 pl-3 text-left font-semibold text-gray-900">
                                    Description
                                  </th>
                                  <th className="bg-gray-100 px-2 py-3.5 pl-3 text-center font-semibold text-gray-900">
                                    Customer Item Code
                                  </th>
                                  <th className="bg-gray-100 px-2 py-3.5 pl-3 text-center font-semibold text-gray-900">
                                    &nbsp;
                                  </th>
                                </tr>
                              </thead>
                              <tbody>
                                {productCustomerDefaults.map((d) => (
                                  <ProductCustomerDefault
                                    key={d.id}
                                    productCustomerDefault={d}
                                  />
                                ))}
                              </tbody>
                            </table>
                          </HeadlessUI.Tab.Panel>
                        )}
                        {shipTos.map((shipTo) => (
                          <HeadlessUI.Tab.Panel
                            key={shipTo.id}
                            className="w-full rounded-lg border p-2"
                          >
                            <ProductShipToDefaults shipTo={shipTo} />
                          </HeadlessUI.Tab.Panel>
                        ))}
                      </HeadlessUI.Tab.Panels>
                    </div>
                  </HeadlessUI.Tab.Group>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
      <Alert
        title="Confirm Deletion"
        message="This will delete all existing Customer Item Codes for this customer. Would you like to continue?"
        colour="danger"
        open={showDeleteCustomerSettingsDialog}
        cancel={handleDeleteCustomerSettingsCancel}
        confirm={handleDeleteCustomerSettingsConfirm}
        confirmButtonText="Yes"
        cancelButtonText="No"
      />
    </>
  );
}
