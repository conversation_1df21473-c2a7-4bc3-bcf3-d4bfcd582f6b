import { Combobox } from '@headlessui/react';
import { ProductionOrder } from 'api/boekestyn-service';
import * as boeks from 'api/models/boekestyns';
import * as prebooks from 'api/models/prebooks';
import * as settings from 'api/models/settings';
import * as spire from 'api/models/spire';
import { Icon } from '@/components/icon';
import { classNames } from '@/utils/class-names';
import { equals } from '@/utils/equals';
import {
  dateFromWeekAndYear,
  isDateInWeek,
  weekFromDate,
  weekFromWeekAndYear,
} from '@/utils/weeks';
import { InventoryListItemBlanketItem } from './inventory-list-item-blanket-item';

export interface InventoryListItemProps {
  item: spire.InventoryItem;
  customerItemCodeDefaults: settings.CustomerItemCodeDefault[];
  productDefaults: settings.ProductDefault[];
  seasons: prebooks.Season[];
  allBlanketItems: prebooks.PrebookBlanketItem[];
  productionOrders: ProductionOrder[];
  allVendors: spire.Vendor[];
  customer: spire.Customer | null;
  shipTo: spire.CustomerShipTo | null;
  requiredDate: string | null;
  seasonName: string | null;
}

export function InventoryListItem({
  item,
  customerItemCodeDefaults,
  productDefaults,
  seasons,
  allBlanketItems,
  productionOrders,
  allVendors,
  customer,
  shipTo,
  requiredDate,
  seasonName,
}: InventoryListItemProps) {
  const season = seasons?.find((s) => equals(s.name, seasonName)),
    week = weekFromDate(requiredDate || '')?.week || 0,
    productDefault = productDefaults.find(
      (d) => d.spireInventoryId === item.id
    ),
    overrides = productDefault?.overrides.filter(
      (o) => o.startWeek <= week && o.endWeek >= week
    ),
    boekestynPlantId =
      // if the plant is a single
      productDefault?.boekestynPlantId
        ? productDefault.boekestynPlantId
        : // if it's multiple & it's got a single override for this week & there's just 1 product here
        overrides?.length === 1
        ? overrides[0].boekestynPlantId
        : // if it's a multiple with just a single product
        productDefault?.products.length === 1
        ? productDefault.products[0].boekestynPlantId
        : '',
    boekestynCustomer =
      // if the plant is a single
      (productDefault?.boekestynPlantId
        ? productDefault.boekestynCustomerAbbreviation
        : // if it's multiple & it's got a single override for this week & there's just 1 product here
        overrides?.length === 1
        ? overrides[0].boekestynCustomerAbbreviation
        : // if it's a multiple with just a single product
        productDefault?.products.length === 1
        ? productDefault.products[0].boekestynCustomerAbbreviation
        : '') || boeks.WeeklyCustomerAbbreviation,
    blanketItems = allBlanketItems
      .filter(
        (i) =>
          i.spireInventoryId === item.id &&
          (!customer || !i.customerId || i.customerId === customer.id) &&
          (!shipTo || !i.shipToId || i.shipToId === shipTo.id) &&
          // no date at all - don't bother filtering
          ((!requiredDate && !season) ||
            // just a season date - use that
            (!requiredDate &&
              season &&
              season.seasonDate <= i.requiredDate &&
              (!i.blanketStartDate ||
                season.seasonDate >= i.blanketStartDate)) ||
            // required date - use that
            (requiredDate &&
              requiredDate <= i.requiredDate &&
              (!i.blanketStartDate || requiredDate >= i.blanketStartDate))) &&
          isDateInWeek(i.blanketWeekId, requiredDate)
      )
      .concat(
        productionOrders
          .map((o) => ({ ...o }))
          .flatMap((o) => {
            if (Array.isArray(o.salesWeeks)) {
              return o.salesWeeks.map(({ cases, week }) => {
                const parts = week.split('/'),
                  weekNumber = parseInt(parts[0]) || o.weekNumber,
                  year = parseInt(parts[1]) || o.year;
                return { ...o, cases, weekNumber, year } as ProductionOrder;
              });
            } else {
              return [o];
            }
          })
          .filter((o) => {
            const matches =
              equals(boekestynPlantId, o.plantId) &&
              (equals(boekestynCustomer, o.customer) ||
                (boekestynCustomer === boeks.WeeklyCustomerAbbreviation &&
                  !o.customer)) &&
              (!requiredDate ||
                (requiredDate &&
                  isDateInWeek(
                    weekFromWeekAndYear(o.weekNumber, o.year).weekId,
                    requiredDate
                  )));

            if (matches) {
              return true;
            }

            return false;
          })
          .reduce((memo, o) => {
            const existing = memo.find((m) => equals(m.customer, o.customer));
            if (existing) {
              existing.cases += o.cases;
            } else {
              memo.push(o);
            }

            return memo;
          }, [] as ProductionOrder[])
          .map((o, index) => ({
            id: -index,
            spireInventoryId: item.id,
            spirePartNumber: item.partNo,
            description: item.description,
            vendorId: boeks.BoekestynVendorId,
            vendorName: 'BOEKESTYN GREENHOUSES',
            customerId: null,
            customerName: equals(o.customer, boeks.WeeklyCustomerAbbreviation)
              ? 'Weekly'
              : o.customer || null,
            shipToId: null,
            shipToName: null,
            blanketStartDate: dateFromWeekAndYear(o.weekNumber, o.year, 1),
            requiredDate: dateFromWeekAndYear(o.weekNumber, o.year, 7),
            blanketWeekId: weekFromWeekAndYear(o.weekNumber, o.year).weekId,
            blanketQuantity: o.cases,
            bookedQuantity: 0,
            boekestynPlantId: o.plantId,
            boekestynCustomerAbbreviation: o.customer,
          }))
      )
      .sort((a, b) => {
        if (
          a.vendorId === boeks.BoekestynVendorId &&
          b.vendorId !== boeks.BoekestynVendorId
        ) {
          return -1;
        }

        if (
          a.vendorId !== boeks.BoekestynVendorId &&
          b.vendorId === boeks.BoekestynVendorId
        ) {
          return 1;
        }

        return a.requiredDate.localeCompare(b.requiredDate);
      }),
    hasBlanketItems = !!blanketItems.length,
    customerItemCodeDefault = customerItemCodeDefaults.find(
      (d) =>
        d.spireInventoryId === item.id &&
        d.customerId === customer?.id &&
        (d.shipToId == null || d.shipToId === shipTo?.id)
    ),
    defaultVendor = allVendors?.find(
      (v) => v.vendorNo === item.primaryVendor?.vendorNo
    );

  if (!hasBlanketItems) {
    return (
      <Combobox.Option
        value={{ item, blanketItemId: null, hasBlanketItems }}
        className={({ active }) =>
          classNames(
            'm-2 grid cursor-pointer grid-cols-8 rounded border border-gray-200 py-2 pl-4 pr-2 text-left shadow-md',
            active ? 'bg-blue-600 text-white' : 'bg-white text-gray-700'
          )
        }
      >
        {({ active }) => (
          <>
            <div
              className={classNames(
                'col-span-7 cursor-pointer truncate text-sm',
                active ? 'text-white' : 'text-gray-900'
              )}
            >
              <div
                className={classNames(
                  'font-medium',
                  active ? 'text-white' : 'text-gray-900'
                )}
              >
                {item.partNo}
              </div>

              <div
                className={classNames(
                  'cursor-pointer truncate text-sm',
                  active ? 'text-white' : 'text-gray-500'
                )}
              >
                {item.description}
              </div>
            </div>
            <div
              className={classNames(
                'flex justify-end border-gray-200 p-4 align-middle',
                active ? 'text-white' : 'text-blue-500'
              )}
            >
              <Icon icon="chevron-right" />
            </div>
            {!!customerItemCodeDefault?.customerItemCode && (
              <div
                className={classNames(
                  'col-span-8 cursor-pointer text-sm italic',
                  active ? 'text-white' : 'text-gray-500'
                )}
              >
                {customer?.name}
                {!!customerItemCodeDefault.shipToId && !!shipTo && (
                  <>
                    &nbsp;
                    <span
                      className={classNames(
                        'cursor-pointer text-xs italic',
                        active ? 'text-white' : 'text-gray-400'
                      )}
                    >
                      ({shipTo.name})
                    </span>
                    &nbsp;
                  </>
                )}
                Item Code {customerItemCodeDefault.customerItemCode}
              </div>
            )}
          </>
        )}
      </Combobox.Option>
    );
  }

  return (
    <div className="my-2 bg-blue-50 pb-1">
      <Combobox.Option
        value={{
          item,
          blanketItemId: null,
          hasBlanketItems: blanketItems.some(
            (i) => i.vendorId === defaultVendor?.id
          ),
        }}
        className={({ active }) =>
          classNames(
            'm-2 grid cursor-pointer grid-cols-4 rounded border border-b border-gray-200 py-2 pl-4 pr-2 text-left shadow-md',
            active ? 'bg-blue-600 text-white' : 'bg-white text-gray-700'
          )
        }
      >
        {({ active }) => (
          <>
            <div
              className={classNames(
                'col-span-3 cursor-pointer truncate text-sm',
                active ? 'text-white' : 'text-gray-900'
              )}
            >
              <span
                className={classNames(
                  'font-medium',
                  active ? 'text-white' : 'text-gray-900'
                )}
              >
                {item.partNo}
              </span>
              <br />
              <span
                className={classNames(
                  'col-span-3 col-start-1 cursor-pointer truncate text-sm',
                  active ? 'text-white' : 'text-gray-500'
                )}
              >
                {item.description}
              </span>
              {!!customerItemCodeDefault && (
                <div
                  className={classNames(
                    'cursor-pointer truncate text-sm italic',
                    active ? 'text-white' : 'text-gray-500'
                  )}
                >
                  {customer?.name}
                  {!!customerItemCodeDefault.shipToId && !!shipTo && (
                    <>
                      &nbsp;
                      <span
                        className={classNames(
                          'cursor-pointer text-xs italic',
                          active ? 'text-white' : 'text-gray-400'
                        )}
                      >
                        ({shipTo.name})
                      </span>
                      &nbsp;
                    </>
                  )}
                  Item Code {customerItemCodeDefault.customerItemCode}
                </div>
              )}
            </div>
            <div
              className={classNames(
                'flex flex-row justify-between border-gray-200 p-4 align-middle',
                active ? 'text-white' : 'text-blue-500'
              )}
            >
              <span className="cursor-pointer">No Blanket </span>
              <Icon icon="chevron-right" />
            </div>
          </>
        )}
      </Combobox.Option>

      <div className="mx-4 border-b-2 text-left italic text-gray-700">
        Open Blanket Prebooks
      </div>
      {blanketItems.map((blanketItem) => (
        <InventoryListItemBlanketItem
          key={blanketItem.id}
          blanketItem={blanketItem}
          item={item}
          customer={customer}
          shipTo={shipTo}
          customerItemCodeDefault={customerItemCodeDefault}
        />
      ))}
    </div>
  );
}
