import React, { Fragment, useState, useEffect } from 'react';
import { serializeError } from 'serialize-error';
import * as HeadlessUI from '@headlessui/react';
import { useInventoryItemsQuery } from 'api/spire-service';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import { parsePackQuantity } from '@/components/future-orders/item-functions';
import { classNames } from '@/utils/class-names';
import { handleFocus } from '@/utils/focus';
import {
  createProblemDetails,
  isProblemDetails,
} from '@/utils/problem-details';
import {
  selectSelectedProductDefault,
  setSelectedProductDefault,
  selectInventoryItems,
  saveProductDefault,
} from './product-default-settings-slice';
import {
  selectBoekestynPlantId,
  setBoekestynPlantId,
  selectBoekestynCustomerAbbreviation,
  setBoekestynCustomerAbbreviation,
  selectQuantityPerFinishedItem,
  setQuantityPerFinishedItem,
  selectUpgradeLabourHours,
  selectIsUpgrade,
  setUpgradeLabourHours,
  setIsUpgrade,
  selectMultipleProducts,
  setMultipleProducts,
  selectMappings,
  setMappings,
  selectOverrides,
  setOverrides,
  selectError,
  setError,
  selectIgnoreOverrideQuantity,
  setIgnoreOverrideQuantity,
} from './product-default-detail-slice';
import { ProductDefaultDetailMappings } from './product-default-detail-mappings';
import { ProductDefaultDetailOverrides } from './product-default-detail-overrides';
import { ProductDefaultDetailSingleMapping } from './product-default-detail-single-mapping';
import { BoekestynPrebookItemProductOverride } from 'api/models/boekestyns';

export function ProductDefaultDetail() {
  const dispatch = useAppDispatch(),
    { refetch } = useInventoryItemsQuery(),
    productDefault = useAppSelector(selectSelectedProductDefault),
    inventoryItems = useAppSelector(selectInventoryItems),
    error = useAppSelector(selectError),
    inventoryItem = inventoryItems.find(
      (i) => i.id === productDefault?.spireInventoryId
    ),
    boekestynPlantId = useAppSelector(selectBoekestynPlantId),
    boekestynCustomerAbbreviation = useAppSelector(
      selectBoekestynCustomerAbbreviation
    ),
    upgradeLabourHours = useAppSelector(selectUpgradeLabourHours),
    isUpgrade = useAppSelector(selectIsUpgrade),
    ignoreOverrideQuantity = useAppSelector(selectIgnoreOverrideQuantity),
    quantityPerFinishedItem = useAppSelector(selectQuantityPerFinishedItem),
    multipleProducts = useAppSelector(selectMultipleProducts),
    mappings = useAppSelector(selectMappings),
    overrides = useAppSelector(selectOverrides),
    [updateExisting, setUpdateExisting] = useState(true),
    packQuantity = parsePackQuantity(inventoryItem?.description);

  useEffect(() => {
    if (productDefault) {
      dispatch(setBoekestynPlantId(productDefault.boekestynPlantId));
      dispatch(
        setBoekestynCustomerAbbreviation(
          productDefault.boekestynCustomerAbbreviation
        )
      );
      dispatch(setUpgradeLabourHours(productDefault.upgradeLabourHours));
      dispatch(setIsUpgrade(productDefault.isUpgrade));
      dispatch(
        setIgnoreOverrideQuantity(productDefault.ignoreOverrideQuantity)
      );
      dispatch(
        setQuantityPerFinishedItem(productDefault.quantityPerFinishedItem)
      );
      const hasMultiple = !!productDefault.products.length;
      dispatch(setMultipleProducts(hasMultiple));
      if (hasMultiple) {
        const mappings = productDefault.products.map((p) => ({ ...p }));
        dispatch(setMappings(mappings));

        const overrides = productDefault.overrides.map((o) => ({
          ...o,
        }));

        dispatch(setOverrides(overrides));
      }
    }
  }, [dispatch, productDefault]);

  const onChange = (checked: boolean) => {
    dispatch(setMultipleProducts(checked));

    if (checked) {
      dispatch(
        setMappings([
          {
            id: 0,
            boekestynPlantId: '',
            boekestynCustomerAbbreviation: null,
            quantityPerFinishedItem: 1,
          },
        ])
      );
    }
  };

  const handleUpdateExistingChange = (value: boolean) => {
    setUpdateExisting(value);
  };

  const handleLabourHoursChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setUpgradeLabourHours(e.target.valueAsNumber || null));
  };

  const handleIsUpgradeChange = (value: boolean) => {
    dispatch(setIsUpgrade(value));
  };

  const handleCancelClick = () => {
    dispatch(setSelectedProductDefault(null));
  };

  const handleSaveClick = async () => {
    if (productDefault) {
      if (multipleProducts) {
        if (!mappings.length) {
          return dispatch(
            setError(
              createProblemDetails(
                'Please ensure there is at least one plant mapping.'
              )
            )
          );
        }

        if (mappings.some((m) => !m.boekestynPlantId)) {
          return dispatch(
            setError(
              createProblemDetails(
                'Please ensure all mappings have a Boekestyn Plant Id selected.'
              )
            )
          );
        }
        if (mappings.some((m) => !m.quantityPerFinishedItem)) {
          return dispatch(
            setError(
              createProblemDetails(
                'Please ensure all mappings have a Qty / Finished Item set.'
              )
            )
          );
        }

        if (
          !ignoreOverrideQuantity &&
          mappings.reduce(
            (total, m) => total + m.quantityPerFinishedItem,
            0
          ) !== packQuantity
        ) {
          return dispatch(
            setError(
              createProblemDetails(
                "Please ensure the total of all mappings' Qty / Finished Item equals the Pack Quantity of the product."
              )
            )
          );
        }

        if (overrides.some((o) => !o.boekestynPlantId)) {
          return dispatch(
            setError(
              createProblemDetails(
                'Please ensure all overrides have a Boekestyn Plant Id selected.'
              )
            )
          );
        }
        if (overrides.some((o) => !o.quantityPerFinishedItem)) {
          return dispatch(
            setError(
              createProblemDetails(
                'Please ensure all overrides have a Qty / Finished Item set.'
              )
            )
          );
        }
        if (
          overrides.some((o1) =>
            overrides.some(
              (o2) =>
                // o2 starts between o1's start/end
                (o2.startWeek > o1.startWeek && o2.startWeek <= o1.endWeek) ||
                // o2 ends between o1's start/end
                (o2.endWeek >= o1.startWeek && o2.endWeek < o1.endWeek)
            )
          )
        ) {
          return dispatch(
            setError(
              createProblemDetails(
                'Please ensure the override weeks do not overlap.'
              )
            )
          );
        }

        if (overrides.some((o) => o.endWeek < o.startWeek)) {
          return dispatch(
            setError(
              createProblemDetails(
                'Please ensure all overrides have an end week equal to or later than the start week.'
              )
            )
          );
        }

        const overridesByWeek = overrides.reduce((memo, o) => {
            const key = o.startWeek.toString();
            if (key in memo) {
              memo[key].push(o);
            } else {
              memo[key] = [o];
            }
            return memo;
          }, {} as { [index: string]: BoekestynPrebookItemProductOverride[] }),
          overridesByWeekValues = Array.from(Object.values(overridesByWeek));

        if (
          !ignoreOverrideQuantity &&
          overridesByWeekValues.some(
            (o) =>
              o.reduce((total, m) => total + m.quantityPerFinishedItem, 0) !==
              packQuantity
          )
        ) {
          return dispatch(
            setError(
              createProblemDetails(
                'Please ensure the total of all overrides for a given week equals the Pack Quantity of the product.'
              )
            )
          );
        }
      }

      dispatch(setError(null));

      try {
        const spireInventoryId = productDefault.spireInventoryId,
          updated = {
            spireInventoryId,
            boekestynPlantId,
            boekestynCustomerAbbreviation,
            upgradeLabourHours,
            quantityPerFinishedItem,
            isUpgrade,
            ignoreOverrideQuantity,
            updateExisting,
            products: mappings.map((m) => ({ ...m, spireInventoryId })),
            overrides: overrides.map((o) => ({ ...o, spireInventoryId })),
          };

        const result = await dispatch(saveProductDefault(updated));
        if ('error' in result) {
          if (isProblemDetails(result.payload)) {
            dispatch(setError(result.payload));
          } else if (result.payload instanceof Error) {
            const error = serializeError(result.payload);
            dispatch(
              setError(
                createProblemDetails(error.name || 'Error', error.message)
              )
            );
          } else {
            dispatch(
              setError(
                createProblemDetails(
                  JSON.stringify(result.payload || result.error)
                )
              )
            );
          }
        } else {
          refetch();
        }
      } catch (e) {
        console.error(e);
      }
    }
  };

  const handleClose = () => {
    dispatch(setSelectedProductDefault(null));
  };

  const handleClearErrorClick = () => {
    dispatch(setError(null));
  };

  return (
    <HeadlessUI.Transition.Root show={!!productDefault} as={Fragment}>
      <HeadlessUI.Dialog
        as="div"
        className="relative z-30"
        onClose={handleClose}
      >
        <HeadlessUI.Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </HeadlessUI.Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex items-center justify-center p-0 text-center">
            <HeadlessUI.Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-0 scale-95"
              enterTo="opacity-100 translate-y-0 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 scale-100"
              leaveTo="opacity-0 translate-y-0 scale-95"
            >
              <HeadlessUI.Dialog.Panel className="relative h-screen w-screen transform overflow-hidden p-6 transition-all">
                <div
                  className="mx-auto flex max-w-7xl flex-col overflow-y-auto rounded-lg bg-white p-6 text-left shadow-xl"
                  style={{ maxHeight: 'calc(100vh - 3rem)' }}
                >
                  <div className="mb-4 flex justify-center border-b-2 pb-4">
                    <HeadlessUI.Dialog.Title
                      as="h3"
                      className="text-lg font-medium leading-6 text-gray-900"
                    >
                      <Icon
                        icon="boxes-stacked"
                        className="h-6 w-6"
                        aria-hidden="true"
                      />
                      &nbsp; Edit Product Defaults
                    </HeadlessUI.Dialog.Title>
                  </div>
                  <div className="flex flex-grow flex-col overflow-y-auto">
                    <form className="flex w-full overflow-y-auto">
                      <div className="flex w-full flex-col overflow-y-auto">
                        <label className="mt-2 block pt-2 text-sm font-medium text-gray-700">
                          Product
                        </label>
                        <div className="block w-full min-w-0 flex-1 border-b-2 border-gray-300 font-semibold">
                          {inventoryItem?.partNo}: {inventoryItem?.description}
                        </div>

                        <div className="mt-8 grid grid-cols-3 gap-x-4 overflow-y-auto">
                          <div>
                            <h3 className="text-semibold text-xl">
                              Upgrade Settings
                            </h3>

                            <label className="mt-2 block pt-2 text-sm font-medium text-gray-700">
                              Labour Hours
                            </label>
                            <input
                              type="number"
                              value={upgradeLabourHours || ''}
                              onChange={handleLabourHoursChange}
                              onFocus={handleFocus}
                              className="block w-32 min-w-0 flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            />

                            <HeadlessUI.Switch.Group as="div" className="my-2">
                              <HeadlessUI.Switch.Label className="mr-2 cursor-pointer">
                                Item requires an Upgrade Sheet
                              </HeadlessUI.Switch.Label>
                              <HeadlessUI.Switch
                                checked={isUpgrade}
                                onChange={handleIsUpgradeChange}
                                className={classNames(
                                  isUpgrade
                                    ? 'bg-blue-400 outline-none ring-2 ring-blue-500 ring-offset-2'
                                    : 'bg-gray-200',
                                  'relative mx-2 inline-flex h-4 w-8 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out'
                                )}
                              >
                                <span
                                  aria-hidden="true"
                                  className={classNames(
                                    isUpgrade
                                      ? 'translate-x-4'
                                      : 'translate-x-0',
                                    'pointer-events-none inline-block h-3 w-3 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                                  )}
                                />
                              </HeadlessUI.Switch>
                            </HeadlessUI.Switch.Group>
                          </div>
                          <div className="col-span-2 flex flex-col overflow-auto">
                            <h3 className="text-semibold text-xl">
                              Boekestyn Settings
                            </h3>

                            <HeadlessUI.Switch.Group as="div" className="my-2">
                              <HeadlessUI.Switch.Label className="mr-2 cursor-pointer">
                                Single Product
                              </HeadlessUI.Switch.Label>
                              <HeadlessUI.Switch
                                checked={multipleProducts}
                                onChange={onChange}
                                className={classNames(
                                  multipleProducts
                                    ? 'bg-blue-400 outline-none ring-2 ring-blue-500 ring-offset-2'
                                    : 'bg-gray-200',
                                  'relative mx-2 inline-flex h-4 w-8 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out'
                                )}
                              >
                                <span
                                  aria-hidden="true"
                                  className={classNames(
                                    multipleProducts
                                      ? 'translate-x-4'
                                      : 'translate-x-0',
                                    'pointer-events-none inline-block h-3 w-3 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                                  )}
                                />
                              </HeadlessUI.Switch>
                              <HeadlessUI.Switch.Label className="ml-2 cursor-pointer">
                                Multiple Products
                                <span className="text-xs italic text-gray-500">
                                  &nbsp;(or single product with seasonal
                                  overrides)
                                </span>
                              </HeadlessUI.Switch.Label>
                            </HeadlessUI.Switch.Group>
                            <ProductDefaultDetailSingleMapping />
                            <div className="overflow-y-auto">
                              <ProductDefaultDetailMappings />
                              <ProductDefaultDetailOverrides />
                            </div>
                          </div>
                        </div>
                      </div>
                    </form>
                  </div>

                  <Error error={error} clear={handleClearErrorClick} />

                  <div className="mt-4 flex border-t-2 pt-4">
                    <div className="mr-8 mt-2 flex-grow text-right">
                      <HeadlessUI.Switch.Group as="div">
                        <HeadlessUI.Switch
                          checked={updateExisting}
                          onChange={handleUpdateExistingChange}
                          className={classNames(
                            updateExisting
                              ? 'bg-blue-400 outline-none ring-2 ring-blue-500 ring-offset-2'
                              : 'bg-gray-200',
                            'relative inline-flex h-4 w-8 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out'
                          )}
                        >
                          <span
                            aria-hidden="true"
                            className={classNames(
                              updateExisting
                                ? 'translate-x-4'
                                : 'translate-x-0',
                              'pointer-events-none inline-block h-3 w-3 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                            )}
                          />
                        </HeadlessUI.Switch>
                        <HeadlessUI.Switch.Label className="ml-2">
                          Update existing Future Orders
                        </HeadlessUI.Switch.Label>
                      </HeadlessUI.Switch.Group>
                    </div>
                    <div>
                      <button
                        type="button"
                        className="btn-secondary text-lg"
                        onClick={handleCancelClick}
                      >
                        Cancel
                      </button>
                      <button
                        type="button"
                        className="btn-primary ml-4 text-lg"
                        onClick={handleSaveClick}
                      >
                        Save&nbsp;
                        <Icon icon="save" className="ml-2" />
                      </button>
                    </div>
                  </div>
                </div>
              </HeadlessUI.Dialog.Panel>
            </HeadlessUI.Transition.Child>
          </div>
        </div>
      </HeadlessUI.Dialog>
    </HeadlessUI.Transition.Root>
  );
}
