import {
  createAction,
  createAsyncThunk,
  createSlice,
  AsyncThunk,
} from '@reduxjs/toolkit';
import { settingsService } from 'api/settings-service';
import * as settings from 'api/models/settings';
import { RootState } from '@/services/store';
import { ProblemDetails } from '@/utils/problem-details';

export interface UpgradeOptionSettingsState {
  isLoading: boolean;
  error: ProblemDetails | null;
  options: settings.UpgradeOption[];
}

const initialState: UpgradeOptionSettingsState = {
  isLoading: false,
  error: null,
  options: [],
};

export const getData: AsyncThunk<
  settings.UpgradeOption[],
  void,
  { state: RootState }
> = createAsyncThunk(
  'upgrade-option-settings-getData',
  async (_, { rejectWithValue }) => {
    try {
      return await settingsService.getAllUpgradeOptions();
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const createUpgradeOption: AsyncThunk<
  settings.UpgradeOption[],
  settings.UpgradeOption,
  { state: RootState }
> = createAsyncThunk(
  'upgrade-option-settings-createUpgradeOption',
  async (
    { containerPickDescription, origins, costs, tariffCode },
    { rejectWithValue }
  ) => {
    try {
      const { upgradeOptions } =
        await settingsService.createOrUpdateUpgradeOption(
          containerPickDescription || '',
          origins,
          costs,
          tariffCode
        );
      return upgradeOptions;
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const updateUpgradeOption: AsyncThunk<
  settings.UpgradeOption[],
  settings.UpgradeOption,
  { state: RootState }
> = createAsyncThunk(
  'upgrade-option-settings-updateUpgradeOption',
  async (option, { rejectWithValue }) => {
    try {
      const { upgradeOptions } = await settingsService.updateUpgradeOption(
        option
      );
      return upgradeOptions;
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const deleteUpgradeOption: AsyncThunk<
  settings.UpgradeOption[],
  number,
  { state: RootState }
> = createAsyncThunk(
  'upgrade-option-settings-deleteUpgradeOption',
  async (id, { rejectWithValue }) => {
    try {
      const { upgradeOptions } = await settingsService.deleteUpgradeOption(id);
      return upgradeOptions;
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

const getDataPending = createAction(getData.pending.type),
  getDataFulfilled = createAction<settings.UpgradeOption[]>(
    getData.fulfilled.type
  ),
  getDataRejected = createAction<ProblemDetails>(getData.rejected.type),
  updateOptionPending = createAction(updateUpgradeOption.pending.type),
  updateOptionFulfilled = createAction<settings.UpgradeOption[]>(
    updateUpgradeOption.fulfilled.type
  ),
  updateOptionRejected = createAction<ProblemDetails>(
    updateUpgradeOption.rejected.type
  ),
  createOptionPending = createAction(createUpgradeOption.pending.type),
  createOptionFulfilled = createAction<settings.UpgradeOption[]>(
    createUpgradeOption.fulfilled.type
  ),
  createOptionRejected = createAction<ProblemDetails>(
    createUpgradeOption.rejected.type
  ),
  deleteOptionPending = createAction(deleteUpgradeOption.pending.type),
  deleteOptionFulfilled = createAction<settings.UpgradeOption[]>(
    deleteUpgradeOption.fulfilled.type
  ),
  deleteOptionRejected = createAction<ProblemDetails>(
    deleteUpgradeOption.rejected.type
  );

export const upgradeOptionsSettingsSlice = createSlice({
  name: 'upgrade-option-settings',
  initialState,
  reducers: {
    clearState(state) {
      Object.assign(state, { ...initialState });
    },
    clearError(state) {
      state.error = null;
    },
  },
  extraReducers: (builder) =>
    builder
      .addCase(getDataPending, (state) => {
        state.isLoading = true;
      })
      .addCase(getDataFulfilled, (state, { payload }) => {
        state.isLoading = false;
        state.options = payload;
      })
      .addCase(getDataRejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      .addCase(createOptionPending, (state) => {
        state.isLoading = true;
      })
      .addCase(createOptionFulfilled, (state, { payload }) => {
        state.isLoading = false;
        state.options = payload;
      })
      .addCase(createOptionRejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      .addCase(updateOptionPending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateOptionFulfilled, (state, { payload }) => {
        state.isLoading = false;
        state.options = payload;
      })
      .addCase(updateOptionRejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      .addCase(deleteOptionPending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteOptionFulfilled, (state, { payload }) => {
        state.isLoading = false;
        state.options = payload;
      })
      .addCase(deleteOptionRejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      }),
});

export const { clearError, clearState } = upgradeOptionsSettingsSlice.actions;

export const selectIsLoading = ({ upgradeOptionsSettings }: RootState) =>
  upgradeOptionsSettings.isLoading;
export const selectError = ({ upgradeOptionsSettings }: RootState) =>
  upgradeOptionsSettings.error;
export const selectOptions = ({ upgradeOptionsSettings }: RootState) =>
  upgradeOptionsSettings.options;

export default upgradeOptionsSettingsSlice.reducer;
