import { useState } from 'react';
import { Icon } from '@/components/icon';
import { useAppSelector } from '@/services/hooks';
import { classNames } from '@/utils/class-names';
import { equals } from '@/utils/equals';
import {
  selectWeeks,
  selectSingleProductItems,
  selectMultipleProductItems,
  selectProductionCustomers,
} from './sales-slice';
import { CustomerWeekItem } from './customer-week-item';
import { CustomerShipToItem } from './customer-ship-to-item';

interface CustomerItemProps {
  salesCustomer: string;
  multiple?: boolean;
}

export function CustomerItem({ salesCustomer, multiple }: CustomerItemProps) {
  const weeks = useAppSelector(selectWeeks),
    singleProductItems = useAppSelector(selectSingleProductItems),
    multipleProductItems = useAppSelector(selectMultipleProductItems),
    productionCustomers = useAppSelector(selectProductionCustomers),
    [showShipTos, setShowShipTos] = useState(false),
    prebookItems = multiple ? multipleProductItems : singleProductItems,
    customerPrebookItems = prebookItems.filter(
      (o) =>
        equals(o.customerName, salesCustomer) &&
        (!productionCustomers.length ||
          productionCustomers.indexOf(o.boekestynCustomerAbbreviation) !== -1)
    ),
    shipTos = customerPrebookItems.reduce((memo, o) => {
      const shipTo = o.shipToName || o.boxCode || '';
      if (!memo.some((m) => equals(m, shipTo))) {
        memo.push(shipTo);
      }
      return memo;
    }, [] as string[]);

  const handleToggleShipTosClick = () => {
    setShowShipTos(!showShipTos);
  };

  return (
    <>
      <tr
        className={classNames(
          'border-gray-100',
          showShipTos ? '' : 'border-b-2'
        )}
      >
        <th className="whitespace-nowrap p-2 text-left font-semibold">
          <div className="flex w-full">
            <div className="flex flex-grow">
              {salesCustomer}
              {shipTos.length === 1 && !!shipTos[0] && (
                <span className="italic text-gray-700">
                  &nbsp;({shipTos[0]})
                </span>
              )}
            </div>
            <div>
              {(shipTos.length > 1 ||
                shipTos.some(
                  (st) =>
                    customerPrebookItems.filter(
                      (i) =>
                        equals(i.couchCustomer, `${salesCustomer}: ${st}`) &&
                        i.orderQuantity
                    ).length > 1
                )) && (
                <button
                  type="button"
                  className="btn-secondary p-2 text-xs focus:ring-0"
                  onClick={handleToggleShipTosClick}
                >
                  <Icon icon={showShipTos ? 'chevron-up' : 'chevron-down'} />
                </button>
              )}
            </div>
          </div>
        </th>
        {weeks.map(({ week, customer }) =>
          showShipTos ? null : (
            <CustomerWeekItem
              key={`${week.weekId}-${customer}`}
              customer={customer}
              week={week}
              salesCustomer={salesCustomer}
              multiple={!!multiple}
            />
          )
        )}
      </tr>
      {showShipTos && (
        <>
          {shipTos.map((shipTo) => (
            <tr key={shipTo} className="border-b border-gray-100">
              <th className="whitespace-nowrap py-2 pr-12 text-right font-semibold">
                {shipTo}
              </th>
              {weeks.map(({ week, customer }) => (
                <CustomerShipToItem
                  key={`${week.weekId}-${customer}-${shipTo}`}
                  customer={customer}
                  shipTo={shipTo}
                  week={week}
                  salesCustomer={salesCustomer}
                  multiple={!!multiple}
                />
              ))}
            </tr>
          ))}
          <tr className="border-b border-gray-300 bg-gray-100">
            <th className="whitespace-nowrap py-2 pr-12 text-right text-sm font-semibold">
              {salesCustomer}&nbsp;Total:
            </th>
            {weeks.map(({ week, customer }) => (
              <CustomerWeekItem
                key={`${week.weekId}-${customer}`}
                customer={customer}
                week={week}
                salesCustomer={salesCustomer}
                multiple={!!multiple}
              />
            ))}
          </tr>
        </>
      )}
    </>
  );
}
