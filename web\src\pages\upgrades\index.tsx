import React, { useState } from 'react';
import Head from 'next/head';
import { useUpgradeItemsQuery } from 'api/future-orders-service';
import * as futureOrders from 'api/models/future-orders';
import { usePermissions } from '@/services/auth';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import {
  clearError,
  clearSearchAndFilters,
  setStartDate,
  setEndDate,
  setSearch,
  setCustomerFilter,
  setShipToFilter,
  setItemSort,
  selectStartDate,
  selectEndDate,
  selectSearch,
  selectIsLoading,
  selectError,
  selectItemDates,
  selectItemCustomers,
  selectItemShipTos,
  selectFilter,
  selectUpgradeItems,
  selectItemsSort,
  selectSortItemsDescending,
  downloadUpgradeItemsList,
  setShowConfirmed,
} from '@/components/upgrades/upgrade-item-list-slice';
import { UpgradeReportWithAdditions } from '@/components/upgrades/upgrade-report-with-additions';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import { Loading } from '@/components/loading';
import { UpgradeDate } from '@/components/upgrades/upgrade-date';
import { classNames } from '@/utils/class-names';

type ConfirmedFilter = 'Show Confirmed' | 'Show Unconfirmed' | 'Show All';

// 10 minutes
const RefreshInterval = 10 * 60 * 1000;

export default function Items() {
  const dispatch = useAppDispatch(),
    { can } = usePermissions(),
    items = useAppSelector(selectUpgradeItems),
    customers = useAppSelector(selectItemCustomers),
    shipTos = useAppSelector(selectItemShipTos),
    dates = useAppSelector(selectItemDates),
    startDate = useAppSelector(selectStartDate),
    endDate = useAppSelector(selectEndDate),
    search = useAppSelector(selectSearch),
    sort = useAppSelector(selectItemsSort),
    sortDescending = useAppSelector(selectSortItemsDescending),
    isLoading = useAppSelector(selectIsLoading),
    error = useAppSelector(selectError),
    filter = useAppSelector(selectFilter),
    { refetch } = useUpgradeItemsQuery({ startDate, endDate }),
    [showAdvanced, setShowAdvanced] = useState(
      !!filter.customer || !!filter.shipTo
    ),
    showConfirmed: ConfirmedFilter =
      filter.showConfirmed && !filter.showUnconfirmed
        ? 'Show Confirmed'
        : !filter.showConfirmed && filter.showUnconfirmed
        ? 'Show Unconfirmed'
        : 'Show All';

  const handleDownloadClick = () => {
    const start = startDate ? `-${startDate}` : '',
      end = endDate && endDate !== startDate ? ` - ${endDate}` : '',
      args = {
        filename: `UpgradeItems${start}${end}.xlsx`,
        items,
      };
    dispatch(downloadUpgradeItemsList(args));
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setSearch(e.target.value));
  };

  const handleStartDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setStartDate(e.target.value || ''));
  };

  const handleEndDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setEndDate(e.target.value || ''));
  };

  const handleResetSearchClick = async () => {
    await dispatch(clearSearchAndFilters());
    console.log('Refetching from Index Reset Search');
    window.setTimeout(refetch);
  };

  const handleRefreshClick = () => {
    console.log('Refetching from Index Refresh');
    refetch();
  };

  const handleClearError = () => {
    dispatch(clearError());
  };

  const handleColumnSort = (sortProp: keyof futureOrders.UpgradeItem) => {
    const descending = sortProp === sort ? !sortDescending : false;
    dispatch(setItemSort({ sort: sortProp, sortDescending: descending }));
  };

  const handleToggleAdvanced = () => {
    setShowAdvanced(!showAdvanced);
    if (showAdvanced) {
      dispatch(setCustomerFilter(null));
      dispatch(setShipToFilter(null));
    }
  };

  const handleCustomerFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setCustomerFilter(e.target.value || null));
  };

  const handleShipToFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setShipToFilter(e.target.value || null));
  };

  const handleShowConfirmedChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    const showConfirmed = e.target.value as ConfirmedFilter,
      filter =
        showConfirmed === 'Show Confirmed'
          ? { showConfirmed: true, showUnconfirmed: false }
          : showConfirmed === 'Show Unconfirmed'
          ? { showConfirmed: false, showUnconfirmed: true }
          : { showConfirmed: true, showUnconfirmed: true };

    dispatch(setShowConfirmed(filter));
  };

  interface HeaderButtonProps {
    text: string;
    propName: keyof futureOrders.UpgradeItem;
  }
  const HeaderButton = ({ text, propName }: HeaderButtonProps) => (
    <button
      type="button"
      className="group inline-flex"
      onClick={() => handleColumnSort(propName)}
    >
      {text}
      <span
        className={classNames(
          'ml-2 flex-none rounded text-gray-400 group-hover:visible group-focus:visible',
          sort !== propName && 'invisible'
        )}
      >
        <Icon
          icon={sortDescending ? 'chevron-down' : 'chevron-up'}
          className="h-5 w-5"
          aria-hidden="true"
        />
      </span>
    </button>
  );

  return (
    <>
      <Head>
        <title>Upgrade Items</title>
      </Head>
      <header className="bg-white shadow">
        <div className="grid grid-cols-1">
          <div className="bg-gray-100 py-2">
            <div className="mx-auto flex max-w-7xl items-center justify-between px-8">
              <div className="flex flex-grow items-center align-top">
                <div className="flex w-full flex-col rounded p-2">
                  <div className="grid w-full grid-cols-2 gap-2 rounded-sm text-xs md:grid-cols-8">
                    <div>
                      <label htmlFor="start-date">From Date</label>
                      <input
                        type="date"
                        max="2050-01-01"
                        id="start-date"
                        value={startDate}
                        onChange={handleStartDateChange}
                        className="w-full !max-w-none text-xs"
                      />
                    </div>
                    <div>
                      <label htmlFor="end-date">To Date</label>
                      <input
                        type="date"
                        max="2050-01-01"
                        id="end-date"
                        value={endDate}
                        onChange={handleEndDateChange}
                        className="w-full !max-w-none text-xs"
                      />
                    </div>
                    <div className="col-span-2 md:col-span-4">
                      <label htmlFor="end-date">Search</label>
                      <div className="flex">
                        <input
                          type="search"
                          autoComplete="off"
                          id="search"
                          name="search"
                          value={search}
                          onChange={handleSearchChange}
                          className="flex-grow text-xs"
                          placeholder="Search"
                        />
                        <button
                          type="button"
                          className="btn-secondary flex px-1 focus:ring-0"
                          onClick={handleToggleAdvanced}
                        >
                          <Icon
                            icon={showAdvanced ? 'chevron-up' : 'chevron-down'}
                            className="h-5 w-5"
                          />
                        </button>
                        <button
                          type="button"
                          className="btn-secondary flex px-1 focus:ring-0"
                          title="Reset Search Filters"
                          onClick={handleResetSearchClick}
                        >
                          <Icon
                            icon="magnifying-glass-arrows-rotate"
                            className="h-5 w-5"
                          />
                        </button>
                      </div>
                    </div>
                    <div className="col-span-2 grid grid-cols-2 items-start justify-center gap-x-4 pt-3 md:col-span-1 md:flex">
                      <button
                        type="button"
                        onClick={handleRefreshClick}
                        className="btn-secondary flex justify-center p-3 text-center text-blue-700"
                      >
                        <Icon icon="refresh" spin={isLoading} />
                        <span className="md:hidden">&nbsp;Refresh</span>
                      </button>
                      <button
                        type="button"
                        onClick={handleDownloadClick}
                        className="btn-secondary flex justify-center p-3 text-center text-green-700"
                      >
                        <Icon icon="file-excel" />
                        <span className="md:hidden">&nbsp;Download</span>
                      </button>
                    </div>
                    {showAdvanced && (
                      <>
                        <div className="col-start-1">
                          <label htmlFor="customer-filter">Customer</label>
                          <select
                            id="customer-filter"
                            value={filter.customer || ''}
                            onChange={handleCustomerFilterChange}
                            className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                          >
                            <option value="">All Customers</option>
                            {customers.map((customer) => (
                              <option key={customer}>{customer}</option>
                            ))}
                          </select>
                        </div>
                        <div className="">
                          <label htmlFor="ship-to-filter">Ship To</label>
                          <select
                            id="ship-to-filter"
                            value={filter.shipTo || ''}
                            onChange={handleShipToFilterChange}
                            className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                          >
                            <option value="">All Ship Tos</option>
                            {shipTos.map((shipTo) => (
                              <option key={shipTo}>{shipTo}</option>
                            ))}
                          </select>
                        </div>
                        <div className="col-span-2">
                          <label htmlFor="show-confirmed-filter">
                            Confirmed
                          </label>
                          <select
                            id="show-confirmed-filter"
                            value={showConfirmed || ''}
                            onChange={handleShowConfirmedChange}
                            className="mt-1 block rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                          >
                            <option value="Show All">Show All</option>
                            <option value="Show Confirmed">
                              Show Confirmed
                            </option>
                            <option value="Show Unconfirmed">
                              Show Unconfirmed
                            </option>
                          </select>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>
      <main className="flex-grow overflow-auto">
        <div className="mx-auto h-full md:px-8">
          <Error error={error} clear={handleClearError} />
          {isLoading && <Loading />}
          <div className="flex h-full flex-col md:mt-8">
            <div className="h-full md:-mx-8 md:-my-2">
              <div className="inline-block min-w-full align-middle md:px-8 md:py-2">
                <div className="rounded-lg shadow ring-1 ring-black ring-opacity-5">
                  <table className="min-w-full divide-y divide-gray-300">
                    <thead>
                      <tr className="sticky top-0 z-10 text-xs font-semibold text-gray-900">
                        <th className="bg-gray-100 px-2 py-3 text-center">
                          &nbsp;
                        </th>
                        <th className="bg-gray-100 px-2 py-3 text-center">
                          <HeaderButton text="Qty" propName="orderQuantity" />
                        </th>
                        <th className="bg-gray-100 px-2 py-3 text-center">
                          Pack
                        </th>
                        <th className="bg-gray-100 px-2 py-3 text-left">
                          <HeaderButton
                            text="Description"
                            propName="description"
                          />
                        </th>
                        <th className="bg-gray-100 px-2 py-3 text-center">
                          <HeaderButton
                            text="Product Coming From"
                            propName="productComingFrom"
                          />
                        </th>
                        <th className="whitespace-nowrap bg-gray-100 px-2 py-3 text-center">
                          UPC | Date Code | Retail | W&M
                        </th>
                        <th className="bg-gray-100 px-2 py-3 text-center">
                          <HeaderButton
                            text="Container/Pick Description"
                            propName="containerPickDescription"
                          />
                        </th>
                        <th className="bg-gray-100 px-2 py-3 text-center">
                          <HeaderButton text="Origins" propName="origins" />
                        </th>
                        <th className="bg-gray-100 px-2 py-3 text-center">
                          <HeaderButton text="Costs" propName="costs" />
                        </th>
                        <th className="bg-gray-100 px-2 py-3 text-center">
                          <HeaderButton text="Box Code" propName="boxCode" />
                        </th>
                        <th className="bg-gray-100 px-2 py-3 text-center">
                          <HeaderButton text="Hours" propName="labourHours" />
                        </th>
                        <th className="bg-gray-100 px-2 py-3 text-center">
                          <HeaderButton text="Prebook" propName="prebookId" />
                        </th>
                      </tr>
                    </thead>
                    {dates.map((date) => (
                      <UpgradeDate
                        key={`${date.date}-${date.season}`}
                        date={date}
                        refetch={refetch}
                      />
                    ))}
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
        <UpgradeReportWithAdditions />
      </main>
    </>
  );
}
