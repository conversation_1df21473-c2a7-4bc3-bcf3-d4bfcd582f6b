import { useState } from 'react';
import Link from 'next/link';
import * as models from 'api/models/prebooks';
import { Icon } from '@/components/icon';
import { formatDate, formatNumber } from '@/utils/format';
import { sortBy } from '@/utils/sort';
import { classNames } from '@/utils/class-names';
import { routes } from '@/services/routes';
import { weekFromId } from '@/utils/weeks';

export interface BlanketItemWeeksItemWeekProps {
  item: models.BlanketItemListItem;
  hasSeasons: boolean;
}

export function BlanketItemWeeksItemWeek({
  item,
  hasSeasons,
}: BlanketItemWeeksItemWeekProps) {
  const [showBookings, setShowBookings] = useState(false),
    bookingItems = item.bookingItems
      .map((i) => ({ ...i }))
      .sort(sortBy('requiredDate')),
    remaining = item.blanketQuantity - item.bookedQuantity,
    week = weekFromId(item.blanketWeekId),
    hasBookings = !!item.bookingItems.length;

  const handleToggleBookingsClick = () => {
    setShowBookings(!showBookings);
  };

  return (
    <>
      <tr>
        <td
          colSpan={4}
          className="whitespace-nowrap px-3 py-2 text-sm text-gray-500"
        >
          &nbsp;
        </td>
        <td
          colSpan={hasSeasons ? 5 : 4}
          className={classNames(
            !showBookings && 'border-b border-gray-200',
            'whitespace-nowrap px-3 py-2 text-sm italic text-gray-500'
          )}
        >
          Week {week?.week}, {week?.year}
          &nbsp;
          {hasBookings && (
            <button
              type="button"
              className="btn-secondary p-1 text-xs focus:ring-0"
              onClick={handleToggleBookingsClick}
            >
              <Icon icon={showBookings ? 'chevron-up' : 'chevron-down'} />
            </button>
          )}
        </td>
        <td
          className={classNames(
            !showBookings && 'border-b border-gray-200',
            'whitespace-nowrap px-3 py-2 text-right text-sm italic text-gray-500'
          )}
        >
          {formatNumber(item.blanketQuantity)}
        </td>
        <td
          className={classNames(
            !showBookings && 'border-b border-gray-200',
            'whitespace-nowrap px-3 py-2 text-right text-sm italic text-gray-500'
          )}
        >
          {formatNumber(item.bookedQuantity)}
        </td>
        <td
          className={classNames(
            remaining < 0 ? 'text-red-500' : 'text-gray-500',
            !showBookings && 'border-b border-gray-200',
            'whitespace-nowrap px-3 py-2 text-right text-sm italic'
          )}
        >
          {formatNumber(remaining)}
        </td>
      </tr>
      {!!showBookings && (
        <>
          {bookingItems.map((booking) => (
            <tr key={booking.id}>
              <td
                colSpan={5}
                className="whitespace-nowrap p-2 text-right text-xs"
              >
                {!!booking.prebookId && (
                  <Link href={routes.prebooks.detail.to(booking.prebookId)}>
                    Edit Prebook
                  </Link>
                )}
                {!!booking.futureOrderId && (
                  <Link
                    href={routes.futureOrders.detail.to(booking.futureOrderId)}
                  >
                    Edit Future Order
                  </Link>
                )}
              </td>
              <td
                className={classNames(
                  'whitespace-nowrap border-b-2 border-white p-2 text-left text-xs text-gray-600',
                  booking.prebookId && 'bg-blue-100',
                  booking.futureOrderId && 'bg-orange-100'
                )}
              >
                {formatDate(booking.requiredDate, 'MMM d')}
              </td>
              <td
                className={classNames(
                  'whitespace-nowrap border-b-2 border-white p-2 text-left text-xs text-gray-600',
                  booking.prebookId && 'bg-blue-100',
                  booking.futureOrderId && 'bg-orange-100'
                )}
              >
                {booking.salesperson}
              </td>
              <td
                className={classNames(
                  'whitespace-nowrap border-b-2 border-white p-2 text-left text-xs text-gray-600',
                  booking.prebookId && 'bg-blue-100',
                  booking.futureOrderId && 'bg-orange-100'
                )}
              >
                {booking.customer}
                {!!booking.shipTo && (
                  <>
                    <br />
                    <span className="italic">{booking.shipTo}</span>
                  </>
                )}
              </td>
              {hasSeasons && (
                <td
                  className={classNames(
                    'whitespace-nowrap border-b-2 border-white p-2 text-left text-xs text-gray-600',
                    booking.prebookId && 'bg-blue-100',
                    booking.futureOrderId && 'bg-orange-100'
                  )}
                >
                  {booking.season}
                </td>
              )}
              <td
                className={classNames(
                  'whitespace-nowrap border-b-2 border-white p-2 text-left text-xs text-gray-600',
                  booking.prebookId && 'bg-blue-100',
                  booking.futureOrderId && 'bg-orange-100'
                )}
              >
                &nbsp;
              </td>
              <td
                className={classNames(
                  'whitespace-nowrap border-b-2 border-white p-2 text-center text-xs text-gray-600',
                  booking.prebookId && 'bg-blue-100',
                  booking.futureOrderId && 'bg-orange-100'
                )}
              >
                {formatNumber(booking.orderQuantity)}
              </td>
              <td
                className={classNames(
                  'whitespace-nowrap border-b-2 border-white p-2 text-left text-xs text-gray-600'
                )}
              >
                &nbsp;
              </td>
            </tr>
          ))}
          <tr className="border-b-2 border-gray-300">
            <td
              colSpan={3}
              className="whitespace-nowrap p-2 text-right align-top text-sm text-gray-700"
            >
              &nbsp;
            </td>
            <td
              colSpan={hasSeasons ? 6 : 5}
              className="whitespace-nowrap bg-gray-200 p-2 text-right text-sm italic text-gray-700"
            >
              Blanket Total:
            </td>
            <td className="whitespace-nowrap bg-gray-200 p-2 text-center text-sm italic text-gray-700">
              {formatNumber(item.blanketQuantity)}
            </td>
            <td className="whitespace-nowrap bg-gray-200 p-2 text-center text-sm italic text-gray-700">
              {formatNumber(item.bookedQuantity)}
            </td>
            <td
              className={classNames(
                'whitespace-nowrap bg-gray-200 p-2 text-center text-sm italic',
                remaining < 0 ? 'text-red-600' : 'text-gray-700'
              )}
            >
              {formatNumber(remaining)}
            </td>
          </tr>
        </>
      )}
    </>
  );
}
