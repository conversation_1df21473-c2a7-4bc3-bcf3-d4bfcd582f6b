import React, { useEffect, useState } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useInventoryItemsQuery } from 'api/spire-service';
import * as spire from 'api/models/spire';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { routes } from '@/services/routes';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import { Loading } from '@/components/loading';
import {
  getPlants,
  getCustomers,
  clearError,
  clearState,
  selectError,
  selectIsLoading,
  selectProductDefaults,
  setSelectedProductDefault,
  selectSearch,
  setSearch,
} from '@/components/settings/products/product-default-settings-slice';
import { ProductDefaultDetail } from '@/components/settings/products/product-default-detail';
import { ProductDefaultNew } from '@/components/settings/products/product-default-new';

export default function ProductDefaults() {
  const dispatch = useAppDispatch(),
    search = useAppSelector(selectSearch),
    isLoading = useAppSelector(selectIsLoading),
    error = useAppSelector(selectError),
    productDefaults = useAppSelector(selectProductDefaults),
    { refetch, isFetching } = useInventoryItemsQuery(),
    [showNewDialog, setShowNewDialog] = useState(false);

  useEffect(() => {
    dispatch(clearState());
    dispatch(getPlants());
    dispatch(getCustomers());
  }, [dispatch]);

  const handleClearError = () => {
    dispatch(clearError());
  };

  const handleEditProductDefaultClick = (id: number) => {
    dispatch(setSelectedProductDefault(id));
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setSearch(e.target.value));
  };

  const handleRefreshClick = () => {
    refetch();
  };

  const handleAddNewClick = () => {
    setShowNewDialog(true);
  };

  const handleAddNewClose = async (item?: spire.InventoryItem) => {
    setShowNewDialog(false);
    if (item) {
      await refetch();
      dispatch(setSelectedProductDefault(item.id));
    }
  };

  return (
    <>
      <Head>
        <title>Settings: Product Defaults</title>
      </Head>
      <header className="sticky top-0 z-20 flex-wrap bg-white shadow">
        <div className="mx-auto flex max-w-7xl items-center justify-between px-8 py-6">
          <h2 className="flex-shrink-0 text-2xl font-bold leading-7 text-gray-900">
            <Icon icon="boxes-stacked" />
            &nbsp; Product Default Settings
          </h2>
          <div className="flex flex-grow px-10">
            <input
              type="search"
              value={search}
              onChange={handleSearchChange}
              className="w-full text-xs"
              placeholder="Search for Products"
            />
            <button
              type="button"
              className="btn-secondary"
              onClick={handleRefreshClick}
              disabled={isFetching}
            >
              <Icon icon="refresh" spin={isFetching} />
            </button>
          </div>
          <Link href={routes.settings.home.to()} className="btn-secondary">
            Close
          </Link>
          <button
            type="button"
            className="btn-new ml-2"
            onClick={handleAddNewClick}
          >
            Add Product Default &nbsp;
            <Icon icon="plus" />
          </button>
        </div>
      </header>
      <main className="flex-grow overflow-auto">
        <div className="mx-auto h-full px-8">
          <Error error={error} clear={handleClearError} />
          {isLoading ? (
            <Loading />
          ) : (
            <div className="mt-8 flex h-full flex-col">
              <div className="-mx-8 -my-2 h-full">
                <div className="inline-block min-w-full px-8 py-2 align-middle">
                  <div className="rounded-lg shadow ring-1 ring-black ring-opacity-5">
                    <table className="min-w-full divide-y divide-gray-300">
                      <thead className="text-sm">
                        <tr className="sticky top-0">
                          <th
                            rowSpan={2}
                            className="bg-gray-100 px-2 py-3.5 text-left text-gray-900"
                          >
                            &nbsp;
                          </th>
                          <th
                            rowSpan={2}
                            className="bg-gray-100 px-2 py-3.5 text-left text-gray-900"
                          >
                            Part Number
                          </th>
                          <th
                            rowSpan={2}
                            className="bg-gray-100 px-2 py-3 text-left text-gray-900"
                          >
                            Description
                          </th>
                          <th className="border-l-2 bg-gray-100 p-2 text-center text-gray-900">
                            Upgrade
                          </th>
                          <th
                            rowSpan={2}
                            className="bg-gray-100 px-2 py-3.5 text-center text-gray-900"
                          >
                            Upgrade Item
                          </th>
                          <th
                            colSpan={3}
                            className="border-l-2 bg-gray-100 p-2 text-center text-gray-900"
                          >
                            Boekestyn Settings
                          </th>
                        </tr>
                        <tr className="sticky top-[37px]">
                          <th className="bg-gray-100 px-2 py-3.5 text-center text-gray-900">
                            Labour Hours
                          </th>
                          <th className="border-l-2 bg-gray-100 px-2 py-3.5 text-center text-gray-900">
                            Plant Mapping
                          </th>
                          <th className="border-l-2 bg-gray-100 px-2 py-3.5 text-center text-gray-900">
                            Customer
                          </th>
                          <th className="border-l-2 bg-gray-100 px-2 py-3.5 text-center text-gray-900">
                            Qty / Finished Item
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white">
                        {productDefaults.map(
                          ({
                            id,
                            partNo,
                            description,
                            plantName,
                            customerAbbreviation,
                            labourHours,
                            quantityPerFinishedItem,
                            isUpgrade,
                          }) => (
                            <tr key={id} className="border-b text-sm">
                              <td className="whitespace-nowrap p-4 text-left">
                                <button
                                  type="button"
                                  onClick={() =>
                                    handleEditProductDefaultClick(id)
                                  }
                                  className="secondary text-blue-600"
                                >
                                  <Icon icon="edit" />
                                </button>
                              </td>
                              <td className="whitespace-nowrap px-2 py-4 text-left">
                                {partNo}
                              </td>
                              <td className="whitespace-nowrap px-2 py-4 text-left">
                                {description}
                              </td>
                              <td className="whitespace-nowrap px-2 py-4 text-center">
                                {labourHours}
                              </td>
                              <td className="whitespace-nowrap px-2 py-4 text-center">
                                {isUpgrade && <Icon icon="check" />}
                              </td>
                              <td className="whitespace-nowrap px-2 py-4 text-center">
                                {plantName}
                              </td>
                              <td className="whitespace-nowrap px-2 py-4 text-center">
                                {customerAbbreviation}
                              </td>
                              <td className="whitespace-nowrap px-2 py-4 text-center">
                                {quantityPerFinishedItem}
                              </td>
                            </tr>
                          )
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
        <ProductDefaultDetail />
        <ProductDefaultNew open={showNewDialog} onClose={handleAddNewClose} />
      </main>
    </>
  );
}
