import { DateTime } from 'luxon';

export function equals(
  a: string | null | undefined,
  b: string | null | undefined
): boolean {
  if (a == null && b == null) return true;
  if (a == null || b == null) return false;
  return a.toLowerCase() === b.toLowerCase();
}

export function contains(
  str: string | null | undefined,
  value: string | null | undefined
) {
  return (str || '').toLowerCase().indexOf((value || '').toLowerCase()) !== -1;
}

export function startsWith(
  str: string | null | undefined,
  value: string | null | undefined
) {
  return (str || '').toLowerCase().indexOf((value || '').toLowerCase()) === 0;
}

export function dateIsInPast(date: string | null) {
  if (!date) {
    return false;
  }

  const dateTime = DateTime.fromISO(date);

  if (!dateTime.isValid) {
    return false;
  }

  const diff = dateTime.diffNow('day'),
    days = diff.get('day');

  return days <= -1;
}

export function isUpc(value: string | null | undefined) {
  if (!value) {
    return false;
  }

  return /[\d\- ]{8,16}/.test(value);
}
