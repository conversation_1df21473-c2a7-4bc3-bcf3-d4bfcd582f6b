import { useAppDispatch } from '@/services/hooks';
import { classNames } from '@/utils/class-names';
import { handleFocus } from '@/utils/focus';
import {
  SplitOrderItem,
  setItemSelected,
  setItemSplitQuantity,
} from './split-order-slice';

interface SplitOrderSelectItemProps {
  item: SplitOrderItem;
}

export function SplitOrderSelectItem({ item }: SplitOrderSelectItemProps) {
  const itemId = item.id,
    dispatch = useAppDispatch();

  const handleSelectedChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setItemSelected({ itemId, value: e.target.checked }));
    if (e.target.checked && !item.splitQuantity) {
      dispatch(setItemSplitQuantity({ itemId, value: 0 }));
    }
  };

  const handleValueChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value) || 0;
    dispatch(setItemSplitQuantity({ itemId, value }));
  };

  return (
    <tr>
      <td className="w-1 px-2">
        <input
          type="checkbox"
          checked={item.selected}
          onChange={handleSelectedChange}
        />
      </td>
      <td className="w-1 whitespace-nowrap p-2">{item.spirePartNumber}</td>
      <td className="p-2">{item.description}</td>
      <td className="w-1 px-4 py-2 text-right">{item.orderQuantity}</td>
      <td className="w-1">
        <input
          type="text"
          value={item.splitQuantity}
          onChange={handleValueChange}
          onFocus={handleFocus}
          className={classNames(
            'w-32 rounded-md border-gray-300 text-right shadow-sm focus:border-blue-500 focus:ring-blue-500',
            item.selected ? '' : 'invisible'
          )}
        />
      </td>
    </tr>
  );
}
