import { useMemo } from 'react';
import * as boeks from 'api/models/boekestyns';
import { Icon } from '@/components/icon';
import { classNames } from '@/utils/class-names';
import { formatNumber } from '@/utils/format';
import { Timing } from './timing';

export function HarvestingItem({
  workOrder,
}: {
  workOrder: boeks.HarvestingWorkOrderItem;
}) {
  const labour = useMemo(() => workOrder.labour || [], [workOrder]),
    inProcess = useMemo(() => labour.find((l) => !l.endTime) || null, [labour]),
    isFinalLabour = useMemo(() => labour.some((l) => l.finalLabour), [labour]);
  return (
    <tr
      className={classNames(
        'border-b border-gray-200',
        isFinalLabour ? 'bg-green-600' : ''
      )}
    >
      <td
        className={classNames(
          'p-2 text-left align-top text-gray-900',
          inProcess ? 'border-b-4 border-t-4 border-blue-600' : ''
        )}
      >
        <Timing workOrder={workOrder} />
      </td>
      <td
        className={classNames(
          'p-2 text-left align-top text-gray-900',
          inProcess ? 'border-b-4 border-t-4 border-blue-600' : ''
        )}
      >
        <span className="font-semibold">{workOrder.orderNumber}</span>
        {!!workOrder.orderComments && (
          <div className="ml-2 italic">{workOrder.orderComments}</div>
        )}
        {!!workOrder.harvestingComments && (
          <div className="ml-2 italic">{workOrder.harvestingComments}</div>
        )}
      </td>
      <td
        className={classNames(
          'p-2 text-left align-top text-gray-900',
          inProcess ? 'border-b-4 border-t-4 border-blue-600' : ''
        )}
      >
        <span className="font-semibold">
          {workOrder.plantSize}&nbsp;{workOrder.plantCrop}&nbsp;
          {workOrder.customer}
        </span>
        {!isFinalLabour && !!workOrder.varieties.length && (
          <div className="ml-2 italic">
            {workOrder.varieties.map((variety: any) => (
              <div key={variety.name}>
                {variety.name}: {formatNumber(variety.pots)}
              </div>
            ))}
          </div>
        )}
      </td>
    </tr>
  );
}
