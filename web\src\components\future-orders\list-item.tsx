import { Fragment, useState, useEffect } from 'react';
import Link from 'next/link';
import * as HeadlessUI from '@headlessui/react';
import { useLazyChildrenQuery } from 'api/future-orders-service';
import * as models from 'api/models/future-orders';
import { useAppSelector } from '@/services/hooks';
import { routes } from '@/services/routes';
import { Icon } from '@/components/icon';
import { formatNumber, formatDate, formatCurrency } from '@/utils/format';
import { classNames } from '@/utils/class-names';
import { selectFutureOrders } from './future-order-list-slice';
import { ListItemPrebook } from './list-item-prebook';

export interface ListItemProps {
  futureOrder: models.FutureOrderListItem;
  refresh: () => void;
}

export function ListItem({ futureOrder, refresh }: ListItemProps) {
  const [queryChildren, { data: children, isFetching: isFetchingChildren }] =
      useLazyChildrenQuery(),
    futureOrders = useAppSelector(selectFutureOrders),
    [showPrebooks, setShowPrebooks] = useState(false),
    [statusTooltipOpen, setStatusTooltipOpen] = useState(false),
    [parentTooltipOpen, setParentTooltipOpen] = useState(false),
    [childTooltipOpen, setChildTooltipOpen] = useState(false),
    hasSpireOrders = futureOrders.some((o) => o.spireSalesOrderNumber);

  useEffect(() => {
    if (parentTooltipOpen) {
      queryChildren(futureOrder.id);
    }
  }, [futureOrder.id, parentTooltipOpen, queryChildren]);

  const handleStatusTooltipMouseEnter = () => {
    setStatusTooltipOpen(true);
  };

  const handleStatusTooltipMouseLeave = () => {
    setStatusTooltipOpen(false);
  };

  const handleParentTooltipMouseEnter = () => {
    setParentTooltipOpen(true);
  };

  const handleParentTooltipMouseLeave = () => {
    setParentTooltipOpen(false);
  };

  const handleChildTooltipMouseEnter = () => {
    setChildTooltipOpen(true);
  };

  const handleChildTooltipMouseLeave = () => {
    setChildTooltipOpen(false);
  };

  const handleShowPrebooksClick = () => {
    setShowPrebooks(!showPrebooks);
  };

  return (
    <>
      <tr className={classNames(!showPrebooks && 'border-b-2 border-gray-100')}>
        <td
          rowSpan={showPrebooks ? futureOrder.prebooks.length + 2 : 1}
          className={classNames(
            'whitespace-nowrap p-4',
            showPrebooks && 'align-bottom'
          )}
        >
          <button
            type="button"
            className="btn-secondary px-2 py-1 focus:ring-0"
            onClick={handleShowPrebooksClick}
          >
            <Icon icon={showPrebooks ? 'chevron-up' : 'chevron-down'} />
          </button>
        </td>
        <td
          className="relative whitespace-nowrap p-4 text-left align-top"
          rowSpan={showPrebooks ? futureOrder.prebooks.length + 2 : 1}
        >
          <Link href={routes.futureOrders.detail.to(futureOrder.id)}>
            {formatNumber(futureOrder.id, '00000')}
          </Link>
          {!!futureOrder.isParentOrder && (
            <HeadlessUI.Popover
              className="relative inline-block cursor-pointer"
              onMouseEnter={handleParentTooltipMouseEnter}
              onMouseLeave={handleParentTooltipMouseLeave}
            >
              <>
                <HeadlessUI.Popover.Button
                  as="div"
                  className="ml-2 p-1 text-gray-400"
                >
                  <Icon icon={'folder-tree'} />
                </HeadlessUI.Popover.Button>
                <HeadlessUI.Transition
                  as={Fragment}
                  show={parentTooltipOpen}
                  enter="transition ease-out duration-200"
                  enterFrom="opacity-0 translate-y-1"
                  enterTo="opacity-100 translate-y-0"
                  leave="transition ease-in duration-150"
                  leaveFrom="opacity-100 translate-y-0"
                  leaveTo="opacity-0 translate-y-1"
                >
                  <HeadlessUI.Popover.Panel
                    static
                    className="absolute left-[25px] z-10 -translate-y-1/2 transform bg-white"
                  >
                    <div className="rounded-lg border bg-yellow-50 p-4 shadow-lg">
                      <p className="my-2 text-xs text-gray-500">
                        Order has been split.
                      </p>
                      {isFetchingChildren && <Icon icon="spinner" spin />}
                      {!isFetchingChildren && !!children?.length && (
                        <table className="text-xs">
                          <thead>
                            <tr>
                              <th></th>
                              <th>Req&apos;d Date</th>
                              <th>Customer</th>
                            </tr>
                          </thead>
                          <tbody className="divide-y divide-gray-200">
                            {children.map((child) => (
                              <tr key={child.id}>
                                <td className="w-1 p-1">
                                  <Link
                                    href={routes.futureOrders.detail.to(
                                      child.id
                                    )}
                                  >
                                    {formatNumber(child.id, '00000')}
                                  </Link>
                                </td>
                                <td className="w-1 p-1">
                                  {!!child.requiredDate &&
                                    formatDate(child.requiredDate)}
                                </td>
                                <td className="p-1">
                                  {!!child.customerName && (
                                    <span className="semibold ml-2">
                                      {child.customerName}
                                    </span>
                                  )}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      )}
                    </div>
                  </HeadlessUI.Popover.Panel>
                </HeadlessUI.Transition>
              </>
            </HeadlessUI.Popover>
          )}
          {!!futureOrder.parentOrderId && (
            <HeadlessUI.Popover
              className="relative inline-block cursor-pointer"
              onMouseEnter={handleChildTooltipMouseEnter}
              onMouseLeave={handleChildTooltipMouseLeave}
            >
              <>
                <HeadlessUI.Popover.Button
                  as="div"
                  className="ml-2 p-1 text-gray-400"
                >
                  <Icon icon={'folders'} />
                </HeadlessUI.Popover.Button>
                <HeadlessUI.Transition
                  as={Fragment}
                  show={childTooltipOpen}
                  enter="transition ease-out duration-200"
                  enterFrom="opacity-0 translate-y-1"
                  enterTo="opacity-100 translate-y-0"
                  leave="transition ease-in duration-150"
                  leaveFrom="opacity-100 translate-y-0"
                  leaveTo="opacity-0 translate-y-1"
                >
                  <HeadlessUI.Popover.Panel
                    static
                    className="absolute left-[25px] z-10 -translate-y-1/2 transform bg-white"
                  >
                    <div className="rounded-lg border bg-yellow-50 p-4 shadow-lg">
                      <p className="my-2 text-xs text-gray-500">
                        This order was split from #
                        <Link
                          href={routes.futureOrders.detail.to(
                            futureOrder.parentOrderId
                          )}
                        >
                          {formatNumber(futureOrder.parentOrderId, '00000')}
                        </Link>
                        .
                      </p>
                    </div>
                  </HeadlessUI.Popover.Panel>
                </HeadlessUI.Transition>
              </>
            </HeadlessUI.Popover>
          )}
        </td>
        <td className="whitespace-nowrap px-3 py-4 text-center text-sm text-gray-700">
          <HeadlessUI.Popover
            className="relative inline-block cursor-pointer"
            onMouseEnter={handleStatusTooltipMouseEnter}
            onMouseLeave={handleStatusTooltipMouseLeave}
          >
            <>
              <HeadlessUI.Popover.Button
                as="div"
                className="font-lg  px-2 py-1"
              >
                <Icon
                  icon={
                    futureOrder.prebooks.some((p) => !p.sent)
                      ? 'triangle-exclamation'
                      : futureOrder.prebooks.some((p) => !p.confirmed)
                      ? 'question-circle'
                      : 'check-circle'
                  }
                  className={
                    futureOrder.prebooks.some((p) => !p.sent)
                      ? 'text-red-600'
                      : futureOrder.prebooks.some((p) => !p.confirmed)
                      ? 'text-yellow-600'
                      : 'text-green-600'
                  }
                />
              </HeadlessUI.Popover.Button>
              <HeadlessUI.Transition
                as={Fragment}
                show={statusTooltipOpen}
                enter="transition ease-out duration-200"
                enterFrom="opacity-0 translate-y-1"
                enterTo="opacity-100 translate-y-0"
                leave="transition ease-in duration-150"
                leaveFrom="opacity-100 translate-y-0"
                leaveTo="opacity-0 translate-y-1"
              >
                <HeadlessUI.Popover.Panel
                  static
                  className="absolute left-[25px] z-10 -translate-y-1/2 transform bg-white"
                >
                  <div className="rounded-lg border p-4 shadow-lg">
                    <p className="my-2 text-xs text-gray-500">
                      Created by{' '}
                      <span className="font-medium">
                        {futureOrder.createdBy}
                      </span>{' '}
                      on{' '}
                      <span className="font-medium">
                        {formatDate(futureOrder.created)}
                      </span>{' '}
                      @{' '}
                      <span className="font-medium">
                        {formatDate(futureOrder.created, 'h:mm a')}
                      </span>
                    </p>
                    {futureOrder.created !== futureOrder.modified && (
                      <p className="my-2 text-xs text-gray-500">
                        Updated by{' '}
                        <span className="font-medium">
                          {futureOrder.modifiedBy}
                        </span>{' '}
                        on{' '}
                        <span className="font-medium">
                          {formatDate(futureOrder.modified)}
                        </span>{' '}
                        @{' '}
                        <span className="font-medium">
                          {formatDate(futureOrder.modified, 'h:mm a')}
                        </span>
                      </p>
                    )}
                    {futureOrder.prebooks.some((p) => !p.sent) && (
                      <p className="my-2 text-xs font-bold italic text-red-500">
                        One or more prebooks were not sent to Grower
                      </p>
                    )}
                    {!futureOrder.prebooks.some((p) => !p.sent) &&
                      futureOrder.prebooks.some((p) => !p.confirmed) && (
                        <p className="my-2 text-xs font-bold italic text-yellow-500">
                          <div className="mr-4 inline-block">
                            One or more prebooks were not confirmed by the
                            Grower
                          </div>
                        </p>
                      )}
                    {!!futureOrder.sentToSpire && (
                      <p className="my-2 text-xs text-gray-500">
                        Sent to Spire by{' '}
                        <span className="font-medium">
                          {futureOrder.sentToSpireBy}
                        </span>{' '}
                        on{' '}
                        <span className="font-medium">
                          {formatDate(futureOrder.sentToSpire)}
                        </span>{' '}
                        @{' '}
                        <span className="font-medium">
                          {formatDate(futureOrder.sentToSpire, 'h:mm a')}
                        </span>
                      </p>
                    )}
                  </div>
                </HeadlessUI.Popover.Panel>
              </HeadlessUI.Transition>
            </>
          </HeadlessUI.Popover>
        </td>
        <td className="whitespace-nowrap px-3 py-4 text-left text-sm text-gray-700">
          {!!futureOrder.requiredDate &&
            formatDate(futureOrder.requiredDate, 'MMM d')}
        </td>
        <td className="whitespace-nowrap px-3 py-4 text-left text-sm text-gray-700">
          {futureOrder.season}
        </td>
        <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-700">
          {futureOrder.customer}
          {!!futureOrder.shipTo && (
            <>
              <br />
              <span className="italic text-gray-400">{futureOrder.shipTo}</span>
            </>
          )}
        </td>
        <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-700">
          {futureOrder.salesperson}
        </td>
        <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-700">
          {futureOrder.customerPurchaseOrderNumber}
        </td>
        <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-700">
          {futureOrder.truck}
        </td>
        <td className="whitespace-nowrap px-3 py-4 text-center text-sm text-gray-700">
          {futureOrder.dollarValue
            ? formatCurrency(futureOrder.dollarValue)
            : '-'}
        </td>
        <td className="whitespace-nowrap px-3 py-4 text-center text-sm text-gray-700">
          {!showPrebooks && formatNumber(futureOrder.caseCount)}
        </td>
        {hasSpireOrders && (
          <td className="whitespace-nowrap px-3 py-4 text-center text-sm text-gray-700">
            {futureOrder.spireSalesOrderNumber}
          </td>
        )}
      </tr>
      {showPrebooks && (
        <>
          {futureOrder.prebooks.map((prebook) => (
            <ListItemPrebook
              key={prebook.id}
              prebook={prebook}
              refresh={refresh}
            />
          ))}
          <tr className="border-b-2 border-gray-300">
            <td
              colSpan={2}
              className="whitespace-nowrap bg-gray-200 p-2 text-right text-sm italic text-gray-700"
            >
              &nbsp;
            </td>
            <td
              colSpan={5}
              className="whitespace-nowrap bg-gray-200 p-2 text-right text-sm italic text-gray-700"
            >
              Future Order Total:
            </td>
            <td className="whitespace-nowrap bg-gray-200 p-2 text-center text-sm italic text-gray-700">
              {formatNumber(futureOrder.caseCount)}
            </td>
            {hasSpireOrders && (
              <td className="whitespace-nowrap bg-gray-200 p-2 text-center text-sm italic text-gray-700">
                &nbsp;
              </td>
            )}
          </tr>
        </>
      )}
    </>
  );
}
