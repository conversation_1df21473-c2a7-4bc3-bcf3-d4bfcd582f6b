import { createApi } from '@reduxjs/toolkit/query/react';
import { ApiBase, axiosBaseQuery } from './api-base';
import * as models from './models/spire';
import * as settings from './models/settings';

class SpireService extends ApiBase {
  async customerDetail(id: number): Promise<SpireCustomerDetailResponse> {
    try {
      return await this.get(`spire/customers/${id}`);
    } catch (e) {
      throw e;
    }
  }

  async shipToDetail(id: number): Promise<SpireShipToDetailResponse> {
    try {
      return await this.get(`/spire/ship-tos/${id}`);
    } catch (e) {
      throw e;
    }
  }
}

export const spireService = new SpireService();

export const spireApi = createApi({
  reducerPath: 'spire-api',
  baseQuery: axiosBaseQuery('spire/'),
  refetchOnMountOrArgChange: true,
  endpoints: (builder) => ({
    inventoryItems: builder.query<SpireInventoryItemsResponse, string | void>({
      query: (search) => ({
        url: 'inventory-items' + (search ? `?search=${search}` : ''),
      }),
    }),
    vendors: builder.query<models.Vendor[], void>({
      query: () => ({ url: 'vendors' }),
      transformResponse: ({ vendors }: SpireVendorResponse) => vendors,
    }),
    customers: builder.query<models.Customer[], void>({
      query: () => ({ url: 'customers' }),
      transformResponse: ({ customers }: SpireCustomersResponse) => customers,
    }),
    shipTos: builder.query<models.CustomerAddress[], string>({
      query: (search) => ({ url: `ship-tos?search=${search}` }),
      transformResponse: ({ shipTos }: SpireShipTosResponse) => shipTos,
    }),
    salespeople: builder.query<models.Salesperson[], void>({
      query: () => ({ url: 'salespeople' }),
      transformResponse: ({ salespeople }: SpireSalespeopleResponse) =>
        salespeople,
    }),
    customerDetail: builder.query<models.CustomerDetail, number>({
      query: (id) => ({ url: `customers/${id}` }),
      transformResponse: ({ customer }: SpireCustomerDetailResponse) =>
        customer,
    }),
    shippingMethods: builder.query<models.ShippingMethod[], void>({
      query: () => ({ url: 'shipping-methods' }),
      transformResponse: ({ shippingMethods }: SpireShippingMethodsResponse) =>
        shippingMethods,
    }),
    inventoryComments: builder.query<models.InventoryComment[], void>({
      query: () => ({ url: 'inventory-comments' }),
      transformResponse: ({ comments }: SpireInventoryCommentsResponse) =>
        comments,
    }),
    emailTemplates: builder.query<models.EmailTemplate[], void>({
      query: () => ({ url: 'email-templates' }),
      transformResponse: ({ templates }: SpireEmailTemplatesResponse) =>
        templates,
    }),
  }),
});

interface SpireInventoryItemsResponse {
  inventoryItems: models.InventoryItem[];
  productDefaults: settings.ProductDefault[];
}

interface SpireVendorResponse {
  vendors: models.Vendor[];
}

interface SpireCustomersResponse {
  customers: models.Customer[];
}

interface SpireShipTosResponse {
  shipTos: models.CustomerAddress[];
}

interface SpireShipToDetailResponse {
  customer: models.CustomerDetail;
  shipTo: models.CustomerShipTo;
}

export interface SpireCustomerDetailResponse {
  customer: models.CustomerDetail;
  customerItemCodes: settings.CustomerItemCodeDefault[];
  potCovers: string[];
}

interface SpireSalespeopleResponse {
  salespeople: models.Salesperson[];
}

interface SpireShippingMethodsResponse {
  shippingMethods: models.ShippingMethod[];
}

interface SpireInventoryCommentsResponse {
  comments: models.InventoryComment[];
}

interface SpireEmailTemplatesResponse {
  templates: models.EmailTemplate[];
}

export const {
  useInventoryItemsQuery,
  useLazyInventoryItemsQuery,
  useVendorsQuery,
  useCustomersQuery,
  useShipTosQuery,
  useSalespeopleQuery,
  useShippingMethodsQuery,
  useInventoryCommentsQuery,
  useLazyCustomerDetailQuery,
  useEmailTemplatesQuery,
} = spireApi;
