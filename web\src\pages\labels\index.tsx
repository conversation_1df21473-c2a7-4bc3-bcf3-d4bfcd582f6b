import Head from 'next/head';
import Link from 'next/link';
import { routes } from '@/services/routes';
import { Icon } from '@/components/icon';

interface LabelType {
  title: string;
  href: string;
}

const labelTypes: LabelType[] = [
  { title: "Topco Albrecht's", href: routes.labels.albrecht.to() },
  { title: "Topco Coborn's", href: routes.labels.coborns.to() },
  { title: 'Heinens', href: routes.labels.heinens.to() },
];

function LabelButton({ title, href }: LabelType) {
  return (
    <Link
      className="m-4 flex h-48 w-48 items-center justify-center rounded border text-4xl text-blue-600 shadow hover:border-blue-900 hover:bg-blue-50 hover:text-blue-900 hover:no-underline"
      href={href}
    >
      <h1 className="-mt-[25%] flex text-center">{title}</h1>
    </Link>
  );
}

export default function Labels() {
  return (
    <>
      <Head>
        <title>Labels</title>
      </Head>
      <header className="sticky top-0 z-20 flex-wrap bg-white shadow">
        <div className="mx-auto flex max-w-7xl items-center justify-between px-8 py-6">
          <h2 className="flex-shrink-0 truncate text-3xl font-bold leading-7 tracking-tight text-gray-900">
            <Icon icon="barcode" />
            &nbsp; Labels
          </h2>
        </div>
      </header>
      <main>
        <div className="mx-auto max-w-7xl px-8 py-6">
          <form className="flex justify-center">
            {labelTypes.map(({ title, href }) => (
              <LabelButton key={title} title={title} href={href} />
            ))}
          </form>
        </div>
      </main>
    </>
  );
}
