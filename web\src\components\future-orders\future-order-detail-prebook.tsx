import { useState } from 'react';
import Link from 'next/link';
import { apiUrl } from 'api/api-base';
import * as prebooks from 'api/models/prebooks';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { routes } from '@/services/routes';
import {
  selectEmails,
  setGrowerConfirmed,
  updatePrebook,
  selectDirtyPrebooks,
} from './future-order-detail-slice';
import { FutureOrderDetailPrebookItem } from './future-order-detail-prebook-item';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import { EmailReview } from '../prebooks/email-review';
import { Email } from '../prebooks/email';
import { classNames } from '@/utils/class-names';
import { formatDate, formatNumber } from '@/utils/format';
import { FutureOrderDetailPrebookItemDeleted } from './future-order-detail-prebook-item-deleted';
import { access } from 'fs';

interface FutureOrderDetailPrebookProps {
  prebook: prebooks.PrebookDetail;
}

export function FutureOrderDetailPrebook({
  prebook,
}: FutureOrderDetailPrebookProps) {
  const dispatch = useAppDispatch(),
    [showReviewEmails, setShowReviewEmails] = useState(false),
    [showEmailDialog, setShowEmailDialog] = useState(false),
    emails = useAppSelector(selectEmails).filter(
      (e) => e.prebookId === prebook.id
    ),
    { dirty } = useAppSelector(selectDirtyPrebooks),
    isDirty = dirty.some((p) => p.id === prebook.id),
    cellClassName = 'py-2 px-1 text-left text-sm font-semibold text-gray-900',
    isNew = prebook.id < 0,
    deletedItems = emails
      // go from neweds to oldest
      .sort((a, b) => b.id - a.id)
      .reduce((acc, email) => {
        email.items
          .filter((i) => i.prebookItemId == null)
          .forEach((i) => {
            if (
              !acc.some(
                (i2) =>
                  i2.spirePartNumber === i.spirePartNumber &&
                  i2.potCover === i.potCover &&
                  i2.upc === i.upc &&
                  i2.dateCode === i.dateCode &&
                  i2.weightsAndMeasures === i.weightsAndMeasures &&
                  i2.retail === i.retail &&
                  i2.comments === i.comments
              )
            ) {
              acc.push(i);
            }
          });
        return acc;
      }, [] as prebooks.PrebookEmailItem[]);

  const handleEmailClick = async () => {
    const saved = await save();
    if (saved) {
      setShowEmailDialog(true);
    }
  };

  const handleDownloadClick = async () => {
    const saved = await save();
    if (saved) {
      window.open(apiUrl(`reports/prebooks/${prebook.id}?download`), '_blank');
    }
  };

  const handleEmailCancel = () => {
    setShowEmailDialog(false);
  };

  const handleEmailClose = () => {
    setShowEmailDialog(false);
  };

  const handleReviewEmailsClick = () => {
    setShowReviewEmails(true);
  };

  const handleReviewEmailsClose = () => {
    setShowReviewEmails(false);
  };

  const handleSetGrowerConfirmedClick = () => {
    dispatch(setGrowerConfirmed(prebook.id));
  };

  const save = async () => {
    var response: any = await dispatch(updatePrebook(prebook));
    if (!response?.error) {
      return true;
    }
  };

  return (
    <div>
      <div className="grid grid-cols-2">
        <div className="mb-2">
          <h2 className="inline-block text-xl font-medium text-blue-600">
            {isNew && 'New '}Prebook{' '}
            {!isNew && formatNumber(prebook.id, '00000')}
            {!!prebook.deleted && (
              <span className="ml-2 italic text-yellow-600">**Deleted**</span>
            )}
          </h2>
          &nbsp;
          {!isNew && (
            <>
              {!prebook.deleted && (
                <Link
                  href={routes.prebooks.detail.to(prebook.id)}
                  className="btn-secondary ml-2 px-2 py-1 text-xs"
                  tabIndex={-1}
                >
                  Edit
                </Link>
              )}
              {!!emails.length && (
                <>
                  <button
                    type="button"
                    className="btn-secondary ml-2 px-2 py-1 text-xs"
                    tabIndex={-1}
                    onClick={handleEmailClick}
                  >
                    Send Revision to Grower &nbsp;
                    <Icon icon="paper-plane" />
                  </button>
                  <button
                    type="button"
                    className="btn-secondary ml-2 px-2 py-1 text-xs"
                    tabIndex={-1}
                    onClick={handleDownloadClick}
                  >
                    <Icon icon="download"></Icon>
                    &nbsp; Download PDF
                  </button>
                </>
              )}
            </>
          )}
        </div>
        {!isNew && (
          <div className="text-xs">
            <div className="truncate text-right">
              Created by&nbsp;
              <span className="italic">{prebook.createdBy}</span>
              &nbsp;on&nbsp;
              <span className="italic">
                {formatDate(prebook.created, 'MMM d, yyyy')}
              </span>
              &nbsp;@&nbsp;
              <span className="italic">
                {formatDate(prebook.created, 'h:mm a')}
              </span>
            </div>
            {prebook.created !== prebook.modified && (
              <div className="truncate text-right">
                Updated by&nbsp;
                <span className="italic">{prebook.modifiedBy}</span>
                &nbsp;on&nbsp;
                <span className="italic">
                  {formatDate(prebook.modified, 'MMM d, yyyy')}
                </span>
                &nbsp;@&nbsp;
                <span className="italic">
                  {formatDate(prebook.modified, 'h:mm a')}
                </span>
              </div>
            )}
            {!!emails.length && (
              <div className="text-right">
                {emails.map((email) => (
                  <p key={email.id} className="text-xs text-green-700 ">
                    Sent to supplier by&nbsp;
                    <span className="italic">{email.createdBy}</span>
                    &nbsp;on&nbsp;
                    <span className="italic">
                      {formatDate(email.created, 'MMM d, yyyy')}
                    </span>
                    &nbsp;@&nbsp;
                    <span className="italic">
                      {formatDate(email.created, 'h:mm a')}
                    </span>
                  </p>
                ))}
              </div>
            )}
            {!!prebook.confirmed && (
              <p className="truncate text-right text-xs text-green-700">
                Confirmed by Grower by&nbsp;
                <span className="italic">{prebook.confirmedBy}</span>
                &nbsp;on&nbsp;
                <span className="italic">
                  {formatDate(prebook.confirmed, 'MMM d, yyyy')}
                </span>
                &nbsp;@&nbsp;
                <span className="italic">
                  {formatDate(prebook.confirmed, 'h:mm a')}
                </span>
              </p>
            )}
            {!!emails.length && (
              <div className="text-right">
                <button
                  type="button"
                  onClick={handleReviewEmailsClick}
                  className="text-xs text-blue-500 underline hover:no-underline"
                >
                  <Icon icon="glasses"></Icon>
                  &nbsp; Review emails
                </button>
              </div>
            )}
          </div>
        )}
      </div>
      {!isNew && (
        <div>
          {!emails.length && !prebook.deleted && (
            <Error
              containerClasses="col-span-1 lg:col-span-3 xl:col-span-4"
              type="warning"
              error={'Prebook has not been sent to the Grower'}
            >
              <div className="flex flex-grow">
                <button
                  type="button"
                  className="btn-secondary ml-auto px-2 py-1 text-xs"
                  onClick={handleEmailClick}
                >
                  Save & Send &nbsp;
                  <Icon icon="paper-plane" />
                </button>
              </div>
            </Error>
          )}
          {!!emails.length &&
            !prebook.confirmed &&
            !isDirty &&
            !prebook.deleted && (
              <>
                <Error
                  containerClasses="text-xs"
                  type="information"
                  error={'Prebook has not been confirmed by the Grower'}
                >
                  <div className="flex flex-grow">
                    <button
                      type="button"
                      className="btn-secondary ml-auto px-2 py-1 text-xs"
                      onClick={handleSetGrowerConfirmedClick}
                    >
                      Click to Confirm &nbsp;
                      <Icon icon="check-circle" />
                    </button>
                  </div>
                </Error>
              </>
            )}
        </div>
      )}

      {isDirty && !prebook.deleted && (
        <Error
          type="information"
          error={'Prebook has changes that have not been sent to the Grower.'}
        />
      )}

      {!!prebook.deleted && (
        <Error
          type="warning"
          error={`This Prebook was deleted by ${
            prebook.deletedBy
          } on ${formatDate(prebook.deleted)} @ ${formatDate(
            prebook.deleted,
            'h:mm a'
          )}.`}
        />
      )}

      <div className="mt-4">
        <table className="min-w-full divide-y divide-gray-300">
          <thead className="bg-gray-50">
            <tr>
              <th className={cellClassName}>Part No</th>
              <th className={cellClassName}>Description</th>
              <th className={classNames(cellClassName, 'text-center')}>
                Upgrade Sheet
              </th>
              <th className={classNames(cellClassName, 'text-center')}>Qty</th>
              <th className={classNames(cellClassName, 'text-center')}>
                Cover
              </th>
              <th className={classNames(cellClassName, 'text-center')}>UPC</th>
              <th className={classNames(cellClassName, 'text-center')}>
                Date Code
              </th>
              <th className={classNames(cellClassName, 'text-center')}>
                W & M
              </th>
              <th className={classNames(cellClassName, 'text-center')}>
                Retail
              </th>
            </tr>
          </thead>
          <tbody className="bg-white">
            {prebook.items.map((item) => (
              <FutureOrderDetailPrebookItem key={item.id} item={item} />
            ))}
            {deletedItems.map((item) => (
              <FutureOrderDetailPrebookItemDeleted
                key={item.id}
                item={item}
                prebookDeleted={!!prebook.deleted}
              />
            ))}
          </tbody>
        </table>
      </div>
      <Email
        prebook={prebook}
        open={showEmailDialog}
        cancel={handleEmailCancel}
        close={handleEmailClose}
      />
      <EmailReview
        emails={emails}
        open={showReviewEmails}
        close={handleReviewEmailsClose}
      />
    </div>
  );
}
