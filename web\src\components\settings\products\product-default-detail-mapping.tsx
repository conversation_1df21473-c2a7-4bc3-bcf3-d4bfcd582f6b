import * as boeks from 'api/models/boekestyns';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { Icon } from '@/components/icon';
import { handleFocus } from '@/utils/focus';
import { selectMappings, setMappings } from './product-default-detail-slice';
import {
  selectPlants,
  selectCustomers,
} from './product-default-settings-slice';

interface ProductDefaultDetailMappingProps {
  mapping: boeks.BoekestynPrebookItemProduct;
}

export function ProductDefaultDetailMapping({
  mapping,
}: ProductDefaultDetailMappingProps) {
  const dispatch = useAppDispatch(),
    plants = useAppSelector(selectPlants),
    mappings = useAppSelector(selectMappings),
    customers = useAppSelector(selectCustomers);

  const removeMapping = (id: number) => {
    dispatch(setMappings(mappings.filter((m) => m.id !== id)));
  };

  const handleMappingPlantIdChange = (id: number, plantId: string) => {
    const updated = mappings.map((m) => ({ ...m })),
      mapping = updated.find((m) => m.id === id);

    if (mapping) {
      mapping.boekestynPlantId = plantId || '';
    }

    dispatch(setMappings(updated));
  };

  const handleMappingCustomerChange = (
    id: number,
    customerAbbreviation: string
  ) => {
    const updated = mappings.map((m) => ({ ...m })),
      mapping = updated.find((m) => m.id === id);

    if (mapping) {
      mapping.boekestynCustomerAbbreviation = customerAbbreviation || '';
    }

    dispatch(setMappings(updated));
  };

  const handleMappingQuantityChange = (id: number, quantity: number) => {
    const updated = mappings.map((m) => ({ ...m })),
      mapping = updated.find((m) => m.id === id);

    if (mapping) {
      mapping.quantityPerFinishedItem = quantity;
    }

    dispatch(setMappings(updated));
  };

  return (
    <tr className="">
      <td className="p-2">
        <select
          value={mapping.boekestynPlantId || ''}
          onChange={(e) =>
            handleMappingPlantIdChange(mapping.id, e.target.value)
          }
          className="block w-full min-w-0 flex-1 rounded-md border-gray-300 text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
        >
          <option value="">No Boekestyn Plant</option>
          {plants.map((plant) => (
            <option key={plant._id} value={plant._id}>
              {plant.name}
            </option>
          ))}
        </select>
      </td>
      <td className="p-2">
        <select
          value={mapping.boekestynCustomerAbbreviation || ''}
          onChange={(e) =>
            handleMappingCustomerChange(mapping.id, e.target.value)
          }
          className="block w-auto min-w-0 flex-1 rounded-md border-gray-300 text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
        >
          <option value="">No Customer</option>
          {customers.map((customer) => (
            <option key={customer.abbreviation} value={customer.abbreviation}>
              {customer.name} ({customer.abbreviation})
            </option>
          ))}
        </select>
      </td>
      <td className="p-2 text-center">
        <input
          type="number"
          value={mapping.quantityPerFinishedItem}
          onChange={(e) =>
            handleMappingQuantityChange(mapping.id, e.target.valueAsNumber || 0)
          }
          onFocus={handleFocus}
          className="mx-auto block w-16 min-w-0 flex-1 rounded-md border-gray-300 text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
        />
      </td>
      <td className="p-2">
        <button
          type="button"
          onClick={() => removeMapping(mapping.id)}
          className="btn-delete px-2 py-1"
        >
          <Icon icon="trash" />
        </button>
      </td>
    </tr>
  );
}
