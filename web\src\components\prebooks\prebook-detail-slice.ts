import {
  AsyncThunk,
  createAction,
  createAsyncThunk,
  createSelector,
  createSlice,
  PayloadAction,
} from '@reduxjs/toolkit';
import { DateTime } from 'luxon';
import { createPrebookEmail } from './prebook-email-slice';
import {
  BlanketItemsResponse,
  ConfirmPrebookResponse,
  CreateEmailResponse,
  PrebookCreateResponse,
  PrebookDetailResponse,
  prebooksApi,
  seasonsApi,
} from 'api/prebooks-service';
import {
  spireApi,
  SpireCustomerDetailResponse,
  spireService,
} from 'api/spire-service';
import * as boeks from 'api/models/boekestyns';
import * as settings from 'api/models/settings';
import * as spire from 'api/models/spire';
import * as models from 'api/models/prebooks';
import { RootState } from '@/services/store';
import { potCovers as defaultPotCovers } from '@/components/pot-covers';
import { createProblemDetails, ProblemDetails } from '@/utils/problem-details';
import { sortBy } from '@/utils/sort';
import { getBoekestynProducts } from '../boekestyns/sales/boekestyn-sales-functions';

const sortByCreated = sortBy('created');

interface PrebookDetailState {
  id: number | null;
  name: string | null;
  vendor: spire.Vendor | null;
  seasonName: string | null;
  boxCode: string | null;
  salesperson: spire.Salesperson | null;
  shipTo: spire.CustomerShipTo | null;
  customer: spire.Customer | null;
  requiredDate: string | null;
  isBlanket: boolean;
  blanketStartDate: string | null;
  blanketIsClosed: boolean;
  comments: string | null;
  growerItemNotes: string | null;
  items: (models.NewPrebookDetailItem | models.PrebookDetailItem)[];
  prebook: models.PrebookDetail | null;
  emails: models.PrebookEmail[];
  blanketItems: models.PrebookBlanketItem[];
  productDefaults: settings.ProductDefault[];
  customerDetail: spire.CustomerDetail | null;
  salespeople: spire.Salesperson[];
  customerPotCovers: string[];
  seasons: models.Season[];
  isLoading: boolean;
  error: ProblemDetails | null;
}

const initialState: PrebookDetailState = {
  id: null,
  name: null,
  vendor: null,
  seasonName: null,
  boxCode: null,
  shipTo: null,
  customer: null,
  salesperson: null,
  requiredDate: null,
  isBlanket: false,
  blanketStartDate: null,
  blanketIsClosed: false,
  comments: null,
  growerItemNotes: null,
  items: [],
  customerDetail: null,
  salespeople: [],
  customerPotCovers: [],
  seasons: [],
  prebook: null,
  emails: [],
  blanketItems: [],
  productDefaults: [],
  isLoading: false,
  error: null,
};

export const getPrebookDetail: AsyncThunk<
  PrebookDetailResponse,
  number,
  { state: RootState }
> = createAsyncThunk(
  'prebook-detail-getPrebookDetail',
  async (id, { rejectWithValue }) => {
    try {
      return await prebooksApi.detail(id);
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const getCustomerDetail: AsyncThunk<
  SpireCustomerDetailResponse,
  number,
  { state: RootState }
> = createAsyncThunk(
  'prebook-detail-getCustomerDetail',
  async (id, { rejectWithValue }) => {
    try {
      return await spireService.customerDetail(id);
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const getBlanketItems: AsyncThunk<
  BlanketItemsResponse,
  void,
  { state: RootState }
> = createAsyncThunk(
  'prebook-detail-getBlanketItems',
  async (_, { rejectWithValue }) => {
    try {
      return await prebooksApi.blanketItems();
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const createPrebook: AsyncThunk<
  PrebookCreateResponse,
  void,
  { state: RootState }
> = createAsyncThunk(
  'prebook-detail/createPrebook',
  async (_, { rejectWithValue, getState }) => {
    const rootState = getState() as RootState,
      {
        name,
        requiredDate,
        isBlanket,
        blanketStartDate,
        blanketIsClosed,
        seasonName,
        boxCode,
        vendor,
        salesperson,
        customer,
        shipTo,
        comments,
        growerItemNotes,
      } = rootState.prebookDetail,
      vendorId = vendor?.id || null,
      vendorName = vendor?.name || null,
      salespersonId = salesperson?.id || null,
      salespersonName = salesperson?.name || null,
      customerId = customer?.id || null,
      customerName = customer?.name || null,
      shipToId = shipTo?.id || null,
      shipToName = shipTo?.shipId || null,
      items = rootState.prebookDetail.items.map((i) => ({
        ...{
          id: i.id,
          spireInventoryId: i.spireInventoryId || 0,
          spirePartNumber: i.spirePartNumber || '',
          description: i.description || '',
          orderQuantity: i.orderQuantity,
          hasPotCover: !!i.hasPotCover,
          potCover: i.potCover || null,
          dateCode: i.dateCode || null,
          upc: i.upc || null,
          weightsAndMeasures: !!i.weightsAndMeasures,
          retail: i.retail || null,
          comments: i.comments || null,
          spirePurchaseOrderItemId: null,
          blanketItemId: i.blanketItemId || null,
          blanketWeekId: i.blanketWeekId || null,
          upgradeSheet: !!i.upgradeSheet,
          isApproximate: i.isApproximate,
          blanketOptions: i.blanketOptions || [],
          futureOrderItemId: null,
          boekestynPlantId: i.boekestynPlantId || null,
          boekestynCustomerAbbreviation:
            i.boekestynCustomerAbbreviation || null,
          upgradeLabourHours: i.upgradeLabourHours || 0,
          quantityPerFinishedItem: i.quantityPerFinishedItem || null,
          specialPrice: i.specialPrice || null,
          growerItemNotes: i.growerItemNotes || null,
          boekestynProducts: i.boekestynProducts || [],
          boekPriority: i.boekPriority || false,
          upcPrinted: i.upcPrinted || null,
          upcPrintedPrev: i.upcPrintedPrev || null,
        },
      })),
      prebook = {
        name,
        requiredDate,
        isBlanket,
        blanketStartDate,
        blanketIsClosed,
        seasonName,
        boxCode,
        vendorId,
        vendorName,
        salespersonId,
        salespersonName,
        customerId,
        customerName,
        shipToId,
        shipToName,
        items,
        comments,
        growerItemNotes,
      };

    try {
      return await prebooksApi.create(prebook);
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const updatePrebook: AsyncThunk<
  PrebookDetailResponse,
  void,
  { state: RootState }
> = createAsyncThunk(
  'prebook-detail/updatePrebook',
  async (_, { rejectWithValue, getState }) => {
    const rootState = getState() as RootState,
      state = rootState.prebookDetail;

    if (!state.prebook) {
      return rejectWithValue(createProblemDetails('Prebook not found.'));
    }

    const {
        name,
        requiredDate,
        isBlanket,
        blanketStartDate,
        blanketIsClosed,
        seasonName,
        boxCode,
        vendor,
        salesperson,
        customer,
        shipTo,
        comments,
        growerItemNotes,
        prebook,
      } = state,
      vendorId = vendor?.id || null,
      vendorName = vendor?.name || null,
      salespersonId = salesperson?.id || null,
      salespersonName = salesperson?.name || null,
      customerId = customer?.id || null,
      customerName = customer?.name || null,
      shipToId = shipTo?.id || null,
      shipToName = shipTo?.shipId || null,
      items = state.items.map((i) => ({
        ...{
          id: i.id,
          spireInventoryId: i.spireInventoryId || 0,
          spirePartNumber: i.spirePartNumber || '',
          description: i.description || '',
          orderQuantity: i.orderQuantity,
          hasPotCover: !!i.hasPotCover,
          potCover: i.potCover || null,
          dateCode: i.dateCode || null,
          upc: i.upc || null,
          weightsAndMeasures: !!i.weightsAndMeasures,
          retail: i.retail || null,
          comments: i.comments || null,
          spirePurchaseOrderItemId: null,
          blanketItemId: i.blanketItemId || null,
          blanketWeekId: i.blanketWeekId || null,
          upgradeSheet: !!i.upgradeSheet,
          isApproximate: i.isApproximate,
          blanketOptions: i.blanketOptions || [],
          futureOrderItemId: null,
          boekestynPlantId: i.boekestynPlantId || null,
          boekestynCustomerAbbreviation:
            i.boekestynCustomerAbbreviation || null,
          upgradeLabourHours: i.upgradeLabourHours || 0,
          quantityPerFinishedItem: i.quantityPerFinishedItem || null,
          specialPrice: i.specialPrice || null,
          growerItemNotes: i.growerItemNotes || null,
          boekestynProducts: i.boekestynProducts || [],
          boekPriority: i.boekPriority || false,
          upcPrinted: i.upcPrinted || null,
          upcPrintedPrev: i.upcPrintedPrev || null,
        },
      })),
      updated = {
        ...prebook,
        name,
        requiredDate,
        isBlanket,
        blanketStartDate,
        blanketIsClosed,
        seasonName,
        boxCode,
        vendorId,
        vendorName,
        salespersonId,
        salespersonName,
        customerId,
        customerName,
        shipToId,
        shipToName,
        items,
        comments,
        growerItemNotes,
      };

    try {
      return await prebooksApi.update(updated);
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const deletePrebook: AsyncThunk<undefined, void, { state: RootState }> =
  createAsyncThunk(
    'prebook-detail/deletePrebook',
    async (_, { rejectWithValue, getState }) => {
      try {
        const state = (getState() as RootState).prebookDetail;

        if (state.prebook) {
          await prebooksApi.remove(state.prebook.id);
        }
      } catch (e) {
        return rejectWithValue(e as ProblemDetails);
      }
    }
  );

export const setGrowerConfirmed: AsyncThunk<
  ConfirmPrebookResponse,
  number,
  { state: RootState }
> = createAsyncThunk(
  'prebook-detail-setGrowerConfirmed',
  async (id, { rejectWithValue }) => {
    try {
      return await prebooksApi.confirm(id);
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const sendToSpire: AsyncThunk<void, number, { state: RootState }> =
  createAsyncThunk(
    'prebook-detail-sendToSpire',
    async (id, { rejectWithValue }) => {
      try {
        return await prebooksApi.sendToSpire(id);
      } catch (e) {
        return rejectWithValue(e as ProblemDetails);
      }
    }
  );

interface SetItemArgs<T> {
  itemId: number;
  value: T;
}

export interface InventoryItemWithDefaults
  extends spire.InventoryItem,
    Partial<models.ProductShipToDefault> {
  id: number;
  blanketItemId?: number;
  comments?: string | null;
}

const getPrebookDetailPending = createAction(getPrebookDetail.pending.type),
  getPrebookDetailFulfilled = createAction<PrebookDetailResponse>(
    getPrebookDetail.fulfilled.type
  ),
  getPrebookDetailRejected = createAction<ProblemDetails>(
    getPrebookDetail.rejected.type
  ),
  updatePrebookPending = createAction(updatePrebook.pending.type),
  updatePrebookFulfilled = createAction<PrebookDetailResponse>(
    updatePrebook.fulfilled.type
  ),
  updatePrebookRejected = createAction<ProblemDetails>(
    updatePrebook.rejected.type
  ),
  createPrebookPending = createAction(createPrebook.pending.type),
  createPrebookFulfilled = createAction<PrebookCreateResponse>(
    createPrebook.fulfilled.type
  ),
  createPrebookRejected = createAction<ProblemDetails>(
    createPrebook.rejected.type
  ),
  deletePrebookPending = createAction(deletePrebook.pending.type),
  deletePrebookRejected = createAction<ProblemDetails>(
    deletePrebook.rejected.type
  ),
  createPrebookEmailFulfilled = createAction<CreateEmailResponse>(
    createPrebookEmail.fulfilled.type
  ),
  setGrowerConfirmedPending = createAction(setGrowerConfirmed.pending.type),
  setGrowerConfirmedFulfilled = createAction<ConfirmPrebookResponse>(
    setGrowerConfirmed.fulfilled.type
  ),
  setGrowerConfirmedRejected = createAction<ProblemDetails>(
    setGrowerConfirmed.rejected.type
  ),
  sendToSpirePending = createAction(sendToSpire.pending.type),
  sendToSpireFulfilled = createAction(sendToSpire.fulfilled.type),
  sendToSpireRejected = createAction<ProblemDetails>(sendToSpire.rejected.type),
  getCustomerDetailFulfilled = createAction<SpireCustomerDetailResponse>(
    getCustomerDetail.fulfilled.type
  ),
  getCustomerDetailRejected = createAction<ProblemDetails>(
    getCustomerDetail.rejected.type
  ),
  getBlanketItemsFulfilled = createAction<BlanketItemsResponse>(
    getBlanketItems.fulfilled.type
  ),
  getBlanketItemsRejected = createAction<ProblemDetails>(
    getBlanketItems.rejected.type
  );

export const prebookDetailSlice = createSlice({
  name: 'prebook-detail',
  initialState,
  reducers: {
    clearState(state) {
      Object.assign(state, { ...initialState });
    },
    clearError(state) {
      state.error = null;
    },
    setError(state, { payload }: PayloadAction<ProblemDetails>) {
      state.error = payload;
    },
    setName(state, { payload }: PayloadAction<string | null>) {
      state.name = payload;
    },
    setVendor(state, { payload }: PayloadAction<spire.Vendor | null>) {
      state.vendor = payload;
    },
    setShipTo(state, { payload }: PayloadAction<spire.CustomerShipTo | null>) {
      state.shipTo = payload;
    },
    setCustomer(state, { payload }: PayloadAction<spire.Customer | null>) {
      state.customer = payload;
      if (!payload) {
        state.customerDetail = null;
        state.shipTo = null;
      }
    },
    setCustomerDetail(state, { payload }: PayloadAction<spire.CustomerDetail>) {
      state.customerDetail = payload;
      state.customer = {
        id: payload.id,
        customerNo: payload.customerNo,
        name: payload.name,
        defaultShipTo: payload.defaultShipTo,
      };
    },
    setSalesperson(
      state,
      { payload }: PayloadAction<spire.Salesperson | null>
    ) {
      state.salesperson = payload;
    },
    setRequiredDate(state, { payload }: PayloadAction<string | null>) {
      state.requiredDate = payload;
    },
    setIsBlanket(state, { payload }: PayloadAction<boolean>) {
      state.isBlanket = payload;
      if (!payload) {
        state.blanketStartDate = null;
        state.blanketIsClosed = false;
      }
    },
    setBlanketStartDate(state, { payload }: PayloadAction<string | null>) {
      state.blanketStartDate = payload;
    },
    setBlanketIsClosed(state, { payload }: PayloadAction<boolean>) {
      state.blanketIsClosed = payload;
    },
    setSeasonName(state, { payload }: PayloadAction<string | null>) {
      state.seasonName = payload;
    },
    setBoxCode(state, { payload }: PayloadAction<string | null>) {
      state.boxCode = payload;
    },
    setComments(state, { payload }: PayloadAction<string | null>) {
      state.comments = payload;
    },
    setGrowerItemNotes(state, { payload }: PayloadAction<string | null>) {
      state.growerItemNotes = payload;
    },
    addItem(state, { payload }: PayloadAction<InventoryItemWithDefaults>) {
      const id = state.items.reduce((min, i) => Math.min(min, i.id), 0) - 1,
        dateCode = state.items[state.items.length - 1]?.dateCode || null,
        weightsAndMeasures =
          !!payload.weightsAndMeasures ||
          !!state.items[state.items.length - 1]?.weightsAndMeasures,
        productDefault = state.productDefaults.find(
          (pd) => pd.spireInventoryId === payload.id
        ),
        upgradeLabourHours = productDefault?.upgradeLabourHours || 0,
        upgradeSheet = !!productDefault?.isUpgrade,
        quantityPerFinishedItem =
          productDefault?.quantityPerFinishedItem || null,
        boekestynPlantId = productDefault?.boekestynPlantId || null,
        boekestynCustomerAbbreviation =
          productDefault?.boekestynCustomerAbbreviation || null,
        season = state.seasons.find((s) => s.name === state.seasonName) || null,
        boekestynProducts = getBoekestynProducts(
          state.vendor?.id || null,
          state.requiredDate,
          season,
          productDefault
        ).map((p) => ({ ...p, prebookItemId: id })),
        item = {
          id,
          spireInventoryId: payload.id,
          spirePartNumber: payload.partNo,
          description: payload.description,
          hasPotCover: !!payload.hasPotCover,
          potCover: payload.potCover || null,
          dateCode,
          upc: payload.upc || null,
          weightsAndMeasures,
          retail: payload.retail || null,
          orderQuantity: 0,
          isApproximate: false,
          blanketItemId: payload.blanketItemId || null,
          comments: payload.comments || null,
          growerItemNotes: null,
          boekestynPlantId,
          boekestynCustomerAbbreviation,
          specialPrice: null,
          upgradeLabourHours,
          upgradeSheet,
          quantityPerFinishedItem,
          boekestynProducts,
        },
        items = state.items.map((i) => ({ ...i })).concat([item]);

      state.items = items;

      if (!state.vendor && item.blanketItemId) {
        const blanketItem = state.blanketItems.find(
          (i) => i.id === item.blanketItemId
        );
        if (blanketItem) {
          state.vendor = {
            id: blanketItem.vendorId,
            name: blanketItem.vendorName,
          };
        }
      }
    },
    duplicateItem(state, { payload }: PayloadAction<number>) {
      const id = state.items.reduce((min, i) => Math.min(min, i.id), 0) - 1,
        existing = state.items.find((i) => i.id === payload);

      if (existing) {
        const item: models.NewPrebookDetailItem | models.PrebookDetailItem = {
          id,
          spireInventoryId: existing.spireInventoryId,
          spirePartNumber: existing.spirePartNumber,
          description: existing.description,
          hasPotCover: existing.hasPotCover,
          potCover: existing.potCover,
          dateCode: existing.dateCode,
          upc: existing.upc,
          weightsAndMeasures: existing.weightsAndMeasures,
          retail: existing.retail,
          orderQuantity: 0,
          isApproximate: existing.isApproximate,
          blanketItemId: existing.blanketItemId,
          comments: existing.comments,
          boekestynPlantId: existing.boekestynPlantId,
          boekestynCustomerAbbreviation: existing.boekestynCustomerAbbreviation,
          blanketOptions: existing.blanketOptions || [],
          specialPrice: existing.specialPrice,
          growerItemNotes: existing.growerItemNotes,
          upgradeLabourHours: existing.upgradeLabourHours,
          quantityPerFinishedItem: existing.quantityPerFinishedItem,
          upgradeSheet: existing.upgradeSheet,
          boekestynProducts: existing.boekestynProducts || [],
        };

        const items = state.items.map((i) => ({ ...i })).concat([item]);

        state.items = items;
      }
    },
    removeItem(state, { payload }: PayloadAction<number>) {
      const items = state.items
        .map((i) => ({ ...i }))
        .filter((i) => i.id !== payload);
      state.items = items;
    },
    setItemInventoryItem(
      state,
      { payload }: PayloadAction<SetItemArgs<spire.InventoryItem | null>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.spireInventoryId = value?.id || null;
        item.spirePartNumber = value?.partNo || null;
        item.description = value?.description || null;
      }

      state.items = items;
    },
    setItemHasPotCover(
      state,
      { payload }: PayloadAction<SetItemArgs<boolean>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.hasPotCover = value;
      }

      state.items = items;
    },
    setItemPotCover(
      state,
      { payload }: PayloadAction<SetItemArgs<string | null>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.potCover = value;
      }

      state.items = items;
    },
    setItemDateCode(
      state,
      { payload }: PayloadAction<SetItemArgs<string | null>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.dateCode = value;
      }

      state.items = items;
    },
    setItemUPC(state, { payload }: PayloadAction<SetItemArgs<string | null>>) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.upc = value;
      }

      state.items = items;
    },
    setItemWeightsAndMeasures(
      state,
      { payload }: PayloadAction<SetItemArgs<boolean>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.weightsAndMeasures = value;
      }

      state.items = items;
    },
    setItemRetail(
      state,
      { payload }: PayloadAction<SetItemArgs<string | null>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.retail = value;
      }

      state.items = items;
    },
    setItemComments(
      state,
      { payload }: PayloadAction<SetItemArgs<string | null>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.comments = value;
      }

      state.items = items;
    },
    setItemOrderQuantity(
      state,
      { payload }: PayloadAction<SetItemArgs<number>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.orderQuantity = value;
      }

      state.items = items;
    },
    setItemIsApproximate(
      state,
      { payload }: PayloadAction<SetItemArgs<boolean>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.isApproximate = value;
      }

      state.items = items;
    },
    setItemBlanketItemId(
      state,
      { payload }: PayloadAction<SetItemArgs<number | null>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.blanketItemId = value;
        if (value && !state.vendor) {
          const blanketItem = state.blanketItems.find(
            (i) => i.id === item.blanketItemId
          );
          if (blanketItem) {
            state.vendor = {
              id: blanketItem.vendorId,
              name: blanketItem.vendorName,
            };
          }
        }
      }

      state.items = items;
    },
    setItemBlanketWeekId(
      state,
      { payload }: PayloadAction<SetItemArgs<string | null>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.blanketWeekId = value;
      }

      state.items = items;
    },
    setItemUpgradeSheet(
      state,
      { payload }: PayloadAction<SetItemArgs<boolean>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.upgradeSheet = value;
      }

      state.items = items;
    },
    setItemSpecialPrice(
      state,
      { payload }: PayloadAction<SetItemArgs<number | null>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.specialPrice = value;
      }

      state.items = items;
    },
    setItemGrowerItemNotes(
      state,
      { payload }: PayloadAction<SetItemArgs<string | null>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.growerItemNotes = value;
      }

      state.items = items;
    },
    setItemBlanketOptions(
      state,
      {
        payload,
      }: PayloadAction<SetItemArgs<models.PrebookDetailItemBlanketOption[]>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.blanketOptions = value;
      }

      state.items = items;
    },
    setItemBoekestynProducts(
      state,
      {
        payload,
      }: PayloadAction<SetItemArgs<models.PrebookItemBoekestynProduct[]>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.boekestynProducts = value;
      }

      state.items = items;
    },
    setItemBoekestynProductionOrder(
      state,
      {
        payload,
      }: PayloadAction<
        SetItemArgs<{
          boekestynPlantId: string | null;
          boekestynCustomerAbbreviation: string | null;
        }>
      >
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.boekestynPlantId = value.boekestynPlantId;
        item.boekestynCustomerAbbreviation =
          value.boekestynCustomerAbbreviation;
      }

      state.items = items;
    },
  },
  extraReducers: (builder) =>
    builder
      .addCase(getPrebookDetailPending, (state) => {
        state.isLoading = true;
      })
      .addCase(getPrebookDetailFulfilled, handlePrebookDetailFulfilled)
      .addCase(getPrebookDetailRejected, (state, { payload }) => {
        state.isLoading = false;
        if (payload) {
          state.error = payload;
        }
      })
      .addCase(updatePrebookPending, (state) => {
        state.isLoading = true;
      })
      .addCase(updatePrebookFulfilled, handlePrebookDetailFulfilled)
      .addCase(updatePrebookRejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      .addCase(createPrebookPending, (state) => {
        state.isLoading = true;
      })
      .addCase(createPrebookFulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(createPrebookRejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      .addCase(deletePrebookPending, (state) => {
        state.isLoading = true;
      })
      .addCase(deletePrebookRejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      .addCase(createPrebookEmailFulfilled, (state, { payload }) => {
        const emails = state.emails.map((e) => ({ ...e }));
        emails.unshift(payload.email);
        state.emails = emails;
      })
      .addCase(setGrowerConfirmedPending, (state) => {
        state.isLoading = true;
      })
      .addCase(setGrowerConfirmedFulfilled, (state, { payload }) => {
        state.isLoading = false;
        state.prebook = payload.prebook;
      })
      .addCase(setGrowerConfirmedRejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      .addCase(sendToSpirePending, (state) => {
        state.isLoading = true;
      })
      .addCase(sendToSpireFulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(sendToSpireRejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      .addCase(getCustomerDetailFulfilled, (state, { payload }) => {
        const { customer, potCovers } = payload;
        state.customerDetail = customer;
        state.customerPotCovers = potCovers;
        if (!state.shipTo) {
          const shipToId = state.prebook
              ? state.prebook.shipToId
              : customer.defaultShipTo,
            shipTo =
              customer.shippingAddresses.find((a) => a.shipId === shipToId) ||
              null;
          state.shipTo = shipTo;
          if (shipTo) {
            const salesperson =
              state.salespeople.find(
                (s) => s.code === shipTo.salesperson?.code
              ) || null;
            state.salesperson = salesperson;
            state.boxCode = shipTo.boxCode;
          }
        }
      })
      .addCase(getCustomerDetailRejected, (state, { payload }) => {
        state.error = payload;
      })
      .addCase(getBlanketItemsFulfilled, (state, { payload }) => {
        state.blanketItems = payload.blanketItems;
      })
      .addCase(getBlanketItemsRejected, (state, { payload }) => {
        state.error = payload;
      })
      .addMatcher(
        spireApi.endpoints.salespeople.matchFulfilled,
        (state, { payload }) => {
          state.salespeople = payload;
        }
      )
      .addMatcher(
        spireApi.endpoints.inventoryItems.matchFulfilled,
        (state, { payload }) => {
          state.productDefaults = payload.productDefaults;
        }
      )
      .addMatcher(
        seasonsApi.endpoints.seasons.matchFulfilled,
        (state, { payload }) => {
          state.seasons = payload;
        }
      ),
});

export const {
  clearState,
  clearError,
  setError,
  setName,
  setVendor,
  setSalesperson,
  setShipTo,
  setCustomer,
  setCustomerDetail,
  setRequiredDate,
  setIsBlanket,
  setBlanketStartDate,
  setBlanketIsClosed,
  setSeasonName,
  setBoxCode,
  setComments,
  setGrowerItemNotes,
  addItem,
  duplicateItem,
  removeItem,
  setItemInventoryItem,
  setItemHasPotCover,
  setItemPotCover,
  setItemDateCode,
  setItemUPC,
  setItemWeightsAndMeasures,
  setItemRetail,
  setItemComments,
  setItemOrderQuantity,
  setItemIsApproximate,
  setItemBlanketItemId,
  setItemBlanketWeekId,
  setItemUpgradeSheet,
  setItemSpecialPrice,
  setItemGrowerItemNotes,
  setItemBlanketOptions,
  setItemBoekestynProducts,
  setItemBoekestynProductionOrder,
} = prebookDetailSlice.actions;

export const selectError = (state: RootState) => state.prebookDetail.error;
export const selectName = (state: RootState) => state.prebookDetail.name;
export const selectVendor = (state: RootState) => state.prebookDetail.vendor;
export const selectIsBoekestyn = createSelector(
  selectVendor,
  (vendor) => vendor?.id === boeks.BoekestynVendorId
);
export const selectSalesperson = (state: RootState) =>
  state.prebookDetail.salesperson;
export const selectShipTo = (state: RootState) => state.prebookDetail.shipTo;
export const selectCustomer = (state: RootState) =>
  state.prebookDetail.customer;
export const selectRequiredDate = (state: RootState) =>
  state.prebookDetail.requiredDate;
export const selectIsBlanket = (state: RootState) =>
  state.prebookDetail.isBlanket;
export const selectBlanketStartDate = (state: RootState) =>
  state.prebookDetail.blanketStartDate;
export const selectBlanketIsClosed = (state: RootState) =>
  state.prebookDetail.blanketIsClosed;
export const selectSeasonName = (state: RootState) =>
  state.prebookDetail.seasonName;
export const selectBoxCode = (state: RootState) => state.prebookDetail.boxCode;
export const selectComments = (state: RootState) =>
  state.prebookDetail.comments;
export const selectGrowerItemNotes = (state: RootState) =>
  state.prebookDetail.growerItemNotes;
export const selectItems = (state: RootState) => state.prebookDetail.items;
export const selectIsLoading = (state: RootState) =>
  state.prebookDetail.isLoading;
export const selectCustomerDetail = (state: RootState) =>
  state.prebookDetail.customerDetail;
export const selectPotCovers = (state: RootState) => {
  const customerPotCovers = state.prebookDetail.customerPotCovers,
    potCovers = defaultPotCovers.concat(customerPotCovers);
  return potCovers;
};
export const selectShipTos = createSelector(
  selectCustomerDetail,
  (customerDetail) => customerDetail?.shippingAddresses || []
);
export const selectPrebookDetail = (state: RootState) =>
  state.prebookDetail.prebook;
export const selectEmails = (state: RootState) =>
  state.prebookDetail.emails.map((e) => ({ ...e })).sort(sortByCreated);
export const selectBlanketItems = (state: RootState) =>
  state.prebookDetail.blanketItems;
export const selectProductDefaults = (state: RootState) =>
  state.prebookDetail.productDefaults;
export const selectPreviousItems = createSelector(
  selectPrebookDetail,
  selectEmails,
  (prebook, emails) => {
    if (!prebook || !emails.length) {
      return [];
    }

    return emails[0].items;
  }
);

function handlePrebookDetailFulfilled(
  state: PrebookDetailState,
  { payload }: PayloadAction<PrebookDetailResponse>
) {
  state.isLoading = false;
  state.prebook = payload.prebook;
  state.emails = payload.emails;
  state.blanketItems = payload.blanketItems;
  const {
    name,
    vendorId,
    vendorName,
    customerId,
    customerName,
    requiredDate,
    isBlanket,
    blanketStartDate,
    blanketIsClosed,
    seasonName,
    boxCode,
    comments,
    growerItemNotes,
    items,
  } = payload.prebook;

  const vendor = vendorId ? { id: vendorId, name: vendorName || '' } : null,
    customer = customerId
      ? { id: customerId, name: customerName || '', defaultShipTo: null }
      : null;
  state.name = name;
  state.vendor = vendor;
  state.customer = customer;
  state.requiredDate = requiredDate
    ? DateTime.fromISO(requiredDate).toFormat('yyyy-MM-dd')
    : null;
  state.isBlanket = isBlanket;
  state.blanketStartDate = blanketStartDate
    ? DateTime.fromISO(blanketStartDate).toFormat('yyyy-MM-dd')
    : null;
  state.blanketIsClosed = blanketIsClosed;
  state.seasonName = seasonName;
  state.boxCode = boxCode;
  state.comments = comments;
  state.growerItemNotes = growerItemNotes;
  state.items = items;
}

export default prebookDetailSlice.reducer;
