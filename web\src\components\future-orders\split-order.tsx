import React, { Fragment } from 'react';
import * as HeadlessUI from '@headlessui/react';
import { Icon } from '@/components/icon';
import { Error } from '@/components/error';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { SplitOrderSelectItems } from './split-order-select-items';
import { SplitOrderDestination } from './split-order-destination';
import { SplitOrderConfirmation } from './split-order-confirmation';
import {
  selectError,
  clearError,
  clearState,
  selectStep,
} from './split-order-slice';

interface SplitOrderProps {
  open: boolean;
  onClose: () => void;
}

export function SplitOrder({ open, onClose }: SplitOrderProps) {
  const dispatch = useAppDispatch(),
    step = useAppSelector(selectStep),
    error = useAppSelector(selectError);

  const handleClearErrorClick = () => {
    dispatch(clearError());
  };

  const handleAfterLeave = () => {
    dispatch(clearState());
    onClose();
  };

  const handleCloseClick = () => {
    dispatch(clearState());
    onClose();
  };

  return (
    <HeadlessUI.Transition.Root
      show={open}
      as={Fragment}
      afterLeave={handleAfterLeave}
    >
      <HeadlessUI.Dialog
        as="div"
        className="relative z-30"
        onClose={handleCloseClick}
      >
        <HeadlessUI.Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </HeadlessUI.Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-0 text-center">
            <HeadlessUI.Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-0 scale-95"
              enterTo="opacity-100 translate-y-0 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 scale-100"
              leaveTo="opacity-0 translate-y-0 scale-95"
            >
              <HeadlessUI.Dialog.Panel className="relative my-8 w-full max-w-7xl transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all">
                <div className="h-[90vh] bg-white p-6 px-4 pb-4 pt-5">
                  <div className="ml-4 mt-0 flex h-full flex-col text-left">
                    <div className="flex flex-row">
                      <div className="mx-0 mr-5 flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-blue-100">
                        <Icon
                          icon="search"
                          className="h-6 w-6 text-blue-600"
                          aria-hidden="true"
                        />
                      </div>
                      <HeadlessUI.Dialog.Title
                        as="h1"
                        className="text-3xl font-medium leading-6 text-gray-900"
                      >
                        Split items to another Future Order
                      </HeadlessUI.Dialog.Title>
                    </div>
                    <Error
                      error={error}
                      clear={handleClearErrorClick}
                      containerClasses="mt-4"
                    />
                    <div className="mt-4 flex flex-grow flex-col overflow-y-auto">
                      {step === 1 && (
                        <SplitOrderSelectItems onClose={onClose} />
                      )}
                      {step === 2 && (
                        <SplitOrderDestination onClose={onClose} />
                      )}
                      {step === 3 && (
                        <SplitOrderConfirmation onClose={onClose} />
                      )}
                    </div>
                  </div>
                </div>
              </HeadlessUI.Dialog.Panel>
            </HeadlessUI.Transition.Child>
          </div>
        </div>
      </HeadlessUI.Dialog>
    </HeadlessUI.Transition.Root>
  );
}
