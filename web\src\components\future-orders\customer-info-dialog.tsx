import { Fragment, useState } from 'react';
import * as HeadlessUI from '@headlessui/react';
import { Icon } from '@/components/icon';

interface CustomerInfoDialogProps {
  open: boolean;
  customerInfo: string | null;
  save: (customerInfo: string | null) => void;
  close: () => void;
}

export function CustomerInfoDialog({
  open,
  customerInfo,
  save,
  close,
}: CustomerInfoDialogProps) {
  const [info, setInfo] = useState(''),
    [edit, setEdit] = useState(false);

  const handleDialogEnter = () => {
    setEdit(false);
    setInfo(customerInfo || '');
  };

  const handleCustomerInfoChange = (
    event: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    setInfo(event.target.value);
  };

  const handleEditClick = () => {
    setEdit(true);
  };

  const handleSaveClick = () => {
    save(info);
    close();
  };

  return (
    <HeadlessUI.Transition.Root
      as={Fragment}
      show={open}
      afterEnter={handleDialogEnter}
    >
      <HeadlessUI.Dialog as="div" onClose={close} className="relative z-30">
        <HeadlessUI.Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </HeadlessUI.Transition.Child>
        <div className="fixed inset-0 z-30 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center overflow-y-auto p-0 text-center">
            <HeadlessUI.Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <HeadlessUI.Dialog.Panel className="relative h-screen w-full max-w-screen-lg transform overflow-y-auto p-6 transition-all">
                <div className="flex h-full flex-col overflow-y-auto rounded-lg bg-white p-6 text-left shadow-xl">
                  <div className="mb-4 flex justify-center border-b-2 pb-4">
                    <HeadlessUI.Dialog.Title
                      as="h3"
                      className="text-lg font-medium leading-6 text-gray-900"
                    >
                      <Icon icon="circle-info" className="h-6 w-6" />
                      &nbsp; Additional Customer Info
                    </HeadlessUI.Dialog.Title>
                  </div>
                  <div className="flex flex-grow flex-col overflow-y-auto">
                    {edit && (
                      <>
                        <div className="my-2 text-center font-semibold italic">
                          Note that changes made here will be reflected in the
                          Sage Ship-To.
                        </div>
                        <div className="flex-grow">
                          <textarea
                            className="block h-full w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            value={info}
                            onChange={handleCustomerInfoChange}
                          ></textarea>
                        </div>
                      </>
                    )}
                    {!edit && (
                      <div className="whitespace-pre-line">{customerInfo}</div>
                    )}
                  </div>
                  {!edit && (
                    <div className="mt-4 flex justify-end border-t-2 pt-4">
                      <button
                        type="button"
                        className="btn-secondary text-lg"
                        onClick={close}
                      >
                        Close
                      </button>
                      {/* <button
                        type="button"
                        className="btn-primary ml-2 text-lg"
                        onClick={handleEditClick}
                      >
                        Edit &nbsp;
                        <Icon icon="edit" />
                      </button> */}
                    </div>
                  )}
                  {edit && (
                    <div className="mt-4 flex justify-end border-t-2 pt-4">
                      <button
                        type="button"
                        className="btn-secondary text-lg"
                        onClick={close}
                      >
                        Cancel
                      </button>
                      <button
                        type="button"
                        className="btn-primary ml-2 text-lg"
                        onClick={handleSaveClick}
                      >
                        Save &nbsp;
                        <Icon icon="save" />
                      </button>
                    </div>
                  )}
                </div>
              </HeadlessUI.Dialog.Panel>
            </HeadlessUI.Transition.Child>
          </div>
        </div>
      </HeadlessUI.Dialog>
    </HeadlessUI.Transition.Root>
  );
}
