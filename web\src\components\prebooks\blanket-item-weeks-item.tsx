import { Fragment, useState } from 'react';
import Link from 'next/link';
import { Popover, Transition } from '@headlessui/react';
import { useAppDispatch } from '@/services/hooks';
import * as models from 'api/models/prebooks';
import { Icon } from '@/components/icon';
import { formatDate, formatNumber } from '@/utils/format';
import { sortBy } from '@/utils/sort';
import { classNames } from '@/utils/class-names';
import { routes } from '@/services/routes';
import { setGrowerConfirmed } from './prebook-detail-slice';
import { BlanketItemItem } from './blanket-item-item';
import { BlanketItemWeeksItemWeek } from './blanket-items-weeks-item-week';

export interface BlanketItemListItemWithWeeks
  extends models.BlanketItemListItem {
  weeks: models.BlanketItemListItem[];
}

export interface BlanketItemWeeksItemProps {
  item: BlanketItemListItemWithWeeks;
  hasSeasons: boolean;
  refresh: () => void;
}

const sortByWeekId = sortBy('blanketWeekId');

export function BlanketItemWeeksItem({
  item,
  hasSeasons,
  refresh,
}: BlanketItemWeeksItemProps) {
  const dispatch = useAppDispatch(),
    [tooltipOpen, setTooltipOpen] = useState(false),
    [showWeeks, setShowWeeks] = useState(false),
    blanketQuantity = item.weeks.reduce(
      (memo, i) => memo + i.blanketQuantity,
      0
    ),
    bookedQuantity = item.weeks.reduce((memo, i) => memo + i.bookedQuantity, 0),
    remaining = blanketQuantity - bookedQuantity,
    weeks = item.weeks.filter((w) => w.blanketWeekId).sort(sortByWeekId),
    hasWeeks = weeks.length > 1;

  const handleToggleWeeksClick = () => {
    setShowWeeks(!showWeeks);
  };

  const handleTooltipMouseEnter = () => {
    setTooltipOpen(true);
  };

  const handleTooltipMouseLeave = () => {
    setTooltipOpen(false);
  };

  const handleSetGrowerConfirmedClick = async () => {
    await dispatch(setGrowerConfirmed(item.prebookId));
    refresh();
  };

  if (hasWeeks) {
    return (
      <>
        <tr className={classNames(!showWeeks && 'border-b-2 border-gray-300')}>
          <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-700">
            <Link href={routes.prebooks.detail.to(item.prebookId)}>
              {formatNumber(item.prebookId, '00000')}
            </Link>
          </td>
          <td className="whitespace-nowrap px-3 py-4 text-left text-sm text-gray-700">
            <Popover
              className="-z-1 relative inline-block cursor-pointer"
              onMouseEnter={handleTooltipMouseEnter}
              onMouseLeave={handleTooltipMouseLeave}
            >
              <>
                <Popover.Button as="div" className="font-lg  px-2 py-1">
                  <Icon
                    icon={
                      !item.sent
                        ? 'triangle-exclamation'
                        : !item.confirmed
                        ? 'question-circle'
                        : 'check-circle'
                    }
                    className={
                      !item.sent
                        ? 'text-red-600'
                        : !item.confirmed
                        ? 'text-yellow-600'
                        : 'text-green-600'
                    }
                  />
                </Popover.Button>
                <Transition
                  as={Fragment}
                  show={tooltipOpen}
                  enter="transition ease-out duration-200"
                  enterFrom="opacity-0 translate-y-1"
                  enterTo="opacity-100 translate-y-0"
                  leave="transition ease-in duration-150"
                  leaveFrom="opacity-100 translate-y-0"
                  leaveTo="opacity-0 translate-y-1"
                >
                  <Popover.Panel
                    static
                    className="absolute left-[25px] z-10 -translate-y-1/2 transform bg-white"
                  >
                    <div className="rounded-lg border p-4 shadow-lg">
                      <p className="my-2 text-xs text-gray-500">
                        Created by{' '}
                        <span className="font-medium">{item.createdBy}</span> on{' '}
                        <span className="font-medium">
                          {formatDate(item.created)}
                        </span>{' '}
                        @{' '}
                        <span className="font-medium">
                          {formatDate(item.created, 'h:mm a')}
                        </span>
                      </p>
                      {item.created !== item.modified && (
                        <p className="my-2 text-xs text-gray-500">
                          Updated by{' '}
                          <span className="font-medium">{item.modifiedBy}</span>{' '}
                          on{' '}
                          <span className="font-medium">
                            {formatDate(item.modified)}
                          </span>{' '}
                          @{' '}
                          <span className="font-medium">
                            {formatDate(item.modified, 'h:mm a')}
                          </span>
                        </p>
                      )}
                      {!!item.sent && (
                        <p className="my-2 text-xs text-gray-500">
                          Last sent by{' '}
                          <span className="font-medium">{item.sentBy}</span> on{' '}
                          <span className="font-medium">
                            {formatDate(item.sent)}
                          </span>{' '}
                          @{' '}
                          <span className="font-medium">
                            {formatDate(item.sent, 'h:mm a')}
                          </span>
                        </p>
                      )}
                      {!item.sent && (
                        <p className="my-2 text-xs font-bold italic text-red-500">
                          Not sent to Grower
                        </p>
                      )}
                      {!!item.confirmed && (
                        <p className="my-2 text-xs text-gray-500">
                          Grower Confirmed by{' '}
                          <span className="font-medium">
                            {item.confirmedBy}
                          </span>{' '}
                          on{' '}
                          <span className="font-medium">
                            {formatDate(item.confirmed)}
                          </span>{' '}
                          @{' '}
                          <span className="font-medium">
                            {formatDate(item.confirmed, 'h:mm a')}
                          </span>
                        </p>
                      )}
                      {!!item.sent && !item.confirmed && (
                        <p className="my-2 text-xs font-bold italic text-yellow-500">
                          <div className="mr-4 inline-block">
                            Grower has not confirmed
                          </div>
                          <button
                            type="button"
                            onClick={handleSetGrowerConfirmedClick}
                            className="btn-secondary px-2 py-1 text-xs"
                          >
                            Confirm
                          </button>
                        </p>
                      )}
                    </div>
                  </Popover.Panel>
                </Transition>
              </>
            </Popover>
          </td>
          <td className="whitespace-nowrap px-3 py-4 text-left align-top text-sm text-gray-700">
            <button
              type="button"
              className="btn-secondary p-2 text-xs focus:ring-0"
              onClick={handleToggleWeeksClick}
            >
              <Icon icon={showWeeks ? 'chevron-up' : 'chevron-down'} />
            </button>
          </td>
          <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-700">
            {item.vendor}
          </td>
          <td className="whitespace-nowrap px-3 py-4 text-left text-sm text-gray-700">
            {item.spirePartNumber}
            <br />
            <span className="italic text-gray-400">{item.description}</span>
          </td>
          <td className="whitespace-nowrap px-3 py-4 text-center text-sm text-gray-700">
            {!!item.blanketStartDate && (
              <div className="whitespace-nowrap">
                {`${formatDate(item.blanketStartDate, 'MMM d')} - `}
                {formatDate(item.requiredDate, 'MMM d')}
              </div>
            )}
            {!item.blanketStartDate && (
              <div>{formatDate(item.requiredDate, 'MMM d')}</div>
            )}
            <div className="mt-1 text-xs italic text-gray-500">
              Multiple Weeks
            </div>
          </td>
          <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-700">
            {item.salesperson}
          </td>
          <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-700">
            {item.customer}
            {!!item.shipTo && (
              <>
                <br />
                <span className="italic text-gray-500">{item.shipTo}</span>
              </>
            )}
          </td>
          {hasSeasons && (
            <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-700">
              {item.season}
            </td>
          )}
          <td className="whitespace-nowrap px-3 py-4 text-right text-sm text-gray-700">
            {formatNumber(blanketQuantity)}
          </td>
          <td className="whitespace-nowrap px-3 py-4 text-right text-sm text-gray-700">
            {formatNumber(bookedQuantity)}
          </td>
          <td
            className={classNames(
              'whitespace-nowrap px-3 py-4 text-right text-sm',
              remaining < 0 ? 'text-red-600' : 'text-gray-700'
            )}
          >
            {formatNumber(remaining)}
          </td>
        </tr>
        {showWeeks &&
          weeks.map((week) => (
            <BlanketItemWeeksItemWeek
              key={week.id}
              item={week}
              hasSeasons={hasSeasons}
            />
          ))}
      </>
    );
  }

  return (
    <BlanketItemItem item={item} hasSeasons={hasSeasons} refresh={refresh} />
  );
}
