const { createServer } = require('http'),
  { parse } = require('url'),
  next = require('next'),
  dev = false,
  app = next({ dev }),
  handle = app.getRequestHandler();

app.prepare().then(() => {
  createServer((req, res) => {
    const parsedUrl = parse(req.url, true);
    handle(req, res, parsedUrl);
  }).listen(process.env.PORT || 3000, (err) => {
    if (err) throw err;
    console.log('> Ready on http://localhost');
  });
});
