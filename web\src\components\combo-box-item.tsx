import { Icon } from './icon';
import { classNames } from '@/utils/class-names';
import { Combobox } from '@headlessui/react';

interface ComboboxItemProps {
  displayText: string;
  secondaryDisplayText?: string;
  value?: object | string | null;
  classNames?: string;
}

export function ComboboxItem({
  displayText,
  secondaryDisplayText,
  value = null,
  classNames: additionalClassNames,
}: ComboboxItemProps) {
  return (
    <Combobox.Option
      value={value}
      className={({ active }) =>
        classNames(
          'relative cursor-default select-none py-2 pl-3 pr-9',
          active ? 'bg-indigo-600 text-white' : 'text-gray-900',
          additionalClassNames
        )
      }
    >
      {({ active, selected }) => (
        <>
          <span
            className={classNames(
              'truncate',
              selected && 'font-semibold',
              !secondaryDisplayText && 'block'
            )}
          >
            {displayText}
          </span>

          {!!secondaryDisplayText && (
            <span
              className={classNames(
                'ml-2 truncate text-gray-500',
                active ? 'text-indigo-200' : 'text-gray-500'
              )}
            >
              ({secondaryDisplayText})
            </span>
          )}
          {!!value && selected && (
            <span
              className={classNames(
                'absolute inset-y-0 right-0 flex items-center pr-4',
                active ? 'text-white' : 'text-indigo-600'
              )}
            >
              <Icon icon="check" className="h-5 w-5" aria-hidden="true" />
            </span>
          )}
        </>
      )}
    </Combobox.Option>
  );
}
