import { createSelector, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '@/services/store';
import * as boeks from 'api/models/boekestyns';
import { formatDate } from '@/utils/format';
import { boekestynHarvestingApi } from 'api/boekestyn-harvesting-service';
import { ProblemDetails } from '@/utils/problem-details';

interface HarvestingState {
  date: string;
  line: number | null;
  workOrders: boeks.HarvestingWorkOrderItem[];
  lines: boeks.HarvestingLine[];
  error: ProblemDetails | null;
}

const initialState: HarvestingState = {
  date: formatDate(new Date(), 'yyyy-MM-dd'),
  line: null,
  workOrders: [],
  lines: [],
  error: null,
};

const harvestingSlice = createSlice({
  name: 'harvesting',
  initialState,
  reducers: {
    setError(state, { payload }: PayloadAction<ProblemDetails | null>) {
      state.error = payload;
    },
    setDate(state, { payload }: PayloadAction<string>) {
      state.date = payload;
    },
    setLine(state, { payload }: PayloadAction<number | null>) {
      state.line = payload;
    },
  },
  extraReducers: (builder) =>
    builder
      .addMatcher(
        boekestynHarvestingApi.endpoints.harvesting.matchFulfilled,
        (state, { payload }) => {
          state.lines = payload.lines;
        }
      )
      .addMatcher(
        boekestynHarvestingApi.endpoints.getHarvestingWorkOrdersByDate.matchFulfilled,
        (state, { payload }) => {
          state.workOrders = payload.orders;
        }
      ),
});

export const { setDate, setLine, setError } = harvestingSlice.actions;

const selectAllWorkOrders = ({ boekestynHarvesting }: RootState) =>
  boekestynHarvesting.workOrders;
export const selectDate = ({ boekestynHarvesting }: RootState) =>
  boekestynHarvesting.date;
export const selectLine = ({ boekestynHarvesting }: RootState) =>
  boekestynHarvesting.line;
export const selectLines = ({ boekestynHarvesting }: RootState) =>
  boekestynHarvesting.lines;
export const selectError = ({ boekestynHarvesting }: RootState) =>
  boekestynHarvesting.error;

export const selectWorkOrders = createSelector(
  selectAllWorkOrders,
  selectLines,
  selectLine,
  (workOrders, lines, line) =>
    workOrders.filter(
      (wo) => !line || wo.lineName === lines.find((l) => l.id === line)?.name
    )
);

export default harvestingSlice.reducer;
