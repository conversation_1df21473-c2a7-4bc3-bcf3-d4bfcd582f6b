import { useState, MutableRefObject } from 'react';
import * as HeadlessUI from '@headlessui/react';
import { useShipTosQuery, spireService } from 'api/spire-service';
import * as spire from 'api/models/spire';
import { contains } from '@/utils/equals';
import { ComboboxItem } from './combo-box-item';
import { Icon } from './icon';

interface ShipToComboboxProps {
  value: spire.Address | null;
  onChange: (
    value: spire.CustomerShipTo | null,
    customer?: spire.CustomerDetail
  ) => void;
  customer: spire.CustomerDetail | null;
  inputRef?: MutableRefObject<HTMLInputElement | null>;
  disabled?: boolean;
  hideLabel?: boolean;
}

export function ShipToCombobox({
  value,
  onChange,
  inputRef,
  customer,
  disabled,
  hideLabel,
}: ShipToComboboxProps) {
  const [query, setQuery] = useState(''),
    { data: shipTos } = useShipTosQuery(query),
    collection: spire.Address[] = disabled
      ? []
      : customer?.shippingAddresses || shipTos || [],
    filteredCollection = customer
      ? query
        ? collection.filter((item) => contains(item.shipId, query))
        : collection
      : [];

  const handleShipToChange = async (address: spire.Address | null) => {
    if (address == null) {
      onChange(null);
      // this is a CustomerShippingAddress from the customer
    } else if ('boxCode' in address) {
      onChange(address as spire.CustomerShipTo);
    } else {
      const { shipTo, customer } = await spireService.shipToDetail(address.id);
      if (shipTo && customer) {
        onChange(shipTo, customer);
      }
    }
  };

  return (
    <HeadlessUI.Combobox
      as="div"
      value={value}
      onChange={handleShipToChange}
      disabled={disabled}
      nullable
    >
      {!hideLabel && (
        <HeadlessUI.Combobox.Label className="block text-sm font-medium text-gray-500">
          Ship-To
        </HeadlessUI.Combobox.Label>
      )}
      <div className="relative mt-1">
        <HeadlessUI.Combobox.Input
          type={disabled ? 'text' : 'search'}
          className="w-full rounded-md border border-gray-300 bg-white py-2 pl-3 pr-10 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 sm:text-sm"
          onChange={(e) => setQuery(e.target.value)}
          displayValue={(o: spire.Address | null) => o?.shipId || ''}
          autoComplete="off"
          ref={inputRef}
        />
        {!disabled && (
          <HeadlessUI.Combobox.Button className="absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none">
            <Icon
              icon="caret-down"
              className="h-5 w-5 text-gray-400"
              aria-hidden="true"
            />
          </HeadlessUI.Combobox.Button>
        )}

        {filteredCollection.length > 0 && (
          <HeadlessUI.Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
            <ComboboxItem displayText="No Ship-To" />
            {filteredCollection.map((item) => (
              <ComboboxItem
                key={item.id}
                value={item}
                displayText={getDisplayText(item, customer)}
              />
            ))}
          </HeadlessUI.Combobox.Options>
        )}
      </div>
    </HeadlessUI.Combobox>
  );
}

function getDisplayText(item: spire.Address, customer: spire.Customer | null) {
  return (
    item.shipId +
    (!customer && 'linkNo' in item
      ? ` (${(item as spire.CustomerAddress).linkNo})`
      : '')
  );
}
