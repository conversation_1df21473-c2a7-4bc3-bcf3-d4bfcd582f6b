import * as models from 'api/models/future-orders';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { classNames } from '@/utils/class-names';
import { formatDate, formatNumber } from '@/utils/format';
import { getDetail, selectFutureOrderDetail } from './add-from-order-slice';

interface AddFromOrderItemProps {
  futureOrder: models.FutureOrderSummaryItem;
}

export function AddFromOrderItem({ futureOrder }: AddFromOrderItemProps) {
  const dispatch = useAppDispatch(),
    futureOrderDetail = useAppSelector(selectFutureOrderDetail),
    isSelected = futureOrder.id === futureOrderDetail?.id;

  const handleRowClick = () => {
    dispatch(getDetail(futureOrder.futureOrderId));
  };

  return (
    <tr
      onClick={handleRowClick}
      className={classNames(
        'cursor-pointer border-b-2 border-gray-100',
        isSelected ? 'bg-gray-200' : 'hover:bg-gray-100'
      )}
    >
      <td className="truncate p-2 text-center align-top">
        {formatNumber(futureOrder.futureOrderId, '00000')}
      </td>
      <td className="truncate p-2 align-top">
        {futureOrder.customer}
        {!!futureOrder.shipTo && (
          <div className="italic">{futureOrder.shipTo}</div>
        )}
      </td>
      <td className="truncate p-2 text-center align-top">
        {formatDate(futureOrder.date)}
      </td>
    </tr>
  );
}
