import * as prebooks from 'api/models/prebooks';
import * as spire from 'api/models/spire';
import { Icon } from '@/components/icon';
import { classNames } from '@/utils/class-names';
import { formatDate, formatNumber } from '@/utils/format';
import { weekFromId } from '@/utils/weeks';

interface InventoryListItemBlanketItemProps {
  blanketItem: prebooks.PrebookBlanketItem;
  vendor: spire.Vendor | null;
  onItemSelected: (blanketItemId: number | null) => void;
}

export function InventoryListItemBlanketItem({
  blanketItem,
  vendor,
  onItemSelected,
}: InventoryListItemBlanketItemProps) {
  const week = weekFromId(blanketItem.blanketWeekId);

  const handleSelectItem = (blanketItemId?: number) => {
    onItemSelected(blanketItemId || null);
  };

  return (
    <>
      {!vendor && (
        <div
          className={classNames(
            'col-start-1 truncate text-sm text-gray-500',
            blanketItem.bookedQuantity ? 'col-span-2' : 'col-span-3'
          )}
        >
          {blanketItem.vendorName}
        </div>
      )}
      <div className="col-start-3 text-right">
        {!!blanketItem.bookedQuantity &&
          formatNumber(blanketItem.blanketQuantity)}
      </div>
      <div className="col-start-3 text-right">
        {!!blanketItem.bookedQuantity &&
          `- ${formatNumber(blanketItem.bookedQuantity)}`}
        {!blanketItem.bookedQuantity && <div>&nbsp;</div>}
      </div>

      <div className="row-span-2 flex pl-4 align-middle">
        <button
          type="button"
          className="btn-secondary my-auto flex w-24 justify-between py-1 text-xs text-blue-500 hover:border-blue-300 hover:text-blue-700"
          onClick={() => handleSelectItem(blanketItem.id)}
        >
          Select &nbsp;
          <Icon icon="chevron-right" />
        </button>
      </div>
      <div className="col-span-2 col-start-1 truncate text-xs italic text-gray-500">
        {!!blanketItem.blanketStartDate &&
          `${formatDate(blanketItem.blanketStartDate, 'MMM d, yyyy')} - `}
        {formatDate(blanketItem.requiredDate, 'MMM d, yyyy')}
        {!!week && <div>(Week {week.week})</div>}
      </div>
      <div className="text-md text-right font-bold text-green-600">
        {!!blanketItem.bookedQuantity && '= '}
        {formatNumber(blanketItem.blanketQuantity - blanketItem.bookedQuantity)}
      </div>
      <div className="col-span-4 border-b-2">&nbsp;</div>
    </>
  );
}
