import { useState } from 'react';
import { useRouter } from 'next/router';
import { futureOrdersApi } from 'api/future-orders-service';
import { Combobox } from '@/components/combo-box';
import { Icon } from '@/components/icon';
import { ShipToCombobox } from '@/components/ship-to-combo-box';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { routes } from '@/services/routes';
import { formatNumber } from '@/utils/format';
import { ProblemDetails } from '@/utils/problem-details';
import {
  selectItems,
  selectRequiredDate,
  selectCustomer,
  selectShipTo,
  selectCustomerDetail,
  selectDestinationType,
  selectFutureOrderDetail,
  setStep,
  selectArrivalDate,
  selectSeasonName,
  selectSalesperson,
  selectShipVia,
  selectBoxCode,
  selectRequiresLabels,
  selectCustomerPurchaseOrderNumber,
  selectSpireNotes,
  selectGrowerItemNotes,
  selectComments,
  setError,
  clearState,
  setItems,
} from './split-order-slice';
import {
  selectFutureOrder,
  selectPrebooks,
  setIsParentOrder,
  getFutureOrderDetail,
} from './future-order-detail-slice';

interface SplitOrderConfirmationProps {
  onClose: () => void;
}
export function SplitOrderConfirmation({
  onClose,
}: SplitOrderConfirmationProps) {
  const dispatch = useAppDispatch(),
    router = useRouter(),
    items = useAppSelector(selectItems),
    requiredDate = useAppSelector(selectRequiredDate),
    arrivalDate = useAppSelector(selectArrivalDate),
    seasonName = useAppSelector(selectSeasonName),
    customer = useAppSelector(selectCustomer),
    shipTo = useAppSelector(selectShipTo),
    salesperson = useAppSelector(selectSalesperson),
    shipVia = useAppSelector(selectShipVia),
    boxCode = useAppSelector(selectBoxCode),
    requiresLabels = useAppSelector(selectRequiresLabels),
    customerPurchaseOrderNumber = useAppSelector(
      selectCustomerPurchaseOrderNumber
    ),
    spireNotes = useAppSelector(selectSpireNotes),
    growerItemNotes = useAppSelector(selectGrowerItemNotes),
    comments = useAppSelector(selectComments),
    customerDetail = useAppSelector(selectCustomerDetail),
    destinationType = useAppSelector(selectDestinationType),
    futureOrderDetail = useAppSelector(selectFutureOrderDetail),
    originalFutureOrder = useAppSelector(selectFutureOrder),
    prebooks = useAppSelector(selectPrebooks),
    [isSaving, setIsSaving] = useState(false),
    selectedItems = items.filter((i) => i.selected && !!i.splitQuantity),
    destination =
      destinationType === 'new'
        ? 'New Future Order'
        : `Future Order #${formatNumber(futureOrderDetail?.id, '00000')}`,
    totalOrderQuantity = selectedItems.reduce(
      (total, i) => total + i.orderQuantity,
      0
    ),
    totalSplitQuantity = selectedItems.reduce(
      (total, i) => total + i.splitQuantity,
      0
    );

  const handleSaveClick = async () => {
    if (originalFutureOrder) {
      if (await save()) {
        dispatch(getFutureOrderDetail(originalFutureOrder.id));
        onClose();
      }
    }
  };

  const handleSaveAndNewClick = async () => {
    if (originalFutureOrder) {
      const splitItems = selectedItems.map((i) => ({ ...i }));
      if (await save()) {
        dispatch(clearState());
        const splitableItems = originalFutureOrder.items
          .map((i) => ({
            ...i,
            orderQuantity:
              i.orderQuantity -
              (splitItems.find((si) => si.id === i.id)?.splitQuantity || 0),
          }))
          .filter((i) => !!i.orderQuantity);
        dispatch(setItems(splitableItems));
        dispatch(getFutureOrderDetail(originalFutureOrder.id));
      }
    }
  };

  const handleSaveAndOpenClick = async () => {
    const created = await save();
    if (created) {
      dispatch(clearState());
      router.push(routes.futureOrders.detail.to(created));
    }
  };

  const handleSaveAndSendClick = async () => {
    const created = await save();
    if (created) {
      dispatch(clearState());
      router.push(`${routes.futureOrders.detail.to(created)}?action=send`);
    }
  };

  const handleCloseClick = () => {
    onClose();
  };

  const handleBackClick = () => {
    dispatch(setStep(2));
  };

  const save = async () => {
    try {
      setIsSaving(true);
      if (originalFutureOrder) {
        const items = selectedItems.map((i) => ({
            originalId: i.id,
            orderQuantity: i.splitQuantity,
          })),
          model = {
            existingFutureOrderId: futureOrderDetail?.id || null,
            requiredDate,
            arrivalDate,
            seasonName,
            customerId: customer?.id || null,
            customerName: customer?.name || null,
            shipToId: shipTo?.id || null,
            shipToName: shipTo?.name || null,
            salespersonId: salesperson?.id || null,
            salespersonName: salesperson?.name || null,
            shipViaId: shipVia?.id || null,
            shipViaName: shipVia?.description || null,
            boxCode,
            requiresLabels,
            customerPurchaseOrderNumber,
            freightPerCase: futureOrderDetail?.freightPerCase || null,
            freightPerLoad: futureOrderDetail?.freightPerLoad || null,
            freightIsActual: futureOrderDetail?.freightIsActual || false,
            spireNotes,
            growerItemNotes,
            comments,
            items,
          };

        const { id } = await futureOrdersApi.split(
          originalFutureOrder.id,
          model
        );

        dispatch(setIsParentOrder(true));

        return id;
      }

      return null;
    } catch (e) {
      dispatch(setError(e as ProblemDetails));

      return null;
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="flex flex-grow flex-col overflow-y-auto p-4">
      <h3 className="mt-4 text-2xl">Confirm Order Split</h3>
      <h2 className="mt-4 text-xl">Destination: {destination}</h2>
      <div className="my-4 grid grid-cols-4">
        <Combobox
          value={customer}
          disabled
          label="Customer"
          onChange={(c) => {}}
          collection={[]}
          filter={(q, c) => false}
          secondaryDisplayTextProp="customerNo"
          nullDisplayText="No Customer"
        />
        <ShipToCombobox
          value={shipTo}
          onChange={() => {}}
          customer={customerDetail}
          disabled
        />
        <div>
          <label
            htmlFor="required-date"
            className="block text-sm font-medium text-gray-500"
          >
            Required Date
          </label>
          <div className="mt-1">
            <input
              type="text"
              id="required-date"
              autoComplete="off"
              value={requiredDate || ''}
              readOnly
              className="block w-full rounded-md border-gray-300 text-sm"
            />
          </div>
        </div>
      </div>
      <h2 className="mt-4 text-xl">Items</h2>
      <div className="my-4 flex flex-grow flex-col overflow-y-auto rounded border">
        <table className="divide-y divide-gray-200">
          <thead className="sticky top-0 z-10 bg-white">
            <th className="whitespace-nowrap bg-white p-2">Product No</th>
            <th className="p-2">Description</th>
            <th className="whitespace-nowrap p-2">Order Qty</th>
            <th className="whitespace-nowrap p-2">Qty to Split</th>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {selectedItems.map((item) => (
              <tr key={item.id}>
                <td className="w-1 p-2">{item.spirePartNumber}</td>
                <td className="p-2">{item.description}</td>
                <td className="w-1 p-2 text-right">{item.orderQuantity}</td>
                <td className="w-1 p-2 text-right">{item.splitQuantity}</td>
              </tr>
            ))}
          </tbody>
          <tfoot className="sticky bottom-0 bg-white">
            <tr>
              <th className="p-2" colSpan={2}>
                Total:
              </th>
              <th className="w-1 p-2 text-right">{totalOrderQuantity}</th>
              <th className="w-1 p-2 text-right">{totalSplitQuantity}</th>
            </tr>
          </tfoot>
        </table>
      </div>
      <div className="mt-4 text-right">
        <button
          type="button"
          className="btn-secondary !px-8 !text-lg"
          onClick={handleCloseClick}
          disabled={isSaving}
        >
          Cancel
        </button>
        <button
          type="button"
          className="btn-secondary ml-2 !px-8 !text-lg"
          onClick={handleBackClick}
          disabled={isSaving}
        >
          <Icon icon="chevron-left" />
          &nbsp;Back
        </button>
        <button
          type="button"
          className="btn-secondary ml-2 border-blue-600 !px-8 !text-lg text-blue-600 disabled:border-blue-300 disabled:text-blue-300 disabled:hover:border-blue-300 disabled:hover:text-blue-300"
          onClick={handleSaveAndNewClick}
          disabled={isSaving}
        >
          Save &amp; New Split &nbsp;
          <Icon icon="layer-group-plus" />
        </button>
        <button
          type="button"
          className="btn-secondary ml-2 border-blue-600 !px-8 !text-lg text-blue-600 disabled:border-blue-300 disabled:text-blue-300 disabled:hover:border-blue-300 disabled:hover:text-blue-300"
          onClick={handleSaveAndOpenClick}
          disabled={isSaving}
        >
          Save &amp; Open &nbsp;
          <Icon icon="floppy-disk-circle-arrow-right" />
        </button>
        <button
          type="button"
          className="btn-secondary ml-2 border-blue-600 !px-8 !text-lg text-blue-600 disabled:border-blue-300 disabled:text-blue-300 disabled:hover:border-blue-300 disabled:hover:text-blue-300"
          onClick={handleSaveAndSendClick}
          disabled={isSaving}
        >
          Save &amp; Send &nbsp;
          <Icon icon="paper-plane" />
        </button>
        <button
          type="button"
          className="btn-primary ml-2 !px-8 !text-lg"
          onClick={handleSaveClick}
          disabled={isSaving}
        >
          Save &amp; Close &nbsp;
          <Icon icon="save" />
        </button>
      </div>
    </div>
  );
}
