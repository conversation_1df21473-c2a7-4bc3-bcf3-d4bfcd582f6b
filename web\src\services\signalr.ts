import * as signalR from '@microsoft/signalr';

const url = process.env.NEXT_PUBLIC_API_URL!;

export const connection = new signalR.HubConnectionBuilder()
  .withUrl(`${url}hubs/florapack`)
  .build();

async function start() {
  try {
    connection.start();
  } catch (e) {
    console.error(e);
    setTimeout(() => start(), 5000);
  }
}

connection.onclose(async () => {
  await start();
});

start();

export const BoekestynTaskUpdateMethod = 'BoekestynTaskUpdate';
