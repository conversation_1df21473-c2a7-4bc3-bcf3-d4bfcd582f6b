import React, { Fragment, useState } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import * as HeadlessUI from '@headlessui/react';
import {
  clearError,
  clearBlanketSearchAndFilters,
  setStartDate,
  setEndDate,
  setVendorFilter,
  setCustomerFilter,
  setShipToFilter,
  setSalespersonFilter,
  setSeasonFilter,
  setPartNumberFilter,
  selectStartDate,
  selectEndDate,
  selectItemSearch,
  selectBlanketItemsSort,
  selectSortItemsDescending,
  selectIsLoading,
  selectError,
  selectBlanketItemVendors,
  selectBlanketItemCustomers,
  selectBlanketItemShipTos,
  selectBlanketItemSalespeople,
  selectBlanketItemSeasons,
  selectBlanketItemInventoryItems,
  selectFilter,
  setItemSearch,
  setBlanketItemSort,
  selectBlanketItems,
  FilterItem,
  downloadBlanketItems,
  downloadOverAndAboveReport,
} from '@/components/prebooks/prebook-list-slice';
import {
  BlanketItemWeeksItem,
  BlanketItemListItemWithWeeks,
} from '@/components/prebooks/blanket-item-weeks-item';
import { useBlanketItemsQuery } from 'api/prebooks-service';
import * as models from 'api/models/prebooks';
import { usePermissions } from '@/services/auth';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { routes } from '@/services/routes';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import { Loading } from '@/components/loading';
import { classNames } from '@/utils/class-names';
import { formatNumber } from '@/utils/format';
import { Combobox } from '@headlessui/react';
import { equals, startsWith } from '@/utils/equals';
import { handleFocus } from '@/utils/focus';

export default function Prebooks() {
  const dispatch = useAppDispatch(),
    { can } = usePermissions(),
    items = useAppSelector(selectBlanketItems),
    vendors = useAppSelector(selectBlanketItemVendors),
    customers = useAppSelector(selectBlanketItemCustomers),
    shipTos = useAppSelector(selectBlanketItemShipTos),
    salespeople = useAppSelector(selectBlanketItemSalespeople),
    seasons = useAppSelector(selectBlanketItemSeasons),
    inventoryItems = useAppSelector(selectBlanketItemInventoryItems),
    startDate = useAppSelector(selectStartDate),
    endDate = useAppSelector(selectEndDate),
    search = useAppSelector(selectItemSearch),
    sort = useAppSelector(selectBlanketItemsSort),
    sortDescending = useAppSelector(selectSortItemsDescending),
    isLoading = useAppSelector(selectIsLoading),
    error = useAppSelector(selectError),
    filter = useAppSelector(selectFilter),
    { refetch } = useBlanketItemsQuery({
      startDate,
      endDate,
    }),
    [showAdvanced, setShowAdvanced] = useState(
      !!filter.vendor ||
        !!filter.customer ||
        !!filter.shipTo ||
        !!filter.salesperson ||
        !!filter.season ||
        !!filter.blanket ||
        filter.includeSpirePurchaseOrders
    ),
    totalCases = items.reduce((total, i) => total + i.blanketQuantity, 0),
    totalCasesBooked = items.reduce((total, i) => total + i.bookedQuantity, 0),
    totalRemaining = totalCases - totalCasesBooked,
    filteredInventoryItems = search
      ? inventoryItems.filter(
          (i) =>
            startsWith(i.partNumber, search) ||
            startsWith(i.description, search)
        )
      : inventoryItems,
    hasSeasons = items.some(
      (i) => !!i.season || i.bookingItems.some((bi) => !!bi.season)
    ),
    readonly = !can('Sales Team'),
    itemsByWeek = items.reduce((memo, item) => {
      const existing = memo.find(
        (m) =>
          m.prebookId === item.prebookId &&
          m.spirePartNumber === item.spirePartNumber
      );
      if (existing) {
        existing.weeks.push(item);
      } else {
        memo.push({ ...item, weeks: [item] });
      }

      return memo;
    }, [] as BlanketItemListItemWithWeeks[]);

  const handleRefreshClick = () => {
    refetch();
  };

  const handleDownloadClick = () => {
    const args = {
      blanketItems: items,
    };
    dispatch(downloadBlanketItems(args));
  };

  const handleDownloadOverAndAboveClick = () => {
    const args = {
      startDate: startDate || null,
      endDate: endDate || null,
    };
    dispatch(downloadOverAndAboveReport(args));
  };

  const handleStartDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setStartDate(e.target.value || ''));
  };

  const handleEndDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setEndDate(e.target.value || ''));
  };

  const handleClearError = () => {
    dispatch(clearError());
  };

  const handleColumnSort = (sortProp: keyof models.BlanketItemListItem) => {
    const descending = sortProp === sort ? !sortDescending : false;
    dispatch(
      setBlanketItemSort({ sort: sortProp, sortDescending: descending })
    );
  };

  const handleVendorFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setVendorFilter(e.target.value || null));
  };

  const handleCustomerFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setCustomerFilter(e.target.value || null));
  };

  const handleShipToFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setShipToFilter(e.target.value || null));
  };

  const handleSalespersonFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setSalespersonFilter(e.target.value || null));
  };

  const handleSeasonFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setSeasonFilter(e.target.value || null));
  };

  const handlePartNumberFilterChange = (value: FilterItem) => {
    dispatch(setPartNumberFilter(value?.partNumber || null));
    if (value?.partNumber) {
      dispatch(setItemSearch(value.partNumber));
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    dispatch(setItemSearch(value));
    const item = inventoryItems.find(
      (i) => equals(value, i.partNumber) || equals(value, i.description)
    );
    dispatch(setPartNumberFilter(item?.partNumber || null));
  };

  const handleToggleAdvanced = () => {
    setShowAdvanced(!showAdvanced);
    if (showAdvanced) {
      dispatch(setVendorFilter(null));
      dispatch(setCustomerFilter(null));
      dispatch(setShipToFilter(null));
      dispatch(setSalespersonFilter(null));
      dispatch(setSeasonFilter(null));
    }
  };

  const handleResetSearchClick = async () => {
    await dispatch(clearBlanketSearchAndFilters());
    window.setTimeout(refetch);
  };

  interface HeaderButtonProps {
    text: string;
    propName: keyof models.BlanketItemListItem;
    sortPropName?: keyof models.BlanketItemListItem;
  }
  const HeaderButton = ({
    text,
    propName,
    sortPropName,
  }: HeaderButtonProps) => (
    <button
      type="button"
      className="group inline-flex"
      onClick={() => handleColumnSort(sortPropName || propName)}
    >
      {text}
      <span
        className={classNames(
          'ml-2 flex-none rounded text-gray-400 group-hover:visible group-focus:visible',
          sort !== (sortPropName || propName) && 'invisible'
        )}
      >
        <Icon
          icon={sortDescending ? 'chevron-down' : 'chevron-up'}
          className="h-5 w-5"
          aria-hidden="true"
        />
      </span>
    </button>
  );

  return (
    <>
      <Head>
        <title>Prebook Blanket Item List</title>
      </Head>
      <header className="bg-white shadow">
        <div className="grid grid-cols-1">
          <div className="mb-4 border-b shadow">
            <div className="mx-auto flex max-w-7xl items-center justify-between px-8">
              <div className="flex">
                <nav className="ml-24 flex flex-row">
                  <Link
                    href={routes.prebooks.list.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Prebook List
                  </Link>
                  <Link
                    href={routes.prebooks.items.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Prebook Items
                  </Link>
                  <div className="group relative my-2 text-nowrap rounded-lg bg-blue-500 p-2 text-center text-sm font-medium text-white">
                    Blanket Prebooks
                  </div>
                </nav>
              </div>
            </div>
          </div>
          <div className="bg-gray-100 py-2">
            <div className="mx-auto flex max-w-7xl items-center justify-between px-8">
              <div className="flex flex-grow items-center align-top">
                <div className="flex w-full flex-col p-2">
                  <div className="grid w-full grid-cols-8 gap-2 rounded-sm text-xs">
                    <div>
                      <label htmlFor="start-date">From Date</label>
                      <input
                        type="date"
                        max="2050-01-01"
                        id="start-date"
                        value={startDate}
                        onChange={handleStartDateChange}
                        className="w-full !max-w-none text-xs"
                      />
                    </div>
                    <div>
                      <label htmlFor="end-date">To Date</label>
                      <input
                        type="date"
                        max="2050-01-01"
                        id="end-date"
                        value={endDate}
                        onChange={handleEndDateChange}
                        className="w-full !max-w-none text-xs"
                      />
                    </div>
                    <div className="col-span-3">
                      <Combobox
                        as="div"
                        onChange={handlePartNumberFilterChange}
                        value={{ partNumber: search, description: search }}
                      >
                        <Combobox.Label>Product</Combobox.Label>
                        <div className="relative">
                          <div className="flex flex-row">
                            <Combobox.Input
                              type="search"
                              autoComplete="off"
                              className="w-full !text-xs"
                              placeholder="Item Search"
                              onChange={handleSearchChange}
                              onFocus={handleFocus}
                              displayValue={() => search}
                            />
                            <button
                              type="button"
                              className="btn-secondary flex px-1 focus:ring-0"
                              onClick={handleToggleAdvanced}
                            >
                              <Icon
                                icon={
                                  showAdvanced ? 'chevron-up' : 'chevron-down'
                                }
                                className="h-5 w-5"
                              />
                            </button>
                            <button
                              type="button"
                              className="btn-secondary flex px-1 focus:ring-0"
                              title="Reset Search Filters"
                              onClick={handleResetSearchClick}
                            >
                              <Icon
                                icon="magnifying-glass-arrows-rotate"
                                className="h-5 w-5"
                              />
                            </button>
                          </div>

                          {filteredInventoryItems.length > 0 && (
                            <Combobox.Options className="absolute z-10 mt-1 max-h-60 overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                              <Combobox.Option
                                value={{ partNumber: '', description: '' }}
                                className={({ active }) =>
                                  classNames(
                                    'relative cursor-default select-none py-2 pl-3 pr-9 text-xs',
                                    active
                                      ? 'bg-blue-600 text-white'
                                      : 'text-gray-900'
                                  )
                                }
                              >
                                <>
                                  <span className="block truncate">
                                    All Items
                                  </span>
                                </>
                              </Combobox.Option>
                              {filteredInventoryItems.map((item) => (
                                <Combobox.Option
                                  key={item.partNumber}
                                  value={item}
                                  className={({ active }) =>
                                    classNames(
                                      'relative cursor-default select-none py-2 pl-3 pr-9 text-xs',
                                      active
                                        ? 'bg-blue-600 text-white'
                                        : 'text-gray-900'
                                    )
                                  }
                                >
                                  <>
                                    <span className="block truncate">
                                      {item.partNumber}
                                      <br />
                                      <div className="italic text-gray-400">
                                        {item.description}
                                      </div>
                                    </span>
                                  </>
                                </Combobox.Option>
                              ))}
                            </Combobox.Options>
                          )}
                        </div>
                      </Combobox>
                    </div>
                    <div className="flex items-start justify-center pt-3">
                      <button
                        type="button"
                        onClick={handleRefreshClick}
                        className="btn-secondary flex p-3 text-blue-700"
                      >
                        <Icon icon="refresh" spin={isLoading} />
                      </button>
                      <button
                        type="button"
                        onClick={handleDownloadClick}
                        className="btn-secondary flex p-3 text-green-700"
                      >
                        <Icon icon="file-excel" />
                      </button>
                      <HeadlessUI.Menu as="div" className="relative">
                        <HeadlessUI.Menu.Button className="btn-secondary flex p-3">
                          <Icon icon="chevron-down" />
                        </HeadlessUI.Menu.Button>
                        <HeadlessUI.Transition
                          as={Fragment}
                          enter="transition ease-out duration-100"
                          enterFrom="transform opacity-0 scale-95"
                          enterTo="transform opacity-100 scale-100"
                          leave="transition ease-in duration-75"
                          leaveFrom="transform opacity-100 scale-100"
                          leaveTo="transform opacity-0 scale-95"
                        >
                          <HeadlessUI.Menu.Items className="absolute right-0 z-40 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                            <HeadlessUI.Menu.Item>
                              {({ active }) => (
                                <button
                                  type="button"
                                  className={classNames(
                                    'block w-full px-4 py-2 text-left text-sm no-underline hover:no-underline',
                                    active
                                      ? 'bg-gray-100 text-blue-900'
                                      : 'text-gray-700'
                                  )}
                                  onClick={handleDownloadOverAndAboveClick}
                                >
                                  <Icon icon="file-archive" fixedWidth />
                                  &nbsp; Over &amp; Above Report
                                </button>
                              )}
                            </HeadlessUI.Menu.Item>
                          </HeadlessUI.Menu.Items>
                        </HeadlessUI.Transition>
                      </HeadlessUI.Menu>
                    </div>
                    {!readonly && (
                      <div className="ml-8 flex items-start justify-center pt-3">
                        <Link
                          href={`${routes.prebooks.new.to()}?blanket=true`}
                          className="btn-new flex flex-nowrap"
                        >
                          <Icon icon="plus-circle" className="flex" />
                          &nbsp;
                          <div className="flex whitespace-nowrap">
                            New Blanket Prebook
                          </div>
                        </Link>
                      </div>
                    )}
                    {showAdvanced && (
                      <>
                        <div className="col-start-1">
                          <label htmlFor="vendor-filter">Vendor</label>
                          <select
                            id="vendor-filter"
                            value={filter.vendor || ''}
                            onChange={handleVendorFilterChange}
                            className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                          >
                            <option value="">All Vendors</option>
                            {vendors.map((vendor) => (
                              <option key={vendor}>{vendor}</option>
                            ))}
                          </select>
                        </div>
                        <div>
                          <label htmlFor="customer-filter">Customer</label>
                          <select
                            id="customer-filter"
                            value={filter.customer || ''}
                            onChange={handleCustomerFilterChange}
                            className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                          >
                            <option value="">All Customers</option>
                            {customers.map((customer) => (
                              <option key={customer}>{customer}</option>
                            ))}
                          </select>
                        </div>
                        <div>
                          <label htmlFor="ship-to-filter">Ship To</label>
                          <select
                            id="ship-to-filter"
                            value={filter.shipTo || ''}
                            onChange={handleShipToFilterChange}
                            className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                          >
                            <option value="">All Ship Tos</option>
                            {shipTos.map((shipTo) => (
                              <option key={shipTo}>{shipTo}</option>
                            ))}
                          </select>
                        </div>
                        <div>
                          <label htmlFor="salesperson-filter">
                            Salesperson
                          </label>
                          <select
                            id="salesperson-filter"
                            value={filter.salesperson || ''}
                            onChange={handleSalespersonFilterChange}
                            className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                          >
                            <option value="">All Salespeople</option>
                            {salespeople.map((salesperson) => (
                              <option key={salesperson}>{salesperson}</option>
                            ))}
                          </select>
                        </div>
                        <div>
                          <label htmlFor="season-filter">Season</label>
                          <select
                            id="season-filter"
                            value={filter.season || ''}
                            onChange={handleSeasonFilterChange}
                            className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                          >
                            <option value="">All Seasons</option>
                            {seasons.map((season) => (
                              <option key={season}>{season}</option>
                            ))}
                          </select>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>
      <main className="flex-grow overflow-auto">
        <div className="mx-auto h-full px-8">
          <Error error={error} clear={handleClearError} />
          {isLoading && <Loading />}
          <div className="mt-8 flex h-full flex-col">
            <div className="-mx-8 -my-2 h-full">
              <div className="inline-block min-w-full px-8 py-2 align-middle">
                <div className="rounded-lg shadow ring-1 ring-black ring-opacity-5">
                  <table className="min-w-full divide-y divide-gray-300">
                    <thead>
                      <tr className="sticky top-0 z-10">
                        <th
                          colSpan={3}
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          &nbsp;
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          <HeaderButton text="Vendor" propName="vendor" />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          <HeaderButton
                            text="Product"
                            propName="spirePartNumber"
                          />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-center text-sm font-semibold text-gray-900"
                        >
                          <HeaderButton text="Date" propName="requiredDate" />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          <HeaderButton
                            text="Salesperson"
                            propName="salesperson"
                          />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          <HeaderButton text="Customer" propName="customer" />
                          <span className="mr-7">&nbsp;/&nbsp;</span>
                          <HeaderButton text="Ship To" propName="shipTo" />
                        </th>
                        {hasSeasons && (
                          <th
                            scope="col"
                            className="bg-gray-100 px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                          >
                            <HeaderButton text="Season" propName="season" />
                          </th>
                        )}
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-center text-sm font-semibold text-gray-900"
                        >
                          Blanket Qty
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-center text-sm font-semibold text-gray-900"
                        >
                          Booked Qty
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-center text-sm font-semibold text-gray-900"
                        >
                          Remaining
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white">
                      {itemsByWeek.map((item) => (
                        <BlanketItemWeeksItem
                          key={item.id}
                          item={item}
                          hasSeasons={hasSeasons}
                          refresh={refetch}
                        />
                      ))}
                    </tbody>
                    <tfoot>
                      <tr>
                        <th
                          colSpan={hasSeasons ? 9 : 8}
                          className="whitespace-nowrap bg-gray-100 px-3 py-4 text-right text-sm text-gray-700"
                        >
                          Total:
                        </th>
                        <th className="whitespace-nowrap bg-gray-100 px-3 py-4 text-center text-sm text-gray-700">
                          {formatNumber(totalCases)}
                        </th>
                        <th className="whitespace-nowrap bg-gray-100 px-3 py-4 text-center text-sm text-gray-700">
                          {formatNumber(totalCasesBooked)}
                        </th>
                        <th className="whitespace-nowrap bg-gray-100 px-3 py-4 text-center text-sm text-gray-700">
                          {formatNumber(totalRemaining)}
                        </th>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </>
  );
}
