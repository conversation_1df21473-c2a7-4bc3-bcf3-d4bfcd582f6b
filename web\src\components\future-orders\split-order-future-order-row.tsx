import * as futureOrders from 'api/models/future-orders';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { classNames } from '@/utils/class-names';
import { formatDate, formatNumber } from '@/utils/format';
import { getDetail, selectFutureOrderDetail } from './split-order-slice';

interface SplitOrderFutureOrderRowProps {
  futureOrder: futureOrders.FutureOrderSummaryItem;
}

export function SplitOrderFutureOrderRow({
  futureOrder,
}: SplitOrderFutureOrderRowProps) {
  const dispatch = useAppDispatch(),
    selected = useAppSelector(selectFutureOrderDetail),
    isSelected = futureOrder.futureOrderId === selected?.id;

  const handleRowClick = () => {
    dispatch(getDetail(futureOrder.futureOrderId));
  };

  return (
    <tr
      onClick={handleRowClick}
      className={classNames(
        'cursor-pointer border-b-2 border-gray-100',
        isSelected ? 'bg-blue-700 text-white' : 'hover:bg-gray-100'
      )}
    >
      <td className="truncate p-2 text-center align-top">
        {formatNumber(futureOrder.futureOrderId, '00000')}
      </td>
      <td className="truncate p-2 align-top">{futureOrder.customer}</td>
      <td className="truncate p-2 align-top">{futureOrder.shipTo}</td>
      <td className="truncate p-2 text-center align-top">
        {formatDate(futureOrder.date)}
      </td>
    </tr>
  );
}
