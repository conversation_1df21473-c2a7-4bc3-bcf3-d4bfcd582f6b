import Head from 'next/head';
import Link from 'next/link';
import { usePermissions } from '@/services/auth';
import { routes } from '@/services/routes';
import { Icon } from '@/components/icon';

export default function Settings() {
  const { can } = usePermissions(),
    isAdmin = can('Administrators');

  return (
    <>
      <Head>
        <title>Settings</title>
      </Head>
      <header className="sticky top-0 z-20 flex-wrap bg-white shadow">
        <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 md:flex md:items-center md:justify-between lg:px-8">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight md:flex-shrink-0">
            <Icon icon="cogs" />
            &nbsp; Settings
          </h2>
        </div>
      </header>
      <main>
        <div className="mx-auto max-w-7xl px-8 py-6">
          <form className="grid grid-cols-4 gap-6">
            <Link
              className="btn-secondary h-48 w-64 rounded border text-center align-middle text-4xl shadow"
              href={routes.settings.customers.to()}
            >
              <h1 className="my-auto text-center align-middle">
                Customer
                <br />
                Settings
                <Icon icon="user-tie" className="mx-auto mt-4 !block" />
              </h1>
            </Link>
            <Link
              className="btn-secondary h-48 w-64 rounded border text-center align-middle text-4xl shadow"
              href={routes.settings.productDefaults.to()}
            >
              <h1 className="my-auto text-center align-middle">
                Product Defaults
                <Icon icon="boxes-stacked" className="mx-auto mt-4 !block" />
              </h1>
            </Link>
            <Link
              className="btn-secondary h-48 w-64 rounded border text-center align-middle text-4xl shadow"
              href={routes.settings.seasons.to()}
            >
              <h1 className="my-auto text-center align-middle">
                Seasons
                <Icon icon="calendar-day" className="mx-auto mt-4 !block" />
              </h1>
            </Link>
            <Link
              className="btn-secondary h-48 w-64 rounded border text-center align-middle text-4xl shadow"
              href={routes.settings.spire.to()}
            >
              <h1 className="my-auto text-center align-middle">
                Spire Part Numbers
                <Icon icon="s" className="mx-auto mt-4 !block" />
              </h1>
            </Link>
            <Link
              className="btn-secondary h-48 w-64 rounded border text-center align-middle text-4xl shadow"
              href={routes.settings.upgradeOptions.to()}
            >
              <h1 className="my-auto text-center align-middle">
                Upgrade Options
                <Icon icon="hat-beach" className="mx-auto mt-4 !block" />
              </h1>
            </Link>
            {isAdmin && (
              <Link
                className="btn-secondary h-48 w-64 rounded border text-center align-middle text-4xl shadow"
                href={routes.settings.users.to()}
              >
                <h1 className="my-auto text-center align-middle">
                  User
                  <br />
                  Settings
                  <Icon icon="users" className="mx-auto mt-4 !block" />
                </h1>
              </Link>
            )}
            <Link
              className="btn-secondary h-48 w-64 rounded border text-center align-middle text-4xl shadow"
              href={routes.settings.defaultVendorOverrides.to()}
            >
              <h1 className="my-auto text-center align-middle">
                Default Vendor
                <br />
                Overrides
                <Icon
                  icon="calendar-circle-user"
                  className="mx-auto mt-4 !block"
                />
              </h1>
            </Link>
            <Link
              className="btn-secondary h-48 w-64 rounded border text-center align-middle text-4xl shadow"
              href={routes.settings.freightRates.to()}
            >
              <h1 className="my-auto text-center align-middle">
                Default
                <br />
                Freight Rates
                <Icon icon="truck-fast" className="mx-auto mt-4 !block" />
              </h1>
            </Link>
          </form>
        </div>
      </main>
    </>
  );
}
