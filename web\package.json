{"name": "flora-pack-web", "version": "1.0.2", "private": true, "dependencies": {"@azure/msal-browser": "^2.35.0", "@azure/msal-react": "^1.5.5", "@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/pro-light-svg-icons": "^6.6.0", "@fortawesome/pro-solid-svg-icons": "^6.6.0", "@fortawesome/react-fontawesome": "^0.2.2", "@headlessui/react": "^1.7.13", "@microsoft/signalr": "^8.0.0", "@reduxjs/toolkit": "^1.9.3", "@tailwindcss/forms": "^0.5.3", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "@tiptap/extension-underline": "^2.8.0", "@tiptap/pm": "^2.8.0", "@tiptap/react": "^2.8.0", "@tiptap/starter-kit": "^2.8.0", "@types/jest": "^27.5.2", "@types/node": "^17.0.45", "@types/numeral": "^2.0.2", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "autoprefixer": "^10.4.12", "axios": "^1.1.3", "dequal": "^2.0.3", "dnd-core": "^16.0.1", "eslint-config-next": "^14.2.25", "flora-pack-web": "file:", "luxon": "^3.1.0", "moment": "^2.30.1", "next": "^14.2.25", "numeral": "^2.0.6", "postcss": "^8.4.18", "postcss-import": "^14.1.0", "react": "^18.3.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-hotkeys": "^2.0.0", "react-redux": "^8.0.4", "serialize-error": "^11.0.0", "swr": "^1.3.0", "tailwindcss": "^3.4.1", "typescript": "^5.6.3", "use-debounce": "^10.0.4", "web-vitals": "^2.1.4", "xlsx": "^0.18.5"}, "scripts": {"dev": "next dev -p 3050", "build": "next build", "export": "next build && next export -o ../../Deploy/Web", "start": "next start", "lint": "next lint"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/luxon": "^3.0.2", "eslint-config-prettier": "^9.1.0", "prettier": "^2.7.1", "prettier-plugin-tailwindcss": "^0.1.13"}}