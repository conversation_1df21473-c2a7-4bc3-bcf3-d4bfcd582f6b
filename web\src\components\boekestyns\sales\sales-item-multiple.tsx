import { useAppSelector } from '@/services/hooks';
import { formatNumber } from '@/utils/format';
import { equals } from '@/utils/equals';
import { Week } from '@/utils/weeks';
import { selectMultipleProductItems, selectPlants } from './sales-slice';
import { boekCaseQuantity } from './boekestyn-sales-functions';

interface SalesItemMultipleProps {
  week: Week;
  customer: string;
}

export function SalesItemMultiple({ week, customer }: SalesItemMultipleProps) {
  const prebookItems = useAppSelector(selectMultipleProductItems),
    plants = useAppSelector(selectPlants),
    quantity = prebookItems
      .filter(
        (p) =>
          p.week === week.week &&
          p.year === week.year &&
          equals(p.boekestynCustomerAbbreviation, customer)
      )
      .reduce((total, p) => total + boekCaseQuantity(plants, p), 0);

  return <th className="border font-semibold">{formatNumber(quantity)}</th>;
}
