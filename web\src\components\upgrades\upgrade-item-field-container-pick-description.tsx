import React, { useState, useRef, Fragment, useMemo } from 'react';
import * as HeadlessUI from '@headlessui/react';
import * as futureOrders from 'api/models/future-orders';
import * as settings from 'api/models/settings';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { Icon } from '@/components/icon';
import { classNames } from '@/utils/class-names';
import { handleFocus } from '@/utils/focus';
import {
  SetItemArgs,
  setItemPropertyValue,
  selectUpgradeOptions,
} from './upgrade-item-list-slice';
import { ItemUpgradeOptionDialog } from './item-upgrade-option-dialog';

interface UpgradeItemContainerPickDescriptionProps {
  item: futureOrders.UpgradeItem;
}

export function UpgradeItemContainerPickDescription({
  item,
}: UpgradeItemContainerPickDescriptionProps) {
  const dispatch = useAppDispatch(),
    upgradeOptions = useAppSelector(selectUpgradeOptions),
    [editing, setEditing] = useState(false),
    [editValue, setEditValue] = useState(''),
    [showEditDialog, setShowEditDialog] = useState(false),
    editMultilineRef = useRef<HTMLTextAreaElement>(null),
    tariffCode = useMemo(() => {
      return (
        upgradeOptions.find(
          (option) =>
            option.containerPickDescription === item.containerPickDescription
        )?.tariffCode || null
      );
    }, [upgradeOptions, item.containerPickDescription]),
    id = item.id;

  const handleEditClick = () => {
    setEditing(true);
    setEditValue(item.containerPickDescription || '');

    window.setTimeout(() => {
      editMultilineRef.current?.focus();
    }, 100);
  };

  const handleCancelClick = () => {
    setEditing(false);
  };

  const handleSaveClick = () => {
    const value = editValue,
      args: SetItemArgs = { id, propName: 'containerPickDescription', value };
    dispatch(setItemPropertyValue(args));
    setEditing(false);
  };

  const handleEditChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setEditValue(e.target.value);
  };

  const handleEditKeyUp = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      e.preventDefault();
      handleCancelClick();
    }
  };

  const handleSetUpgradeOptions = async (option: settings.UpgradeOption) => {
    const args1: SetItemArgs = {
        id,
        propName: 'containerPickDescription',
        value: option.containerPickDescription,
      },
      args2: SetItemArgs = {
        id,
        propName: 'origins',
        value: option.origins,
      },
      args3: SetItemArgs = {
        id,
        propName: 'costs',
        value: option.costs,
      };

    await dispatch(setItemPropertyValue(args1));
    await dispatch(setItemPropertyValue(args2));
    await dispatch(setItemPropertyValue(args3));
  };

  const handleSaveOptionClick = () => {
    setShowEditDialog(true);
  };

  const handleEditDialogClose = (success?: boolean) => {
    setShowEditDialog(false);
    if (success) {
      setEditing(false);
    }
  };

  if (editing) {
    return (
      <div className="flex flex-grow rounded-md">
        <div className="relative flex flex-grow focus-within:z-20">
          <textarea
            rows={3}
            ref={editMultilineRef}
            value={editValue}
            onChange={handleEditChange}
            onKeyUp={handleEditKeyUp}
            onFocus={handleFocus}
            className="flex flex-grow rounded border-0 py-0 text-xs text-gray-900 ring-1 ring-inset ring-gray-300 focus:border-blue-500"
          ></textarea>
        </div>
        <div className="grid grid-cols-2 self-start">
          <button
            type="button"
            className="btn-secondary flex px-2 py-1"
            onClick={handleCancelClick}
          >
            <Icon icon="undo" />
          </button>
          <button
            type="button"
            className="btn-secondary flex border-green-700 px-2 py-1"
          >
            <Icon
              icon="check"
              className="text-green-900"
              onClick={handleSaveClick}
            />
          </button>
          <button
            type="button"
            onClick={handleSaveOptionClick}
            className="btn-secondary col-span-2 mt-1 flex w-full border-blue-500 px-2 py-1 text-blue-500"
          >
            Save &hellip;
          </button>
        </div>
        {showEditDialog && (
          <ItemUpgradeOptionDialog
            item={item}
            value={editValue}
            onClose={handleEditDialogClose}
          />
        )}
      </div>
    );
  }

  return (
    <div>
      <div className="grid grid-cols-4">
        <div
          className={classNames(
            item.containerPickDescription && 'block border-b-2 border-dotted',
            'col-span-3 block w-full max-w-xs cursor-pointer truncate whitespace-pre border-gray-300 hover:border-gray-500 hover:bg-yellow-100'
          )}
          onClick={handleEditClick}
        >
          {item.containerPickDescription}
          {!item.containerPickDescription && (
            <div className="mx-auto inline-block border-b-2 border-dotted">
              <Icon icon="plus" />
            </div>
          )}
        </div>
        <div className="text-left">
          {!!upgradeOptions.length && (
            <HeadlessUI.Menu
              as="div"
              className="relative inline-block text-left"
            >
              <div>
                <HeadlessUI.Menu.Button className="btn-secondary border-transparent p-1">
                  <Icon icon="chevron-down" />
                </HeadlessUI.Menu.Button>
              </div>

              <HeadlessUI.Transition
                as={Fragment}
                enter="transition ease-out duration-100"
                enterFrom="transform opacity-0 scale-95"
                enterTo="transform opacity-100 scale-100"
                leave="transition ease-in duration-75"
                leaveFrom="transform opacity-100 scale-100"
                leaveTo="transform opacity-0 scale-95"
              >
                <HeadlessUI.Menu.Items className="absolute right-0 z-20 mt-2 w-96 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                  <div className="max-h-80 overflow-y-auto py-1">
                    {upgradeOptions.map((upgradeOption) => (
                      <HeadlessUI.Menu.Item key={upgradeOption.id}>
                        {({ active }) => (
                          <button
                            type="button"
                            onClick={() =>
                              handleSetUpgradeOptions(upgradeOption)
                            }
                            className={classNames(
                              active
                                ? 'bg-gray-100 text-gray-900'
                                : 'text-gray-700',
                              'block w-full truncate px-4 py-2 text-left text-sm'
                            )}
                          >
                            {upgradeOption.containerPickDescription}
                          </button>
                        )}
                      </HeadlessUI.Menu.Item>
                    ))}
                  </div>
                </HeadlessUI.Menu.Items>
              </HeadlessUI.Transition>
            </HeadlessUI.Menu>
          )}
        </div>
      </div>
      {!!tariffCode && (
        <div className="text-left text-xs italic text-gray-500">
          {tariffCode}
        </div>
      )}
    </div>
  );
}
