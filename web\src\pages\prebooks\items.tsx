import React, { Fragment, useState } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import {
  clearError,
  clearItemSearchAndFilters,
  setStartDate,
  setEndDate,
  setVendorFilter,
  setCustomerFilter,
  setShipToFilter,
  setSalespersonFilter,
  setSeasonFilter,
  setPartNumberFilter,
  selectStartDate,
  selectEndDate,
  selectItemSearch,
  selectItemsSort,
  selectSortItemsDescending,
  selectIsLoading,
  selectError,
  selectItemVendors,
  selectItemCustomers,
  selectItemShipTos,
  selectItemSalespeople,
  selectItemSeasons,
  selectItemInventoryItems,
  selectFilter,
  selectItemOptions,
  setGroupByCustomerItemOption,
  setGroupByShipToItemOption,
  setGroupByVendorItemOption,
  setGroupBySalespersonItemOption,
  setGroupBySeasonItemOption,
  setGroupByIsBlanketItemOption,
  setDateGroupingItemOption,
  setItemSearch,
  setItemSort,
  selectItemSummary,
  FilterItem,
  downloadItemSummary,
  setBlanketFilter,
  BlanketFilter,
} from '../../components/prebooks/prebook-list-slice';
import { DateGrouping, useItemSummaryQuery } from 'api/prebooks-service';
import * as models from 'api/models/prebooks';
import { usePermissions } from '@/services/auth';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { routes } from '@/services/routes';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import { Loading } from '@/components/loading';
import { classNames } from '@/utils/class-names';
import { formatDate, formatNumber, parseQuantity } from '@/utils/format';
import { Combobox, Menu, Transition } from '@headlessui/react';
import { contains, equals } from '@/utils/equals';
import { handleFocus } from '@/utils/focus';

const thisYear = new Date().getFullYear();

export default function Prebooks() {
  const dispatch = useAppDispatch(),
    { can } = usePermissions(),
    items = useAppSelector(selectItemSummary),
    vendors = useAppSelector(selectItemVendors),
    customers = useAppSelector(selectItemCustomers),
    shipTos = useAppSelector(selectItemShipTos),
    salespeople = useAppSelector(selectItemSalespeople),
    seasons = useAppSelector(selectItemSeasons),
    inventoryItems = useAppSelector(selectItemInventoryItems),
    startDate = useAppSelector(selectStartDate),
    endDate = useAppSelector(selectEndDate),
    search = useAppSelector(selectItemSearch),
    sort = useAppSelector(selectItemsSort),
    sortDescending = useAppSelector(selectSortItemsDescending),
    isLoading = useAppSelector(selectIsLoading),
    error = useAppSelector(selectError),
    filter = useAppSelector(selectFilter),
    {
      groupByCustomer,
      groupByShipTo,
      groupByVendor,
      groupBySalesperson,
      groupBySeason,
      groupByIsBlanket,
      dateGrouping,
    } = useAppSelector(selectItemOptions),
    { refetch } = useItemSummaryQuery({
      startDate,
      endDate,
      groupByCustomer,
      groupByShipTo,
      groupByVendor,
      groupBySalesperson,
      groupBySeason,
      groupByIsBlanket,
      dateGrouping,
    }),
    [showColumnsMenu, setShowColumnsMenu] = useState(false),
    totalCases = items.reduce((total, i) => total + i.orderQuantity, 0),
    filteredInventoryItems = search
      ? inventoryItems.filter(
          (i) =>
            contains(i.partNumber, search) || contains(i.description, search)
        )
      : inventoryItems,
    readonly = !can('Sales Team');

  const handleRefreshClick = () => {
    refetch();
  };

  const handleDownloadClick = () => {
    const args = {
      startDate: startDate || null,
      endDate: endDate || null,
      groupByCustomer,
      groupByShipTo,
      groupByVendor,
      groupBySalesperson,
      groupBySeason,
      groupByIsBlanket,
      dateGrouping,
      items,
    };
    dispatch(downloadItemSummary(args));
  };

  const handleStartDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setStartDate(e.target.value || ''));
  };

  const handleEndDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setEndDate(e.target.value || ''));
  };

  const handleResetSearchClick = async () => {
    await dispatch(clearItemSearchAndFilters());
    window.setTimeout(refetch);
  };

  const handleDateGroupingChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    const value = e.target.value as DateGrouping;
    dispatch(setDateGroupingItemOption(value));
  };

  const toggleColumnsMenu = () => {
    setShowColumnsMenu(!showColumnsMenu);
  };

  const handleShowDateColumnChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const grouping = e.target.checked ? 'Date' : 'None';
    dispatch(setDateGroupingItemOption(grouping));
  };

  const handleShowVendorColumnChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    dispatch(setGroupByVendorItemOption(e.target.checked));
  };

  const handleShowSalespersonColumnChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    dispatch(setGroupBySalespersonItemOption(e.target.checked));
  };

  const handleShowCustomerColumnChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    dispatch(setGroupByCustomerItemOption(e.target.checked));
  };

  const handleShowShipToColumnChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    dispatch(setGroupByShipToItemOption(e.target.checked));
  };

  const handleShowSeasonColumnChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    dispatch(setGroupBySeasonItemOption(e.target.checked));
  };

  const handleShowBlanketColumnChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    dispatch(setGroupByIsBlanketItemOption(e.target.checked));
  };

  const handleClearError = () => {
    dispatch(clearError());
  };

  const handleColumnSort = (sortProp: keyof models.PrebookSummaryItem) => {
    const descending = sortProp === sort ? !sortDescending : false;
    dispatch(setItemSort({ sort: sortProp, sortDescending: descending }));
  };

  const handleVendorFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setVendorFilter(e.target.value || null));
  };

  const handleCustomerFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setCustomerFilter(e.target.value || null));
  };

  const handleShipToFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setShipToFilter(e.target.value || null));
  };

  const handleSalespersonFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setSalespersonFilter(e.target.value || null));
  };

  const handleSeasonFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setSeasonFilter(e.target.value || null));
  };

  const handleBlanketFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setBlanketFilter(e.target.value as BlanketFilter));
  };

  const handlePartNumberFilterChange = (value: FilterItem) => {
    dispatch(setPartNumberFilter(value?.partNumber || null));
    if (value?.partNumber) {
      dispatch(setItemSearch(value.partNumber));
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    dispatch(setItemSearch(value));
    const item = inventoryItems.find(
      (i) => equals(value, i.partNumber) || equals(value, i.description)
    );
    dispatch(setPartNumberFilter(item?.partNumber || null));
  };

  interface HeaderButtonProps {
    text: string;
    propName: keyof models.PrebookSummaryItem;
    sortPropName?: keyof models.PrebookSummaryItem;
  }
  const HeaderButton = ({
    text,
    propName,
    sortPropName,
  }: HeaderButtonProps) => (
    <button
      type="button"
      className="group inline-flex"
      onClick={() => handleColumnSort(sortPropName || propName)}
    >
      {text}
      <span
        className={classNames(
          'ml-2 flex-none rounded text-gray-400 group-hover:visible group-focus:visible',
          sort !== (sortPropName || propName) && 'invisible'
        )}
      >
        <Icon
          icon={sortDescending ? 'chevron-down' : 'chevron-up'}
          className="h-5 w-5"
          aria-hidden="true"
        />
      </span>
    </button>
  );

  return (
    <>
      <Head>
        <title>Prebook Item List</title>
      </Head>
      <header className="bg-white shadow">
        <div className="grid grid-cols-1">
          <div className="mb-4 border-b shadow">
            <div className="mx-auto flex max-w-7xl items-center justify-between px-8">
              <div className="flex">
                <nav className="ml-24 flex flex-row">
                  <Link
                    href={routes.prebooks.list.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Prebook List
                  </Link>
                  <div className="group relative my-2 text-nowrap rounded-lg bg-blue-500 p-2 text-center text-sm font-medium text-white">
                    Prebook Items
                  </div>
                  <Link
                    href={routes.prebooks.blanketItems.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Blanket Prebooks
                  </Link>
                </nav>
              </div>
            </div>
          </div>
          <div className="bg-gray-100 py-2">
            <div className="mx-auto flex max-w-7xl items-center justify-between px-8">
              <div className="flex flex-grow items-center align-top">
                <div className="flex w-full flex-col p-2">
                  <div className="grid w-full grid-cols-8 gap-2 rounded-sm text-xs">
                    <div>
                      <label htmlFor="start-date">From Date</label>
                      <input
                        type="date"
                        max="2050-01-01"
                        id="start-date"
                        value={startDate}
                        onChange={handleStartDateChange}
                        className="w-full !max-w-none text-xs"
                      />
                    </div>
                    <div>
                      <label htmlFor="end-date">To Date</label>
                      <input
                        type="date"
                        max="2050-01-01"
                        id="end-date"
                        value={endDate}
                        onChange={handleEndDateChange}
                        className="w-full !max-w-none text-xs"
                      />
                    </div>
                    <div>
                      <label htmlFor="date-grouping">Group Dates By</label>
                      <select
                        id="date-grouping"
                        value={dateGrouping}
                        onChange={handleDateGroupingChange}
                        className="block w-full rounded-md border-gray-300 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                      >
                        <option value="Date">Date</option>
                        <option value="Week">Week</option>
                        <option value="Month">Month</option>
                        <option value="Year">Year</option>
                      </select>
                    </div>
                    <div>
                      <label htmlFor="columns">Columns to Show</label>
                      <Menu as="div" className="relative block text-left">
                        <>
                          <div>
                            <button
                              type="button"
                              onClick={toggleColumnsMenu}
                              className="inline-flex w-full justify-between rounded-md border border-gray-300 bg-white px-4 py-2 text-xs shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-0"
                            >
                              Columns
                              <Icon
                                icon="chevron-down"
                                className="-mr-1 ml-2 h-5 w-5"
                                aria-hidden="true"
                              />
                            </button>
                          </div>

                          {showColumnsMenu && (
                            <Transition
                              as={Fragment}
                              show={showColumnsMenu}
                              enter="transition ease-out duration-100"
                              enterFrom="transform opacity-0 scale-95"
                              enterTo="transform opacity-100 scale-100"
                              leave="transition ease-in duration-75"
                              leaveFrom="transform opacity-100 scale-100"
                              leaveTo="transform opacity-0 scale-95"
                            >
                              <Menu.Items
                                static
                                className="absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
                              >
                                <div className="py-1">
                                  <Menu.Item>
                                    <div className="relative flex items-start px-4 py-2">
                                      <div className="min-w-0 flex-1 text-sm">
                                        <label htmlFor="columns-date">
                                          Date
                                        </label>
                                      </div>
                                      <div className="ml-3 flex h-5 items-center">
                                        <input
                                          type="checkbox"
                                          checked={dateGrouping !== 'None'}
                                          onChange={handleShowDateColumnChange}
                                          className="focus:rind-blue-500 h-4 w-4 rounded border-gray-300 text-blue-600"
                                        />
                                      </div>
                                    </div>
                                  </Menu.Item>
                                  <Menu.Item>
                                    <div className="relative flex items-start px-4 py-2">
                                      <div className="min-w-0 flex-1 text-sm">
                                        <label htmlFor="columns-vendor">
                                          Vendor
                                        </label>
                                      </div>
                                      <div className="ml-3 flex h-5 items-center">
                                        <input
                                          type="checkbox"
                                          id="columns-vendor"
                                          checked={groupByVendor}
                                          onChange={
                                            handleShowVendorColumnChange
                                          }
                                          className="focus:rind-blue-500 h-4 w-4 rounded border-gray-300 text-blue-600"
                                        />
                                      </div>
                                    </div>
                                  </Menu.Item>
                                  <Menu.Item>
                                    <div className="relative flex items-start px-4 py-2">
                                      <div className="min-w-0 flex-1 text-sm">
                                        <label htmlFor="columns-salesperson">
                                          Salesperson
                                        </label>
                                      </div>
                                      <div className="ml-3 flex h-5 items-center">
                                        <input
                                          type="checkbox"
                                          id="columns-salesperson"
                                          checked={groupBySalesperson}
                                          onChange={
                                            handleShowSalespersonColumnChange
                                          }
                                          className="focus:rind-blue-500 h-4 w-4 rounded border-gray-300 text-blue-600"
                                        />
                                      </div>
                                    </div>
                                  </Menu.Item>
                                  <Menu.Item>
                                    <div className="relative flex items-start px-4 py-2">
                                      <div className="min-w-0 flex-1 text-sm">
                                        <label htmlFor="columns-customer">
                                          Customer
                                        </label>
                                      </div>
                                      <div className="ml-3 flex h-5 items-center">
                                        <input
                                          type="checkbox"
                                          id="columns-customer"
                                          checked={groupByCustomer}
                                          onChange={
                                            handleShowCustomerColumnChange
                                          }
                                          className="focus:rind-blue-500 h-4 w-4 rounded border-gray-300 text-blue-600"
                                        />
                                      </div>
                                    </div>
                                  </Menu.Item>
                                  <Menu.Item>
                                    <div className="relative flex items-start px-4 py-2">
                                      <div className="min-w-0 flex-1 text-sm">
                                        <label htmlFor="columns-ship-to">
                                          Ship To
                                        </label>
                                      </div>
                                      <div className="ml-3 flex h-5 items-center">
                                        <input
                                          type="checkbox"
                                          id="columns-ship-to"
                                          checked={groupByShipTo}
                                          onChange={
                                            handleShowShipToColumnChange
                                          }
                                          className="focus:rind-blue-500 h-4 w-4 rounded border-gray-300 text-blue-600"
                                        />
                                      </div>
                                    </div>
                                  </Menu.Item>
                                  <Menu.Item>
                                    <div className="relative flex items-start px-4 py-2">
                                      <div className="min-w-0 flex-1 text-sm">
                                        <label htmlFor="columns-season">
                                          Season
                                        </label>
                                      </div>
                                      <div className="ml-3 flex h-5 items-center">
                                        <input
                                          type="checkbox"
                                          id="columns-season"
                                          checked={groupBySeason}
                                          onChange={
                                            handleShowSeasonColumnChange
                                          }
                                          className="focus:rind-blue-500 h-4 w-4 rounded border-gray-300 text-blue-600"
                                        />
                                      </div>
                                    </div>
                                  </Menu.Item>
                                  <Menu.Item>
                                    <div className="relative flex items-start px-4 py-2">
                                      <div className="min-w-0 flex-1 text-sm">
                                        <label htmlFor="columns-blanket">
                                          Blanket
                                        </label>
                                      </div>
                                      <div className="ml-3 flex h-5 items-center">
                                        <input
                                          type="checkbox"
                                          id="columns-blanket"
                                          checked={groupByIsBlanket}
                                          onChange={
                                            handleShowBlanketColumnChange
                                          }
                                          className="focus:rind-blue-500 h-4 w-4 rounded border-gray-300 text-blue-600"
                                        />
                                      </div>
                                    </div>
                                  </Menu.Item>
                                  <Menu.Item>
                                    <div className="flex px-4 py-2">
                                      <button
                                        type="button"
                                        className="btn-secondary ml-auto py-1 text-xs"
                                        onClick={toggleColumnsMenu}
                                      >
                                        OK
                                      </button>
                                    </div>
                                  </Menu.Item>
                                </div>
                              </Menu.Items>
                            </Transition>
                          )}
                        </>
                      </Menu>
                    </div>
                    <div className="col-span-2">
                      <div className="flex items-end">
                        <Combobox
                          as="div"
                          onChange={handlePartNumberFilterChange}
                          value={{ partNumber: search, description: search }}
                          className="flex-grow"
                        >
                          <Combobox.Label>Product</Combobox.Label>
                          <div className="relative">
                            <Combobox.Input
                              type="search"
                              autoComplete="off"
                              className="w-full !text-xs"
                              placeholder="Item Search"
                              onChange={handleSearchChange}
                              onFocus={handleFocus}
                              displayValue={() => search}
                            />

                            {filteredInventoryItems.length > 0 && (
                              <Combobox.Options className="absolute z-10 mt-1 max-h-60 overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                                <Combobox.Option
                                  value={{ partNumber: '', description: '' }}
                                  className={({ active }) =>
                                    classNames(
                                      'relative cursor-default select-none py-2 pl-3 pr-9 text-xs',
                                      active
                                        ? 'bg-blue-600 text-white'
                                        : 'text-gray-900'
                                    )
                                  }
                                >
                                  <>
                                    <span className="block truncate">
                                      All Items
                                    </span>
                                  </>
                                </Combobox.Option>
                                {filteredInventoryItems.map((item) => (
                                  <Combobox.Option
                                    key={item.partNumber}
                                    value={item}
                                    className={({ active }) =>
                                      classNames(
                                        'relative cursor-default select-none py-2 pl-3 pr-9 text-xs',
                                        active
                                          ? 'bg-blue-600 text-white'
                                          : 'text-gray-900'
                                      )
                                    }
                                  >
                                    <>
                                      <span className="block truncate">
                                        {item.partNumber}
                                        <br />
                                        <div className="italic text-gray-400">
                                          {item.description}
                                        </div>
                                      </span>
                                    </>
                                  </Combobox.Option>
                                ))}
                              </Combobox.Options>
                            )}
                          </div>
                        </Combobox>
                        <button
                          type="button"
                          className="btn-secondary flex px-1 focus:ring-0"
                          title="Reset Search Filters"
                          onClick={handleResetSearchClick}
                        >
                          <Icon
                            icon="magnifying-glass-arrows-rotate"
                            className="h-5 w-5"
                          />
                        </button>
                      </div>
                    </div>
                    <div className="flex items-start justify-center pt-3">
                      <button
                        type="button"
                        onClick={handleRefreshClick}
                        className="btn-secondary flex p-3 text-blue-700"
                      >
                        <Icon icon="refresh" spin={isLoading} />
                      </button>
                      <button
                        type="button"
                        onClick={handleDownloadClick}
                        className="btn-secondary flex p-3 text-green-700"
                      >
                        <Icon icon="file-excel" />
                      </button>
                    </div>
                    {!readonly && (
                      <div className="flex items-start justify-center pt-3">
                        <Link
                          href={routes.prebooks.new.to()}
                          className="btn-new flex flex-nowrap"
                        >
                          <Icon icon="plus-circle" className="flex" />
                          &nbsp;
                          <div className="flex whitespace-nowrap">
                            New Prebook
                          </div>
                        </Link>
                      </div>
                    )}
                    <div className="col-start-1">
                      <label htmlFor="blanket-filter">Blanket</label>
                      <select
                        id="blanket-filter"
                        value={filter.blanket}
                        onChange={handleBlanketFilterChange}
                        className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                      >
                        <option value="">All</option>
                        <option value="Blanket">Blankets</option>
                        <option value="Non-Blanket">Non-Blankets</option>
                      </select>
                    </div>
                    <div>
                      <label htmlFor="vendor-filter">Vendor</label>
                      <select
                        id="vendor-filter"
                        value={filter.vendor || ''}
                        onChange={handleVendorFilterChange}
                        className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                      >
                        <option value="">All Vendors</option>
                        {vendors.map((vendor) => (
                          <option key={vendor}>{vendor}</option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label htmlFor="customer-filter">Customer</label>
                      <select
                        id="customer-filter"
                        value={filter.customer || ''}
                        onChange={handleCustomerFilterChange}
                        className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                      >
                        <option value="">All Customers</option>
                        {customers.map((customer) => (
                          <option key={customer}>{customer}</option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label htmlFor="ship-to-filter">Ship To</label>
                      <select
                        id="ship-to-filter"
                        value={filter.shipTo || ''}
                        onChange={handleShipToFilterChange}
                        className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                      >
                        <option value="">All Ship Tos</option>
                        {shipTos.map((shipTo) => (
                          <option key={shipTo}>{shipTo}</option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label htmlFor="salesperson-filter">Salesperson</label>
                      <select
                        id="salesperson-filter"
                        value={filter.salesperson || ''}
                        onChange={handleSalespersonFilterChange}
                        className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                      >
                        <option value="">All Salespeople</option>
                        {salespeople.map((salesperson) => (
                          <option key={salesperson}>{salesperson}</option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label htmlFor="season-filter">Season</label>
                      <select
                        id="season-filter"
                        value={filter.season || ''}
                        onChange={handleSeasonFilterChange}
                        className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                      >
                        <option value="">All Seasons</option>
                        {seasons.map((season) => (
                          <option key={season}>{season}</option>
                        ))}
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>
      <main className="flex-grow overflow-auto">
        <div className="mx-auto h-full py-6 sm:px-6 lg:px-8">
          <Error error={error} clear={handleClearError} />
          {isLoading && <Loading />}
          <div className="mt-8 flex h-full flex-col">
            <div className="-mx-8 -my-2 h-full">
              <div className="inline-block min-w-full px-8 py-2 align-middle">
                <div className="relative shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                  <table className="min-w-full divide-y divide-gray-300">
                    <thead>
                      <tr className="sticky top-0 z-10">
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          &nbsp;
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          <HeaderButton
                            text="Product"
                            propName="spirePartNumber"
                          />
                        </th>
                        {dateGrouping === 'Date' && (
                          <th
                            scope="col"
                            className="bg-gray-100 px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                          >
                            <HeaderButton text="Date" propName="date" />
                          </th>
                        )}
                        {dateGrouping === 'Week' && (
                          <th
                            scope="col"
                            className="bg-gray-100 px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                          >
                            <HeaderButton
                              text="Week"
                              propName="week"
                              sortPropName="date"
                            />
                          </th>
                        )}
                        {dateGrouping === 'Month' && (
                          <th
                            scope="col"
                            className="bg-gray-100 px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                          >
                            <HeaderButton
                              text="Month"
                              propName="month"
                              sortPropName="date"
                            />
                          </th>
                        )}
                        {dateGrouping === 'Year' && (
                          <th
                            scope="col"
                            className="bg-gray-100 px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                          >
                            <HeaderButton
                              text="Year"
                              propName="year"
                              sortPropName="date"
                            />
                          </th>
                        )}
                        {groupByVendor && (
                          <th
                            scope="col"
                            className="bg-gray-100 px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                          >
                            <HeaderButton text="Vendor" propName="vendor" />
                          </th>
                        )}
                        {groupBySalesperson && (
                          <th
                            scope="col"
                            className="bg-gray-100 px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                          >
                            <HeaderButton
                              text="Salesperson"
                              propName="salesperson"
                            />
                          </th>
                        )}
                        {groupByCustomer && (
                          <th
                            scope="col"
                            className="bg-gray-100 px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                          >
                            <HeaderButton text="Customer" propName="customer" />
                          </th>
                        )}
                        {groupByShipTo && (
                          <th
                            scope="col"
                            className="bg-gray-100 px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                          >
                            <HeaderButton text="Ship To" propName="shipTo" />
                          </th>
                        )}
                        {groupBySeason && (
                          <th
                            scope="col"
                            className="bg-gray-100 px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                          >
                            <HeaderButton text="Season" propName="season" />
                          </th>
                        )}
                        {groupByIsBlanket && (
                          <th
                            scope="col"
                            className="bg-gray-100 px-3 py-3.5 text-center text-sm font-semibold text-gray-900"
                          >
                            <HeaderButton text="Blanket" propName="isBlanket" />
                          </th>
                        )}
                        <th
                          scope="col"
                          className="bg-gray-100 px-3 py-3.5 text-center text-sm font-semibold text-gray-900"
                        >
                          <HeaderButton text="Cases" propName="orderQuantity" />
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white">
                      {items.map((item, index) => (
                        <tr
                          key={item.id}
                          className="border-b-2 border-gray-100"
                        >
                          <td className="whitespace-nowrap px-3 py-4 text-left text-sm font-medium">
                            {(item.prebookIdList.split(',') || []).map(
                              (prebookId) => (
                                <Link
                                  key={prebookId}
                                  href={routes.prebooks.detail.to(
                                    parseQuantity(prebookId)
                                  )}
                                  className="block"
                                >
                                  {formatNumber(
                                    parseQuantity(prebookId),
                                    '00000'
                                  )}
                                </Link>
                              )
                            )}
                          </td>
                          <td className="whitespace-nowrap px-3 py-4 text-left text-sm text-gray-700">
                            {item.spirePartNumber}
                            <br />
                            <span className="italic text-gray-400">
                              {item.description}
                            </span>
                          </td>
                          {dateGrouping === 'Date' && (
                            <td className="whitespace-nowrap px-3 py-4 text-left text-sm text-gray-700">
                              {formatDate(item.date, 'MMM d')}
                              {!isCurrentYear(item.date) &&
                                `, ${formatDate(item.date, 'yyyy')}`}
                            </td>
                          )}
                          {dateGrouping === 'Week' && (
                            <td className="whitespace-nowrap px-3 py-4 text-left text-sm text-gray-700">
                              {item.week}, {item.weekYear}
                            </td>
                          )}
                          {dateGrouping === 'Month' && (
                            <td className="whitespace-nowrap px-3 py-4 text-left text-sm text-gray-700">
                              {monthName(item.month)}, {item.year}
                            </td>
                          )}
                          {dateGrouping === 'Year' && (
                            <td className="whitespace-nowrap px-3 py-4 text-left text-sm text-gray-700">
                              {item.year}
                            </td>
                          )}
                          {groupByVendor && (
                            <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-700">
                              {item.vendor}
                            </td>
                          )}
                          {groupBySalesperson && (
                            <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-700">
                              {item.salesperson}
                            </td>
                          )}
                          {groupByCustomer && (
                            <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-700">
                              {item.customer}
                            </td>
                          )}
                          {groupByShipTo && (
                            <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-700">
                              {item.shipTo}
                            </td>
                          )}
                          {groupBySeason && (
                            <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-700">
                              {item.season}
                            </td>
                          )}
                          {groupByIsBlanket && (
                            <td className="whitespace-nowrap px-3 py-4 text-center text-sm text-gray-700">
                              <Icon
                                icon={
                                  item.isBlanket ? 'square-check' : 'square'
                                }
                              />
                            </td>
                          )}
                          <td className="whitespace-nowrap px-3 py-4 text-center text-sm text-gray-700">
                            {formatNumber(item.orderQuantity)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                    <tfoot>
                      <tr>
                        <td className="whitespace-nowrap px-3 py-4 text-left text-sm text-gray-700">
                          &nbsp;
                        </td>
                        {dateGrouping === 'Date' && (
                          <td className="whitespace-nowrap px-3 py-4 text-left text-sm text-gray-700">
                            &nbsp;
                          </td>
                        )}
                        {dateGrouping === 'Week' && (
                          <td className="whitespace-nowrap px-3 py-4 text-left text-sm text-gray-700">
                            &nbsp;
                          </td>
                        )}
                        {dateGrouping === 'Month' && (
                          <td className="whitespace-nowrap px-3 py-4 text-left text-sm text-gray-700">
                            &nbsp;
                          </td>
                        )}
                        {dateGrouping === 'Year' && (
                          <td className="whitespace-nowrap px-3 py-4 text-left text-sm text-gray-700">
                            &nbsp;
                          </td>
                        )}
                        {groupByVendor && (
                          <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-700">
                            &nbsp;
                          </td>
                        )}
                        {groupBySalesperson && (
                          <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-700">
                            &nbsp;
                          </td>
                        )}
                        {groupByCustomer && (
                          <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-700">
                            &nbsp;
                          </td>
                        )}
                        {groupByShipTo && (
                          <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-700">
                            &nbsp;
                          </td>
                        )}
                        {groupBySeason && (
                          <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-700">
                            &nbsp;
                          </td>
                        )}
                        {groupByIsBlanket && (
                          <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-700">
                            &nbsp;
                          </td>
                        )}
                        <th className="whitespace-nowrap px-3 py-4 text-center text-sm text-gray-700">
                          Total: {formatNumber(totalCases)}
                        </th>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </>
  );
}

function monthName(month: number | null) {
  if (!month) {
    return '';
  }

  return new Date(2022, month - 1, 1).toLocaleString('default', {
    month: 'short',
  });
}

function isCurrentYear(date: string | null) {
  if (date == null || date.length < 4) {
    return false;
  }

  const year = parseQuantity(date.substring(0, 4));

  return year === thisYear;
}
