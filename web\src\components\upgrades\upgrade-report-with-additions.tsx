import { useState, Fragment } from 'react';
import * as HeadlessUI from '@headlessui/react';
import * as futureOrders from 'api/models/future-orders';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { Icon } from '@/components/icon';
import { classNames } from '@/utils/class-names';
import { formatDate } from '@/utils/format';
import {
  selectUpgradeItems,
  selectPrintWithAdditionsDate,
  setPrintWithAdditionsDate,
  downloadUpgradeItemReport,
} from './upgrade-item-list-slice';
import { DateTime } from 'luxon';

export function UpgradeReportWithAdditions() {
  const dispatch = useAppDispatch(),
    allItems = useAppSelector(selectUpgradeItems),
    date = useAppSelector(selectPrintWithAdditionsDate),
    [additionalItems, setAdditionalItems] = useState<
      futureOrders.UpgradeItem[]
    >([]),
    textboxClassName =
      'rounded text-xs text-gray-900 ring-1 ring-inset ring-gray-300 focus:border-blue-500';

  const handleModalAfterEnter = () => {
    if (date) {
      setAdditionalItems([createUpgradeItem(-1, date)]);
    }
  };

  const handleAddClick = () => {
    if (date) {
      const id = additionalItems.reduce((min, i) => Math.min(min, i.id), 0) - 1,
        item = createUpgradeItem(id, date),
        items = additionalItems.concat(item);

      setAdditionalItems(items);
    }
  };

  const handleRemoveClick = (id: number) => {
    const items = additionalItems.filter((i) => i.id !== id);
    setAdditionalItems(items);
  };

  const handlePropertyChange = (
    id: number,
    propName: keyof futureOrders.UpgradeItem,
    value: any
  ) => {
    const items = additionalItems.map((i) => ({ ...i })),
      item = items.find((i) => i.id === id);

    if (item) {
      // @ts-ignore
      item[propName] = value;
      setAdditionalItems(items);
    }
  };

  const handleOrderQuantityChange = (id: number, value: string) => {
    const items = additionalItems.map((i) => ({ ...i })),
      item = items.find((i) => i.id === id),
      quantity = parseInt(value, 10);

    if (item && !isNaN(quantity)) {
      item.orderQuantity = quantity;
      setAdditionalItems(items);
    }
  };

  const handleLabourHoursChange = (id: number, value: string) => {
    const items = additionalItems.map((i) => ({ ...i })),
      item = items.find((i) => i.id === id),
      hours = parseFloat(value);

    if (item && !isNaN(hours)) {
      item.labourHours = hours;
      setAdditionalItems(items);
    }
  };

  const handleCancelClick = () => {
    dispatch(setPrintWithAdditionsDate(null));
  };

  const handlePrintClick = () => {
    const actualItems = allItems.filter((i) => i.date === date),
      suffix = date
        ? DateTime.fromFormat(date, 'yyyy-MM-dd').toFormat('MM-dd-yyyy')
        : DateTime.now().toFormat('MM-dd-yyyy'),
      items = actualItems.concat(additionalItems),
      args = {
        filename: `Upgrade Sheet ${suffix}.pdf`,
        items,
      };
    dispatch(downloadUpgradeItemReport(args));
    dispatch(setPrintWithAdditionsDate(null));
  };

  return (
    <HeadlessUI.Transition.Root
      show={!!date}
      as={Fragment}
      afterEnter={handleModalAfterEnter}
    >
      <HeadlessUI.Dialog
        as="div"
        className="relative z-10"
        onClose={handleCancelClick}
      >
        <HeadlessUI.Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </HeadlessUI.Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-0 text-center">
            <HeadlessUI.Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-0 scale-95"
              enterTo="opacity-100 translate-y-0 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 scale-100"
              leaveTo="opacity-0 translate-y-0 scale-95"
            >
              <HeadlessUI.Dialog.Panel
                className="relative my-8 w-screen transform overflow-hidden rounded-lg bg-white p-6 text-left shadow-xl transition-all"
                style={{
                  width: 'calc(100vw - 10rem)',
                  height: 'calc(100vh - 10rem)',
                }}
              >
                <div className="flex h-full flex-col">
                  <HeadlessUI.Dialog.Title
                    as="h3"
                    className="mb-6 text-center  text-base font-semibold leading-6 text-gray-900"
                  >
                    Print Upgrade Report with Additions
                  </HeadlessUI.Dialog.Title>
                  <div className="flex-grow">
                    <p className="p-2 italic">
                      In addition to the Upgrades for{' '}
                      {formatDate(date, 'MMM d')}, you can add additional
                      upgrades to be included on the report.
                    </p>
                    <table className="min-w-full divide-y divide-gray-300">
                      <thead>
                        <tr className="sticky top-0 z-10 text-xs font-semibold text-gray-900">
                          <th className="bg-gray-100 p-2 text-left">Qty</th>
                          <th className="bg-gray-100 p-2 text-left">
                            Description / Comments
                          </th>
                          <th className="bg-gray-100 p-2 text-left">
                            Product Coming From
                          </th>
                          <th className="bg-gray-100 p-2 text-left">
                            UPC /<br /> Approval Required
                          </th>
                          <th className="bg-gray-100 p-2 text-left">
                            Date Code
                          </th>
                          <th className="bg-gray-100 p-2 text-left">Retail</th>
                          <th className="bg-gray-100 p-2 text-left">W&M</th>
                          <th className="bg-gray-100 p-2 text-left">
                            Container/Pick Description
                          </th>
                          <th className="bg-gray-100 p-2 text-left">Origins</th>
                          <th className="bg-gray-100 p-2 text-left">Costs</th>
                          <th className="bg-gray-100 p-2 text-left">
                            Box Code
                          </th>
                          <th className="bg-gray-100 p-2 text-left">Hours</th>
                          <th className="bg-gray-100 p-2 text-left">&nbsp;</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200">
                        {additionalItems.map((item) => (
                          <tr
                            key={item.id}
                            className="border-b border-gray-200 text-center text-xs font-normal text-gray-700"
                          >
                            <td className="align-top">
                              <input
                                type="text"
                                className={classNames(textboxClassName, 'w-16')}
                                value={item.orderQuantity ?? ''}
                                onChange={(e) =>
                                  handleOrderQuantityChange(
                                    item.id,
                                    e.target.value
                                  )
                                }
                              />
                            </td>
                            <td className="align-top">
                              <input
                                type="text"
                                className={classNames(
                                  textboxClassName,
                                  'w-full'
                                )}
                                value={item.description || ''}
                                onChange={(e) =>
                                  handlePropertyChange(
                                    item.id,
                                    'description',
                                    e.target.value
                                  )
                                }
                              />
                              <input
                                type="text"
                                className={classNames(
                                  textboxClassName,
                                  'w-full'
                                )}
                                value={item.upgradeComments || ''}
                                onChange={(e) =>
                                  handlePropertyChange(
                                    item.id,
                                    'upgradeComments',
                                    e.target.value
                                  )
                                }
                              />
                            </td>
                            <td className="align-top">
                              <input
                                type="text"
                                className={classNames(textboxClassName, 'w-36')}
                                value={item.productComingFrom || ''}
                                onChange={(e) =>
                                  handlePropertyChange(
                                    item.id,
                                    'productComingFrom',
                                    e.target.value
                                  )
                                }
                              />
                            </td>
                            <td className="text-left align-top  ">
                              <div>
                                <input
                                  type="text"
                                  className={classNames(
                                    textboxClassName,
                                    'w-36'
                                  )}
                                  value={item.upc || ''}
                                  onChange={(e) =>
                                    handlePropertyChange(
                                      item.id,
                                      'upc',
                                      e.target.value
                                    )
                                  }
                                />
                              </div>
                              <div className="text-center">
                                <input
                                  type="checkbox"
                                  checked={item.upcApprovalRequired}
                                  onChange={(e) =>
                                    handlePropertyChange(
                                      item.id,
                                      'upcApprovalRequired',
                                      e.target.checked
                                    )
                                  }
                                />
                              </div>
                            </td>
                            <td className="align-top">
                              <input
                                type="text"
                                className={classNames(textboxClassName, 'w-24')}
                                value={item.dateCode || ''}
                                onChange={(e) =>
                                  handlePropertyChange(
                                    item.id,
                                    'dateCode',
                                    e.target.value
                                  )
                                }
                              />
                            </td>
                            <td className="align-top">
                              <input
                                type="text"
                                className={classNames(textboxClassName, 'w-24')}
                                value={item.retail || ''}
                                onChange={(e) =>
                                  handlePropertyChange(
                                    item.id,
                                    'retail',
                                    e.target.value
                                  )
                                }
                              />
                            </td>
                            <td className="p-2 text-center align-top">
                              <input
                                type="checkbox"
                                checked={item.weightsAndMeasures}
                                onChange={(e) =>
                                  handlePropertyChange(
                                    item.id,
                                    'weightsAndMeasures',
                                    e.target.checked
                                  )
                                }
                              />
                            </td>
                            <td className="align-top">
                              <input
                                type="text"
                                className={classNames(textboxClassName)}
                                value={item.containerPickDescription || ''}
                                onChange={(e) =>
                                  handlePropertyChange(
                                    item.id,
                                    'containerPickDescription',
                                    e.target.value
                                  )
                                }
                              />
                            </td>
                            <td className="align-top">
                              <input
                                type="text"
                                className={classNames(textboxClassName, 'w-24')}
                                value={item.origins || ''}
                                onChange={(e) =>
                                  handlePropertyChange(
                                    item.id,
                                    'origins',
                                    e.target.value
                                  )
                                }
                              />
                            </td>
                            <td className="align-top">
                              <input
                                type="text"
                                className={classNames(textboxClassName, 'w-24')}
                                value={item.costs || ''}
                                onChange={(e) =>
                                  handlePropertyChange(
                                    item.id,
                                    'costs',
                                    e.target.value
                                  )
                                }
                              />
                            </td>
                            <td className="align-top">
                              <input
                                type="text"
                                className={classNames(textboxClassName, 'w-24')}
                                value={item.boxCode || ''}
                                onChange={(e) =>
                                  handlePropertyChange(
                                    item.id,
                                    'boxCode',
                                    e.target.value
                                  )
                                }
                              />
                            </td>
                            <td className="align-top">
                              <input
                                type="text"
                                className={classNames(textboxClassName, 'w-24')}
                                value={item.labourHours ?? ''}
                                onChange={(e) =>
                                  handleLabourHoursChange(
                                    item.id,
                                    e.target.value
                                  )
                                }
                              />
                            </td>
                            <td>
                              <button
                                type="button"
                                className="small rounded p-2 text-red-600 hover:bg-red-600 hover:text-white"
                                onClick={() => handleRemoveClick(item.id)}
                              >
                                <Icon icon="trash" />
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                      <tfoot>
                        <tr>
                          <td className="pt-2" colSpan={10}>
                            <button
                              type="button"
                              className="btn-new"
                              onClick={handleAddClick}
                            >
                              Add
                            </button>
                          </td>
                        </tr>
                      </tfoot>
                    </table>
                  </div>
                  <div className="mt-6 text-right">
                    <button
                      type="button"
                      className="btn-secondary"
                      onClick={handleCancelClick}
                    >
                      Cancel
                    </button>
                    <button
                      type="button"
                      onClick={handlePrintClick}
                      className="btn-primary ml-2"
                    >
                      Print &nbsp;
                      <Icon icon="print" />
                    </button>
                  </div>
                </div>
              </HeadlessUI.Dialog.Panel>
            </HeadlessUI.Transition.Child>
          </div>
        </div>
      </HeadlessUI.Dialog>
    </HeadlessUI.Transition.Root>
  );
}

function createUpgradeItem(id: number, date: string): futureOrders.UpgradeItem {
  return {
    id,
    date,
    season: null,
    spireInventoryId: 0,
    spirePartNumber: '',
    description: '',
    comments: null,
    orderQuantity: 0,
    customerId: null,
    boxCode: null,
    upc: null,
    dateCode: null,
    retail: null,
    potCover: null,
    weightsAndMeasures: false,
    containerPickDescription: null,
    upgradeComments: null,
    productComingFrom: null,
    upcComment: null,
    upcApprovalRequired: false,
    origins: null,
    costs: null,
    labourHours: 0,
    growerItemNotes: null,
    itemGrowerItemNotes: null,
    isApproximate: false,
    upgradeConfirmed: new Date().toISOString(),
    upgradeConfirmedBy: 'System',
    priority: false,
    upcPrinted: null,
    upcPrintedPrev: null,
    customer: null,
    shipTo: null,
    futureOrderId: 0,
    prebookId: 0,
    tariffCode: null,
  };
}
