import { Fragment, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { routes } from '@/services/routes';
import * as HeadlessUI from '@headlessui/react';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import { Loading } from '@/components/loading';
import { Group } from '@/components/settings/users/group';
import {
  getData,
  clearError,
  clearState,
  selectError,
  selectIsLoading,
  selectGroups,
  saveGroups,
} from '@/components/settings/users/user-settings-slice';
import { classNames } from '@/utils/class-names';

export default function Users() {
  const dispatch = useAppDispatch(),
    router = useRouter(),
    isLoading = useAppSelector(selectIsLoading),
    error = useAppSelector(selectError),
    groups = useAppSelector(selectGroups);

  useEffect(() => {
    dispatch(clearState());
    dispatch(getData());
  }, [dispatch]);

  const handleClearError = () => {
    dispatch(clearError());
  };

  const handleSaveClick = async () => {
    const result = await dispatch(saveGroups());
    if (!('error' in result)) {
      router.push(routes.settings.home.to());
    }
  };

  return (
    <>
      <Head>
        <title>Settings: Users</title>
      </Head>
      <header className="sticky top-0 z-20 flex-wrap bg-white shadow">
        <div className="mx-auto flex max-w-7xl items-center justify-between px-8 py-6">
          <h2 className="flex-shrink-0 text-2xl font-bold leading-7 text-gray-900">
            <Icon icon="users" />
            &nbsp; User Settings
          </h2>
          <div className="flex">
            <Link
              href={routes.settings.home.to()}
              className="btn-secondary inline-flex"
            >
              Close
            </Link>
            <button
              type="button"
              className="btn-secondary ml-3 inline-flex border-blue-600 px-2 text-blue-600 disabled:border-blue-300 disabled:text-blue-300 disabled:hover:border-blue-300 disabled:hover:text-blue-300"
              disabled={isLoading}
              onClick={handleSaveClick}
            >
              Save &amp; Close &nbsp;
              <Icon icon="save" />
            </button>
          </div>
        </div>
      </header>
      <main className="flex flex-grow flex-col overflow-y-auto">
        <Error error={error} clear={handleClearError} />
        {isLoading && <Loading />}
        <div className="flex h-full w-full">
          <div className="flex h-full w-full flex-col">
            <div className="flex h-full w-full">
              <HeadlessUI.Tab.Group vertical>
                <div className="flex h-full w-full border-b border-t">
                  <HeadlessUI.Tab.List
                    as="nav"
                    className="flex w-64 flex-col overflow-y-auto border-2 bg-gray-200"
                  >
                    {groups.map((group) => (
                      <HeadlessUI.Tab key={group.id} as={Fragment}>
                        {({ selected }) => (
                          <button
                            type="button"
                            className={classNames(
                              'group relative my-2 mr-4 text-nowrap rounded-lg rounded-l-none p-2 text-left text-sm font-medium',
                              selected
                                ? 'bg-blue-500 text-white'
                                : 'bg-white text-gray-500 hover:text-blue-500'
                            )}
                          >
                            {group.name}
                          </button>
                        )}
                      </HeadlessUI.Tab>
                    ))}
                  </HeadlessUI.Tab.List>
                  <HeadlessUI.Tab.Panels className="m-2 h-full w-full overflow-y-auto">
                    {groups.map((group) => (
                      <HeadlessUI.Tab.Panel
                        key={group.id}
                        className="w-full rounded-lg border p-2"
                      >
                        <Group group={group} />
                      </HeadlessUI.Tab.Panel>
                    ))}
                  </HeadlessUI.Tab.Panels>
                </div>
              </HeadlessUI.Tab.Group>
            </div>
          </div>
        </div>
      </main>
    </>
  );
}
