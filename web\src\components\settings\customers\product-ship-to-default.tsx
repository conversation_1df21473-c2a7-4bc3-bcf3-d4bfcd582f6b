import React, { useRef, useState } from 'react';
import * as HeadlessUI from '@headlessui/react';
import { useInventoryItemsQuery } from 'api/spire-service';
import * as prebooks from 'api/models/prebooks';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import {
  updateProductShipToDefault,
  deleteProductShipToDefault,
  selectCustomerItemCodeByShipTo,
  selectPotCovers,
} from './customer-settings-slice';
import { Alert } from '@/components/alert';
import { DropdownMenu } from '@/components/drop-down-menu';
import { potCovers as defaultPotCovers } from '@/components/pot-covers';
import { handleFocus } from '@/utils/focus';
import { Icon } from '@/components/icon';
import { classNames } from '@/utils/class-names';
import { formatCurrency, formatNumber } from '@/utils/format';
import numeral from 'numeral';

interface ProductShipToDefaultProps {
  productShipToDefault: prebooks.ProductShipToDefault;
}

export function ProductShipToDefault({
  productShipToDefault,
}: ProductShipToDefaultProps) {
  const dispatch = useAppDispatch(),
    potCoverRef = useRef<HTMLInputElement | null>(null),
    customerItemCodeByShipTo = useAppSelector(selectCustomerItemCodeByShipTo),
    customerPotCovers = useAppSelector(selectPotCovers),
    { data: inventoryItemsData } = useInventoryItemsQuery(),
    inventory = inventoryItemsData?.inventoryItems || [],
    item = inventory.find(
      (i) => i.id === productShipToDefault.spireInventoryId
    ),
    [showDeleteDialog, setShowDeleteDialog] = useState(false),
    [editing, setEditing] = useState(false),
    [customerItemCode, setCustomerItemCode] = useState(
      productShipToDefault.customerItemCode || ''
    ),
    [unitPrice, setUnitPrice] = useState(
      productShipToDefault.unitPrice
        ? formatNumber(productShipToDefault.unitPrice, '0,0.00')
        : ''
    ),
    [retail, setRetail] = useState(productShipToDefault.retail || ''),
    [weightsAndMeasures, setWeightsAndMeasures] = useState(
      productShipToDefault.weightsAndMeasures
    ),
    [upc, setUPC] = useState(productShipToDefault.upc || ''),
    [hasPotCover, setHasPotCover] = useState(productShipToDefault.hasPotCover),
    [potCover, setPotCover] = useState(productShipToDefault.potCover),
    cellClassName = 'whitespace-nowrap px-2 py-4 text-sm align-middle',
    potCovers = defaultPotCovers.concat(customerPotCovers);

  const handleHasPotCoverChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.checked;
    setHasPotCover(value);
    if (value) {
      setPotCover('PC');
    } else {
      setPotCover(null);
    }
  };

  const handlePotCoverChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const value = e.target.value.toUpperCase() || null;
    setPotCover(value);
  };

  const handlePotCoverSelected = (value: string) => {
    setPotCover(value);
  };

  const handleUPCChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUPC(e.target.value);
  };

  const handleRetailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setRetail(e.target.value);
  };

  const handleUnitPriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUnitPrice(e.target.value);
  };

  const handleUnitPriceBlur = () => {
    const price = numeral(unitPrice).value() || null;
    setUnitPrice(formatNumber(price, '0,0.00'));
  };

  const handleCustomerItemCodeChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setCustomerItemCode(e.target.value);
  };

  const handleEditClick = () => {
    setEditing(true);
  };

  const handleCancelClick = () => {
    setCustomerItemCode(productShipToDefault.customerItemCode || '');
    setEditing(false);
  };

  const handleDeleteClick = () => setShowDeleteDialog(true);

  const handleDeleteCancel = () => setShowDeleteDialog(false);

  const handleDeleteConfirm = () => {
    dispatch(deleteProductShipToDefault(productShipToDefault.id));
    setShowDeleteDialog(false);
  };

  const handleSaveClick = () => {
    const price = numeral(unitPrice).value(),
      args = {
        id: productShipToDefault.id,
        customerItemCode,
        hasPotCover,
        potCover,
        upc,
        weightsAndMeasures,
        retail,
        unitPrice: price,
      };
    dispatch(updateProductShipToDefault(args));
    setEditing(false);
  };

  return (
    <tr>
      <td className={cellClassName}>
        {item?.partNo}
        <br />
        <span className="italic text-gray-700">{item?.description}</span>
      </td>
      <td className={classNames(cellClassName, 'text-center')}>
        {!editing && productShipToDefault.potCover}
        {editing && (
          <div className="flex max-w-[190px] flex-row rounded-md border border-gray-300 shadow-sm">
            <div className="flex items-center px-2">
              <input
                type="checkbox"
                checked={hasPotCover}
                onChange={handleHasPotCoverChange}
              />
            </div>
            <input
              type="text"
              autoComplete="off"
              value={potCover || ''}
              onChange={handlePotCoverChange}
              onFocus={handleFocus}
              className={classNames(
                'flex max-w-[115px] flex-grow rounded-md border-transparent text-center text-xs shadow-sm placeholder:italic placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500',
                hasPotCover ? '' : 'invisible'
              )}
              ref={potCoverRef}
              placeholder="PC"
            />
            <div
              className={classNames(
                'flex px-2 pt-1',
                hasPotCover ? '' : 'invisible'
              )}
            >
              <DropdownMenu
                items={potCovers.map((text) => ({
                  text,
                  selected: handlePotCoverSelected,
                }))}
              />
            </div>
          </div>
        )}
      </td>
      <td className={classNames(cellClassName, 'text-center')}>
        {!editing && productShipToDefault.upc}
        {editing && (
          <input
            type="text"
            value={upc}
            onChange={handleUPCChange}
            onFocus={handleFocus}
            className="w-32 rounded-md border-gray-300 text-center text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
          />
        )}
      </td>
      <td className={classNames(cellClassName, 'text-center')}>
        {!editing && (
          <Icon icon={weightsAndMeasures ? 'check-square' : 'square'} />
        )}
        {editing && (
          <HeadlessUI.Switch.Group as="div">
            <HeadlessUI.Switch
              checked={weightsAndMeasures}
              onChange={setWeightsAndMeasures}
              className={classNames(
                weightsAndMeasures ? 'bg-blue-400' : 'bg-gray-200',
                'relative mt-2 inline-flex h-4 w-8 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
              )}
            >
              <span
                aria-hidden="true"
                className={classNames(
                  weightsAndMeasures ? 'translate-x-4' : 'translate-x-0',
                  'pointer-events-none inline-block h-3 w-3 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                )}
              />
            </HeadlessUI.Switch>
          </HeadlessUI.Switch.Group>
        )}
      </td>
      <td className={classNames(cellClassName, 'text-center')}>
        {!editing && productShipToDefault.retail}
        {editing && (
          <input
            type="text"
            value={retail}
            onChange={handleRetailChange}
            onFocus={handleFocus}
            className="w-24 rounded-md border-gray-300 text-center text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
          />
        )}
      </td>
      <td className={classNames(cellClassName, 'text-center')}>
        {!editing &&
          (productShipToDefault.unitPrice
            ? formatCurrency(productShipToDefault.unitPrice)
            : '')}
        {editing && (
          <div className="relative rounded-md shadow-sm">
            <div className="lfex pointer-events-none absolute inset-y-0 left-0 items-center pl-3">
              <span className="mt-2 flex text-gray-500">$</span>
            </div>
            <input
              type="text"
              value={unitPrice}
              onChange={handleUnitPriceChange}
              onBlur={handleUnitPriceBlur}
              onFocus={handleFocus}
              className="w-24 rounded-md border-gray-300 text-center text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
          </div>
        )}
      </td>
      {customerItemCodeByShipTo && (
        <td className={classNames(cellClassName, 'text-center')}>
          {!editing && productShipToDefault.customerItemCode}
          {editing && (
            <input
              type="text"
              value={customerItemCode}
              onChange={handleCustomerItemCodeChange}
              onFocus={handleFocus}
              className="w-48 rounded-md border-gray-300 text-center text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
          )}
        </td>
      )}
      <td className={cellClassName}>
        {!editing && (
          <div className="flex">
            <button
              type="button"
              onClick={handleEditClick}
              className="btn-secondary px-2 py-1"
            >
              <Icon icon="edit" />
            </button>
            <button
              type="button"
              onClick={handleDeleteClick}
              className="btn-secondary border-red-500 px-2 py-1 text-red-500"
            >
              <Icon icon="trash" />
            </button>
          </div>
        )}
        {editing && (
          <div className="flex">
            <button
              type="button"
              onClick={handleCancelClick}
              className="btn-secondary px-2 py-1"
            >
              <Icon icon="undo" />
            </button>
            <button
              type="button"
              onClick={handleSaveClick}
              className="btn-secondary border-blue-500 px-2 py-1 text-blue-500"
            >
              <Icon icon="save" />
            </button>
          </div>
        )}
      </td>
      <Alert
        title="Delete Ship To Defaults"
        open={showDeleteDialog}
        message="Are you sure you want to delete these Ship To Defaults?"
        colour="danger"
        confirmButtonText="Yes"
        cancelButtonText="No"
        confirm={handleDeleteConfirm}
        cancel={handleDeleteCancel}
      />
    </tr>
  );
}
