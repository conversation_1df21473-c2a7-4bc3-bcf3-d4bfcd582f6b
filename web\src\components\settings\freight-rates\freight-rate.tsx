import { useState, useMemo, useRef } from 'react';
import { useUpdateFreightRateMutation } from 'api/settings-service';
import { Icon } from '@/components/icon';
import { useAppSelector } from '@/services/hooks';
import * as spire from 'api/models/spire';
import { handleFocus } from '@/utils/focus';
import { formatCurrency, formatNumber } from '@/utils/format';
import { selectFreightRates } from './freight-rates-slice';

interface SeasonDetailProps {
  priceLevel: spire.PriceLevel;
}

export function PriceLevel({ priceLevel }: SeasonDetailProps) {
  const [update] = useUpdateFreightRateMutation(),
    freightRates = useAppSelector(selectFreightRates),
    [editing, setEditing] = useState(false),
    [tempRate, setTempRate] = useState(''),
    freightRate = useMemo(
      () => freightRates.find((fr) => fr.priceLevel === priceLevel.code),
      [freightRates, priceLevel]
    ),
    ref = useRef<HTMLInputElement>(null);

  const handleRateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTempRate(e.target.value);
  };

  const handleEditClick = () => {
    const tempRate = freightRate
      ? formatNumber(freightRate.defaultFreightPerCase, '0.00')
      : '';
    setTempRate(tempRate);
    setEditing(true);

    window.setTimeout(() => {
      ref.current?.focus();
      ref.current?.select();
    }, 0);
  };

  const handleSaveClick = async () => {
    const defaultFreightPerCase = parseFloat(tempRate) || null;

    await update({
      priceLevel: priceLevel.code,
      defaultFreightPerCase,
    });
    setEditing(false);
  };

  const handleCancelClick = () => {
    setEditing(false);
  };

  return (
    <tr>
      <td className="w-1 whitespace-nowrap px-2 py-4 text-center">
        {priceLevel.code}
      </td>
      <td className="w-1 whitespace-nowrap px-2 py-4 text-center">
        <div className="flex w-48">
          {editing ? (
            <>
              <input
                type="number"
                className="rounded-md border-gray-300 text-center text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
                ref={ref}
                onFocus={handleFocus}
                value={tempRate}
                onChange={handleRateChange}
              />
              <button
                type="button"
                className="btn-secondary"
                onClick={handleCancelClick}
              >
                <Icon icon="undo" />
              </button>
              <button
                type="button"
                className="btn-primary"
                onClick={handleSaveClick}
              >
                <Icon icon="save" />
              </button>
            </>
          ) : (
            <>
              <input
                type="text"
                readOnly
                className="rounded-md border-transparent text-center text-xs focus:border-blue-500 focus:ring-blue-500"
                value={
                  freightRate
                    ? formatCurrency(freightRate?.defaultFreightPerCase)
                    : '-'
                }
              />

              <button
                type="button"
                className="btn-secondary"
                onClick={handleEditClick}
              >
                <Icon icon="edit" />
              </button>
            </>
          )}
        </div>
      </td>
      <td>&nbsp;</td>
    </tr>
  );
}
