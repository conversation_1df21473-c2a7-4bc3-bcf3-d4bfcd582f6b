'use client';

import { useEffect } from 'react';
import Underline from '@tiptap/extension-underline';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { Icon } from '@/components/icon';
import { classNames } from '@/utils/class-names';

interface TiptapProps {
  content: string;
  onChange: (html: string) => void;
  className?: string;
}

export const Tiptap = ({ content, onChange, className }: TiptapProps) => {
  const editor = useEditor({
    extensions: [StarterKit, Underline],
    content,
    onUpdate({ editor }) {
      onChange(editor.getHTML());
    },
    editorProps: {
      attributes: {
        class: classNames(
          'border rounded p-2 overflow-y-auto flex-grow',
          className
        ),
      },
    },
  });

  useEffect(() => {
    if (editor) {
      if (content !== editor.getHTML()) {
        editor.commands.setContent(content);
        editor.chain().focus('start').run();
      }
    }
  }, [content, editor]);

  return (
    <div className="flex flex-grow flex-col">
      <div className="border">
        <button
          type="button"
          tabIndex={-1}
          className={classNames(
            'btn-secondary px-2 py-1',
            editor?.isActive('bold') && 'border-gray-800'
          )}
          onClick={() => editor?.chain().focus().toggleBold().run()}
        >
          <Icon icon="bold" />
        </button>
        <button
          type="button"
          tabIndex={-1}
          className={classNames(
            'btn-secondary px-2 py-1',
            editor?.isActive('italic') && 'border-gray-800'
          )}
          onClick={() => editor?.chain().focus().toggleItalic().run()}
        >
          <Icon icon="italic" />
        </button>
        <button
          type="button"
          tabIndex={-1}
          className={classNames(
            'btn-secondary px-2 py-1',
            editor?.isActive('underline') && 'border-gray-800'
          )}
          onClick={() => editor?.chain().focus().toggleUnderline().run()}
        >
          <Icon icon="underline" />
        </button>
      </div>
      <EditorContent editor={editor} className="flex flex-grow flex-col" />
    </div>
  );
};
