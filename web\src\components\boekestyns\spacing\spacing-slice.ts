import { createSelector, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '@/services/store';
import * as boeks from 'api/models/boekestyns';
import { formatDate } from '@/utils/format';
import { boekestynSpacingApi } from 'api/boekestyn-spacing-service';
import { ProblemDetails } from '@/utils/problem-details';

interface SpacingState {
  date: string;
  line: number | null;
  workOrders: boeks.SpacingWorkOrderItem[];
  lines: boeks.SpacingLine[];
  error: ProblemDetails | null;
}

const initialState: SpacingState = {
  date: formatDate(new Date(), 'yyyy-MM-dd'),
  line: null,
  workOrders: [],
  lines: [],
  error: null,
};

const spacingSlice = createSlice({
  name: 'spacing',
  initialState,
  reducers: {
    setError(state, { payload }: PayloadAction<ProblemDetails | null>) {
      state.error = payload;
    },
    setDate(state, { payload }: PayloadAction<string>) {
      state.date = payload;
    },
    setLine(state, { payload }: PayloadAction<number | null>) {
      state.line = payload;
    },
  },
  extraReducers: (builder) =>
    builder
      .addMatcher(
        boekestynSpacingApi.endpoints.spacing.matchFulfilled,
        (state, { payload }) => {
          state.lines = payload.lines;
        }
      )
      .addMatcher(
        boekestynSpacingApi.endpoints.getSpacingWorkOrdersByDate.matchFulfilled,
        (state, { payload }) => {
          state.workOrders = payload.orders;
        }
      ),
});

export const { setDate, setLine, setError } = spacingSlice.actions;

const selectAllWorkOrders = ({ boekestynSpacing }: RootState) =>
  boekestynSpacing.workOrders;
export const selectDate = ({ boekestynSpacing }: RootState) =>
  boekestynSpacing.date;
export const selectLine = ({ boekestynSpacing }: RootState) =>
  boekestynSpacing.line;
export const selectLines = ({ boekestynSpacing }: RootState) =>
  boekestynSpacing.lines;
export const selectError = ({ boekestynSpacing }: RootState) =>
  boekestynSpacing.error;

export const selectWorkOrders = createSelector(
  selectAllWorkOrders,
  selectLines,
  selectLine,
  (workOrders, lines, line) =>
    workOrders.filter(
      (wo) => !line || wo.lineName === lines.find((l) => l.id === line)?.name
    )
);

export default spacingSlice.reducer;
