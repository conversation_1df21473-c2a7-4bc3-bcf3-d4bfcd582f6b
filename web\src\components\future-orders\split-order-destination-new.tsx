import { Fragment, useState, useRef, useEffect } from 'react';
import * as HeadlessUI from '@headlessui/react';
import { useSeasonsQuery } from 'api/prebooks-service';
import {
  useCustomersQuery,
  useSalespeopleQuery,
  useShippingMethodsQuery,
  useInventoryCommentsQuery,
} from 'api/spire-service';
import * as spire from 'api/models/spire';
import { Combobox } from '@/components/combo-box';
import { Icon } from '@/components/icon';
import { ShipToCombobox } from '@/components/ship-to-combo-box';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { classNames } from '@/utils/class-names';
import { startsWith } from '@/utils/equals';
import { createProblemDetails } from '@/utils/problem-details';
import {
  selectRequiredDate as selectFutureOrderRequiredDate,
  selectArrivalDate as selectFutureOrderArrivalDate,
  selectCustomer as selectFutureOrderCustomer,
  selectShipTo as selectFutureOrderShipTo,
  selectInternalComments as selectFutureOrderInternalComments,
  select<PERSON><PERSON><PERSON> as selectFutureOrderSalesperson,
  selectShipVia as selectFutureOrderShipVia,
  selectSeasonName as selectFutureOrderSeasonName,
  selectBoxCode as selectFutureOrderBoxCode,
  selectSpireNotes as selectFutureOrderSpireNotes,
  selectCustomerPurchaseOrderNumber as selectFutureOrderCustomerPurchaseOrderNumber,
  selectComments as selectFutureOrderComments,
  selectGrowerItemNotes as selectFutureOrderGrowerItemNotes,
  selectRequiresLabels as selectFutureOrderRequiresLabels,
} from './future-order-detail-slice';
import {
  getCustomerDetail,
  setCustomer,
  setShipTo,
  setSalesperson,
  setRequiredDate,
  setArrivalDate,
  setSeasonName,
  setShipVia,
  setBoxCode,
  setRequiresLabels,
  setCustomerPurchaseOrderNumber,
  setComments,
  setInternalComments,
  setGrowerItemNotes,
  setSpireNotes,
  selectCustomer,
  selectShipTo,
  selectSalesperson,
  selectShipVia,
  selectRequiredDate,
  selectArrivalDate,
  selectSeasonName,
  selectBoxCode,
  selectRequiresLabels,
  selectCustomerPurchaseOrderNumber,
  selectComments,
  selectInternalComments,
  selectGrowerItemNotes,
  selectSpireNotes,
  setStep,
  setError,
  clearError,
  selectCustomerDetail,
} from './split-order-slice';

interface SplitOrderDestinationNewProps {
  onClose: () => void;
}

export function SplitOrderDestinationNew({
  onClose,
}: SplitOrderDestinationNewProps) {
  const dispatch = useAppDispatch(),
    futureOrderRequiredDate = useAppSelector(selectFutureOrderRequiredDate),
    futureOrderArrivalDate = useAppSelector(selectFutureOrderArrivalDate),
    futureOrderCustomer = useAppSelector(selectFutureOrderCustomer),
    futureOrderShipTo = useAppSelector(selectFutureOrderShipTo),
    futureOrderSalesperson = useAppSelector(selectFutureOrderSalesperson),
    futureOrderShipVia = useAppSelector(selectFutureOrderShipVia),
    futureOrderInternalComments = useAppSelector(
      selectFutureOrderInternalComments
    ),
    futureOrderSeasonName = useAppSelector(selectFutureOrderSeasonName),
    futureOrderBoxCode = useAppSelector(selectFutureOrderBoxCode),
    futureOrderSpireNotes = useAppSelector(selectFutureOrderSpireNotes),
    futureOrderCustomerPurchaseOrderNumber = useAppSelector(
      selectFutureOrderCustomerPurchaseOrderNumber
    ),
    futureOrderComments = useAppSelector(selectFutureOrderComments),
    futureOrderGrowerItemNotes = useAppSelector(
      selectFutureOrderGrowerItemNotes
    ),
    futureOrderRequiresLabels = useAppSelector(selectFutureOrderRequiresLabels),
    customer = useAppSelector(selectCustomer),
    shipTo = useAppSelector(selectShipTo),
    salesperson = useAppSelector(selectSalesperson),
    shipVia = useAppSelector(selectShipVia),
    requiredDate = useAppSelector(selectRequiredDate),
    arrivalDate = useAppSelector(selectArrivalDate),
    seasonName = useAppSelector(selectSeasonName),
    boxCode = useAppSelector(selectBoxCode),
    requiresLabels = useAppSelector(selectRequiresLabels),
    customerPurchaseOrderNumber = useAppSelector(
      selectCustomerPurchaseOrderNumber
    ),
    comments = useAppSelector(selectComments),
    internalComments = useAppSelector(selectInternalComments),
    growerItemNotes = useAppSelector(selectGrowerItemNotes),
    spireNotes = useAppSelector(selectSpireNotes),
    customerDetail = useAppSelector(selectCustomerDetail),
    [showNewComment, setShowNewComment] = useState(false),
    { data: customers } = useCustomersQuery(),
    { data: salespeople } = useSalespeopleQuery(),
    { data: shippingMethods } = useShippingMethodsQuery(),
    { data: inventoryComments } = useInventoryCommentsQuery(),
    { data: seasons } = useSeasonsQuery(),
    requiredDateRef = useRef<HTMLInputElement | null>(null),
    shipToRef = useRef<HTMLInputElement | null>(null),
    seasonNames = seasons?.map((s) => s.name) || [];

  useEffect(() => {
    dispatch(setRequiredDate(futureOrderRequiredDate));
  }, [dispatch, futureOrderRequiredDate]);

  const handleArrivalDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setArrivalDate(e.target.value));
  };

  const handleRequiresLabelsChange = (value: boolean) => {
    dispatch(setRequiresLabels(value));
  };

  const handleSeasonNameChange = (value: string | null) => {
    dispatch(setSeasonName(value));
  };

  const handleCustomerChange = (value: spire.Customer | null) => {
    dispatch(getCustomerDetail(value?.id || null));

    if (value) {
      shipToRef.current?.focus();
    }
  };

  const handleShipToChange = (value: spire.CustomerShipTo | null) => {
    dispatch(setShipTo(value));
  };

  const handleSalespersonChange = (value: spire.Salesperson | null) => {
    dispatch(setSalesperson(value));
  };

  const handleShipViaChange = (value: spire.ShippingMethod | null) => {
    dispatch(setShipVia(value));
  };

  const handleBoxCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setBoxCode(e.target.value));
  };

  const handleCustomerPurchaseOrderNumberChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    dispatch(setCustomerPurchaseOrderNumber(e.target.value));
  };

  const handleSpireNotesChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    dispatch(setSpireNotes(e.target.value));
  };

  const handleInternalCommentsChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    dispatch(setInternalComments(e.target.value));
  };

  const handleAddCommentsClick = () => {
    const id =
      comments.reduce((min, comments) => Math.min(min, comments.id), 0) - 1;
    dispatch(
      setComments(
        comments.concat([
          {
            id,
            comments: '',
            isStandardComment: false,
            created: '',
            createdBy: '',
          },
        ])
      )
    );
    setShowNewComment(true);
  };

  const handleAddStandardComment = (comment: spire.InventoryComment) => {
    dispatch(
      setComments(
        comments.concat([
          {
            ...comment,
            isStandardComment: true,
            created: '',
            createdBy: '',
            id: 0,
          },
        ])
      )
    );
  };

  const handleDeleteComment = (id: number) => {
    dispatch(setComments(comments.filter((c) => c.id !== id)));
  };

  const handleDeleteStandardCommentClick = (id: number) => {
    handleDeleteComment(id);
  };

  const handleGrowerItemNotesChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    dispatch(setGrowerItemNotes(e.target.value));
  };

  const handleCopyClick = () => {
    dispatch(setRequiredDate(futureOrderRequiredDate));
    dispatch(setArrivalDate(futureOrderArrivalDate));
    dispatch(setSeasonName(futureOrderSeasonName));
    dispatch(setCustomer(futureOrderCustomer));
    dispatch(setShipTo(futureOrderShipTo));
    dispatch(setSalesperson(futureOrderSalesperson));
    dispatch(setShipVia(futureOrderShipVia));
    dispatch(setBoxCode(futureOrderBoxCode));
    dispatch(setSpireNotes(futureOrderSpireNotes));
    dispatch(
      setCustomerPurchaseOrderNumber(futureOrderCustomerPurchaseOrderNumber)
    );
    dispatch(setInternalComments(futureOrderInternalComments));
    dispatch(setComments(futureOrderComments));
    dispatch(setGrowerItemNotes(futureOrderGrowerItemNotes));
    dispatch(setRequiresLabels(futureOrderRequiresLabels));
    if (futureOrderCustomer) {
      dispatch(getCustomerDetail(futureOrderCustomer.id));
    }
  };

  const handleCancelClick = () => {
    onClose();
  };

  const handleBackClick = () => {
    dispatch(setStep(1));
  };

  const handleNextClick = () => {
    dispatch(clearError());

    if (!requiredDate && !seasonName) {
      const error =
        !requiredDate && !seasonName
          ? 'Please enter either the Required Date or the Season'
          : 'Please enter the Required Date';
      return dispatch(setError(createProblemDetails(error)));
    } else if (!customer) {
      return dispatch(
        setError(createProblemDetails('Please select the Customer.'))
      );
    }

    dispatch(setStep(3));
  };

  return (
    <div className="flex flex-grow flex-col overflow-y-auto">
      <form className="flex flex-grow flex-col space-y-8 divide-y divide-gray-200 overflow-y-auto rounded border p-4">
        <div className="grid grid-cols-4 gap-x-4 gap-y-6">
          <div className="col-span-2 col-start-2">
            <button
              type="button"
              className="btn-primary flex w-full justify-around text-xl"
              onClick={handleCopyClick}
            >
              <Icon icon="chevron-down" />
              &nbsp;
              <div>
                Copy from current &nbsp;
                <Icon icon="copy" />
              </div>
              &nbsp;
              <Icon icon="chevron-down" />
            </button>
          </div>
          <div className="col-span-2 col-start-1 mt-1 grid grid-cols-2 gap-x-2">
            <label
              htmlFor="required-date"
              className="block text-sm font-medium text-gray-500"
            >
              Required Date &nbsp;
            </label>
            <label
              htmlFor="required-date"
              className="block text-sm font-medium text-gray-500"
            >
              Arrival Date
              <span className="text-red-500">&nbsp;*</span>
            </label>
            <div className="mt-1">
              <input
                type="date"
                max="2050-01-01"
                name="requiredDate"
                id="required-date"
                value={requiredDate || ''}
                ref={requiredDateRef}
                readOnly
                className="block w-full rounded-md border-gray-300 text-sm shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
            <div className="mt-1">
              <input
                type="date"
                max="2050-01-01"
                name="arrivalDate"
                id="arrival-date"
                value={arrivalDate || ''}
                onChange={handleArrivalDateChange}
                min={requiredDate || ''}
                className="block w-full rounded-md border-gray-300 text-sm shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
          </div>
          <HeadlessUI.Switch.Group as="div">
            <label className="invisible block text-sm font-medium text-gray-500">
              &nbsp;
            </label>
            <HeadlessUI.Switch
              checked={requiresLabels}
              onChange={handleRequiresLabelsChange}
              className={classNames(
                requiresLabels ? 'bg-blue-400' : 'bg-gray-200',
                'relative inline-flex h-4 w-8 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
              )}
            >
              <span
                aria-hidden="true"
                className={classNames(
                  requiresLabels ? 'translate-x-4' : 'translate-x-0',
                  'pointer-events-none inline-block h-3 w-3 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                )}
              />
            </HeadlessUI.Switch>
            <HeadlessUI.Switch.Label className="ml-2">
              <span className="text-xs">Special Labels</span>
            </HeadlessUI.Switch.Label>
          </HeadlessUI.Switch.Group>
          <div className="col-start-1">
            <div className="flex align-bottom">
              <div className="flex-grow rounded shadow-sm">
                <Combobox
                  value={seasonName}
                  onChange={handleSeasonNameChange}
                  label="Season / Holiday"
                  collection={seasonNames}
                  filter={(q, s) => startsWith(s, q)}
                />
              </div>
            </div>
          </div>
          <div>
            <div className="mt-1 rounded-md shadow-sm">
              <Combobox
                value={customer}
                onChange={handleCustomerChange}
                label="Customer"
                required
                collection={customers}
                filter={(q, c) =>
                  startsWith(c.name, q) || startsWith(c.customerNo, q)
                }
                secondaryDisplayTextProp="customerNo"
                nullDisplayText="No Customer"
              />
            </div>
          </div>
          <div>
            <div className="mt-1 rounded-md shadow-sm">
              <ShipToCombobox
                value={shipTo}
                onChange={handleShipToChange}
                inputRef={shipToRef}
                customer={customerDetail}
              />
            </div>
          </div>
          <div className="col-start-1">
            <div className="mt-1 rounded shadow-sm">
              <Combobox
                value={salesperson}
                onChange={handleSalespersonChange}
                label="Salesperson"
                collection={salespeople}
                filter={(q, s) => startsWith(s.name, q)}
              />
            </div>
          </div>
          <div>
            <div className="mt-1">
              <div className="mt-1 rounded shadow-sm">
                <Combobox
                  value={shipVia}
                  onChange={handleShipViaChange}
                  label="Truck"
                  displayTextProp="description"
                  collection={shippingMethods}
                  filter={(q, s) => startsWith(s.description, q)}
                />
              </div>
            </div>
          </div>
          <div>
            <label
              htmlFor="box-code"
              className="block text-sm font-medium text-gray-500"
            >
              Box Code
            </label>
            <div className="mt-1">
              <input
                type="text"
                name="boxCode"
                id="box-code"
                autoComplete="off"
                value={boxCode || ''}
                onChange={handleBoxCodeChange}
                className="block w-full rounded-md border-gray-300 text-sm shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
          </div>
          <div>
            <label
              htmlFor="customer-purchase-order-number"
              className="block text-sm font-medium text-gray-500"
            >
              Customer PO
            </label>
            <div className="mt-1">
              <input
                type="text"
                name="customerPurchaseOrderNumber"
                id="customer-purchase-order-number"
                maxLength={20}
                autoComplete="off"
                value={customerPurchaseOrderNumber || ''}
                onChange={handleCustomerPurchaseOrderNumberChange}
                className="block w-full rounded-md border-gray-300 text-sm shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>
        <div className="mt-4 grid grid-cols-2 gap-x-4 pt-4">
          <div>
            <label
              htmlFor="spire-notes"
              className="block text-sm font-medium text-gray-500"
            >
              Shipment Details&nbsp;
              <HeadlessUI.Popover className="relative inline-block">
                <HeadlessUI.Popover.Button
                  className="btn-secondary mb-1 rounded-full px-1 py-0 text-sm text-blue-500 focus:ring-0"
                  tabIndex={-1}
                >
                  <Icon icon="question-circle" />
                </HeadlessUI.Popover.Button>
                <HeadlessUI.Transition
                  as={Fragment}
                  enter="transition ease-out duration-200"
                  enterFrom="opacity-0 translate-y-1"
                  enterTo="opacity-100 translate-y-0"
                  leave="transition ease-in duration-150"
                  leaveFrom="opacity-100 translate-y-0"
                  leaveTo="opacity-0 translate-y-1"
                >
                  <HeadlessUI.Popover.Panel className="absolute bottom-10 z-10 w-96 -translate-x-1/2 transform px-4">
                    <div className="rounded-lg bg-yellow-50 p-4 font-normal shadow-lg ring-1 ring-black ring-opacity-5">
                      Shipment Details will go into the Ship Sheet Comments
                      field on the User Defined tab in the Spire Order. These
                      notes will not be seen by the Customer.
                    </div>
                  </HeadlessUI.Popover.Panel>
                </HeadlessUI.Transition>
              </HeadlessUI.Popover>
            </label>
            <textarea
              rows={2}
              name="spireNotes"
              id="spire-notes"
              value={spireNotes || ''}
              onChange={handleSpireNotesChange}
              className="block w-full rounded-md border-gray-300 text-sm shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
          </div>
          <div>
            <div className="flex w-full justify-between">
              <label
                htmlFor="comments"
                className="text-sm font-medium text-gray-500"
              >
                Internal Comments &nbsp;
                <HeadlessUI.Popover className="relative inline-block">
                  <HeadlessUI.Popover.Button
                    className="btn-secondary mb-1 rounded-full px-1 py-0 text-sm text-blue-500 focus:ring-0"
                    tabIndex={-1}
                  >
                    <Icon icon="question-circle" />
                  </HeadlessUI.Popover.Button>
                  <HeadlessUI.Transition
                    as={Fragment}
                    enter="transition ease-out duration-200"
                    enterFrom="opacity-0 translate-y-1"
                    enterTo="opacity-100 translate-y-0"
                    leave="transition ease-in duration-150"
                    leaveFrom="opacity-100 translate-y-0"
                    leaveTo="opacity-0 translate-y-1"
                  >
                    <HeadlessUI.Popover.Panel className="absolute bottom-10 z-10 w-96 -translate-x-1/2 transform px-4">
                      <div className="rounded-lg bg-yellow-50 p-4 font-normal shadow-lg ring-1 ring-black ring-opacity-5">
                        Internal notes are Flora Pack notes that will not be
                        visible to the Customer or Growers.
                      </div>
                    </HeadlessUI.Popover.Panel>
                  </HeadlessUI.Transition>
                </HeadlessUI.Popover>
              </label>
              {!showNewComment && (
                <button
                  type="button"
                  onClick={handleAddCommentsClick}
                  className="btn-secondary mb-1 border-green-600 px-2 py-1 text-xs text-green-600"
                >
                  <Icon icon="comment-plus" />
                  &nbsp; Add Comment
                </button>
              )}
            </div>
            {showNewComment && (
              <textarea
                rows={2}
                name="comments"
                id="comments"
                value={internalComments || ''}
                onChange={handleInternalCommentsChange}
                className="block w-full rounded-md border-gray-300 text-sm shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            )}
          </div>
          <div className="mt-4">
            <label
              htmlFor="grower-item-notes"
              className="block text-sm font-medium text-gray-500"
            >
              Grower Item Notes&nbsp;
              <HeadlessUI.Popover className="relative inline-block">
                <HeadlessUI.Popover.Button
                  className="btn-secondary mb-1 rounded-full px-1 py-0 text-sm text-blue-500 focus:ring-0"
                  tabIndex={-1}
                >
                  <Icon icon="question-circle" />
                </HeadlessUI.Popover.Button>
                <HeadlessUI.Transition
                  as={Fragment}
                  enter="transition ease-out duration-200"
                  enterFrom="opacity-0 translate-y-1"
                  enterTo="opacity-100 translate-y-0"
                  leave="transition ease-in duration-150"
                  leaveFrom="opacity-100 translate-y-0"
                  leaveTo="opacity-0 translate-y-1"
                >
                  <HeadlessUI.Popover.Panel className="absolute bottom-10 z-10 w-96 -translate-x-1/2 transform px-4">
                    <div className="rounded-lg bg-yellow-50 p-4 font-normal shadow-lg ring-1 ring-black ring-opacity-5">
                      Grower Item notes will be included on{' '}
                      <span className="font-semibold italic">each item</span> of{' '}
                      <span className="font-semibold italic">each Prebook</span>
                      .
                    </div>
                  </HeadlessUI.Popover.Panel>
                </HeadlessUI.Transition>
              </HeadlessUI.Popover>
            </label>
            <textarea
              rows={2}
              name="growerItemNotes"
              id="grower-item-notes"
              value={growerItemNotes || ''}
              onChange={handleGrowerItemNotesChange}
              className="block w-full rounded-md border-gray-300 text-sm shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
          </div>
          <div>
            <div className="mt-4 flex w-full justify-between">
              <label
                htmlFor="standard-comments"
                className="text-sm font-medium text-gray-500"
              >
                Standard Comments &nbsp;
                <HeadlessUI.Popover className="relative inline-block">
                  <HeadlessUI.Popover.Button
                    className="btn-secondary mb-1 rounded-full px-1 py-0 text-sm text-blue-500 focus:ring-0"
                    tabIndex={-1}
                  >
                    <Icon icon="question-circle" />
                  </HeadlessUI.Popover.Button>
                  <HeadlessUI.Transition
                    as={Fragment}
                    enter="transition ease-out duration-200"
                    enterFrom="opacity-0 translate-y-1"
                    enterTo="opacity-100 translate-y-0"
                    leave="transition ease-in duration-150"
                    leaveFrom="opacity-100 translate-y-0"
                    leaveTo="opacity-0 translate-y-1"
                  >
                    <HeadlessUI.Popover.Panel className="absolute bottom-10 z-10 w-96 -translate-x-1/2 transform px-4">
                      <div className="rounded-lg bg-yellow-50 p-4 font-normal shadow-lg ring-1 ring-black ring-opacity-5">
                        Standard Comments will be included as line-items on the
                        Spire order.
                      </div>
                    </HeadlessUI.Popover.Panel>
                  </HeadlessUI.Transition>
                </HeadlessUI.Popover>
              </label>
              <HeadlessUI.Menu as="div" className="relative">
                <HeadlessUI.Menu.Button className="btn-secondary mb-1 border-blue-600 px-2 py-1 text-xs text-blue-600">
                  <Icon icon="comment-alt-captions" />
                  &nbsp; Add Standard Comment&nbsp;
                  <Icon icon="chevron-down" />
                </HeadlessUI.Menu.Button>
                <HeadlessUI.Transition
                  as={Fragment}
                  enter="transition duration-100 ease-out"
                  enterFrom="transform scale-95 opacity-0"
                  enterTo="transform scale-100 opacity-100"
                  leave="transition duration-75 ease-out"
                  leaveFrom="transform scale-100 opacity-100"
                  leaveTo="transform scale-95 opacity-0"
                >
                  <HeadlessUI.Menu.Items className="absolute right-0 z-40 mt-2 max-h-60 w-56 origin-top-right divide-y divide-gray-100 overflow-y-auto rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                    {(inventoryComments || []).map((comment) => (
                      <div className="px-1 py-1" key={comment.id}>
                        <HeadlessUI.Menu.Item>
                          {({ active }) => (
                            <button
                              type="button"
                              className={`${
                                active
                                  ? 'bg-blue-500 text-white'
                                  : 'text-gray-900'
                              } group flex w-full flex-col items-start rounded-md px-2 py-1 text-sm`}
                              onClick={() => handleAddStandardComment(comment)}
                            >
                              <div>{comment.code}</div>
                              <div className="truncate text-xs italic">
                                {comment.description}
                              </div>
                            </button>
                          )}
                        </HeadlessUI.Menu.Item>
                      </div>
                    ))}
                  </HeadlessUI.Menu.Items>
                </HeadlessUI.Transition>
              </HeadlessUI.Menu>
            </div>
            <div className="overflow-y-auto">
              {comments
                .filter((c) => c.isStandardComment)
                .map((comment) => (
                  <div
                    key={comment.id}
                    className="border-b-2 text-xs text-gray-500"
                  >
                    <button
                      type="button"
                      className="btn-delete border-transparent px-2 py-1"
                      onClick={() =>
                        handleDeleteStandardCommentClick(comment.id)
                      }
                    >
                      <Icon icon="trash" />
                    </button>
                    {comment.comments}
                  </div>
                ))}
            </div>
          </div>
        </div>
      </form>
      <div className="mt-4 flex justify-end">
        <button
          type="button"
          className="btn-secondary px-8 text-lg"
          onClick={handleCancelClick}
        >
          Cancel
        </button>
        <button
          type="button"
          className="btn-secondary ml-2 px-8 text-lg disabled:px-8 disabled:text-lg"
          onClick={handleBackClick}
        >
          <Icon icon="chevron-left" />
          &nbsp; Back
        </button>
        <button
          type="button"
          className="btn-primary ml-2 px-8 text-lg disabled:px-8 disabled:text-lg"
          onClick={handleNextClick}
        >
          Next &nbsp;
          <Icon icon="chevron-right" />
        </button>
      </div>
    </div>
  );
}
