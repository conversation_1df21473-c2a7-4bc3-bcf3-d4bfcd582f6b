import React, { Fragment, useEffect, useState } from 'react';
import * as HeadlessUI from '@headlessui/react';
import { Icon } from '@/components/icon';
import { boekestynApi, ProductionOrder } from 'api/boekestyn-service';
import {
  useOpenBlanketItemsQuery,
  useSeasonsQuery,
} from 'api/prebooks-service';
import { useInventoryItemsQuery, useVendorsQuery } from 'api/spire-service';
import * as boeks from 'api/models/boekestyns';
import * as prebooks from 'api/models/prebooks';
import * as settings from 'api/models/settings';
import * as spire from 'api/models/spire';
import { InventoryListItem } from './inventory-list-item';
import { contains } from '@/utils/equals';
import { formatDate, formatNumber } from '@/utils/format';
import { weekFromDate, weekFromWeekAndYear } from '@/utils/weeks';

interface InventorySelection {
  item: spire.InventoryItem;
  blanketItemId: number | null;
  boekestynPlantId: string | null;
  boekestynCustomerAbbreviation: string | null;
  hasBlanketItems: boolean;
}

export interface InventoryProps {
  customerItemCodeDefaults: settings.CustomerItemCodeDefault[];
  customer: spire.Customer | null;
  shipTo: spire.CustomerShipTo | null;
  requiredDate: string | null;
  seasonName: string | null;
  open: boolean;
  confirm: (
    value: spire.InventoryItem,
    blanketItemId: number | null,
    boekestynPlantId: string | null,
    boekestynCustomerAbbreviation: string | null,
    comment: typeof prebooks.overAndAboveBlanketComment | null
  ) => void;
  cancel: () => void;
}

export function Inventory({
  open,
  confirm,
  cancel,
  customerItemCodeDefaults,
  customer,
  shipTo,
  requiredDate,
  seasonName,
}: InventoryProps) {
  const [query, setQuery] = useState(''),
    [comments, setComments] = useState<
      typeof prebooks.overAndAboveBlanketComment | null
    >(null),
    [productionOrders, setProductionOrders] = useState<ProductionOrder[]>([]),
    { data: inventoryItemsData } = useInventoryItemsQuery(),
    { data: allVendors } = useVendorsQuery(),
    { data: openBlanketItemsResponse } = useOpenBlanketItemsQuery(),
    { data: seasons } = useSeasonsQuery(),
    [comboBoxValue, setComboBoxValue] = useState<InventorySelection | null>(
      null
    ),
    filteredCustomerItemCodeDefaults = customerItemCodeDefaults
      .filter(
        (d) =>
          d.customerId === customer?.id &&
          (d.shipToId == null || d.shipToId === shipTo?.id) &&
          contains(d.customerItemCode, query)
      )
      .map((d) => d.spireInventoryId),
    inventoryItems = inventoryItemsData?.inventoryItems || [],
    productDefaults = inventoryItemsData?.productDefaults || [],
    filteredCollection =
      query.length >= 3
        ? inventoryItems.filter(
            (i) =>
              contains(i.partNo, query) ||
              contains(i.description, query) ||
              filteredCustomerItemCodeDefaults.indexOf(i.id) !== -1
          )
        : [],
    collection = filteredCollection.slice(0, 100);

  useEffect(() => {
    boekestynApi.productionOrders().then((orders) => {
      const today = formatDate(new Date()),
        { weekId } = weekFromDate(today)!,
        upcomingOrders = orders.filter(
          (o) => weekFromWeekAndYear(o.weekNumber, o.year)?.weekId >= weekId
        );
      setProductionOrders(upcomingOrders);
    });
  }, []);

  const handleQueryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(e.target.value);
  };

  const handleComboboxChange = (selection: InventorySelection) => {
    setComboBoxValue(selection);
    const vendor = allVendors?.find(
        (v) => v.vendorNo === selection.item.primaryVendor?.vendorNo
      ),
      comments =
        selection.hasBlanketItems &&
        !selection.blanketItemId &&
        vendor?.id !== boeks.BoekestynVendorId
          ? prebooks.overAndAboveBlanketComment
          : null;
    setComments(comments);
    cancel();
  };

  const handleClose = () => {
    cancel();
  };

  const handleAfterLeave = () => {
    if (comboBoxValue) {
      confirm(
        comboBoxValue.item,
        comboBoxValue.blanketItemId,
        comboBoxValue.boekestynPlantId,
        comboBoxValue.boekestynCustomerAbbreviation,
        comments
      );
    } else {
      cancel();
    }

    setQuery('');
    setComboBoxValue(null);
  };

  return (
    <HeadlessUI.Transition.Root
      show={open}
      as={Fragment}
      afterLeave={handleAfterLeave}
    >
      <HeadlessUI.Dialog
        as="div"
        className="relative z-30"
        onClose={handleClose}
      >
        <HeadlessUI.Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </HeadlessUI.Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-0 text-center">
            <HeadlessUI.Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-0 scale-95"
              enterTo="opacity-100 translate-y-0 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 scale-100"
              leaveTo="opacity-0 translate-y-0 scale-95"
            >
              <HeadlessUI.Dialog.Panel className="relative my-8 w-full max-w-2xl transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all">
                <HeadlessUI.Combobox
                  onChange={handleComboboxChange}
                  value={comboBoxValue}
                >
                  <div className="h-[75vh] bg-white p-6 px-4 pb-4 pt-5">
                    <div className="ml-4 mt-0 flex h-full flex-col text-left">
                      <div className="flex flex-row">
                        <div className="mx-0 mr-5 flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-blue-100">
                          <Icon
                            icon="search"
                            className="h-6 w-6 text-blue-600"
                            aria-hidden="true"
                          />
                        </div>
                        <div className="flex flex-col">
                          <HeadlessUI.Dialog.Title
                            as="h3"
                            className="text-lg font-medium leading-6 text-gray-900"
                          >
                            Find Inventory Item
                          </HeadlessUI.Dialog.Title>
                          <p>
                            Search for a Spire Inventory Item by Part Number or
                            Description
                          </p>
                        </div>
                      </div>
                      <div className="mx-8 flex flex-col overflow-auto text-center text-sm">
                        <div className="flex pt-2">
                          <HeadlessUI.Combobox.Input
                            type="search"
                            className="mx-[1px] w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-xs shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                            autoComplete="off"
                            value={query}
                            onChange={handleQueryChange}
                          />
                        </div>
                        <div className="mt-3 overflow-auto rounded-md border">
                          <HeadlessUI.Combobox.Options
                            static
                            className="divide-y-4 divide-white"
                          >
                            {query.length > 3 && !inventoryItems?.length && (
                              <li className="relative bg-white px-4 py-5">
                                No Items found
                              </li>
                            )}
                            {collection.map((item) => (
                              <InventoryListItem
                                key={item.id}
                                item={item}
                                customerItemCodeDefaults={
                                  customerItemCodeDefaults
                                }
                                productDefaults={productDefaults}
                                seasons={seasons || []}
                                allBlanketItems={
                                  openBlanketItemsResponse?.blanketItems || []
                                }
                                productionOrders={productionOrders}
                                allVendors={allVendors || []}
                                customer={customer}
                                shipTo={shipTo}
                                requiredDate={requiredDate}
                                seasonName={seasonName}
                              />
                            ))}
                          </HeadlessUI.Combobox.Options>
                        </div>
                      </div>
                    </div>
                  </div>
                </HeadlessUI.Combobox>
                <div className="w-100 flex flex-row bg-gray-50 px-6 py-3">
                  <div className="flex-1 text-center">
                    {!!query &&
                      `${
                        filteredCollection.length
                          ? formatNumber(filteredCollection.length)
                          : 'No'
                      } item${
                        filteredCollection.length === 1 ? '' : 's'
                      } found`}
                    {filteredCollection.length > collection.length &&
                      ` (limited to ${collection.length} items)`}
                  </div>
                  <button
                    type="button"
                    className="ml-3 mt-0 inline-flex w-auto justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    onClick={handleClose}
                  >
                    Cancel
                  </button>
                </div>
              </HeadlessUI.Dialog.Panel>
            </HeadlessUI.Transition.Child>
          </div>
        </div>
      </HeadlessUI.Dialog>
    </HeadlessUI.Transition.Root>
  );
}
