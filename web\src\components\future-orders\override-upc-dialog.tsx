import { Fragment, useState, useRef } from 'react';
import * as HeadlessUI from '@headlessui/react';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import {
  ProblemDetails,
  createProblemDetails,
  isProblemDetails,
} from '@/utils/problem-details';
import { formatDate, formatNumber } from '@/utils/format';
import {
  upcOverride,
  DetailItem,
  selectFutureOrder,
  selectPrebooks,
} from './future-order-detail-slice';

interface OverrideUPCDialogProps {
  onClose: () => void;
  item: DetailItem;
  open: boolean;
}

export function OverrideUPCDialog({
  onClose,
  item,
  open,
}: OverrideUPCDialogProps) {
  const dispatch = useAppDispatch(),
    futureOrder = useAppSelector(selectFutureOrder),
    prebooks = useAppSelector(selectPrebooks),
    prebook = prebooks.find((p) =>
      p.items.some((i) => i.futureOrderItemId === item.id)
    ),
    bodyRef = useRef<HTMLTextAreaElement>(null),
    [body, setBody] = useState(''),
    [subject, setSubject] = useState('Printed UPC Changed'),
    [cc, setCC] = useState(''),
    [isLoading, setIsLoading] = useState(false),
    [error, setError] = useState<ProblemDetails | null>(null),
    preamble =
      `The UPC had been marked as printed for ${item.spirePartNumber} (${item.description}), but changes have been made. ` +
      'Please be sure to discard of the original UPC’s.\n' +
      `\n\tFuture Order: #${formatNumber(futureOrder?.id, '00000')}` +
      (prebook ? `\n\tPrebook: #${formatNumber(prebook.id, '00000')}` : '') +
      (futureOrder?.requiredDate
        ? `\n\tRequired Date: ${formatDate(futureOrder.requiredDate)}`
        : '') +
      (futureOrder?.boxCode ? `\n\tBox Code: ${futureOrder.boxCode}` : '') +
      (item.dateCode ? `\n\tDate Code: ${item.dateCode}` : '') +
      `\n\tQuantity: ${formatNumber(item.orderQuantity)}`;

  const handleClearErrorClick = () => {
    setError(null);
  };

  const handleCCChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCC(e.target.value);
  };

  const handleSubjectChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSubject(e.target.value);
  };

  const handleBodyChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setBody(e.target.value);
  };

  const handleSaveClick = async () => {
    if (!body) {
      return setError(
        createProblemDetails('Please enter the body of the email.')
      );
    }

    if (!subject) {
      return setError(
        createProblemDetails('Please enter the subject of the email.')
      );
    }

    try {
      setIsLoading(true);

      const args = {
        prebookItemId: item.id,
        subject,
        body:
          preamble.replaceAll(/\n/g, '<br>') +
          (body ? '<br><br>Additional Comments:<br>' : '') +
          body.replaceAll(/\n/g, '<br>'),
        cc,
      };
      const response = await dispatch(upcOverride(args));
      if ('error' in response) {
        if (isProblemDetails(response.error)) {
          return setError(response.error);
        } else if (isProblemDetails(response.payload)) {
          return setError(response.payload);
        } else if (typeof response.error === 'string') {
          return setError(createProblemDetails(response.error));
        } else if (typeof response.error.message === 'string') {
          return setError(createProblemDetails(response.error.message));
        }

        return setError(
          createProblemDetails('There was a problem sending the email.')
        );
      }

      onClose();
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <HeadlessUI.Transition.Root as={Fragment} show={open}>
      <HeadlessUI.Dialog
        as="div"
        onClose={onClose}
        className="relative z-30"
        initialFocus={bodyRef}
      >
        <HeadlessUI.Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </HeadlessUI.Transition.Child>

        <div className="fixed inset-0 z-30 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <HeadlessUI.Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <HeadlessUI.Dialog.Panel className="relative w-full max-w-screen-sm transform overflow-hidden p-6 transition-all">
                <div className="flex h-full flex-col rounded-lg bg-white p-6 text-left shadow-xl">
                  <div className="mb-4 flex justify-center border-b-2 pb-4">
                    <HeadlessUI.Dialog.Title
                      as="h3"
                      className="text-lg font-medium leading-6 text-gray-900"
                    >
                      <Icon
                        icon="envelope"
                        className="h-6 w-6"
                        aria-hidden="true"
                      />
                      &nbsp; Send UPC Override Email
                    </HeadlessUI.Dialog.Title>
                  </div>
                  <form>
                    <label
                      htmlFor="subject"
                      className="block pt-2 text-sm font-medium text-gray-700"
                    >
                      Subject
                    </label>
                    <input
                      type="text"
                      name="subject"
                      id="subject"
                      tabIndex={0}
                      value={subject}
                      onChange={handleSubjectChange}
                      autoComplete="off"
                      className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    />
                    <label
                      htmlFor="to"
                      className="block pt-2 text-sm font-medium text-gray-700"
                    >
                      To
                    </label>
                    <input
                      type="text"
                      id="to"
                      tabIndex={-1}
                      value="<EMAIL>"
                      readOnly
                      className="block w-full cursor-default rounded-md border-gray-300 shadow-sm ring-0 focus:border-gray-300 focus:ring-0"
                    />

                    <label
                      htmlFor="cc"
                      className="block pt-2 text-sm font-medium text-gray-700"
                    >
                      CC
                    </label>
                    <input
                      type="text"
                      id="cc"
                      tabIndex={1}
                      value={cc}
                      onChange={handleCCChange}
                      autoComplete="off"
                      className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    />

                    <p className="mt-2 text-sm text-gray-500">
                      Separate multiple email addresses with semicolons (;)
                    </p>

                    <div className="col-span-3">
                      <label
                        htmlFor="body"
                        className="mt-px block pt-2 text-sm font-medium text-gray-700"
                      >
                        Body
                      </label>
                      <p className="whitespace-pre-wrap text-sm">{preamble}</p>
                      <label
                        htmlFor="body"
                        className="mt-px block pt-2 text-sm font-medium text-gray-700"
                      >
                        Additional Comments / Change Details
                      </label>
                      <textarea
                        name="body"
                        id="body"
                        tabIndex={2}
                        rows={7}
                        value={body}
                        onChange={handleBodyChange}
                        autoComplete="off"
                        ref={bodyRef}
                        className="block w-full min-w-0 flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                  </form>
                  <Error
                    error={error}
                    clear={handleClearErrorClick}
                    containerClasses="w-full mt-4"
                  />
                  <div className="mt-4 flex justify-end border-t-2 pt-4">
                    <button
                      type="button"
                      className="btn-secondary text-lg"
                      onClick={onClose}
                    >
                      Cancel
                    </button>
                    <button
                      type="button"
                      className="btn-secondary ml-4 text-lg"
                      onClick={handleSaveClick}
                      disabled={isLoading}
                    >
                      Send
                      <Icon
                        icon={isLoading ? 'spinner' : 'send'}
                        className="ml-2"
                        spin={isLoading}
                      />
                    </button>
                  </div>
                </div>
              </HeadlessUI.Dialog.Panel>
            </HeadlessUI.Transition.Child>
          </div>
        </div>
      </HeadlessUI.Dialog>
    </HeadlessUI.Transition.Root>
  );
}
