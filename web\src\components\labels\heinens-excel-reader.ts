import * as XLSX from 'xlsx';
import * as models from 'api/models/labels';
import { DateTime } from 'luxon';
import { equals } from '@/utils/equals';
import { parseCurrency, parseQuantity } from '@/utils/format';

export function read(data: ArrayBuffer): HeinensLabels {
  const workbook = XLSX.read(data, { type: 'buffer' }),
    items: models.HeinensLabelItem[] = [],
    sheetname = workbook.SheetNames[0],
    sheet = workbook.Sheets[sheetname],
    rows = XLSX.utils.sheet_to_json<any>(sheet, {
      header: 'A',
      raw: false,
    }),
    requiredDateRegex = /ORDER DATE:\s+(\d{2}\/\d{2}\/\d{4})/i,
    requiredDateValue = (rows.find((r) => r.__rowNum__ === 2)?.A || '').trim(),
    requiredDate = requiredDateRegex.test(requiredDateValue)
      ? DateTime.fromFormat(
          requiredDateRegex.exec(requiredDateValue)?.[1] || '',
          'MM/dd/yyyy'
        ).toFormat('yyyy-MM-dd')
      : DateTime.local().toFormat('yyyy-MM-dd'),
    purchaseOrderRegex = /(\d+)$/i,
    purchaseOrderValue = (rows[0].A || '').trim(),
    purchaseOrderNumber = purchaseOrderRegex.test(purchaseOrderValue)
      ? purchaseOrderRegex.exec(purchaseOrderValue)?.[1] || ''
      : '',
    labels: HeinensLabels = {
      stores: [],
      purchaseOrderNumber,
      requiredDate,
    };

  let rowNumber = 0,
    storeRowNumber = 0;

  for (let i = 0; i < rows.length; i++) {
    if (equals(rows[i].A, 'ArticleCode')) {
      storeRowNumber = i;
      rowNumber = i + 1;
    }
  }

  let row = rows[rowNumber];

  while (row) {
    // depending on the cell format there's sometimes a leading $
    const productNumber = (row.A || '').replaceAll('$', '');

    if (productNumber) {
      const upc = (row.B || '')
          .toString()
          .replaceAll('-', '')
          .replaceAll(' ', ''),
        description: string = row.C || '',
        packQuantityValue = row.D,
        packQuantity =
          typeof packQuantityValue === 'string'
            ? parseQuantity(packQuantityValue)
            : typeof packQuantityValue === 'number'
            ? packQuantityValue
            : null,
        type = models.heinensLabelType(description);

      items.push({
        rowNumber,
        upc,
        description,
        productNumber,
        packQuantity,
        type,
      });
    }
    rowNumber++;
    row = rows[rowNumber];
  }

  const columnSet = new Set<string>();
  rows.forEach((row) => {
    Object.getOwnPropertyNames(row).forEach((_, index) => {
      columnSet.add(XLSX.utils.encode_col(index)); // Convert column index to Excel-style column name
    });
  });

  const storeRow = rows[storeRowNumber],
    storeRegex = /#\d+/,
    props = Array.from(columnSet),
    skidColumn = props.find((_, i) => {
      const previous = props[i - 1],
        value = storeRow[previous];
      return typeof value === 'string' && equals(value, 'Total');
    });
  props
    .filter((p) => p !== '__rowNum__')
    .forEach((prop, i) => {
      const raw: string | number | null | undefined = storeRow[prop],
        storeNumber =
          typeof raw === 'string'
            ? raw
            : typeof raw === 'number'
            ? raw.toString()
            : '';
      if (storeRegex.test(storeNumber)) {
        const destination = models.heinensLabelDestination(storeNumber),
          store: models.HeinensLabelStore = {
            storeNumber,
            destination,
            orders: [],
          };
        items.forEach((item) => {
          const itemRow = rows[item.rowNumber],
            rawQuantity: string | number | null | undefined = itemRow[prop],
            quantity =
              typeof rawQuantity === 'string'
                ? // again with the leading $, depending on the cell format
                  parseCurrency(rawQuantity)
                : typeof rawQuantity === 'number'
                ? rawQuantity
                : 0,
            skidQuantityVal = skidColumn
              ? (itemRow[skidColumn] as unknown)
              : null,
            skidQuantity =
              typeof skidQuantityVal === 'string'
                ? parseInt(skidQuantityVal) || null
                : typeof skidQuantityVal === 'number'
                ? skidQuantityVal
                : null;

          if (quantity) {
            store.orders.push({ ...item, quantity, skidQuantity });
          }
        });
        if (store.orders.length) {
          labels.stores.push(store);
        }
      }
    });

  return labels;
}

export interface HeinensLabels {
  purchaseOrderNumber: string;
  requiredDate: string;
  stores: models.HeinensLabelStore[];
}
