import {
  createAction,
  createAsyncThunk,
  createSlice,
  AsyncThunk,
  PayloadAction,
} from '@reduxjs/toolkit';
import { boekestynApi } from 'api/boekestyn-service';
import * as boeks from 'api/models/boekestyns';
import * as futureOrders from 'api/models/future-orders';
import { RootState } from '@/services/store';
import { ProblemDetails } from '@/utils/problem-details';
import { sortBy } from '@/utils/sort';

const sortByName = sortBy('name');

export interface BoekestynProduct
  extends futureOrders.FutureOrderBoekestynProduct {
  id: number;
}

export interface ProductSettingsState {
  showDialog:
    | futureOrders.FutureOrderDetailItem
    | futureOrders.FutureOrderCreateItem
    | null;
  boekestynProducts: BoekestynProduct[];
  error: ProblemDetails | null;
  plants: boeks.Plant[];
  customers: boeks.Customer[];
}

const initialState: ProductSettingsState = {
  showDialog: null,
  boekestynProducts: [],
  error: null,
  plants: [],
  customers: [],
};

interface GetDataResponse {
  plants: boeks.Plant[];
  customers: boeks.Customer[];
}

export const getData: AsyncThunk<GetDataResponse, void, { state: RootState }> =
  createAsyncThunk(
    'boekestyn-product-getData',
    async (_, { rejectWithValue }) => {
      try {
        const [plants, customers] = await Promise.all([
          boekestynApi.plants(),
          boekestynApi.customers(),
        ]);

        return { plants, customers };
      } catch (e) {
        return rejectWithValue(e as ProblemDetails);
      }
    }
  );

const getDataFulfilled = createAction<GetDataResponse>(getData.fulfilled.type),
  getDataRejected = createAction<ProblemDetails>(getData.rejected.type);

export const boekestynProductSlice = createSlice({
  name: 'boekestyn-product-slice',
  initialState,
  reducers: {
    setError(state, { payload }: PayloadAction<ProblemDetails | null>) {
      state.error = payload;
    },
    setFutureOrderItem(
      state,
      {
        payload,
      }: PayloadAction<
        futureOrders.FutureOrderDetailItem | futureOrders.FutureOrderCreateItem
      >
    ) {
      state.boekestynProducts = payload.boekestynProducts.map((p, id) => ({
        ...p,
        id,
      }));
      state.showDialog = payload;
    },
    setBoekestynProducts(
      state,
      { payload }: PayloadAction<BoekestynProduct[]>
    ) {
      state.boekestynProducts = payload;
    },
    hideDialog(state) {
      state.showDialog = null;
    },
  },
  extraReducers: (builder) =>
    builder
      .addCase(getDataFulfilled, (state, { payload }) => {
        state.plants = payload.plants.sort(sortByName);
        state.customers = payload.customers.sort(sortByName);
      })
      .addCase(getDataRejected, (state, { payload }) => {
        state.error = payload;
      }),
});

export const {
  setError,
  setBoekestynProducts,
  hideDialog,
  setFutureOrderItem,
} = boekestynProductSlice.actions;

export const selectShowDialog = ({ boekestynProducts }: RootState) =>
  boekestynProducts.showDialog;
export const selectError = ({ boekestynProducts }: RootState) =>
  boekestynProducts.error;
export const selectPlants = ({ boekestynProducts }: RootState) =>
  boekestynProducts.plants;
export const selectCustomers = ({ boekestynProducts }: RootState) =>
  boekestynProducts.customers;
export const selectBoekestynProducts = ({ boekestynProducts }: RootState) =>
  boekestynProducts.boekestynProducts;

export default boekestynProductSlice.reducer;
