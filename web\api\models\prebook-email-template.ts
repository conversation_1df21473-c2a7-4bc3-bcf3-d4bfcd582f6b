import { AccountInfo } from '@azure/msal-browser';
import { PendingPrebookEmail } from './prebooks';
import { formatNumber, formatDate } from '@/utils/format';

export function replaceTokens(
  value: string,
  prebookEmail: PendingPrebookEmail | null,
  account: AccountInfo | null
) {
  if (!prebookEmail) {
    return value;
  }

  const boxCode = prebookEmail.items.filter((i) => i.boxCode)[0]?.boxCode || '',
    dateCode = prebookEmail.items.filter((i) => i.dateCode)[0]?.dateCode || '',
    userName = account?.name || '';

  return value
    .replaceAll(
      '{PrebookNumber}',
      formatNumber(prebookEmail.prebookId, '00000')
    )
    .replaceAll(
      '{RequiredDate}',
      formatDate(prebookEmail.requiredDate) || prebookEmail.seasonName || ''
    )
    .replaceAll('{BoxCode}', boxCode)
    .replaceAll('{DateCode}', dateCode)
    .replaceAll('{UserName}', userName);
}
