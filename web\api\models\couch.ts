import { SalesWeek } from './boekestyns';

export interface ModelBase {
  _id: string;
  _rev?: string;
}

export interface Variety {
  name: string;
}

export interface PlantModel extends ModelBase {
  type: string;
  name: string;
  abbreviation: string;
  crop: string;
  size: string;
  colour?: string | null;
  cuttingsPerPot: number;
  cuttingsPerTableTight: number;
  cuttingsPerTableSpaced: number;
  cuttingsPerTablePartiallySpaced: number;
  potsPerCase: number;
  hasLightsOut: boolean;
  hasPinching: boolean;
  pinchingPotsPerHour: number;
  daysToPinch?: number;
  stickingCuttingsPerHour: number;
  spacingPotsPerHour: number;
  packingCasesPerHour: number;
  varieties?: Variety[];
}

export interface OrderModel extends ModelBase {
  type: string;
  orderNumber: string;
  customer: Customer;
  plant: PlantModel;
  cuttings: number;
  pots: number;
  cases: number;
  supplierPoNumber: string;
  stickDate: string;
  stickZone: Zone;
  hasPartialSpace: boolean;
  partialSpaceDate?: string;
  partialSpaceZone?: Zone;
  hasSpacing: boolean;
  fullSpaceDate?: string;
  fullSpaceZone?: Zone;
  hasLightsOut: boolean;
  lightsOutDate?: string;
  lightsOutZone?: Zone;
  hasPinching: boolean;
  pinchDate?: string;
  flowerDate: string;
  tableCountTight: number;
  tableCountPartiallySpaced?: number;
  tableCountSpaced?: number;
  notes: string | null;
  season: string | null;
  varieties?: OrderVariety[];
  salesWeeks?: SalesWeek[];
}

export interface Customer extends ModelBase {
  type: string;
  abbreviation: string;
  name: string;
}

export interface Zone extends ModelBase {
  type: string;
  name: string;
  tables: number;
}

export interface OrderVariety {
  name: string;
  cuttings: number;
  pots: number;
  cases: number;
  comment: string | null;
}
