<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <system.webServer>
  <iisnode nodeProcessCommandLine="C:\Users\<USER>\AppData\Roaming\nvm_symlink\node.exe" debuggingEnabled="false" loggingEnabled="true" />
  <handlers>
      <add name="iisnode" path="server.js" verb="*" modules="iisnode" />
    </handlers>
    <httpErrors errorMode="Custom" defaultResponseMode="File">
        <remove statusCode="404" />
        <error statusCode="404" path="/404.html" responseMode="ExecuteURL" />
   </httpErrors>
    <rewrite>
      <rules>
        <rule name="app" patternSyntax="Wildcard">
          <match url="*" />
          <action type="Rewrite" url="server.js" />
        </rule>
      </rules>
    </rewrite>
  </system.webServer>
</configuration>
