import React from 'react';
import * as futureOrders from 'api/models/future-orders';
import { useAppDispatch } from '@/services/hooks';
import { classNames } from '@/utils/class-names';
import { formatCurrency, formatNumber } from '@/utils/format';
import { addItem } from './add-from-order-slice';

export interface AddFromOrderItemListRowProps {
  item: futureOrders.FutureOrderDetailItem;
  order: futureOrders.FutureOrderDetail;
}

export function AddFromOrderItemListRow({
  item,
  order,
}: AddFromOrderItemListRowProps) {
  const dispatch = useAppDispatch(),
    cellClassName =
      'whitespace-nowrap px-1 py-1 text-gray-500 border-bottom-0 text-xs align-top';

  const handleClick = () => {
    const orderNumber = formatNumber(order.id, '00000'),
      customer = order.customerName || '',
      shipTo = order.shipToName || '';
    dispatch(addItem({ ...item, orderNumber, customer, shipTo }));
  };

  return (
    <tr
      className="cursor-pointer border border-b-2 border-gray-200 hover:bg-gray-200"
      onClick={handleClick}
    >
      <td className={cellClassName}>{item.spirePartNumber}</td>
      <td className={cellClassName}>
        {item.description}
        {!!item.comments && <div className="italic">{item.comments}</div>}
      </td>
      <td className={classNames(cellClassName, 'text-center')}>
        {item.orderQuantity}
      </td>
      <td className={classNames(cellClassName, 'text-center')}>
        {!!item.unitPrice && formatCurrency(item.unitPrice)}
      </td>
    </tr>
  );
}
