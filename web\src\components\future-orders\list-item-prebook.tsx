import { Fragment, useState } from 'react';
import Link from 'next/link';
import * as HeadlessUI from '@headlessui/react';
import * as models from 'api/models/future-orders';
import { usePermissions } from '@/services/auth';
import { routes } from '@/services/routes';
import { useAppDispatch } from '@/services/hooks';
import { Icon } from '@/components/icon';
import { setGrowerConfirmed } from '@/components/prebooks/prebook-detail-slice';
import { formatNumber, formatDate } from '@/utils/format';

interface ListItemPrebookArgs {
  prebook: models.FutureOrderListItemPrebook;
  refresh: () => void;
}

export function ListItemPrebook({ prebook, refresh }: ListItemPrebookArgs) {
  const dispatch = useAppDispatch(),
    { can } = usePermissions(),
    [tooltipOpen, setTooltipOpen] = useState(false),
    readonly = !can('Sales Team');

  const handleTooltipMouseEnter = () => {
    setTooltipOpen(true);
  };

  const handleTooltipMouseLeave = () => {
    setTooltipOpen(false);
  };

  const handleSetGrowerConfirmedClick = async (id: number) => {
    await dispatch(setGrowerConfirmed(id));
    refresh();
  };

  return (
    <tr className="bg-gray-100">
      <td className="whitespace-nowrap border-b-2 border-white bg-blue-100 p-2 text-left text-xs text-gray-600">
        <HeadlessUI.Popover
          className="inline-block translate-y-0 cursor-pointer"
          onMouseEnter={handleTooltipMouseEnter}
          onMouseLeave={handleTooltipMouseLeave}
        >
          <>
            <HeadlessUI.Popover.Button as="div" className="font-lg px-2 py-1">
              <Icon
                icon={
                  !prebook.sent
                    ? 'triangle-exclamation'
                    : !prebook.confirmed
                    ? 'question-circle'
                    : 'check-circle'
                }
                className={
                  !prebook.sent
                    ? 'text-red-600'
                    : !prebook.confirmed
                    ? 'text-yellow-600'
                    : 'text-green-600'
                }
              />
            </HeadlessUI.Popover.Button>
            <HeadlessUI.Transition
              as={Fragment}
              show={tooltipOpen}
              enter="transition ease-out duration-200"
              enterFrom="opacity-0 translate-y-1"
              enterTo="opacity-100 translate-y-0"
              leave="transition ease-in duration-150"
              leaveFrom="opacity-100 translate-y-0"
              leaveTo="opacity-0 translate-y-1"
            >
              <HeadlessUI.Popover.Panel
                static
                className="absolute left-[25px] z-10 -translate-y-1/2 transform bg-white"
              >
                <div className="rounded-lg border p-4 shadow-lg">
                  <p className="my-2 text-left text-xs text-gray-500">
                    Created by{' '}
                    <span className="font-medium">{prebook.createdBy}</span> on{' '}
                    <span className="font-medium">
                      {formatDate(prebook.created)}
                    </span>{' '}
                    @{' '}
                    <span className="font-medium">
                      {formatDate(prebook.created, 'h:mm a')}
                    </span>
                  </p>
                  {prebook.created !== prebook.modified && (
                    <p className="my-2 text-left text-xs text-gray-500">
                      Updated by{' '}
                      <span className="font-medium">{prebook.modifiedBy}</span>{' '}
                      on{' '}
                      <span className="font-medium">
                        {formatDate(prebook.modified)}
                      </span>{' '}
                      @{' '}
                      <span className="font-medium">
                        {formatDate(prebook.modified, 'h:mm a')}
                      </span>
                    </p>
                  )}
                  {!!prebook.sent && (
                    <p className="my-2 text-left text-xs text-gray-500">
                      Last sent by{' '}
                      <span className="font-medium">{prebook.sentBy}</span> on{' '}
                      <span className="font-medium">
                        {formatDate(prebook.sent)}
                      </span>{' '}
                      @{' '}
                      <span className="font-medium">
                        {formatDate(prebook.sent, 'h:mm a')}
                      </span>
                    </p>
                  )}
                  {!prebook.sent && (
                    <p className="my-2 text-left text-xs font-bold italic text-red-500">
                      <span className="mr-4 inline-block">
                        Not sent to Grower
                      </span>
                      <Link
                        href={`${routes.prebooks.detail.to(
                          prebook.id
                        )}?action=send`}
                        className="btn-secondary px-2 py-1 text-xs"
                      >
                        Send
                      </Link>
                    </p>
                  )}
                  {!!prebook.confirmed && (
                    <p className="my-2 text-left text-xs text-gray-500">
                      Grower Confirmed by{' '}
                      <span className="font-medium">{prebook.confirmedBy}</span>{' '}
                      on{' '}
                      <span className="font-medium">
                        {formatDate(prebook.confirmed)}
                      </span>{' '}
                      @{' '}
                      <span className="font-medium">
                        {formatDate(prebook.confirmed, 'h:mm a')}
                      </span>
                    </p>
                  )}
                  {!!prebook.sent && !prebook.confirmed && (
                    <p className="my-2 text-left text-xs font-bold italic text-yellow-500">
                      <span className="mr-4 inline-block">
                        Grower has not confirmed
                      </span>
                      {!readonly && (
                        <button
                          type="button"
                          onClick={() =>
                            handleSetGrowerConfirmedClick(prebook.id)
                          }
                          className="btn-secondary px-2 py-1 text-xs"
                        >
                          Confirm
                        </button>
                      )}
                    </p>
                  )}
                </div>
              </HeadlessUI.Popover.Panel>
            </HeadlessUI.Transition>
          </>
        </HeadlessUI.Popover>
      </td>
      <td className="whitespace-nowrap border-b-2 border-white bg-blue-100 p-2 text-left text-xs text-gray-600">
        <Link href={routes.prebooks.detail.to(prebook.id)}>
          {formatNumber(prebook.id, '00000')}
        </Link>
      </td>
      <td
        colSpan={5}
        className="whitespace-nowrap border-b-2 border-white bg-blue-100 p-2 text-left text-xs text-gray-600"
      >
        {prebook.vendor}
      </td>
      <td className="whitespace-nowrap border-b-2 border-white bg-blue-100 p-2 text-center text-xs text-gray-600">
        {formatNumber(prebook.caseCount)}
      </td>
    </tr>
  );
}
