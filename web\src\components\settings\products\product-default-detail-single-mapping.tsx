import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { selectMultipleProducts } from './product-default-detail-slice';
import { handleFocus } from '@/utils/focus';
import {
  selectPlants,
  selectCustomers,
} from './product-default-settings-slice';
import {
  selectBoekestynPlantId,
  setBoekestynPlantId,
  selectBoekestynCustomerAbbreviation,
  setBoekestynCustomerAbbreviation,
  selectQuantityPerFinishedItem,
  setQuantityPerFinishedItem,
} from './product-default-detail-slice';

export function ProductDefaultDetailSingleMapping() {
  const dispatch = useAppDispatch(),
    multipleProducts = useAppSelector(selectMultipleProducts),
    plants = useAppSelector(selectPlants),
    customers = useAppSelector(selectCustomers),
    boekestynPlantId = useAppSelector(selectBoekestynPlantId),
    boekestynCustomerAbbreviation = useAppSelector(
      selectBoekestynCustomerAbbreviation
    ),
    quantityPerFinishedItem = useAppSelector(selectQuantityPerFinishedItem);

  const handlePlantIdChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    dispatch(setBoekestynPlantId(e.target.value));
  };

  const handleCustomerAbbreviationChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setBoekestynCustomerAbbreviation(e.target.value));
  };

  const handleQuantityPerFinishedItemChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    dispatch(setQuantityPerFinishedItem(e.target.valueAsNumber || null));
  };

  if (multipleProducts) {
    return null;
  }

  return (
    <div className="grid grid-cols-2 gap-x-4">
      <div>
        <label className="mt-2 block pt-2 text-sm font-medium text-gray-700">
          Plant Mapping
        </label>
        <select
          value={boekestynPlantId || ''}
          onChange={handlePlantIdChange}
          className="block w-full min-w-0 flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
        >
          <option value="">No Boekestyn Plant</option>
          {plants.map((plant) => (
            <option key={plant._id} value={plant._id}>
              {plant.name}
            </option>
          ))}
        </select>
      </div>

      <div>
        <label className="mt-2 block pt-2 text-sm font-medium text-gray-700">
          Customer
        </label>
        <select
          value={boekestynCustomerAbbreviation || ''}
          onChange={handleCustomerAbbreviationChange}
          className="block w-full min-w-0 flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
        >
          <option value="">No Boekestyn Customer</option>
          {customers.map((customer) => (
            <option key={customer.abbreviation} value={customer.abbreviation}>
              {customer.name} ({customer.abbreviation})
            </option>
          ))}
        </select>
      </div>

      <div>
        <label className="mt-2 block pt-2 text-sm font-medium text-gray-700">
          Quantity / Finished Item
          <div className="font-normal italic text-gray-500">
            (leave blank for 1 per)
          </div>
        </label>
        <input
          type="number"
          value={quantityPerFinishedItem || ''}
          onChange={handleQuantityPerFinishedItemChange}
          onFocus={handleFocus}
          className="block w-32 min-w-0 flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
        />
      </div>
    </div>
  );
}
