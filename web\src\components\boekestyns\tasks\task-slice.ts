import {
  createAction,
  createSlice,
  PayloadAction,
  AsyncThunk,
  createAsyncThunk,
  createSelector,
} from '@reduxjs/toolkit';
import {
  BoekestynTaskListResponse,
  BoekestynTaskReportUrlResponse,
  boekestynApi,
} from 'api/boekestyn-service';
import * as models from 'api/models/boekestyns';
import { RootState } from '@/services/store';
import { ProblemDetails } from '@/utils/problem-details';
import { equals } from '@/utils/equals';
import { formatDate } from '@/utils/format';
import { sortBy } from '@/utils/sort';

export const getTaskList: AsyncThunk<
  BoekestynTaskListResponse,
  void,
  { state: RootState }
> = createAsyncThunk(
  'boekestyn-task-getTaskList',
  async (_, { rejectWithValue, getState }) => {
    try {
      const {
        boekestynTasks: { startDate, endDate },
      } = getState() as RootState;

      return await boekestynApi.purchaseOrderTaskList(startDate, endDate);
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const selectTask: AsyncThunk<
  BoekestynTaskReportUrlResponse | null,
  models.BoekestynTask | null,
  { state: RootState }
> = createAsyncThunk(
  'boekestyn-task-selectTask',
  async (task, { rejectWithValue, dispatch }) => {
    try {
      dispatch(boekestynTaskSlice.actions.setSelectedTask(task));

      if (task) {
        return await boekestynApi.purchaseOrderTaskReportUrl(task.id);
      } else {
        return null;
      }
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const completeUpc: AsyncThunk<undefined, number, { state: RootState }> =
  createAsyncThunk(
    'boekestyn-task-completeUpc',
    async (id, { rejectWithValue, dispatch }) => {
      try {
        await boekestynApi.completePurchaseOrderItemUpc(id);
        dispatch(boekestynTaskSlice.actions.setUpcComplete(id));
      } catch (e) {
        return rejectWithValue(e as ProblemDetails);
      }
    }
  );

interface SetPriorityArgs {
  id: number;
  priority: boolean;
}

export const setPriority: AsyncThunk<
  undefined,
  SetPriorityArgs,
  { state: RootState }
> = createAsyncThunk(
  'boekestyn-task-setPriority',
  async ({ id, priority }, { rejectWithValue, dispatch }) => {
    try {
      await boekestynApi.setPurchaseOrderItemPriority(id, priority);
      dispatch(boekestynTaskSlice.actions.setPriority({ id, priority }));
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const startPrep: AsyncThunk<undefined, number, { state: RootState }> =
  createAsyncThunk(
    'boekestyn-task-startPrep',
    async (id, { rejectWithValue, dispatch }) => {
      try {
        await boekestynApi.startPrep(id);
        dispatch(boekestynTaskSlice.actions.setPrepStart(id));
      } catch (e) {
        return rejectWithValue(e as ProblemDetails);
      }
    }
  );

export const completePrep: AsyncThunk<undefined, number, { state: RootState }> =
  createAsyncThunk(
    'boekestyn-task-completePrep',
    async (id, { rejectWithValue, dispatch }) => {
      try {
        await boekestynApi.completePrep(id);
        dispatch(boekestynTaskSlice.actions.setPrepComplete(id));
      } catch (e) {
        return rejectWithValue(e as ProblemDetails);
      }
    }
  );

export const startPacking: AsyncThunk<undefined, number, { state: RootState }> =
  createAsyncThunk(
    'boekestyn-task-startPacking',
    async (id, { rejectWithValue, dispatch }) => {
      try {
        await boekestynApi.startPacking(id);
        dispatch(boekestynTaskSlice.actions.setPackingStart(id));
      } catch (e) {
        return rejectWithValue(e as ProblemDetails);
      }
    }
  );

export const completePacking: AsyncThunk<
  undefined,
  number,
  { state: RootState }
> = createAsyncThunk(
  'boekestyn-task-completePacking',
  async (id, { rejectWithValue, dispatch }) => {
    try {
      await boekestynApi.completePacking(id);
      dispatch(boekestynTaskSlice.actions.setPackingComplete(id));
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

interface BoekestynTaskState {
  startDate: string | null;
  endDate: string | null;
  tasks: models.BoekestynTask[];
  selectedTask: models.BoekestynTask | null;
  sort: keyof models.BoekestynTaskItem;
  sortDescending: boolean;
  taskUrl: string | null;
  error: ProblemDetails | null;
}

const initialState: BoekestynTaskState = {
  startDate: formatDate(new Date(), 'yyyy-MM-dd'),
  endDate: formatDate(new Date(), 'yyyy-MM-dd'),
  tasks: [],
  selectedTask: null,
  sort: 'description',
  sortDescending: false,
  taskUrl: null,
  error: null,
};

const getTaskListPending = createAction(getTaskList.pending.type),
  getTaskListFulfilled = createAction<BoekestynTaskListResponse>(
    getTaskList.fulfilled.type
  ),
  getTaskListRejected = createAction<ProblemDetails>(getTaskList.rejected.type),
  selectTaskPending = createAction(selectTask.pending.type),
  selectTaskFulfilled = createAction<BoekestynTaskReportUrlResponse | null>(
    selectTask.fulfilled.type
  ),
  selectTaskRejected = createAction<ProblemDetails>(selectTask.rejected.type);

export interface TaskItemSortArgs {
  sort: keyof models.BoekestynTaskItem;
  sortDescending: boolean;
}

const boekestynTaskSlice = createSlice({
  name: 'boekestyn-tasks',
  initialState,
  reducers: {
    clearError(state) {
      state.error = null;
    },
    setSelectedTask(
      state,
      { payload }: PayloadAction<models.BoekestynTask | null>
    ) {
      state.selectedTask = payload;
    },
    setStartDate(state, { payload }: PayloadAction<string | null>) {
      state.startDate = payload;
    },
    setEndDate(state, { payload }: PayloadAction<string | null>) {
      state.endDate = payload;
    },
    setSort(state, { payload }: PayloadAction<TaskItemSortArgs>) {
      state.sort = payload.sort;
      state.sortDescending = payload.sortDescending;
    },
    setUpcComplete(state, { payload }: PayloadAction<number>) {
      const tasks = state.tasks.map((t) => ({ ...t }));

      tasks.forEach((t) => {
        const item = t.items.find((i) => i.id === payload);
        if (item) {
          item.upcComplete = formatDate(new Date(), 'yyyy-MM-dd HH:mm');
        }
      });

      state.tasks = tasks;
    },
    setPriority(state, { payload }: PayloadAction<SetPriorityArgs>) {
      const tasks = state.tasks.map((t) => ({ ...t }));

      tasks.forEach((t) => {
        const item = t.items.find((i) => i.id === payload.id);
        if (item) {
          item.priority = payload.priority;
        }
      });

      state.tasks = tasks;
    },
    setPrepStart(state, { payload }: PayloadAction<number>) {
      const tasks = state.tasks.map((t) => ({ ...t }));

      tasks.forEach((t) => {
        const item = t.items.find((i) => i.id === payload);
        if (item) {
          item.prepStart = formatDate(new Date(), 'yyyy-MM-dd HH:mm');
        }
      });

      state.tasks = tasks;
    },
    setPrepComplete(state, { payload }: PayloadAction<number>) {
      const tasks = state.tasks.map((t) => ({ ...t }));

      tasks.forEach((t) => {
        const item = t.items.find((i) => i.id === payload);
        if (item) {
          item.prepComplete = formatDate(new Date(), 'yyyy-MM-dd HH:mm');
        }
      });

      state.tasks = tasks;
    },
    setPackingStart(state, { payload }: PayloadAction<number>) {
      const tasks = state.tasks.map((t) => ({ ...t }));

      tasks.forEach((t) => {
        const item = t.items.find((i) => i.id === payload);
        if (item) {
          item.packStart = formatDate(new Date(), 'yyyy-MM-dd HH:mm');
        }
      });

      state.tasks = tasks;
    },
    setPackingComplete(state, { payload }: PayloadAction<number>) {
      const tasks = state.tasks.map((t) => ({ ...t }));

      tasks.forEach((t) => {
        const item = t.items.find((i) => i.id === payload);
        if (item) {
          item.packComplete = formatDate(new Date(), 'yyyy-MM-dd HH:mm');
        }
      });

      state.tasks = tasks;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(getTaskListPending, (state) => {
      state.error = null;
    });
    builder.addCase(getTaskListFulfilled, (state, { payload }) => {
      state.tasks = payload.tasks;
    });
    builder.addCase(getTaskListRejected, (state, { payload }) => {
      state.error = payload;
    });
    builder.addCase(selectTaskPending, (state) => {
      state.error = null;
      state.taskUrl = null;
    });
    builder.addCase(selectTaskFulfilled, (state, { payload }) => {
      state.taskUrl = payload?.url || null;
    });
    builder.addCase(selectTaskRejected, (state, { payload }) => {
      state.error = payload;
    });
  },
});

export const {
  setStartDate,
  setEndDate,
  setSort,
  clearError,
  setSelectedTask,
} = boekestynTaskSlice.actions;

const selectAllTasks = ({ boekestynTasks }: RootState) => boekestynTasks.tasks;
export const selectStartDate = ({ boekestynTasks }: RootState) =>
  boekestynTasks.startDate;
export const selectEndDate = ({ boekestynTasks }: RootState) =>
  boekestynTasks.endDate;
export const selectSelectedTask = ({ boekestynTasks }: RootState) =>
  boekestynTasks.selectedTask;
export const selectSort = ({ boekestynTasks }: RootState) =>
  boekestynTasks.sort;
export const selectSortDescending = ({ boekestynTasks }: RootState) =>
  boekestynTasks.sortDescending;
export const selectTaskUrl = ({ boekestynTasks }: RootState) =>
  boekestynTasks.taskUrl;
export const selectError = ({ boekestynTasks }: RootState) =>
  boekestynTasks.error;

const sortByDate = sortBy('requiredDate'),
  sortByPriority = sortBy('priority', 'descending');

export const selectTasks = createSelector(selectAllTasks, (tasks) => {
  return tasks.map((t) => ({ ...t })).sort(sortByDate);
});

export const selectTaskDates = createSelector(selectAllTasks, (tasks) => {
  return tasks
    .reduce((memo, t) => {
      if (
        t.requiredDate &&
        memo.findIndex((m) => equals(m, t.requiredDate)) === -1
      ) {
        memo.push(t.requiredDate);
      }
      return memo;
    }, [] as string[])
    .sort();
});

export const selectTaskItems = createSelector(
  selectAllTasks,
  selectSort,
  selectSortDescending,
  (tasks, sortProp, sortDescending) => {
    return tasks
      .map((t) => ({ ...t }))
      .flatMap((t) =>
        t.items.map((i) => ({ ...i, requiredDate: t.requiredDate }))
      )
      .sort((a, b) => {
        return (
          sortByPriority(a, b) ||
          sortBy(sortProp, sortDescending ? 'descending' : '')(a, b)
        );
      });
  }
);

export default boekestynTaskSlice.reducer;
