import React from 'react';
import { useAppSelector, useAppDispatch } from '@/services/hooks';
import { Loading } from '../loading';
import { classNames } from '@/utils/class-names';
import { formatNumber } from '@/utils/format';
import {
  selectFutureOrderDetail,
  selectItems,
  selectIsLoading,
  addAllItems,
} from './add-from-order-slice';
import { AddFromOrderItemListRow } from './add-from-order-item-list-row';

export function AddFromOrderItemList() {
  const dispatch = useAppDispatch(),
    futureOrderDetail = useAppSelector(selectFutureOrderDetail),
    items = useAppSelector(selectItems),
    isLoading = useAppSelector(selectIsLoading);

  const handleAddAllClick = () => {
    dispatch(addAllItems());
  };

  return (
    <div
      className={classNames(
        'flex h-full w-full flex-col overflow-y-auto rounded border p-4',
        isLoading || futureOrderDetail ? 'bg-white' : 'bg-gray-400'
      )}
    >
      {isLoading && <Loading />}
      {!!futureOrderDetail && (
        <>
          <div className="mb-4 flex">
            <button
              type="button"
              onClick={handleAddAllClick}
              className="btn-secondary px-2 py-1 text-xs"
            >
              Add All
            </button>
            <h2 className="flex-grow text-center text-xl font-semibold">
              Future Order {formatNumber(futureOrderDetail.id, '00000')}
            </h2>
          </div>
          <div className="flex-grow overflow-y-auto">
            <table className="min-w-full divide-y divide-gray-300 text-xs">
              <thead>
                <tr className="sticky top-0">
                  <th className="bg-gray-100 p-2">Product</th>
                  <th className="bg-gray-100 p-2">Description</th>
                  <th className="bg-gray-100 p-2 text-center">
                    Order Quantity
                  </th>
                  <th className="bg-gray-100 p-2 text-center">Price</th>
                </tr>
              </thead>
              <tbody>
                {items.map((item) => (
                  <AddFromOrderItemListRow
                    key={item.id}
                    item={item}
                    order={futureOrderDetail}
                  />
                ))}
              </tbody>
            </table>
          </div>
        </>
      )}
    </div>
  );
}
