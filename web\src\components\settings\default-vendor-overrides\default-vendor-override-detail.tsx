import React, { Fragment, useEffect, useState } from 'react';
import * as HeadlessUI from '@headlessui/react';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import {
  useCreateDefaultVendorOverrideMutation,
  useUpdateDefaultVendorOverrideMutation,
  useDeleteDefaultVendorOverrideMutation,
} from 'api/settings-service';
import { useInventoryItemsQuery } from 'api/spire-service';
import {
  selectAllItems,
  selectError,
  clearError,
  setSelectedOverride,
  selectSelectedOverride,
  addOverrideItem,
  removeOverridePartNumber,
  addOverridePartNumber,
} from './default-vendor-overrides-slice';
import { DefaultVendorOverrideDetailItem } from './default-vendor-override-detail-item';
import { Alert } from '@/components/alert';
import { InventoryItem } from 'api/models/spire';
import { startsWith } from '@/utils/equals';

export function DefaultVendorOverrideDetail() {
  const dispatch = useAppDispatch(),
    [create] = useCreateDefaultVendorOverrideMutation(),
    [update] = useUpdateDefaultVendorOverrideMutation(),
    [deleteFn] = useDeleteDefaultVendorOverrideMutation(),
    [showDeleteDialog, setShowDeleteDialog] = useState(false),
    [query, setQuery] = useState(''),
    selectedOverride = useAppSelector(selectSelectedOverride),
    allItems = useAppSelector(selectAllItems),
    error = useAppSelector(selectError),
    [options, setOptions] = useState<InventoryItem[]>([]),
    collection =
      query.length >= 3
        ? allItems.filter(
            (i) =>
              selectedOverride?.spirePartNumbers.indexOf(i.partNo) === -1 &&
              startsWith(i.partNo, query)
          ) || []
        : [];

  useEffect(() => {
    setOptions(
      selectedOverride?.spirePartNumbers
        .map((p) => allItems.find((i) => i.partNo === p))
        .flatMap((p) => (p ? [p] : [])) ?? []
    );
  }, [selectedOverride?.spirePartNumbers, allItems]);

  useInventoryItemsQuery();

  const handleClose = () => {
    dispatch(setSelectedOverride(null));
  };

  const handleAddClick = () => {
    dispatch(addOverrideItem());
  };

  const handleQueryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(e.target.value);
  };

  const handleRemovePartNumberClick = (inventory: InventoryItem) => {
    dispatch(removeOverridePartNumber(inventory.partNo));
  };

  const handleAddProductNumber = (inventory: InventoryItem) => {
    dispatch(addOverridePartNumber(inventory.partNo));
  };

  const handleAddAllItemsClick = () => {};

  const handleClearErrorClick = () => {
    dispatch(clearError());
  };

  const handleCancelClick = () => {
    dispatch(setSelectedOverride(null));
  };

  const handleSaveClick = async () => {
    if (selectedOverride) {
      if (selectedOverride.id <= 0) {
        const response = await create(selectedOverride);
        if ('error' in response) {
          return;
        }
      } else {
        const response = await update(selectedOverride);
        if ('error' in response) {
          return;
        }
      }
    }
    dispatch(setSelectedOverride(null));
  };

  const handleDeleteClick = () => {
    setShowDeleteDialog(true);
  };

  const handleDeleteCancel = () => {
    setShowDeleteDialog(false);
  };

  const handleDeleteConfirm = async () => {
    setShowDeleteDialog(false);
    if (selectedOverride) {
      const response = await deleteFn(selectedOverride.id);
      if ('error' in response) {
        return;
      }
    }
    dispatch(setSelectedOverride(null));
  };

  return (
    <>
      <HeadlessUI.Transition.Root show={!!selectedOverride} as={Fragment}>
        <HeadlessUI.Dialog
          as="div"
          className="relative z-30"
          onClose={() => void 0}
        >
          <HeadlessUI.Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
          </HeadlessUI.Transition.Child>

          <div className="fixed inset-0 z-10 overflow-y-auto">
            <div className="flex items-center justify-center p-0 text-center">
              <HeadlessUI.Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 translate-y-0 scale-95"
                enterTo="opacity-100 translate-y-0 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 translate-y-0 scale-100"
                leaveTo="opacity-0 translate-y-0 scale-95"
              >
                <HeadlessUI.Dialog.Panel className="relative h-screen w-screen transform overflow-hidden p-6 transition-all">
                  <div
                    className="mx-auto flex max-w-7xl flex-col overflow-y-auto rounded-lg bg-white p-6 text-left shadow-xl"
                    style={{ height: 'calc(100vh - 3rem)' }}
                  >
                    <div className="mb-4 flex justify-center border-b-2 pb-4">
                      <HeadlessUI.Dialog.Title
                        as="h3"
                        className="text-lg font-medium leading-6 text-gray-900"
                      >
                        <Icon
                          icon="boxes-stacked"
                          className="h-6 w-6"
                          aria-hidden="true"
                        />
                        &nbsp; Default Vendor Overrides
                      </HeadlessUI.Dialog.Title>
                    </div>
                    <div className="flex flex-grow flex-col overflow-y-auto">
                      <form className="flex w-full flex-grow overflow-y-auto">
                        <div className="flex w-full flex-col overflow-y-auto">
                          <div className="grid h-full grid-cols-2">
                            <div className="mx-8 flex flex-col overflow-auto text-center text-sm">
                              <div className="flex flex-col pt-2">
                                <input
                                  type="search"
                                  autoComplete="off"
                                  className="mx-[1px] rounded-md border border-gray-300 bg-white px-3 py-2 text-xs shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                                  value={query}
                                  onChange={handleQueryChange}
                                />
                                <p className="mt-2 text-left italic text-gray-500">
                                  Search for items by Spire Part Number or
                                  Description.
                                </p>
                              </div>
                              <div className="mt-3 overflow-auto">
                                <h5 className="text-md mx-4 border-b-2 border-gray-200 text-left font-medium">
                                  Options
                                </h5>
                                {!!selectedOverride &&
                                  !selectedOverride.spirePartNumbers.length && (
                                    <div className="mx-4 mb-4 bg-yellow-100 p-4 text-lg italic text-yellow-700">
                                      No products Selected
                                    </div>
                                  )}
                                <ul>
                                  {options.map((option) => (
                                    <li
                                      key={option.id}
                                      className="mx-4 mb-2 grid grid-cols-4 border-b-2 border-gray-200 bg-blue-100 p-2 text-left"
                                    >
                                      <div className="col-span-3 truncate text-sm font-medium text-gray-900">
                                        {option.partNo}
                                      </div>
                                      <div className="row-span-2 flex h-full items-center justify-center border-gray-200 align-middle">
                                        <input
                                          type="checkbox"
                                          checked
                                          onClick={() =>
                                            handleRemovePartNumberClick(option)
                                          }
                                        />
                                      </div>
                                      <div className="col-span-3 col-start-1 truncate text-sm text-gray-500">
                                        {option.description}
                                      </div>
                                    </li>
                                  ))}
                                </ul>

                                <div className="mx-4 grid grid-cols-4 border-b-2 border-gray-200 p-2">
                                  <h5 className="text-md col-span-3 text-left font-medium">
                                    Inventory Items
                                  </h5>
                                  <div className="flex h-full items-center justify-center align-middle">
                                    <input
                                      type="checkbox"
                                      checked={false}
                                      onClick={handleAddAllItemsClick}
                                    />
                                  </div>
                                </div>

                                {query.length >= 3 && !collection.length && (
                                  <div className="mx-4 mb-4 bg-yellow-100 p-4 text-lg italic text-yellow-700">
                                    No Items found
                                  </div>
                                )}
                                <ul className="divide-y divide-gray-200 rounded-md">
                                  {collection.map((item) => (
                                    <li
                                      key={item.id}
                                      className="mx-4 grid grid-cols-4 bg-white p-2 text-left hover:bg-gray-50"
                                    >
                                      <div className="col-span-3 truncate text-sm font-medium text-gray-900">
                                        {item.partNo}
                                      </div>
                                      <div className="row-span-2 flex h-full items-center justify-center border-gray-200 align-middle">
                                        <input
                                          type="checkbox"
                                          onClick={() =>
                                            handleAddProductNumber(item)
                                          }
                                        />
                                      </div>
                                      <div className="col-span-3 col-start-1 truncate text-sm text-gray-500">
                                        {item.description}
                                      </div>
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            </div>

                            {!!selectedOverride && (
                              <div className="my-4 flex flex-grow flex-col overflow-y-auto">
                                <h3 className="text-semibold text-xl">
                                  Default Vendor Overrides
                                </h3>
                                <div className="flex flex-grow flex-col overflow-y-auto">
                                  <table>
                                    <thead>
                                      <th className="text-center">
                                        Start Week
                                      </th>
                                      <th className="text-center">End Week</th>
                                      <th className="px-4">Vendor</th>
                                      <th className="px-4">&nbsp;</th>
                                    </thead>
                                    <tbody>
                                      {selectedOverride.items.map((o) => (
                                        <DefaultVendorOverrideDetailItem
                                          key={o.id}
                                          item={o}
                                        />
                                      ))}
                                    </tbody>
                                  </table>
                                </div>
                                <div className="pt-2">
                                  <button
                                    type="button"
                                    className="btn-new mt-2 px-2 py-1"
                                    onClick={handleAddClick}
                                  >
                                    <Icon icon="plus" />
                                    &nbsp;Add Override
                                  </button>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </form>
                      <Error error={error} clear={handleClearErrorClick} />
                      {!!selectedOverride && (
                        <div className="mt-4 flex justify-between border-t pt-4">
                          <div>
                            <button
                              type="button"
                              className="btn-secondary text-lg"
                              onClick={handleCancelClick}
                            >
                              Cancel
                            </button>
                            <button
                              type="button"
                              className="btn-primary ml-4 text-lg"
                              onClick={handleSaveClick}
                            >
                              Save&nbsp;
                              <Icon icon="save" className="ml-2" />
                            </button>
                          </div>
                          {selectedOverride.id > 0 && (
                            <button
                              type="button"
                              className="btn-delete ml-4 text-lg"
                              onClick={handleDeleteClick}
                            >
                              Delete&nbsp;
                              <Icon icon="trash" className="ml-2" />
                            </button>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </HeadlessUI.Dialog.Panel>
              </HeadlessUI.Transition.Child>
            </div>
          </div>
        </HeadlessUI.Dialog>
      </HeadlessUI.Transition.Root>
      <Alert
        open={showDeleteDialog}
        colour="danger"
        confirm={handleDeleteConfirm}
        cancel={handleDeleteCancel}
        title="Delete Override"
        message="Are you sure you want to delete this Override?"
      />
    </>
  );
}
