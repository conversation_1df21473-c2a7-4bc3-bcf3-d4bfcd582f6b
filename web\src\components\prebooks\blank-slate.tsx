import { Fragment, useRef, useState } from 'react';
import { Combobox, Dialog, Transition } from '@headlessui/react';
import {
  addItem,
  getCustomerDetail,
  selectBlanketItems,
  setBoxCode,
  setCustomer,
  setSalesperson,
  setShipTo,
  setVendor,
  selectRequiredDate,
  selectBlanketStartDate,
  setRequiredDate,
  setBlanketStartDate,
  selectIsBlanket,
} from './prebook-detail-slice';
import { useSalespeopleQuery, useVendorsQuery } from 'api/spire-service';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { Icon } from '@/components/icon';
import { startsWith } from '@/utils/equals';
import { classNames } from '@/utils/class-names';
import { CustomerDetail, Vendor } from 'api/models/spire';
import { PrebookBlanketItem } from 'api/models/prebooks';
import { BlankSlateBlanketItem } from './blank-slate-blanket-item';

export interface BlankStateProps {
  open: boolean;
  close: () => void;
}

export function BlankSlate({ open, close }: BlankStateProps) {
  const dispatch = useAppDispatch(),
    [rawQuery, setRawQuery] = useState(''),
    { data: vendors } = useVendorsQuery(),
    blanketItems = useAppSelector(selectBlanketItems),
    requiredDate = useAppSelector(selectRequiredDate),
    blanketStartDate = useAppSelector(selectBlanketStartDate),
    isBlanket = useAppSelector(selectIsBlanket),
    { data: salespeople } = useSalespeopleQuery(),
    blanketStartDateRef = useRef<HTMLInputElement | null>(null),
    requiredDateRef = useRef<HTMLInputElement | null>(null),
    query = rawQuery.toLowerCase().replace(/^[vb]/, ''),
    filteredVendors =
      rawQuery === 'v'
        ? vendors || []
        : query === '' || rawQuery.startsWith('b')
        ? []
        : (vendors || []).filter((v) => startsWith(v.name, query)),
    filteredBlanketItems =
      rawQuery === '' || rawQuery.startsWith('v')
        ? []
        : blanketItems.filter(
            (i) =>
              (!requiredDate ||
                (requiredDate <= i.requiredDate &&
                  (!i.blanketStartDate ||
                    requiredDate >= i.blanketStartDate))) &&
              (startsWith(i.vendorName, query) ||
                startsWith(i.description, query) ||
                startsWith(i.spirePartNumber, query))
          );

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setRawQuery(e.target.value);
  };

  const handleComboboxChange = async (item: Vendor | PrebookBlanketItem) => {
    if (isVendor(item)) {
      dispatch(setVendor(item));
    } else {
      const inventory = {
          id: item.spireInventoryId,
          partNo: item.spirePartNumber,
          description: item.description,
          blanketItemId: item.id,
        },
        vendor = { id: item.vendorId, vendorNo: '', name: item.vendorName };
      dispatch(addItem(inventory));
      dispatch(setVendor(vendor));
      if (item.customerId) {
        const detail = await dispatch(getCustomerDetail(item.customerId));
        await dispatch(
          setCustomer({
            id: item.customerId,
            name: item.customerName || '',
            defaultShipTo: item.shipToName,
          })
        );

        const shipTo = (
          detail?.payload as CustomerDetail
        )?.shippingAddresses.find((st) => st.id === item.shipToId);
        if (shipTo) {
          dispatch(setShipTo(shipTo));
          const salesperson =
            salespeople?.find((s) => s.code === shipTo.salesperson?.code) ||
            null;
          dispatch(setSalesperson(salesperson));
          dispatch(setBoxCode(shipTo?.boxCode || null));
        }
      }
    }

    close();
  };

  const handleRequiredDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setRequiredDate(e.target.value || null));
  };

  const handleBlanketStartDateChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    dispatch(setBlanketStartDate(e.target.value || null));
  };

  return (
    <Transition.Root
      show={open}
      as={Fragment}
      afterLeave={() => setRawQuery('')}
      appear
    >
      <Dialog
        as="div"
        className="relative z-40"
        onClose={close}
        initialFocus={isBlanket ? blanketStartDateRef : requiredDateRef}
      >
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-25 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-40 overflow-y-auto p-20">
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0 scale-95"
            enterTo="opacity-100 scale-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100 scale-100"
            leaveTo="opacity-0 scale-95"
          >
            <Dialog.Panel className="mx-auto max-w-lg transform divide-y divide-gray-100 overflow-hidden rounded-xl bg-white shadow-2xl ring-1 ring-black ring-opacity-5 transition-all">
              <div className="flex justify-center">
                {isBlanket && (
                  <div className="row flex">
                    <label
                      htmlFor="blanket-start-date"
                      className="block whitespace-nowrap p-4 text-sm font-medium text-gray-500"
                    >
                      Blanket From
                    </label>
                    <div className="mt-2 px-4">
                      <input
                        type="date"
                        max="2050-01-01"
                        name="blanketStartDate"
                        id="blanket-start-date"
                        value={blanketStartDate || ''}
                        onChange={handleBlanketStartDateChange}
                        ref={blanketStartDateRef}
                        className="block w-full rounded-md border-gray-300 text-sm shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                )}
                <div className="row flex">
                  <label
                    htmlFor="required-date"
                    className="block whitespace-nowrap p-4 text-sm font-medium text-gray-500"
                  >
                    {isBlanket ? 'To' : 'Required Date'}
                  </label>
                  <div className="mt-2 px-4">
                    <input
                      type="date"
                      max="2050-01-01"
                      name="requiredDate"
                      id="required-date"
                      value={requiredDate || ''}
                      onChange={handleRequiredDateChange}
                      min={blanketStartDate || ''}
                      ref={requiredDateRef}
                      className="block w-full rounded-md border-gray-300 text-sm shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>
              <Combobox onChange={handleComboboxChange}>
                <div className="relative">
                  <Icon
                    icon="magnifying-glass"
                    className="pointer-events-none absolute left-4 top-3.5 h-5 w-5 text-gray-400"
                    aria-hidden="true"
                  />
                  <Combobox.Input
                    type="search"
                    className="h-12 w-full border-0 bg-transparent pl-11 pr-4 text-sm text-gray-800 placeholder-gray-400 focus:ring-0"
                    placeholder="Search..."
                    autoComplete="off"
                    value={rawQuery}
                    onChange={handleSearchChange}
                  />
                </div>

                {(filteredVendors.length > 0 ||
                  filteredBlanketItems.length > 0) && (
                  <Combobox.Options
                    static
                    className="max-h-80 scroll-py-10 scroll-pb-2 space-y-4 overflow-y-auto p-4 pb-2"
                  >
                    {filteredVendors.length > 0 && (
                      <li>
                        <h2 className="text-xs font-semibold text-gray-900">
                          Vendors
                        </h2>
                        <ul className="-mx-4 mt-2 text-sm text-gray-700">
                          {filteredVendors.map((vendor) => (
                            <Combobox.Option
                              key={vendor.id}
                              value={vendor}
                              className={({ active }) =>
                                classNames(
                                  'flex cursor-default select-none items-center px-4 py-2',
                                  active
                                    ? 'bg-blue-600 text-white'
                                    : 'bg-white text-gray-400'
                                )
                              }
                            >
                              {vendor.name}
                            </Combobox.Option>
                          ))}
                        </ul>
                      </li>
                    )}
                    {filteredBlanketItems.length > 0 && (
                      <li>
                        <h2 className="text-xs font-semibold text-gray-900">
                          Blanket Items
                        </h2>
                        <ul className="-mx-4 mt-2 text-sm text-gray-700">
                          {filteredBlanketItems.map((blanketItem) => (
                            <BlankSlateBlanketItem
                              key={blanketItem.id}
                              blanketItem={blanketItem}
                            />
                          ))}
                        </ul>
                      </li>
                    )}
                  </Combobox.Options>
                )}

                {rawQuery === '?' && (
                  <div className="px-14 py-14 text-center text-sm">
                    <Icon
                      icon="life-ring"
                      className="mx-auto h-6 w-6 text-gray-400"
                      aria-hidden="true"
                    />
                    <p className="mt-4 font-semibold text-gray-900">
                      Help with searching
                    </p>
                    <p className="mt-2 text-gray-500">
                      Use this tool to quickly search for Vendors and Blanket
                      Items across the entire platform. You can also use the
                      search modifiers found in the footer below to limit the
                      results to just vendors or blanket items.
                    </p>
                  </div>
                )}

                {query !== '' &&
                  rawQuery !== '?' &&
                  filteredVendors.length === 0 &&
                  filteredBlanketItems.length === 0 && (
                    <div className="px-14 py-14 text-center text-sm">
                      <Icon
                        icon="exclamation-triangle"
                        className="mx-auto h-6 w-6 text-gray-400"
                        aria-hidden="true"
                      />
                      <p className="mt-4 font-semibold text-gray-900">
                        No results found
                      </p>
                      <p className="mt-2 text-gray-500">
                        We couldn’t find anything with that term. Please try
                        again.
                      </p>
                    </div>
                  )}

                <div className="flex flex-wrap items-center bg-gray-50 px-4 py-2.5 text-xs text-gray-700">
                  Type{' '}
                  <kbd
                    className={classNames(
                      'mx-2 flex h-5 w-5 items-center justify-center rounded border bg-white font-semibold',
                      rawQuery.startsWith('v')
                        ? 'border-blue-600 text-blue-600'
                        : 'border-gray-400 text-gray-900'
                    )}
                  >
                    v
                  </kbd>{' '}
                  <span className="inline">to access vendors,</span>
                  <kbd
                    className={classNames(
                      'mx-2 flex h-5 w-5 items-center justify-center rounded border bg-white font-semibold',
                      rawQuery.startsWith('b')
                        ? 'border-blue-600 text-blue-600'
                        : 'border-gray-400 text-gray-900'
                    )}
                  >
                    b
                  </kbd>{' '}
                  for blanket items, and{' '}
                  <kbd
                    className={classNames(
                      'mx-2 flex h-5 w-5 items-center justify-center rounded border bg-white font-semibold',
                      rawQuery === '?'
                        ? 'border-blue-600 text-blue-600'
                        : 'border-gray-400 text-gray-900'
                    )}
                  >
                    ?
                  </kbd>{' '}
                  for help.
                </div>
              </Combobox>
            </Dialog.Panel>
          </Transition.Child>
        </div>
      </Dialog>
    </Transition.Root>
  );
}

function isVendor(item: Vendor | PrebookBlanketItem): item is Vendor {
  return (
    typeof item['id'] === 'number' &&
    'name' in item &&
    typeof item['name'] === 'string'
  );
}
