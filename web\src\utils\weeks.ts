import { DateTime, WeekdayNumbers } from 'luxon';
import { parseQuantity, formatNumber } from './format';

export interface Week {
  year: number;
  week: number;
  weekId: string;
}

export function weekFromId(weekId: string | null): Week | null {
  if (weekId == null || weekId.length !== 6) {
    return null;
  }

  const year = parseQuantity(weekId.substring(0, 4)),
    week = parseQuantity(weekId.substring(4, 6));

  if (!year || !week) {
    return null;
  }

  return { year, week, weekId };
}

export function weekFromWeekAndYear(week: number, year: number): Week {
  const weekId = formatNumber(year, '0000') + formatNumber(week, '00');

  return { year, week, weekId };
}

export function isDateInWeek(weekId: string | null, date: string | null) {
  if (!weekId || !date) {
    return true;
  }

  const week = weekFromId(weekId);

  if (!week) {
    return true;
  }

  const startObj = DateTime.fromObject({
      weekYear: week.year,
      weekNumber: week.week,
      weekday: 1,
    }),
    start = startObj.toFormat('yyyy-MM-dd'),
    end = startObj.plus({ days: 6 }).toFormat('yyyy-MM-dd');

  const isInWeek = date >= start && date <= end;
  return isInWeek;
}

export function weeksBetweenDates(startDate: string, endDate: string): Week[] {
  const weeks: Week[] = [];

  const start = DateTime.fromFormat(startDate, 'yyyy-MM-dd'),
    endDay = DateTime.fromFormat(endDate, 'yyyy-MM-dd'),
    endWeekId = endDay.toFormat('kkkkWW'),
    end = endDay.set({ hour: 23, minute: 59, second: 59 }).toISO() || '';

  let current = start.plus({ weeks: 0 });

  while (current.toFormat('yyyy-MM-dd') < end) {
    const week = current.weekNumber,
      year = current.weekYear,
      weekId = current.toFormat('kkkkWW');

    weeks.push({ week, year, weekId });

    current = current.plus({ weeks: 1 });
  }

  if (!weeks.some((w) => w.weekId === endWeekId)) {
    weeks.push({
      week: endDay.weekNumber,
      year: endDay.weekYear,
      weekId: endWeekId,
    });
  }

  return weeks;
}

export function weekFromDate(date: string, format: string = 'yyyy-MM-dd') {
  const parsed = DateTime.fromFormat(date, format);
  if (parsed.isValid) {
    return {
      week: parsed.weekNumber,
      year: parsed.weekYear,
      weekId: parsed.toFormat('kkkkWW'),
    };
  }

  return null;
}

export function dateFromWeekAndYear(
  weekNumber: number,
  weekYear: number,
  weekday: WeekdayNumbers = 1
) {
  const firstDay = DateTime.fromObject({
    weekYear,
    weekNumber,
    weekday,
  });

  return firstDay.toFormat('yyyy-MM-dd');
}
