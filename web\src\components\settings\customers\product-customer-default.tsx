import React, { useRef, useState } from 'react';
import { useInventoryItemsQuery } from 'api/spire-service';
import * as futureOrders from 'api/models/future-orders';
import { useAppDispatch } from '@/services/hooks';
import {
  updateProductCustomerDefault,
  deleteProductCustomerDefault,
} from './customer-settings-slice';
import { Alert } from '@/components/alert';
import { handleFocus } from '@/utils/focus';
import { Icon } from '@/components/icon';
import { classNames } from '@/utils/class-names';

interface ProductCustomerDefaultProps {
  productCustomerDefault: futureOrders.ProductCustomerDefault;
}

export function ProductCustomerDefault({
  productCustomerDefault,
}: ProductCustomerDefaultProps) {
  const dispatch = useAppDispatch(),
    inputRef = useRef<HTMLInputElement>(null),
    { data: inventoryItemsData } = useInventoryItemsQuery(),
    inventory = inventoryItemsData?.inventoryItems || [],
    item = inventory.find(
      (i) => i.id === productCustomerDefault.spireInventoryId
    ),
    [showDeleteDialog, setShowDeleteDialog] = useState(false),
    [editing, setEditing] = useState(false),
    [customerItemCode, setCustomerItemCode] = useState(
      productCustomerDefault.customerItemCode || ''
    ),
    cellClassName = 'whitespace-nowrap px-2 py-4 text-sm';

  const handleCustomerItemCodeChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setCustomerItemCode(e.target.value);
  };

  const handleEditClick = () => {
    setEditing(true);
    window.setTimeout(() => {
      inputRef?.current?.focus();
    }, 100);
  };

  const handleCancelClick = () => {
    setCustomerItemCode(productCustomerDefault.customerItemCode || '');
    setEditing(false);
  };

  const handleDeleteClick = () => setShowDeleteDialog(true);

  const handleDeleteCancel = () => setShowDeleteDialog(false);

  const handleDeleteConfirm = () => {
    dispatch(deleteProductCustomerDefault(productCustomerDefault.id));
    setShowDeleteDialog(false);
  };

  const handleSaveClick = () => {
    dispatch(
      updateProductCustomerDefault({
        id: productCustomerDefault.id,
        customerItemCode,
      })
    );
    setEditing(false);
  };

  return (
    <tr>
      <td className={cellClassName}>{item?.partNo}</td>
      <td className={cellClassName}>{item?.description}</td>
      <td className={classNames(cellClassName, 'text-center')}>
        {!editing && (
          <div className="mx-auto w-48 border-transparent pt-2 text-center text-xs">
            {productCustomerDefault.customerItemCode}
          </div>
        )}
        {editing && (
          <input
            type="text"
            value={customerItemCode || ''}
            onChange={handleCustomerItemCodeChange}
            onFocus={handleFocus}
            ref={inputRef}
            className="w-48 rounded-md border-gray-300 text-center text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
          />
        )}
      </td>
      <td className={cellClassName}>
        {!editing && (
          <div className="flex">
            <div>
              <button
                type="button"
                onClick={handleEditClick}
                className="btn-secondary px-2 py-1"
              >
                <Icon icon="edit" />
              </button>
              <button
                type="button"
                onClick={handleDeleteClick}
                className="btn-secondary border-red-500 px-2 py-1 text-red-500"
              >
                <Icon icon="trash" />
              </button>
            </div>
          </div>
        )}
        {editing && (
          <div className="flex">
            <div>
              <button
                type="button"
                onClick={handleCancelClick}
                className="btn-secondary px-2 py-1"
              >
                <Icon icon="undo" />
              </button>
              <button
                type="button"
                onClick={handleSaveClick}
                className="btn-secondary border-blue-500 px-2 py-1 text-blue-500"
              >
                <Icon icon="save" />
              </button>
            </div>
          </div>
        )}
      </td>
      <Alert
        title="Delete Customer Item Code"
        open={showDeleteDialog}
        message="Are you sure you want to delete this Customer Item Code?"
        colour="danger"
        confirmButtonText="Yes"
        cancelButtonText="No"
        confirm={handleDeleteConfirm}
        cancel={handleDeleteCancel}
      />
    </tr>
  );
}
