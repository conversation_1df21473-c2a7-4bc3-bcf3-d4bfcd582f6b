import * as XLSX from 'xlsx';
import * as models from 'api/models/labels';
import { DateTime } from 'luxon';
import { equals } from '@/utils/equals';
import { parseQuantity } from '@/utils/format';
import { createProblemDetails } from '@/utils/problem-details';

export function read(data: ArrayBuffer): AlbrechtsLabels {
  const workbook = XLSX.read(data, { type: 'buffer' }),
    items: models.AlbrechtsLabelItem[] = [],
    sheetname = workbook.SheetNames[0],
    sheet = workbook.Sheets[sheetname],
    rows = XLSX.utils.sheet_to_json<any>(sheet, {
      header: 'A',
      raw: false,
    }),
    requiredDateRegex = /Delivery Week of\s+(\d{2}\/\d{2}\/\d{4})/i,
    requiredDateValue = (rows.find((r) => r.__rowNum__ === 1)?.C || '').trim(),
    requiredDate = requiredDateRegex.test(requiredDateValue)
      ? DateTime.fromFormat(
          requiredDateRegex.exec(requiredDateValue)?.[1] || '',
          'MM/dd/yyyy'
        ).toFormat('yyyy-MM-dd')
      : null;

  if (!requiredDate) {
    throw createProblemDetails('Could not find valid date in cell C2.');
  }
  const labels: AlbrechtsLabels = {
    stores: [],
    purchaseOrderNumber: '',
    requiredDate,
  };

  let row = rows.find((r) => r.__rowNum__ === 5);

  while (row) {
    const rowNum = row.__rowNum__,
      index = rows.findIndex((r) => r.__rowNum__ === rowNum);

    if (equals(row?.A, 'Item')) {
      const description =
          rows.find((r) => r.__rowNum__ === rowNum + 1)?.A || '' || '',
        upc = (rows.find((r) => r.__rowNum__ === rowNum + 2)?.A || '')
          .toString()
          .replaceAll('-', '')
          .replaceAll(' ', '');
      let packQuantity = 0,
        cost = '',
        retail = '',
        productNumber = '';
      for (let i = 0; i < 4; i++) {
        const currentRow = rows.find((r) => r.__rowNum__ === rowNum + i);
        if (
          equals(currentRow?.B, 'Case Pack') ||
          equals(currentRow?.B, 'Pack Size')
        ) {
          const packQuantityValue = currentRow?.C || '';
          packQuantity =
            typeof packQuantityValue === 'string'
              ? parseQuantity(packQuantityValue)
              : typeof packQuantityValue === 'number'
              ? packQuantityValue
              : 0;
          cost = rows.find((r) => r.__rowNum__ === rowNum + i + 1)?.C || '';
          retail = rows.find((r) => r.__rowNum__ === rowNum + i + 2)?.C || '';
          productNumber =
            rows.find((r) => r.__rowNum__ === rowNum + i + 3)?.C || '' || '';
          break;
        }
      }

      items.push({
        rowNumber: row.__rowNum__,
        upc,
        description,
        productNumber,
        cost,
        retail,
        packQuantity,
      });
      row = rows[index + 4];
    } else {
      row = rows[index + 1];
    }
  }

  const storeNameRow = rows.find((r) => r.__rowNum__ === 3),
    storeRow = rows.find((r) => r.__rowNum__ === 4),
    storeRegex = /^(\d+)$/,
    props = Object.getOwnPropertyNames(storeRow);
  props
    .filter((p) => p !== '__rowNum__')
    .forEach((prop) => {
      const raw: string | number | null | undefined = storeRow[prop],
        storeNumberValue =
          typeof raw === 'string'
            ? raw
            : typeof raw === 'number'
            ? raw.toString()
            : '',
        name = (storeNameRow[prop] || '').toString();
      if (storeRegex.test(storeNumberValue)) {
        const storeNumber = parseQuantity(
            storeRegex.exec(storeNumberValue)?.[1]
          ),
          store: models.AlbrechtsLabelStore = {
            storeNumber,
            name,
            orders: [],
          };
        items.forEach((item) => {
          // the row the quantities are on varies
          for (let i = 0; i < 5; i++) {
            const itemRow = rows.find(
                (r) => r.__rowNum__ === item.rowNumber + i
              ),
              rawQuantity: string | number | null | undefined = itemRow?.[prop],
              quantity =
                typeof rawQuantity === 'string'
                  ? parseQuantity(rawQuantity)
                  : typeof rawQuantity === 'number'
                  ? rawQuantity
                  : 0;
            if (quantity) {
              store.orders.push({ ...item, quantity });
              break;
            }
          }
        });
        if (store.orders.length) {
          labels.stores.push(store);
        }
      }
    });

  return labels;
}

export interface AlbrechtsLabels {
  purchaseOrderNumber: string;
  requiredDate: string;
  stores: models.AlbrechtsLabelStore[];
}
