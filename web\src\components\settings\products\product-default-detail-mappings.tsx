import * as HeadlessUI from '@headlessui/react';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { classNames } from '@/utils/class-names';
import { ProductDefaultDetailMapping } from './product-default-detail-mapping';
import {
  selectMappings,
  setMappings,
  selectMultipleProducts,
  selectIgnoreOverrideQuantity,
  setIgnoreOverrideQuantity,
} from './product-default-detail-slice';

export function ProductDefaultDetailMappings() {
  const dispatch = useAppDispatch(),
    multipleProducts = useAppSelector(selectMultipleProducts),
    ignoreOverrideQuantity = useAppSelector(selectIgnoreOverrideQuantity),
    mappings = useAppSelector(selectMappings);

  const handleAddMappingClick = () => {
    const id = mappings.reduce((min, m) => Math.min(min, m.id), 0) - 1;
    dispatch(
      setMappings(
        mappings.concat({
          id,
          boekestynPlantId: '',
          boekestynCustomerAbbreviation: null,
          quantityPerFinishedItem: 1,
        })
      )
    );
  };

  const handleIgnoreOverrideQuantityChange = (value: boolean) => {
    dispatch(setIgnoreOverrideQuantity(value));
  };

  if (!multipleProducts) {
    return null;
  }

  return (
    <div className="p-2">
      <HeadlessUI.Switch.Group as="div" className="my-2">
        <HeadlessUI.Switch.Label className="mr-2 cursor-pointer">
          Quantity of finished goods doesn&apos;t have to equal the Pack
          Quantity (e.g. for a Planter)
        </HeadlessUI.Switch.Label>
        <HeadlessUI.Switch
          checked={ignoreOverrideQuantity}
          onChange={handleIgnoreOverrideQuantityChange}
          className={classNames(
            ignoreOverrideQuantity
              ? 'bg-blue-400 outline-none ring-2 ring-blue-500 ring-offset-2'
              : 'bg-gray-200',
            'relative mx-2 inline-flex h-4 w-8 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out'
          )}
        >
          <span
            aria-hidden="true"
            className={classNames(
              ignoreOverrideQuantity ? 'translate-x-4' : 'translate-x-0',
              'pointer-events-none inline-block h-3 w-3 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
            )}
          />
        </HeadlessUI.Switch>
      </HeadlessUI.Switch.Group>
      <table className="mt-2 w-full">
        <thead>
          <tr>
            <th className="p-2">Plant</th>
            <th className="p-2">Customer</th>
            <th className="whitespace-nowrap p-2 text-center">
              Qty / Finished Item
            </th>
            <th>&nbsp;</th>
          </tr>
        </thead>
        <tbody>
          {mappings.map((mapping) => (
            <ProductDefaultDetailMapping key={mapping.id} mapping={mapping} />
          ))}
        </tbody>
      </table>
      <div className="p-2">
        <button
          type="button"
          className="btn-new px-2 py-1"
          onClick={handleAddMappingClick}
        >
          Add Plant
        </button>
      </div>
    </div>
  );
}
