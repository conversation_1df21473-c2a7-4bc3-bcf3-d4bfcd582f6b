import { useAppDispatch, useAppSelector } from '@/services/hooks';
import {
  selectOverrides,
  setOverrides,
  selectMultipleProducts,
} from './product-default-detail-slice';
import { ProductDefaultDetailOverride } from './product-default-detail-override';

export function ProductDefaultDetailOverrides() {
  const dispatch = useAppDispatch(),
    overrides = useAppSelector(selectOverrides),
    multipleProducts = useAppSelector(selectMultipleProducts),
    overrideWeeks = overrides.reduce((memo, o) => {
      if (!memo.some((m) => m[0] === o.startWeek && m[1] === o.endWeek)) {
        memo.push([o.startWeek, o.endWeek]);
      }
      return memo;
    }, [] as [number, number][]);

  const handleAddOverrideClick = () => {
    const id = overrides.reduce((min, m) => Math.min(min, m.id), 0) - 1,
      nextWeek = overrides.reduce((max, m) => Math.max(max, m.endWeek), 0) + 1;
    dispatch(
      setOverrides(
        overrides.concat([
          {
            id,
            startWeek: nextWeek,
            endWeek: nextWeek,
            boekestynPlantId: '',
            boekestynCustomerAbbreviation: null,
            quantityPerFinishedItem: 1,
          },
        ])
      )
    );
  };

  if (!multipleProducts) {
    return null;
  }

  return (
    <div className="mt-2 border-t-4 p-2">
      <h3 className="text-lg font-semibold">Seasonal Overrides</h3>
      <table className="mt-2 w-full text-xs">
        {overrideWeeks.map(([start, end]) => (
          <ProductDefaultDetailOverride
            key={`${start}|${end}`}
            startWeek={start}
            endWeek={end}
          />
        ))}
        <tfoot>
          <tr>
            <th className="p-2">
              <button
                type="button"
                className="btn-new px-2 py-1"
                onClick={handleAddOverrideClick}
              >
                Add Override
              </button>
            </th>
          </tr>
        </tfoot>
      </table>
    </div>
  );
}
