import React, { Fragment, useState } from 'react';
import * as HeadlessUI from '@headlessui/react';
import { classNames } from '@/utils/class-names';

export interface ClearItemsResults {
  upc: boolean;
  upcValue: string | null;
  pricing: boolean;
  dateCode: boolean;
  retail: boolean;
  weightsAndMeasures: boolean;
  potCover: boolean;
}

interface ClearItemsProps {
  open: boolean;
  confirm: (results: ClearItemsResults) => void;
  cancel: () => void;
}

export function ClearItems({ open, confirm, cancel }: ClearItemsProps) {
  const [upc, setUPC] = useState(true),
    [upcValue, setUPCValue] = useState<string | null>(null),
    [pricing, setPricing] = useState(true),
    [dateCode, setDateCode] = useState(true),
    [retail, setRetail] = useState(true),
    [weightsAndMeasures, setWeightsAndMeasures] = useState(true),
    [potCover, setPotCover] = useState(true);

  const handleCloseClick = () => {
    cancel();
  };

  const handleClearClick = () => {
    const results = {
      upc,
      upcValue,
      pricing,
      dateCode,
      retail,
      weightsAndMeasures,
      potCover,
    };
    confirm(results);
  };

  const handleUPCChange = (clear: boolean) => {
    setUPC(clear);
  };

  const handleUPCValueChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value || null;
    setUPCValue(value);
  };

  const handlePricingChange = (clear: boolean) => {
    setPricing(clear);
  };

  const handleDateCodeChange = (clear: boolean) => {
    setDateCode(clear);
  };

  const handleRetailChange = (clear: boolean) => {
    setRetail(clear);
  };

  const handleWeightsAndMeasuresChange = (clear: boolean) => {
    setWeightsAndMeasures(clear);
  };

  const handlePotCoverChange = (clear: boolean) => {
    setPotCover(clear);
  };

  const switches = [
    {
      label: 'Retail',
      checked: retail,
      onChange: handleRetailChange,
      twoColumns: true,
    },
    {
      label: 'Date Code',
      checked: dateCode,
      onChange: handleDateCodeChange,
      twoColumns: true,
    },
    {
      label: 'W&M',
      checked: weightsAndMeasures,
      onChange: handleWeightsAndMeasuresChange,
      twoColumns: true,
    },
    {
      label: 'Pot Covers',
      checked: potCover,
      onChange: handlePotCoverChange,
      twoColumns: true,
    },
    {
      label: 'Pricing',
      checked: pricing,
      onChange: handlePricingChange,
      twoColumns: true,
    },
  ];

  return (
    <HeadlessUI.Transition.Root show={open} as={Fragment}>
      <HeadlessUI.Dialog
        as="div"
        className="relative z-30"
        onClose={handleCloseClick}
      >
        <HeadlessUI.Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </HeadlessUI.Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-0 text-center">
            <HeadlessUI.Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-0 scale-95"
              enterTo="opacity-100 translate-y-0 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 scale-100"
              leaveTo="opacity-0 translate-y-0 scale-95"
            >
              <HeadlessUI.Dialog.Panel className="relative my-8 w-full max-w-2xl transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all">
                <div className="grid grid-cols-2 p-8">
                  <Switch
                    label="UPC"
                    onChange={handleUPCChange}
                    checked={upc}
                  />
                  <div className="mb-2 border-b-2 pb-2">
                    <select
                      className={classNames(
                        'block w-full rounded-md border-gray-300 text-sm shadow-sm focus:border-blue-500 focus:ring-blue-500',
                        !upc && 'invisible'
                      )}
                      value={upcValue || ''}
                      onChange={handleUPCValueChange}
                    >
                      <option value="">Blank UPC</option>
                      <option value="TBA">TBA</option>
                    </select>
                  </div>
                  {switches.map((s) => (
                    <Switch
                      key={s.label}
                      checked={s.checked}
                      label={s.label}
                      onChange={s.onChange}
                      twoColumns={s.twoColumns}
                    />
                  ))}
                </div>
                <div className="w-100 flex flex-row justify-end bg-gray-50 px-6 py-3">
                  <button
                    type="button"
                    className="btn-secondary"
                    onClick={handleCloseClick}
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn-primary ml-4"
                    onClick={handleClearClick}
                  >
                    Clear
                  </button>
                </div>
              </HeadlessUI.Dialog.Panel>
            </HeadlessUI.Transition.Child>
          </div>
        </div>
      </HeadlessUI.Dialog>
    </HeadlessUI.Transition.Root>
  );
}

interface SwitchProps {
  checked: boolean;
  label: string;
  onChange: (clear: boolean) => void;
  twoColumns?: boolean;
}

function Switch({ checked, label, onChange, twoColumns }: SwitchProps) {
  return (
    <HeadlessUI.Switch.Group
      as="div"
      className={classNames(
        'col-start-1 mb-2 border-b-2 pb-2 pl-4 pt-1',
        twoColumns && 'col-span-2'
      )}
    >
      <HeadlessUI.Switch
        checked={checked}
        onChange={onChange}
        className={classNames(
          checked
            ? 'bg-blue-400 outline-none ring-2 ring-blue-500 ring-offset-2'
            : 'bg-gray-200',
          'relative inline-flex h-4 w-8 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out'
        )}
      >
        <span
          aria-hidden="true"
          className={classNames(
            checked ? 'translate-x-4' : 'translate-x-0',
            'pointer-events-none inline-block h-3 w-3 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
          )}
        />
      </HeadlessUI.Switch>
      <HeadlessUI.Switch.Label className="ml-2">
        Clear {` ${label}`}
      </HeadlessUI.Switch.Label>
    </HeadlessUI.Switch.Group>
  );
}
