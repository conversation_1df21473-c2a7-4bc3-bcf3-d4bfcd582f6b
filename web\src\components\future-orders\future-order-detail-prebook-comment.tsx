import { Fragment, useState } from 'react';
import * as HeadlessUI from '@headlessui/react';
import * as models from 'api/models/future-orders';
import { Alert } from '@/components/alert';
import { Icon } from '@/components/icon';
import { useAppDispatch } from '@/services/hooks';
import { formatDate } from '@/utils/format';
import {
  removeInternalComment,
  editInternalComment,
} from './future-order-detail-slice';

interface FutureOrderDetailPrebookCommentProps {
  comment: models.FutureOrderDetailComment;
}

export function FutureOrderDetailPrebookComment({
  comment,
}: FutureOrderDetailPrebookCommentProps) {
  const dispatch = useAppDispatch(),
    [isEditing, setIsEditing] = useState(false),
    [confirmDelete, setConfirmDelete] = useState(false);

  const handleEditClick = () => {
    setIsEditing(true);
  };

  const handleSaveClick = () => {
    setIsEditing(false);
  };

  const handleDeleteClick = () => {
    setConfirmDelete(true);
  };

  const handleDeleteCancel = () => {
    setConfirmDelete(false);
  };

  const handleDeleteConfirm = () => {
    dispatch(removeInternalComment(comment.id));
    setConfirmDelete(false);
  };

  const handleCommentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const update = { ...comment, comments: e.target.value };
    dispatch(editInternalComment(update));
  };

  return (
    <div className="mb-2 rounded-lg border p-2 text-xs">
      <div>
        <div className="flex items-start">
          {!isEditing && (
            <>
              <div className="flex-grow truncate">{comment.comments}</div>
              <div>
                <HeadlessUI.Popover className="relative">
                  <HeadlessUI.Popover.Button
                    className="btn-secondary border-transparent px-1 py-0"
                    tabIndex={-1}
                  >
                    <Icon icon="ellipsis-h" />
                  </HeadlessUI.Popover.Button>
                  <HeadlessUI.Transition
                    as={Fragment}
                    enter="transition ease-out duration-200"
                    enterFrom="opacity-0 translate-y-1"
                    enterTo="opacity-100 translate-y-0"
                    leave="transition ease-in duration-150"
                    leaveFrom="opacity-100 translate-y-0"
                    leaveTo="opacity-0 translate-y-1"
                  >
                    <HeadlessUI.Popover.Panel className="absolute bottom-10 z-40 w-96 -translate-x-1/2 transform px-4">
                      <div className="rounded-lg bg-yellow-50 p-4 font-normal shadow-lg ring-1 ring-black ring-opacity-5">
                        <div className="whitespace-pre-line text-sm text-gray-700">
                          {comment.comments}
                        </div>
                        <div className="mt-2 italic text-gray-500">
                          {formatDate(comment.created, 'MMM d, yyyy')} @{' '}
                          {formatDate(comment.created, 'h:mm a')}
                        </div>
                        <div className="truncate italic text-gray-500">
                          {comment.createdBy}
                        </div>
                      </div>
                    </HeadlessUI.Popover.Panel>
                  </HeadlessUI.Transition>
                </HeadlessUI.Popover>
              </div>
              <button
                type="button"
                onClick={handleEditClick}
                className="btn-secondary border-transparent px-2 py-0"
              >
                <Icon icon="edit" />
              </button>
            </>
          )}
          {isEditing && (
            <>
              <div className="flex-grow">
                <textarea
                  rows={3}
                  value={comment.comments || ''}
                  onChange={handleCommentChange}
                  className="w-full rounded-md border-gray-300 text-sm shadow-sm focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
              <button
                type="button"
                onClick={handleSaveClick}
                className="btn-secondary ml-1 border-transparent px-2 py-0"
              >
                <Icon icon="save" />
              </button>
            </>
          )}
          <button
            type="button"
            className="btn-delete border-transparent px-2 py-0"
            onClick={handleDeleteClick}
          >
            <Icon icon="trash" />
          </button>
        </div>
      </div>
      <Alert
        title="Remove Comment?"
        message="Are you sure you want to remove this comment?"
        colour="danger"
        open={confirmDelete}
        cancel={handleDeleteCancel}
        confirm={handleDeleteConfirm}
      />
    </div>
  );
}
