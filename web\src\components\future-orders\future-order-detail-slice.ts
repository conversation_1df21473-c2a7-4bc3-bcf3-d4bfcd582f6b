import {
  AsyncThunk,
  createAction,
  createAsyncThunk,
  createSelector,
  createSlice,
  PayloadAction,
} from '@reduxjs/toolkit';
import { DateTime } from 'luxon';
import {
  boekestynApi,
  OverridePrebookItemUpcArgs,
  OverridePrebookUpcArgs,
} from 'api/boekestyn-service';
import {
  FutureOrderDetailResponse,
  futureOrdersApi,
  futureOrdersListApi,
} from 'api/future-orders-service';
import {
  prebooksApi,
  ConfirmPrebookResponse,
  PrebookDetailResponse,
  CreateEmailResponse,
} from 'api/prebooks-service';
import {
  spireApi,
  SpireCustomerDetailResponse,
  spireService,
} from 'api/spire-service';
import { prebookListApi, seasonsApi } from 'api/prebooks-service';
import { settingsApi } from 'api/settings-service';
import * as models from 'api/models/future-orders';
import * as holidays from 'api/models/holidays';
import * as prebooks from 'api/models/prebooks';
import * as settings from 'api/models/settings';
import * as spire from 'api/models/spire';
import { RootState } from '@/services/store';
import { isDirty } from './dirty-prebooks';
import {
  potCovers as defaultPotCovers,
  noPotCover,
} from '@/components/pot-covers';
import { createPrebookEmail } from '@/components/prebooks/prebook-email-slice';
import { createProblemDetails, ProblemDetails } from '@/utils/problem-details';
import { equals } from '@/utils/equals';
import { sortBy } from '@/utils/sort';
import { getBoekestynProducts } from '../boekestyns/sales/boekestyn-sales-functions';
import { findDefaultVendorOverride } from './item-functions';

const sortByCreated = sortBy('created');
const sortBySortOrder = sortBy('sortOrder');

export interface DetailItem extends models.FutureOrderDetailItem {
  isBlanket?: boolean;
  createPrebook?: boolean;
  expanded?: boolean;
}

interface FutureOrderDetailState {
  requiredDate: string | null;
  arrivalDate: string | null;
  seasonName: string | null;
  salesperson: spire.Salesperson | null;
  shipTo: spire.CustomerShipTo | null;
  customer: spire.Customer | null;
  shipVia: spire.ShippingMethod | null;
  boxCode: string | null;
  requiresLabels: boolean;
  customerPurchaseOrderNumber: string | null;
  freightPerCase: number | null;
  freightPerLoad: number | null;
  freightIsActual: boolean;
  spireNotes: string | null;
  growerItemNotes: string | null;
  comments: models.FutureOrderDetailComment[];
  internalComments: string | null;
  customerInfo: string | null;
  items: DetailItem[];
  productDefaults: settings.ProductDefault[];
  blanketItems: prebooks.PrebookBlanketItem[];
  seasons: prebooks.Season[];
  futureOrder: models.FutureOrderDetail | null;
  prebooks: prebooks.PrebookDetail[];
  originalPrebooks: prebooks.PrebookDetail[];
  emails: prebooks.PrebookEmail[];
  customerDetail: spire.CustomerDetail | null;
  salespeople: spire.Salesperson[];
  vendors: spire.Vendor[];
  customerItemCodeDefaults: settings.CustomerItemCodeDefault[];
  customerPotCovers: string[];
  availabilityUnitPrices: { [index: number]: number };
  defaultVendorOverrides: settings.DefaultVendorOverride[];
  holidays: holidays.Holiday[];
  showInventoryDialog: boolean;
  showAddFromOrder: boolean;
  showSplitOrder: boolean;
  isLoading: boolean;
  error: ProblemDetails | null;
}

const initialState: FutureOrderDetailState = {
  requiredDate: null,
  arrivalDate: null,
  seasonName: null,
  customer: null,
  shipTo: null,
  salesperson: null,
  shipVia: null,
  boxCode: null,
  requiresLabels: false,
  customerPurchaseOrderNumber: null,
  freightPerCase: null,
  freightPerLoad: null,
  freightIsActual: false,
  spireNotes: null,
  growerItemNotes: null,
  comments: [],
  internalComments: null,
  customerInfo: null,
  items: [],
  productDefaults: [],
  blanketItems: [],
  seasons: [],
  futureOrder: null,
  prebooks: [],
  originalPrebooks: [],
  emails: [],
  customerDetail: null,
  salespeople: [],
  vendors: [],
  customerItemCodeDefaults: [],
  customerPotCovers: [],
  availabilityUnitPrices: {},
  defaultVendorOverrides: [],
  holidays: [],
  showInventoryDialog: false,
  showAddFromOrder: false,
  showSplitOrder: false,
  isLoading: false,
  error: null,
};

export const getCustomerDetail: AsyncThunk<
  SpireCustomerDetailResponse | null,
  number | null,
  { state: RootState }
> = createAsyncThunk(
  'future-order-setCustomerDetail',
  async (id, { rejectWithValue }) => {
    try {
      return id ? await spireService.customerDetail(id) : null;
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const getFutureOrderDetail: AsyncThunk<
  FutureOrderDetailResponse,
  number,
  { state: RootState }
> = createAsyncThunk(
  'future-order-getFutureOrderDetail',
  async (id, { rejectWithValue }) => {
    try {
      return futureOrdersApi.detail(id);
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const setGrowerConfirmed: AsyncThunk<
  ConfirmPrebookResponse,
  number,
  { state: RootState }
> = createAsyncThunk(
  'future-order-detail-setGrowerConfirmed',
  async (id, { rejectWithValue }) => {
    try {
      return await prebooksApi.confirm(id);
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const updatePrebook: AsyncThunk<
  PrebookDetailResponse,
  prebooks.PrebookDetail,
  { state: RootState }
> = createAsyncThunk(
  'prebook-detail/updatePrebook',
  async (prebook, { rejectWithValue }) => {
    try {
      return await prebooksApi.update(prebook);
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const updateFutureOrder: AsyncThunk<
  FutureOrderDetailResponse,
  void,
  { state: RootState }
> = createAsyncThunk(
  'future-order-detail/updateFutureOrder',
  async (_, { rejectWithValue, getState }) => {
    const rootState = getState() as RootState,
      state = rootState.futureOrderDetail,
      futureOrder = state.futureOrder;

    if (!futureOrder) {
      return rejectWithValue(createProblemDetails('Future Order not found.'));
    }

    const {
        requiredDate,
        arrivalDate,
        seasonName,
        customer,
        salesperson,
        shipTo,
        shipVia,
        boxCode,
        requiresLabels,
        customerPurchaseOrderNumber,
        freightPerCase,
        freightPerLoad,
        freightIsActual,
        spireNotes,
        growerItemNotes,
        internalComments,
      } = state,
      items = state.items
        .map((i) => ({ ...i }))
        .sort(sortBySortOrder)
        .map((i, index) => ({
          id: i.id,
          sortOrder: index + 1,
          vendorId: i.vendorId,
          vendorName: i.vendorName,
          spireInventoryId: i.spireInventoryId,
          spirePartNumber: i.spirePartNumber,
          description: i.description,
          orderQuantity: i.orderQuantity,
          isApproximate: i.isApproximate,
          hasPotCover: i.hasPotCover,
          potCover: i.potCover,
          dateCode: i.dateCode,
          upc: i.upc,
          weightsAndMeasures: i.weightsAndMeasures,
          retail: i.retail,
          comments: i.comments,
          blanketItemId: i.blanketItemId,
          unitPrice: i.unitPrice,
          useAvailabilityPricing: i.useAvailabilityPricing,
          customerItemCode: i.customerItemCode,
          upgradeSheet: i.upgradeSheet,
          createPrebook: !!i.createPrebook,
          isBlanket: !!i.isBlanket,
          phytoRequired: !!i.phytoRequired,
          phytoOrdered: !!i.phytoOrdered,
          boekestynPlantId: i.boekestynPlantId,
          boekestynCustomerAbbreviation: i.boekestynCustomerAbbreviation,
          upgradeLabourHours: i.upgradeLabourHours,
          quantityPerFinishedItem: i.quantityPerFinishedItem,
          specialPrice: i.specialPrice,
          growerItemNotes: i.growerItemNotes,
          boekestynProducts: i.boekestynProducts.map((p) => ({ ...p })),
        })),
      nextCommentId =
        state.comments.reduce((min, c) => Math.min(min, c.id), 0) - 1,
      comments = state.comments
        .map((c) => ({ ...c } as models.FutureOrderDetailComment))
        .concat(
          internalComments
            ? [
                {
                  id: nextCommentId,
                  comments: internalComments,
                  isStandardComment: false,
                  created: null,
                  createdBy: null,
                },
              ]
            : []
        ),
      prebooks = state.prebooks.map((p) => ({ ...p }));

    const update: models.FutureOrderUpdate = {
      id: futureOrder.id,
      requiredDate,
      arrivalDate,
      seasonName,
      customerId: customer?.id || null,
      customerName: customer?.name || null,
      shipToId: shipTo?.id || null,
      shipToName: shipTo?.shipId || null,
      salespersonId: salesperson?.id || null,
      salespersonName: salesperson?.name || null,
      shipViaId: shipVia?.id || null,
      shipViaName: shipVia?.description || null,
      boxCode,
      requiresLabels,
      customerPurchaseOrderNumber,
      freightPerCase,
      freightPerLoad,
      freightIsActual,
      spireNotes,
      growerItemNotes,
      comments,
      items,
    };

    try {
      return await futureOrdersApi.update(update, prebooks);
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const sendToSpire: AsyncThunk<void, number, { state: RootState }> =
  createAsyncThunk(
    'future-order-detail-sendToSpire',
    async (id, { rejectWithValue }) => {
      try {
        return await futureOrdersApi.sendToSpire(id);
      } catch (e) {
        return rejectWithValue(e as ProblemDetails);
      }
    }
  );

export const upcOverride: AsyncThunk<
  undefined,
  OverridePrebookItemUpcArgs,
  { state: RootState }
> = createAsyncThunk(
  'future-order-upcOverride',
  async (args, { rejectWithValue, dispatch }) => {
    try {
      await boekestynApi.overridePrebookItemUpc(args);

      dispatch(
        futureOrderDetailSlice.actions.clearItemUpcCompleted(args.prebookItemId)
      );
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const upcOverrideAll: AsyncThunk<
  undefined,
  OverridePrebookUpcArgs,
  { state: RootState }
> = createAsyncThunk(
  'future-order-upcOverride-all',
  async (args, { rejectWithValue, dispatch }) => {
    try {
      await boekestynApi.overridePrebookUpc(args);

      dispatch(futureOrderDetailSlice.actions.clearAllItemsUpcCompleted());
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

interface SetItemArgs<T> {
  itemId: number;
  value: T;
}

export interface MoveItemArgs {
  existingItem: DetailItem;
  movingItem: DetailItem;
}

export interface InventoryItemWithDefaults
  extends spire.InventoryItem,
    Partial<prebooks.ProductShipToDefault> {
  id: number;
  blanketItemId: number | null;
  comments: string | null;
  vendorId: number | null;
  vendorName: string | null;
  boekestynPlantId: string | null;
  boekestynCustomerAbbreviation: string | null;
  useAvailabilityPricing: boolean;
  upgradeLabourHours?: number | null;
  upgradeSheet?: boolean;
  quantityPerFinishedItem?: number | null;
  specialPrice?: number | null;
  growerItemNotes?: string | null;
}

const getCustomerDetailFulfilled =
    createAction<SpireCustomerDetailResponse | null>(
      getCustomerDetail.fulfilled.type
    ),
  getCustomerDetailRejected = createAction<ProblemDetails>(
    getCustomerDetail.rejected.type
  ),
  getFutureOrderDetailPending = createAction(getFutureOrderDetail.pending.type),
  getFutureOrderDetailFulfilled = createAction<FutureOrderDetailResponse>(
    getFutureOrderDetail.fulfilled.type
  ),
  getFutureOrderDetailRejected = createAction<ProblemDetails>(
    getFutureOrderDetail.rejected.type
  ),
  setGrowerConfirmedFulfilled = createAction<ConfirmPrebookResponse>(
    setGrowerConfirmed.fulfilled.type
  ),
  setGrowerConfirmedRejected = createAction<ProblemDetails>(
    setGrowerConfirmed.rejected.type
  ),
  updatePrebookFulfilled = createAction<PrebookDetailResponse>(
    updatePrebook.fulfilled.type
  ),
  updatePrebookRejected = createAction<ProblemDetails>(
    updatePrebook.rejected.type
  ),
  updateFutureOrderFulfilled = createAction<FutureOrderDetailResponse>(
    updateFutureOrder.fulfilled.type
  ),
  updateFutureOrderRejected = createAction<ProblemDetails>(
    updateFutureOrder.rejected.type
  ),
  createPrebookEmailFulfilled = createAction<CreateEmailResponse>(
    createPrebookEmail.fulfilled.type
  ),
  createPrebookEmailRejected = createAction<ProblemDetails>(
    createPrebookEmail.rejected.type
  ),
  sendToSpireRejected = createAction<ProblemDetails>(sendToSpire.rejected.type);

const futureOrderDetailSlice = createSlice({
  name: 'future-order-detail',
  initialState,
  reducers: {
    clearState(state) {
      const salespeople = state.salespeople.map((s) => ({ ...s }));
      Object.assign(state, { ...initialState, salespeople });
    },
    clearError(state) {
      state.error = null;
    },
    setError(state, { payload }: PayloadAction<ProblemDetails | string>) {
      const error =
        typeof payload === 'string' ? createProblemDetails(payload) : payload;
      state.error = error;
    },
    setCustomer(state, { payload }: PayloadAction<spire.Customer | null>) {
      state.customer = payload;
      const customerId = payload?.id || null,
        customerName = payload?.name || null;
      const prebooks = state.prebooks.map((p) => ({
        ...p,
        customerId,
        customerName,
      }));
      state.prebooks = prebooks;
    },
    setCustomerDetail(state, { payload }: PayloadAction<spire.CustomerDetail>) {
      state.customerDetail = payload;
      state.customer = {
        id: payload.id,
        customerNo: payload.customerNo,
        name: payload.name,
        defaultShipTo: payload.defaultShipTo,
      };
    },
    setShipTo(state, { payload }: PayloadAction<spire.CustomerShipTo | null>) {
      state.shipTo = payload;

      const salesperson =
          state.salespeople.find(
            (s) => s.code === payload?.salesperson?.code
          ) || null,
        boxCode = payload?.boxCode || null,
        labels = payload?.labels || null;

      state.salesperson = salesperson;
      state.boxCode = boxCode;
      state.requiresLabels = equals(labels, models.SpecialLabels);
      state.freightPerCase = payload?.defaultFreightPerCase || null;
      // freight per load gets set in a useEffect() block in the component

      const shipToId = payload?.id || null,
        shipToName = payload?.shipId || payload?.name || null,
        salespersonId = salesperson?.id || null,
        salespersonName = salesperson?.name || null;

      const prebooks = state.prebooks.map((p) => ({
        ...p,
        shipToId,
        shipToName,
        salespersonId,
        salespersonName,
        boxCode,
      }));
      state.prebooks = prebooks;
    },
    setSalesperson(
      state,
      { payload }: PayloadAction<spire.Salesperson | null>
    ) {
      state.salesperson = payload;

      const salespersonId = payload?.id || null,
        salespersonName = payload?.name || null;

      const prebooks = state.prebooks.map((p) => ({
        ...p,
        salespersonId,
        salespersonName,
      }));
      state.prebooks = prebooks;
    },
    setRequiredDate(state, { payload }: PayloadAction<string | null>) {
      state.requiredDate = payload;

      const prebooks = state.prebooks.map((p) => ({
        ...p,
        requiredDate: payload,
      }));

      const items = state.items.map((i) => ({ ...i })),
        season = state.seasons.find((s) => s.name === state.seasonName) || null;
      items.forEach((item) => {
        item.boekestynProducts = getBoekestynProducts(
          item.vendorId,
          payload,
          season,
          state.productDefaults.find(
            (d) => d.spireInventoryId === item.spireInventoryId
          )
        );
      });
      state.items = items;

      state.prebooks = prebooks;
    },
    setArrivalDate(state, { payload }: PayloadAction<string | null>) {
      state.arrivalDate = payload;
    },
    setSeasonName(state, { payload }: PayloadAction<string | null>) {
      state.seasonName = payload;

      const prebooks = state.prebooks.map((p) => ({
        ...p,
        seasonName: payload,
      }));
      state.prebooks = prebooks;
    },
    setShipVia(state, { payload }: PayloadAction<spire.ShippingMethod | null>) {
      state.shipVia = payload;
    },
    setBoxCode(state, { payload }: PayloadAction<string | null>) {
      state.boxCode = payload;

      const prebooks = state.prebooks.map((p) => ({
        ...p,
        boxCode: payload,
      }));
      state.prebooks = prebooks;
    },
    setRequiresLabels(state, { payload }: PayloadAction<boolean>) {
      state.requiresLabels = payload;
    },
    setCustomerPurchaseOrderNumber(
      state,
      { payload }: PayloadAction<string | null>
    ) {
      state.customerPurchaseOrderNumber = payload;
    },
    setFreightPerCase(state, { payload }: PayloadAction<number | null>) {
      state.freightPerCase = payload;
    },
    setFreightPerLoad(state, { payload }: PayloadAction<number | null>) {
      state.freightPerLoad = payload;
    },
    setFreightIsActual(state, { payload }: PayloadAction<boolean>) {
      state.freightIsActual = payload;
    },
    setComments(
      state,
      { payload }: PayloadAction<models.FutureOrderDetailComment[]>
    ) {
      state.comments = payload;
    },
    setInternalComments(state, { payload }: PayloadAction<string | null>) {
      state.internalComments = payload;
    },
    removeInternalComment(state, { payload }: PayloadAction<number>) {
      const comments = state.comments.filter((c) => c.id !== payload);
      state.comments = comments;
    },
    editInternalComment(
      state,
      { payload }: PayloadAction<models.FutureOrderDetailComment>
    ) {
      const comments = state.comments.map((c) => ({ ...c })),
        comment = comments.find((c) => c.id === payload.id);

      if (comment) {
        comment.comments = payload.comments;
      }
      state.comments = comments;
    },
    setGrowerItemNotes(state, { payload }: PayloadAction<string | null>) {
      state.growerItemNotes = payload;
      const prebooks = state.prebooks.map((p) => ({
        ...p,
        growerItemNotes: payload,
      }));
      state.prebooks = prebooks;
    },
    setSpireNotes(state, { payload }: PayloadAction<string | null>) {
      state.spireNotes = payload;
    },
    setCustomerInfo(state, { payload }: PayloadAction<string | null>) {
      state.customerInfo = payload;
    },
    addItem(state, { payload }: PayloadAction<InventoryItemWithDefaults>) {
      const id = state.items.reduce((min, i) => Math.min(min, i.id), 0) - 1,
        sortOrder =
          state.items.reduce((max, i) => Math.max(max, i.sortOrder), 0) + 1,
        {
          dateCode,
          weightsAndMeasures,
          hasPotCover,
          customerItemCode,
          potCover,
          comments,
          blanketItemId,
          upc,
          retail,
          useAvailabilityPricing,
          unitPrice,
          boekestynPlantId,
          boekestynCustomerAbbreviation,
          upgradeLabourHours: payloadUpgradeLabourHours,
          quantityPerFinishedItem: payloadQuantityPerFinishedItem,
          upgradeSheet,
          specialPrice,
          growerItemNotes,
        } = payload,
        productDefault = state.productDefaults.find(
          (pd) => pd.spireInventoryId === payload.id
        ),
        upgradeLabourHours =
          payloadUpgradeLabourHours || productDefault?.upgradeLabourHours || 0,
        quantityPerFinishedItem =
          payloadQuantityPerFinishedItem === undefined
            ? productDefault?.quantityPerFinishedItem || null
            : payloadQuantityPerFinishedItem,
        phytoRequired = equals(payload?.uom?.location, spire.PhytoLocation),
        season = state.seasons.find((s) => s.name === state.seasonName) || null,
        boekestynProducts = getBoekestynProducts(
          payload.vendorId,
          state.requiredDate,
          season,
          productDefault
        ),
        item: DetailItem = {
          id,
          sortOrder,
          vendorId: payload.vendorId || null,
          vendorName: payload?.vendorName || null,
          spireInventoryId: payload.id,
          spirePartNumber: payload.partNo,
          description: payload.description,
          hasPotCover: !!hasPotCover,
          potCover: potCover || null,
          dateCode: dateCode || null,
          upc: upc || null,
          weightsAndMeasures: !!weightsAndMeasures,
          retail: retail || null,
          orderQuantity: 0,
          isApproximate: false,
          createPrebook: true,
          blanketItemId: blanketItemId || null,
          comments: comments || null,
          unitPrice: unitPrice || null,
          useAvailabilityPricing: !!useAvailabilityPricing,
          customerItemCode: customerItemCode || null,
          upgradeSheet: !!upgradeSheet,
          phytoRequired,
          phytoOrdered: false,
          boekestynPlantId: boekestynPlantId || null,
          boekestynCustomerAbbreviation: boekestynCustomerAbbreviation || null,
          expanded: true,
          upgradeLabourHours,
          quantityPerFinishedItem,
          specialPrice: specialPrice || null,
          growerItemNotes: growerItemNotes || null,
          boekestynProducts,
        };

      if (payload.blanketItemId) {
        const blanketItem = state.blanketItems.find(
          (i) => i.id === payload.blanketItemId
        );
        if (blanketItem) {
          item.vendorId = blanketItem.vendorId;
          item.vendorName = blanketItem.vendorName;
        }
      } else {
        const override = findDefaultVendorOverride(
            item.spirePartNumber,
            state.requiredDate,
            state.defaultVendorOverrides
          ),
          overrideVendor = state.vendors.find(
            (v) => v.id === override?.vendorId
          );
        if (overrideVendor) {
          item.vendorId = overrideVendor.id;
          item.vendorName = overrideVendor.name;
        }
      }

      if (!item.vendorId && payload.primaryVendor?.vendorNo) {
        const primaryVendor = state.vendors.find(
          (v) => v.vendorNo === payload.primaryVendor?.vendorNo
        );
        if (primaryVendor) {
          item.vendorId = primaryVendor.id;
          item.vendorName = primaryVendor.name;
        }
      }

      const items = state.items.map((i) => ({ ...i })).concat([item]);

      state.items = items;

      if (
        item.vendorId &&
        item.spireInventoryId &&
        item.spirePartNumber &&
        item.description
      ) {
        const prebooks = state.prebooks.map((p) => ({ ...p })),
          prebookItemId =
            prebooks.reduce(
              (min, p) =>
                Math.min(
                  min,
                  p.items.reduce((m, i) => Math.min(m, i.id), 0)
                ),
              0
            ) - 1,
          prebookItem: prebooks.PrebookDetailItem = {
            id: prebookItemId,
            futureOrderItemId: item.id,
            futureOrderId: state.futureOrder?.id || null,
            spireInventoryId: item.spireInventoryId,
            spirePartNumber: item.spirePartNumber,
            description: item.description,
            orderQuantity: item.orderQuantity,
            hasPotCover: item.hasPotCover,
            potCover: item.potCover,
            dateCode: item.dateCode,
            upc: item.upc,
            weightsAndMeasures: item.weightsAndMeasures,
            retail: item.retail,
            comments: item.comments,
            isApproximate: item.isApproximate,
            blanketItemId: item.blanketItemId,
            blanketWeekId: null,
            upgradeSheet: item.upgradeSheet,
            boekestynPlantId: item.boekestynPlantId,
            boekestynCustomerAbbreviation: item.boekestynCustomerAbbreviation,
            upgradeLabourHours: item.upgradeLabourHours,
            quantityPerFinishedItem: item.quantityPerFinishedItem,
            spirePurchaseOrderItemId: null,
            specialPrice: item.specialPrice,
            growerItemNotes: item.growerItemNotes,
            blanketOptions: [],
            upcPrinted: null,
            upcPrintedPrev: null,
            boekPriority: false,
            boekestynProducts: boekestynProducts.map((p) => ({
              ...p,
              prebookItemId,
            })),
          };
        // create a new Prebook
        if (!prebooks.some((p) => p.vendorId === item.vendorId && !p.deleted)) {
          const prebookId =
              prebooks.reduce((min, p) => Math.min(min, p.id), 0) - 1,
            prebook: prebooks.PrebookDetail = {
              id: prebookId,
              name: null,
              requiredDate: state.requiredDate,
              isBlanket: !!item.isBlanket,
              blanketStartDate: null,
              blanketIsClosed: false,
              seasonName: state.seasonName,
              boxCode: state.boxCode,
              vendorId: item.vendorId,
              vendorName: item.vendorName,
              salespersonId: state.salesperson?.id || null,
              salespersonName: state.salesperson?.name || null,
              customerId: state.customer?.id || null,
              customerName: state.customer?.name || null,
              shipToId: state.shipTo?.id || null,
              shipToName: state.shipTo?.shipId || null,
              comments: item.comments,
              growerItemNotes: state.growerItemNotes,
              spirePurchaseOrderId: null,
              spirePurchaseOrderNumber: null,
              futureOrderId: state.futureOrder?.id || null,
              created: new Date().toISOString(),
              createdBy: '',
              modified: new Date().toISOString(),
              modifiedBy: '',
              confirmed: null,
              confirmedBy: null,
              deleted: null,
              deletedBy: null,
              items: [prebookItem],
            };
          prebooks.push(prebook);
          // add an item to existing Prebook
        } else if (
          !prebooks.some(
            (p) =>
              p.vendorId === item.vendorId &&
              p.items.some((i) => i.futureOrderItemId === item.id) &&
              !p.deleted
          )
        ) {
          const prebook = prebooks.find(
            (p) => p.vendorId === item.vendorId && !p.deleted
          );
          if (prebook) {
            prebook.items.push(prebookItem);
          }
        }

        state.prebooks = prebooks;
      }
    },
    duplicateItem(state, { payload }: PayloadAction<number>) {
      const id = state.items.reduce((min, i) => Math.min(min, i.id), 0) - 1,
        sortOrder =
          state.items.reduce((max, i) => Math.max(max, i.sortOrder), 0) + 1,
        existing = state.items.find((i) => i.id === payload);

      if (existing) {
        const item: DetailItem = {
          id,
          sortOrder,
          vendorId: existing.vendorId,
          vendorName: existing.vendorName,
          spireInventoryId: existing.spireInventoryId,
          spirePartNumber: existing.spirePartNumber,
          description: existing.description,
          hasPotCover: existing.hasPotCover,
          potCover: existing.potCover,
          dateCode: existing.dateCode,
          upc: existing.upc,
          weightsAndMeasures: existing.weightsAndMeasures,
          retail: existing.retail,
          orderQuantity: 0,
          isApproximate: existing.isApproximate,
          createPrebook: true,
          blanketItemId: existing.blanketItemId,
          comments: existing.comments,
          unitPrice: existing.unitPrice,
          useAvailabilityPricing: existing.useAvailabilityPricing,
          customerItemCode: existing.customerItemCode,
          upgradeSheet: existing.upgradeSheet,
          phytoRequired: existing.phytoRequired,
          phytoOrdered: existing.phytoOrdered,
          boekestynPlantId: existing.boekestynPlantId,
          boekestynCustomerAbbreviation: existing.boekestynCustomerAbbreviation,
          upgradeLabourHours: existing.upgradeLabourHours,
          quantityPerFinishedItem: existing.quantityPerFinishedItem,
          specialPrice: existing.specialPrice,
          growerItemNotes: existing.growerItemNotes,
          boekestynProducts: existing.boekestynProducts,
          expanded: true,
        };

        const items = state.items.map((i) => ({ ...i })).concat([item]);
        state.items = items;

        if (item.spireInventoryId && item.spirePartNumber && item.description) {
          const prebooks = state.prebooks.map((p) => ({ ...p })),
            prebookItemId =
              prebooks.reduce(
                (min, p) =>
                  Math.min(
                    min,
                    p.items.reduce((m, i) => Math.min(m, i.id), 0)
                  ),
                0
              ) - 1,
            prebookItem: prebooks.PrebookDetailItem = {
              id: prebookItemId,
              futureOrderItemId: item.id,
              futureOrderId: state.futureOrder?.id || null,
              spireInventoryId: item.spireInventoryId,
              spirePartNumber: item.spirePartNumber,
              description: item.description,
              orderQuantity: item.orderQuantity,
              hasPotCover: item.hasPotCover,
              potCover: item.potCover,
              dateCode: item.dateCode,
              upc: item.upc,
              weightsAndMeasures: item.weightsAndMeasures,
              retail: item.retail,
              comments: item.comments,
              isApproximate: item.isApproximate,
              blanketItemId: item.blanketItemId,
              blanketWeekId: null,
              upgradeSheet: item.upgradeSheet,
              boekestynPlantId: item.boekestynPlantId,
              boekestynCustomerAbbreviation: item.boekestynCustomerAbbreviation,
              upgradeLabourHours: item.upgradeLabourHours,
              quantityPerFinishedItem: item.quantityPerFinishedItem,
              spirePurchaseOrderItemId: null,
              specialPrice: item.specialPrice,
              growerItemNotes: item.growerItemNotes,
              blanketOptions: [],
              upcPrinted: null,
              upcPrintedPrev: null,
              boekPriority: false,
              boekestynProducts: item.boekestynProducts.map((p) => ({
                ...p,
                prebookItemId,
              })),
            };
          const prebook = prebooks.find(
            (p) => p.vendorId === item.vendorId && !p.deleted
          );
          if (prebook) {
            prebook.items.push(prebookItem);
          }

          state.prebooks = prebooks;
        }
      }
    },
    removeItem(state, { payload }: PayloadAction<number>) {
      const items = state.items
          .filter((i) => i.id !== payload)
          .map((i, index) => ({ ...i, sortOrder: index + 1 })),
        prebooks = state.prebooks
          .map((p) => ({
            ...p,
            items: p.items.filter((i) => i.futureOrderItemId !== payload),
          }))
          .filter((p) => p.items.length);

      state.items = items;
      state.prebooks = prebooks;
    },
    setShowInventoryDialog(state, { payload }: PayloadAction<boolean>) {
      state.showInventoryDialog = payload;
    },
    setShowAddFromOrder(state, { payload }: PayloadAction<boolean>) {
      state.showAddFromOrder = payload;
    },
    setShowSplitOrder(state, { payload }: PayloadAction<boolean>) {
      state.showSplitOrder = payload;
    },
    setItemInventoryItem(
      state,
      { payload }: PayloadAction<SetItemArgs<spire.InventoryItem | null>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        const spireInventoryId = value?.id || null,
          spirePartNumber = value?.partNo || null,
          description = value?.description || null;

        item.spireInventoryId = spireInventoryId;
        item.spirePartNumber = spirePartNumber;
        item.description = description;

        const defaults = state.customerItemCodeDefaults.find(
          (d) =>
            d.customerId === state.customer?.id &&
            d.spireInventoryId === item.spireInventoryId
        );
        if (defaults?.customerItemCode) {
          item.customerItemCode = defaults.customerItemCode;
        }

        const prebooks = state.prebooks.map((p) => ({ ...p }));

        prebooks.forEach((p) => {
          p.items = p.items.map((i) => {
            const clone = { ...i };
            if (
              spireInventoryId &&
              spirePartNumber &&
              description &&
              i.futureOrderItemId === item.id
            ) {
              clone.spireInventoryId = spireInventoryId;
              clone.spirePartNumber = spirePartNumber;
              clone.description = description;
            }

            return clone;
          });
        });

        state.prebooks = prebooks;
      }

      state.items = items;
    },
    setItemHasPotCover(
      state,
      { payload }: PayloadAction<SetItemArgs<boolean>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.hasPotCover = value;

        const prebooks = state.prebooks.map((p) => ({ ...p }));

        prebooks.forEach((p) => {
          p.items = p.items.map((i) => {
            const clone = { ...i };
            if (i.futureOrderItemId === item.id) {
              clone.hasPotCover = value;
            }

            return clone;
          });
        });

        state.prebooks = prebooks;
      }

      state.items = items;
    },
    setItemPotCover(
      state,
      { payload }: PayloadAction<SetItemArgs<string | null>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.potCover = value;

        const prebooks = state.prebooks.map((p) => ({ ...p }));

        prebooks.forEach((p) => {
          p.items = p.items.map((i) => {
            const clone = { ...i };
            if (i.futureOrderItemId === item.id) {
              clone.potCover = value;
            }

            return clone;
          });
        });

        state.prebooks = prebooks;
      }

      state.items = items;
    },
    setItemDateCode(
      state,
      { payload }: PayloadAction<SetItemArgs<string | null>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.dateCode = value;

        const prebooks = state.prebooks.map((p) => ({ ...p }));

        prebooks.forEach((p) => {
          p.items = p.items.map((i) => {
            const clone = { ...i };
            if (i.futureOrderItemId === item.id) {
              clone.dateCode = value;
            }

            return clone;
          });
        });

        state.prebooks = prebooks;
      }

      state.items = items;
    },
    setItemUPC(state, { payload }: PayloadAction<SetItemArgs<string | null>>) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.upc = value;

        const prebooks = state.prebooks.map((p) => ({ ...p }));

        prebooks.forEach((p) => {
          p.items = p.items.map((i) => {
            const clone = { ...i };
            if (i.futureOrderItemId === item.id) {
              clone.upc = value;
            }

            return clone;
          });
        });

        state.prebooks = prebooks;
      }

      state.items = items;
    },
    setItemWeightsAndMeasures(
      state,
      { payload }: PayloadAction<SetItemArgs<boolean>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.weightsAndMeasures = value;

        const prebooks = state.prebooks.map((p) => ({ ...p }));

        prebooks.forEach((p) => {
          p.items = p.items.map((i) => {
            const clone = { ...i };
            if (i.futureOrderItemId === item.id) {
              clone.weightsAndMeasures = value;
            }

            return clone;
          });
        });

        state.prebooks = prebooks;
      }

      state.items = items;
    },
    setItemRetail(
      state,
      { payload }: PayloadAction<SetItemArgs<string | null>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.retail = value;

        const prebooks = state.prebooks.map((p) => ({ ...p }));

        prebooks.forEach((p) => {
          p.items = p.items.map((i) => {
            const clone = { ...i };
            if (i.futureOrderItemId === item.id) {
              clone.retail = value;
            }

            return clone;
          });
        });

        state.prebooks = prebooks;
      }

      state.items = items;
    },
    setItemComments(
      state,
      { payload }: PayloadAction<SetItemArgs<string | null>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.comments = value;

        const prebooks = state.prebooks.map((p) => ({ ...p }));

        prebooks.forEach((p) => {
          p.items = p.items.map((i) => {
            const clone = { ...i };
            if (i.futureOrderItemId === item.id) {
              clone.comments = value;
            }

            return clone;
          });
        });

        state.prebooks = prebooks;
      }

      state.items = items;
    },
    setItemOrderQuantity(
      state,
      { payload }: PayloadAction<SetItemArgs<number>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        const previousOrderQuantity = item.orderQuantity,
          prebooks = state.prebooks.map((p) => ({ ...p }));

        item.orderQuantity = value;

        prebooks.forEach((p) => {
          p.items = p.items.map((i) => {
            const clone = { ...i };
            if (
              i.futureOrderItemId === item.id &&
              i.orderQuantity === previousOrderQuantity
            ) {
              clone.orderQuantity = value;
            }

            return clone;
          });
        });

        state.prebooks = prebooks;
      }

      state.items = items;
    },
    setItemIsApproximate(
      state,
      { payload }: PayloadAction<SetItemArgs<boolean>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.isApproximate = value;

        const prebooks = state.prebooks.map((p) => ({ ...p }));

        prebooks.forEach((p) => {
          p.items = p.items.map((i) => {
            const clone = { ...i };
            if (i.futureOrderItemId === item.id) {
              clone.isApproximate = value;
            }

            return clone;
          });
        });

        state.prebooks = prebooks;
      }

      state.items = items;
    },
    setItemVendor(
      state,
      { payload }: PayloadAction<SetItemArgs<spire.Vendor | null>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.vendorId = value?.id || null;
        item.vendorName = value?.name || null;

        if (item.comments === prebooks.overAndAboveBlanketComment) {
          item.comments = null;
        }

        const season =
            state.seasons.find((s) => s.name === state.seasonName) || null,
          boekestynProducts = getBoekestynProducts(
            value?.id,
            state.requiredDate,
            season,
            state.productDefaults.find(
              (d) => d.spireInventoryId === item.spireInventoryId
            )
          );

        item.boekestynProducts = boekestynProducts;

        if (item.spireInventoryId && item.spirePartNumber && item.description) {
          const prebooks = state.prebooks.map((p) => ({ ...p })),
            prebookItemId =
              prebooks.reduce(
                (min, p) =>
                  Math.min(
                    min,
                    p.items.reduce((m, i) => Math.min(m, i.id), 0)
                  ),
                0
              ) - 1,
            previousVendorPrebook = prebooks.find((p) =>
              p.items.some((i) => i.futureOrderItemId === item.id)
            ),
            prebookItem: prebooks.PrebookDetailItem = {
              id: prebookItemId,
              futureOrderItemId: item.id,
              spireInventoryId: item.spireInventoryId,
              spirePartNumber: item.spirePartNumber,
              description: item.description,
              orderQuantity: item.orderQuantity,
              hasPotCover: item.hasPotCover,
              potCover: item.potCover,
              dateCode: item.dateCode,
              upc: item.upc,
              weightsAndMeasures: item.weightsAndMeasures,
              retail: item.retail,
              comments: item.comments,
              isApproximate: item.isApproximate,
              blanketItemId: item.blanketItemId,
              blanketWeekId: null,
              upgradeSheet: item.upgradeSheet,
              boekestynPlantId: item.boekestynPlantId,
              boekestynCustomerAbbreviation: item.boekestynCustomerAbbreviation,
              upgradeLabourHours: item.upgradeLabourHours,
              quantityPerFinishedItem: item.quantityPerFinishedItem,
              spirePurchaseOrderItemId: null,
              specialPrice: item.specialPrice,
              growerItemNotes: item.growerItemNotes,
              blanketOptions: [],
              upcPrinted: null,
              upcPrintedPrev: null,
              boekPriority: false,
              boekestynProducts: boekestynProducts.map((p) => ({
                ...p,
                prebookItemId,
              })),
            };
          // create a new Prebook
          if (
            item.vendorId &&
            !prebooks.some((p) => p.vendorId === item.vendorId && !p.deleted)
          ) {
            const prebookId =
                prebooks.reduce((min, p) => Math.min(min, p.id), 0) - 1,
              prebook: prebooks.PrebookDetail = {
                id: prebookId,
                name: null,
                requiredDate: state.requiredDate,
                isBlanket: !!item.isBlanket,
                blanketStartDate: null,
                blanketIsClosed: false,
                seasonName: state.seasonName,
                boxCode: state.boxCode,
                vendorId: item.vendorId,
                vendorName: item.vendorName,
                salespersonId: state.salesperson?.id || null,
                salespersonName: state.salesperson?.name || null,
                customerId: state.customer?.id || null,
                customerName: state.customer?.name || null,
                shipToId: state.shipTo?.id || null,
                shipToName: state.shipTo?.shipId || null,
                comments: item.comments,
                growerItemNotes: state.growerItemNotes,
                spirePurchaseOrderId: null,
                spirePurchaseOrderNumber: null,
                futureOrderId: state.futureOrder?.id || null,
                created: new Date().toISOString(),
                createdBy: '',
                modified: new Date().toISOString(),
                modifiedBy: '',
                confirmed: null,
                confirmedBy: null,
                deleted: null,
                deletedBy: null,
                items: [prebookItem],
              };
            prebooks.push(prebook);
            // add an item to existing Prebook
          } else if (
            item.vendorId &&
            !prebooks.some(
              (p) =>
                p.vendorId === item.vendorId &&
                p.items.some((i) => i.futureOrderItemId === item.id) &&
                !p.deleted
            )
          ) {
            const prebook = prebooks.find(
              (p) => p.vendorId === item.vendorId && p.deleted
            );
            if (prebook) {
              prebook.items.push(prebookItem);
            }
          }

          // if a prebook was created, but the vendor changed, remove it if this is the only item.
          if (
            previousVendorPrebook &&
            previousVendorPrebook.vendorId !== item.vendorId
          ) {
            previousVendorPrebook.items = previousVendorPrebook.items
              .filter((i) => i.futureOrderItemId !== item.id)
              .map((i) => ({ ...i }));

            if (!previousVendorPrebook.items.length) {
              const index = prebooks.findIndex(
                (p) => p.id === previousVendorPrebook.id
              );
              if (index !== -1) {
                prebooks.splice(index, 1);
              }
            }
          }

          item.createPrebook = true;

          state.prebooks = prebooks;
        }
      }

      state.items = items;
    },
    setItemCreatePrebook(
      state,
      { payload }: PayloadAction<SetItemArgs<boolean>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.createPrebook = value;

        if (item.spireInventoryId && item.spirePartNumber && item.description) {
          const prebooks = state.prebooks.map((p) => ({ ...p }));
          // create prebook
          if (value) {
            const prebookItemId =
                prebooks.reduce(
                  (min, p) =>
                    Math.min(
                      min,
                      p.items.reduce((m, i) => Math.min(m, i.id), 0)
                    ),
                  0
                ) - 1,
              prebookItem: prebooks.PrebookDetailItem = {
                id: prebookItemId,
                futureOrderItemId: item.id,
                spireInventoryId: item.spireInventoryId,
                spirePartNumber: item.spirePartNumber,
                description: item.description,
                orderQuantity: item.orderQuantity,
                hasPotCover: item.hasPotCover,
                potCover: item.potCover,
                dateCode: item.dateCode,
                upc: item.upc,
                weightsAndMeasures: item.weightsAndMeasures,
                retail: item.retail,
                comments: item.comments,
                isApproximate: item.isApproximate,
                blanketItemId: item.blanketItemId,
                blanketWeekId: null,
                upgradeSheet: item.upgradeSheet,
                boekestynPlantId: item.boekestynPlantId,
                boekestynCustomerAbbreviation:
                  item.boekestynCustomerAbbreviation,
                upgradeLabourHours: item.upgradeLabourHours,
                quantityPerFinishedItem: item.quantityPerFinishedItem,
                spirePurchaseOrderItemId: null,
                specialPrice: item.specialPrice,
                growerItemNotes: item.growerItemNotes,
                blanketOptions: [],
                upcPrinted: null,
                upcPrintedPrev: null,
                boekPriority: false,
                boekestynProducts: item.boekestynProducts.map((p) => ({
                  ...p,
                  prebookItemId,
                })),
              };
            // create a new Prebook
            if (
              item.vendorId &&
              !prebooks.some((p) => p.vendorId === item.vendorId && !p.deleted)
            ) {
              const prebookId =
                  prebooks.reduce((min, p) => Math.min(min, p.id), 0) - 1,
                prebook: prebooks.PrebookDetail = {
                  id: prebookId,
                  name: null,
                  requiredDate: state.requiredDate,
                  isBlanket: !!item.isBlanket,
                  blanketStartDate: null,
                  blanketIsClosed: false,
                  seasonName: state.seasonName,
                  boxCode: state.boxCode,
                  vendorId: item.vendorId,
                  vendorName: item.vendorName,
                  salespersonId: state.salesperson?.id || null,
                  salespersonName: state.salesperson?.name || null,
                  customerId: state.customer?.id || null,
                  customerName: state.customer?.name || null,
                  shipToId: state.shipTo?.id || null,
                  shipToName:
                    state.shipTo?.shipId || state.shipTo?.name || null,
                  comments: item.comments,
                  growerItemNotes: state.growerItemNotes,
                  spirePurchaseOrderId: null,
                  spirePurchaseOrderNumber: null,
                  futureOrderId: state.futureOrder?.id || null,
                  created: new Date().toISOString(),
                  createdBy: '',
                  modified: new Date().toISOString(),
                  modifiedBy: '',
                  confirmed: null,
                  confirmedBy: null,
                  deleted: null,
                  deletedBy: null,
                  items: [prebookItem],
                };
              prebooks.push(prebook);
              // add an item to existing Prebook
            } else if (
              item.vendorId &&
              !prebooks.some(
                (p) =>
                  p.vendorId === item.vendorId &&
                  p.items.some((i) => i.futureOrderItemId === item.id) &&
                  !p.deleted
              )
            ) {
              const prebook = prebooks.find(
                (p) => p.vendorId === item.vendorId && !p.deleted
              );
              if (prebook) {
                prebook.items.push(prebookItem);
              }
            }
            // don't create a prebook
          } else {
            const previousVendorPrebook = prebooks.find((p) =>
              p.items.some((i) => i.futureOrderItemId === item.id)
            );
            // if a prebook was created, remove it if this is the only item.
            if (previousVendorPrebook) {
              previousVendorPrebook.items = previousVendorPrebook.items
                .filter((i) => i.futureOrderItemId !== item.id)
                .map((i) => ({ ...i }));

              if (!previousVendorPrebook.items.length) {
                const index = prebooks.findIndex(
                  (p) => p.id === previousVendorPrebook.id
                );
                if (index !== -1) {
                  prebooks.splice(index, 1);
                }
              }
            }
          }

          state.prebooks = prebooks;
        }
      }

      state.items = items;
    },
    setItemBlanketItemId(
      state,
      { payload }: PayloadAction<SetItemArgs<number | null>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.blanketItemId = value;
        if (value && !item.vendorId) {
          const blanketItem = state.blanketItems.find(
            (i) => i.id === item.blanketItemId
          );
          if (blanketItem) {
            item.vendorId = blanketItem.vendorId;
            item.vendorName = blanketItem.vendorName;
          }
        }

        const prebooks = state.prebooks.map((p) => ({ ...p }));

        prebooks.forEach((p) => {
          p.items = p.items.map((i) => {
            const clone = { ...i };
            if (i.futureOrderItemId === item.id) {
              clone.blanketItemId = value;
            }

            return clone;
          });
        });

        state.prebooks = prebooks;
      }

      state.items = items;
    },
    setItemIsBlanket(state, { payload }: PayloadAction<SetItemArgs<boolean>>) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.isBlanket = value;
      }

      state.items = items;
    },
    setItemUnitPrice(
      state,
      { payload }: PayloadAction<SetItemArgs<number | null>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.unitPrice = value;
      }

      state.items = items;
    },
    setItemSpecialPrice(
      state,
      { payload }: PayloadAction<SetItemArgs<number | null>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.specialPrice = value;

        const prebooks = state.prebooks.map((p) => ({ ...p }));

        prebooks.forEach((p) => {
          p.items = p.items.map((i) => {
            const clone = { ...i };
            if (i.futureOrderItemId === item.id) {
              clone.specialPrice = value;
            }

            return clone;
          });
        });

        state.prebooks = prebooks;
      }

      state.items = items;
    },
    setItemGrowerItemNotes(
      state,
      { payload }: PayloadAction<SetItemArgs<string | null>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.growerItemNotes = value;

        const prebooks = state.prebooks.map((p) => ({ ...p }));

        prebooks.forEach((p) => {
          p.items = p.items.map((i) => {
            const clone = { ...i };
            if (i.futureOrderItemId === item.id) {
              clone.growerItemNotes = value;
            }

            return clone;
          });
        });

        state.prebooks = prebooks;
      }

      state.items = items;
    },
    setItemUseAvailabilityPricing(
      state,
      { payload }: PayloadAction<SetItemArgs<boolean>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.useAvailabilityPricing = value;

        if (value) {
          item.unitPrice = null;
        }
      }

      state.items = items;
    },
    setItemCustomerItemCode(
      state,
      { payload }: PayloadAction<SetItemArgs<string | null>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.customerItemCode = value;
      }

      state.items = items;
    },
    setItemUpgradeSheet(
      state,
      { payload }: PayloadAction<SetItemArgs<boolean>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.upgradeSheet = value;

        const prebooks = state.prebooks.map((p) => ({ ...p }));

        prebooks.forEach((p) => {
          p.items = p.items.map((i) => {
            const clone = { ...i };
            if (i.futureOrderItemId === item.id) {
              clone.upgradeSheet = value;
            }

            return clone;
          });
        });

        state.prebooks = prebooks;
      }

      state.items = items;
    },
    setItemPhytoRequired(
      state,
      { payload }: PayloadAction<SetItemArgs<boolean>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.phytoRequired = value;
      }

      state.items = items;
    },
    setItemPhytoOrdered(
      state,
      { payload }: PayloadAction<SetItemArgs<boolean>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.phytoOrdered = value;
      }

      state.items = items;
    },
    setItemBoekestynProductionOrder(
      state,
      {
        payload,
      }: PayloadAction<
        SetItemArgs<{
          boekestynPlantId: string | null;
          boekestynCustomerAbbreviation: string | null;
        }>
      >
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId),
        prebooks = state.prebooks.map((p) => ({ ...p }));

      if (item) {
        item.boekestynPlantId = value.boekestynPlantId;
        item.boekestynCustomerAbbreviation =
          value.boekestynCustomerAbbreviation;

        prebooks.forEach((p) => {
          p.items = p.items.map((i) => {
            const clone = { ...i };
            if (i.futureOrderItemId === item.id) {
              clone.boekestynPlantId = value.boekestynPlantId;
              clone.boekestynCustomerAbbreviation =
                value.boekestynCustomerAbbreviation;
            }

            return clone;
          });
        });

        state.prebooks = prebooks;
      }

      state.items = items;
    },
    setItemUpgradeLabourHours(
      state,
      { payload }: PayloadAction<SetItemArgs<number>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.upgradeLabourHours = value;

        const prebooks = state.prebooks.map((p) => ({ ...p }));

        prebooks.forEach((p) => {
          p.items = p.items.map((i) => {
            const clone = { ...i };
            if (i.futureOrderItemId === item.id) {
              clone.upgradeLabourHours = value;
            }

            return clone;
          });
        });

        state.prebooks = prebooks;
      }

      state.items = items;
    },
    setItemBoekestynProducts(
      state,
      {
        payload,
      }: PayloadAction<SetItemArgs<models.FutureOrderBoekestynProduct[]>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.boekestynProducts = value;

        const prebooks = state.prebooks.map((p) => ({ ...p }));

        prebooks.forEach((p) => {
          p.items = p.items.map((i) => {
            const clone = { ...i };
            if (i.futureOrderItemId === item.id) {
              clone.boekestynProducts = value.map((p) => ({
                ...p,
                prebookItemId: i.id,
              }));
            }

            return clone;
          });
        });

        state.prebooks = prebooks;
      }

      state.items = items;
    },
    setItemQuantityPerFinishedItem(
      state,
      { payload }: PayloadAction<SetItemArgs<number | null>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.quantityPerFinishedItem = value;

        const prebooks = state.prebooks.map((p) => ({ ...p }));

        prebooks.forEach((p) => {
          p.items = p.items.map((i) => {
            const clone = { ...i };
            if (i.futureOrderItemId === item.id) {
              clone.quantityPerFinishedItem = value;
            }

            return clone;
          });
        });

        state.prebooks = prebooks;
      }

      state.items = items;
    },
    setItemAvailabilityPrice(
      state,
      { payload }: PayloadAction<SetItemArgs<number | null | undefined>>
    ) {
      const availabilityUnitPrices = { ...state.availabilityUnitPrices };

      if (payload.value) {
        availabilityUnitPrices[payload.itemId] = payload.value;
      } else {
        delete availabilityUnitPrices[payload.itemId];
      }

      state.availabilityUnitPrices = availabilityUnitPrices;
    },
    setPrebookItemOrderQuantity(
      state,
      { payload }: PayloadAction<SetItemArgs<number>>
    ) {
      const prebooks = state.prebooks;

      prebooks.forEach((p) => {
        p.items
          .filter((i) => i.id === payload.itemId)
          .forEach((i) => {
            i.orderQuantity = payload.value;
          });
      });

      state.prebooks = prebooks;
    },
    setAllWeightsAndMeasures(state, { payload }: PayloadAction<boolean>) {
      const items = state.items.map((i) => ({
          ...i,
        })),
        prebooks = state.prebooks.map((p) => ({ ...p })),
        prebookItems = prebooks.flatMap((p) => p.items);

      items
        .filter(
          (i) =>
            !prebookItems.some(
              (p) => p.futureOrderItemId === i.id && p.upcPrinted
            )
        )
        .forEach((i) => {
          i.weightsAndMeasures = payload;
        });

      prebooks.forEach((p) =>
        p.items
          .filter((i) => i.futureOrderItemId && !i.upcPrinted)
          .forEach((i) => (i.weightsAndMeasures = payload))
      );

      state.items = items;
      state.prebooks = prebooks;
    },
    setAllUseAvailabilityPricing(state, { payload }: PayloadAction<boolean>) {
      const items = state.items.map((i) => ({
        ...i,
        useAvailabilityPricing: payload,
        unitPrice: payload ? null : i.unitPrice,
      }));

      state.items = items;
    },
    setAllDateCodes(state, { payload }: PayloadAction<string | null>) {
      const items = state.items.map((i) => ({
          ...i,
        })),
        prebooks = state.prebooks.map((p) => ({ ...p })),
        prebookItems = prebooks.flatMap((p) => p.items);

      items
        .filter(
          (i) =>
            !prebookItems.some(
              (p) => p.futureOrderItemId === i.id && p.upcPrinted
            )
        )
        .forEach((i) => {
          i.dateCode = payload;
        });

      prebooks.forEach((p) =>
        p.items
          .filter((i) => i.futureOrderItemId && !i.upcPrinted)
          .forEach((i) => (i.dateCode = payload))
      );

      state.items = items;
      state.prebooks = prebooks;
    },
    setAllHasPotCovers(state, { payload }: PayloadAction<boolean>) {
      const items = state.items.map((i) => ({ ...i })),
        prebooks = state.prebooks.map((p) => ({ ...p })),
        prebookItems = prebooks.flatMap((p) => p.items);

      items
        .filter(
          (i) =>
            !noPotCover(i.spirePartNumber, i.description) &&
            !prebookItems.some(
              (p) => p.futureOrderItemId === i.id && p.upcPrinted
            )
        )
        .forEach((i) => {
          i.hasPotCover = payload;
          i.potCover = payload ? i.potCover : null;
        });

      prebooks.forEach((p) =>
        p.items
          .filter(
            (i) =>
              i.futureOrderItemId &&
              !noPotCover(i.spirePartNumber, i.description) &&
              !i.upcPrinted
          )
          .forEach((i) => {
            i.hasPotCover = payload;
            i.potCover = payload ? i.potCover : null;
          })
      );

      state.items = items;
    },
    setAllPotCovers(state, { payload }: PayloadAction<string | null>) {
      const items = state.items.map((i) => ({ ...i })),
        prebooks = state.prebooks.map((p) => ({ ...p })),
        prebookItems = prebooks.flatMap((p) => p.items);

      items
        .filter(
          (i) =>
            !noPotCover(i.spirePartNumber, i.description) &&
            !prebookItems.some(
              (p) => p.futureOrderItemId === i.id && p.upcPrinted
            )
        )
        .forEach((i) => (i.potCover = payload));

      prebooks.forEach((p) =>
        p.items
          .filter(
            (i) =>
              i.futureOrderItemId &&
              !noPotCover(i.spirePartNumber, i.description) &&
              !i.upcPrinted
          )
          .forEach((i) => (i.potCover = payload))
      );

      state.items = items;
      state.prebooks = prebooks;
    },
    setAllRetails(state, { payload }: PayloadAction<string | null>) {
      const items = state.items.map((i) => ({ ...i, retail: payload })),
        prebooks = state.prebooks.map((p) => ({ ...p }));

      prebooks.forEach((p) =>
        p.items
          .filter((i) => i.futureOrderItemId)
          .forEach((i) => (i.retail = payload))
      );

      state.items = items;
      state.prebooks = prebooks;
    },
    setAllUpcs(state, { payload }: PayloadAction<string | null>) {
      const items = state.items.map((i) => ({ ...i })),
        prebooks = state.prebooks.map((p) => ({ ...p })),
        prebookItems = prebooks.flatMap((p) => p.items);

      items
        .filter(
          (i) =>
            !prebookItems.some(
              (p) => p.futureOrderItemId === i.id && p.upcPrinted
            )
        )
        .forEach((i) => {
          i.upc = payload;
        });

      prebooks.forEach((p) =>
        p.items
          .filter((i) => i.futureOrderItemId && !i.upcPrinted)
          .forEach((i) => (i.upc = payload))
      );

      state.items = items;
      state.prebooks = prebooks;
    },
    setAllUnitPrices(state, { payload }: PayloadAction<number | null>) {
      const items = state.items.map((i) => ({ ...i, unitPrice: payload }));
      state.items = items;
    },
    setItemExpanded(state, { payload }: PayloadAction<SetItemArgs<boolean>>) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.expanded = value;
      }

      state.items = items;
    },
    setAllExpanded(state, { payload }: PayloadAction<boolean>) {
      const items = state.items.map((i) => ({
        ...i,
        expanded: payload,
      }));

      state.items = items;
    },
    moveItem(state, { payload }: PayloadAction<MoveItemArgs>) {
      const { movingItem, existingItem } = payload,
        copy = state.items
          .filter((i) => i.id !== movingItem.id)
          .map((i) => ({ ...i })),
        index = copy.findIndex((i) => i.id === existingItem.id);

      if (movingItem) {
        copy.splice(index, 0, { ...movingItem });

        copy.forEach((i, index) => (i.sortOrder = index + 1));

        state.items = copy;
      }
    },
    clearItemUpcCompleted(state, { payload }: PayloadAction<number>) {
      const prebooks = state.prebooks.map((p) => ({ ...p }));

      prebooks.forEach((p) => {
        const items = p.items.map((i) => ({ ...i })),
          itemPrebookItems = items.filter(
            (i) => i.futureOrderItemId === payload
          );

        itemPrebookItems.forEach((i) => {
          i.upcPrinted = null;
        });

        p.items = items;
      });

      state.prebooks = prebooks;
    },
    clearAllItemsUpcCompleted(state) {
      const prebooks = state.prebooks.map((p) => ({ ...p }));

      prebooks.forEach((p) => {
        const items = p.items.map((i) => ({ ...i, upcPrinted: null }));
        p.items = items;
      });

      state.prebooks = prebooks;
    },
    setIsParentOrder(state, { payload }: PayloadAction<boolean>) {
      if (state.futureOrder) {
        state.futureOrder = { ...state.futureOrder, isParentOrder: payload };
      }
    },
  },
  extraReducers: (builder) =>
    builder
      .addCase(getCustomerDetailFulfilled, (state, { payload }) => {
        if (payload) {
          const { customer: detail, customerItemCodes, potCovers } = payload,
            customer = {
              id: detail.id,
              customerNo: detail.customerNo,
              name: detail.name,
              defaultShipTo: detail.defaultShipTo,
            },
            shipTo =
              detail.shippingAddresses.find((a) => {
                if (state.shipTo?.id) {
                  return a.id === state.shipTo.id;
                } else {
                  return a.shipId === detail.defaultShipTo;
                }
              }) || null;

          state.customerDetail = detail;
          state.customer = customer;
          state.shipTo = shipTo;
          state.customerItemCodeDefaults = customerItemCodes;
          if (shipTo) {
            state.salesperson =
              state.salespeople.find(
                (s) => s.code === shipTo.salesperson?.code
              ) || null;
            state.boxCode = shipTo.boxCode || null;
            state.freightPerCase = shipTo.defaultFreightPerCase || null;
            state.requiresLabels = equals(shipTo.labels, models.SpecialLabels);
            // freight per load gets set in a useEffect() block in the component
          }
          state.customerPotCovers = potCovers;
        } else {
          state.customerDetail = null;
          state.customer = null;
          state.shipTo = null;
          state.customerItemCodeDefaults = [];
          state.salesperson = null;
          state.boxCode = null;
          state.freightPerCase = null;
          state.customerPotCovers = [];
        }
      })
      .addCase(getCustomerDetailRejected, (state, { payload }) => {
        state.error = payload;
      })
      .addCase(getFutureOrderDetailPending, (state) => {
        state.isLoading = true;
      })
      .addCase(getFutureOrderDetailFulfilled, handleFutureOrderDetailFulfilled)
      .addCase(getFutureOrderDetailRejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      .addCase(setGrowerConfirmedFulfilled, (state, { payload }) => {
        const { prebook } = payload,
          prebooks = state.prebooks.map((p) => ({ ...p })),
          index = prebooks.findIndex((p) => p.id === prebook.id),
          originalPrebooks = state.originalPrebooks.map((p) => ({ ...p })),
          emails = state.emails.map((e) => ({ ...e })),
          originalPrebookIndex = originalPrebooks.findIndex(
            (p) => p.id === prebook.id
          ),
          wasDirty = isDirty(prebooks[index], emails);

        if (index !== -1) {
          prebooks.splice(index, 1, prebook);
        }

        if (!wasDirty && originalPrebookIndex !== -1) {
          originalPrebooks.splice(originalPrebookIndex, 1, prebook);
        }

        state.prebooks = prebooks;
        state.originalPrebooks = originalPrebooks;
      })
      .addCase(setGrowerConfirmedRejected, (state, { payload }) => {
        state.error = payload;
      })
      .addCase(updatePrebookFulfilled, (state, { payload }) => {
        const { prebook } = payload,
          prebooks = state.prebooks.map((p) => ({ ...p })),
          index = prebooks.findIndex((p) => p.id === prebook.id),
          originalPrebooks = state.originalPrebooks.map((p) => ({ ...p })),
          originalPrebookIndex = originalPrebooks.findIndex(
            (p) => p.id === prebook.id
          );

        if (index !== -1) {
          prebooks.splice(index, 1, prebook);
        }

        if (originalPrebookIndex !== -1) {
          originalPrebooks.splice(originalPrebookIndex, 1, prebook);
        }

        state.prebooks = prebooks;
        state.originalPrebooks = originalPrebooks;
      })
      .addCase(updateFutureOrderFulfilled, (state, response) => {
        const { prebooks, emails } = state,
          updatedPrebooks = response.payload.prebooks,
          cleanIds = prebooks
            .filter((p) => !isDirty(p, emails))
            .map((p) => p.id);

        handleFutureOrderDetailFulfilled(state, response);

        // only keep clean prebooks
        state.originalPrebooks = updatedPrebooks.filter(
          (p) => cleanIds.indexOf(p.id) !== -1
        );
      })
      .addCase(updateFutureOrderRejected, (state, { payload }) => {
        state.error = payload;
      })
      .addCase(updatePrebookRejected, (state, { payload }) => {
        state.error = payload;
      })
      .addCase(createPrebookEmailFulfilled, (state, { payload }) => {
        state.emails.push(payload.email);
        const originalPrebooks = state.originalPrebooks.map((p) => ({ ...p })),
          prebook = state.prebooks
            .map((p) => ({ ...p }))
            .find((p) => p.id === payload.email.prebookId),
          originalIndex = state.originalPrebooks.findIndex(
            (p) => p.id === payload.email.prebookId
          );

        if (prebook) {
          if (originalIndex === -1) {
            originalPrebooks.push(prebook);
          } else {
            originalPrebooks.splice(originalIndex, 1, prebook);
          }

          state.originalPrebooks = originalPrebooks;
        }
      })
      .addCase(createPrebookEmailRejected, (state, { payload }) => {
        state.error = payload;
      })
      .addCase(sendToSpireRejected, (state, { payload }) => {
        state.error = payload;
      })
      .addMatcher(
        prebookListApi.endpoints.openBlanketItems.matchFulfilled,
        (state, { payload }) => {
          state.blanketItems = payload.blanketItems;
        }
      )
      .addMatcher(
        spireApi.endpoints.inventoryItems.matchFulfilled,
        (state, { payload }) => {
          state.productDefaults = payload.productDefaults;
        }
      )
      .addMatcher(
        spireApi.endpoints.salespeople.matchFulfilled,
        (state, { payload }) => {
          state.salespeople = payload;
        }
      )
      .addMatcher(
        spireApi.endpoints.vendors.matchFulfilled,
        (state, { payload }) => {
          state.vendors = payload;
        }
      )
      .addMatcher(
        seasonsApi.endpoints.seasons.matchFulfilled,
        (state, { payload }) => {
          state.seasons = payload;
        }
      )
      .addMatcher(
        settingsApi.endpoints.defaultVendorOverrides.matchFulfilled,
        (state, { payload }) => {
          state.defaultVendorOverrides = payload;
        }
      )
      .addMatcher(
        futureOrdersListApi.endpoints.holidays.matchFulfilled,
        (state, { payload }) => {
          state.holidays = payload;
        }
      ),
});

export const {
  clearState,
  clearError,
  setError,
  setCustomerDetail,
  setShipTo,
  setSalesperson,
  setRequiredDate,
  setArrivalDate,
  setSeasonName,
  setShipVia,
  setBoxCode,
  setRequiresLabels,
  setCustomerPurchaseOrderNumber,
  setFreightPerCase,
  setFreightPerLoad,
  setFreightIsActual,
  setComments,
  setInternalComments,
  removeInternalComment,
  editInternalComment,
  setGrowerItemNotes,
  setSpireNotes,
  setCustomerInfo,
  addItem,
  removeItem,
  duplicateItem,
  setShowInventoryDialog,
  setShowAddFromOrder,
  setShowSplitOrder,
  setItemInventoryItem,
  setItemHasPotCover,
  setItemPotCover,
  setItemDateCode,
  setItemUPC,
  setItemWeightsAndMeasures,
  setItemRetail,
  setItemComments,
  setItemOrderQuantity,
  setItemIsApproximate,
  setItemVendor,
  setItemCreatePrebook,
  setItemBlanketItemId,
  setItemIsBlanket,
  setItemUnitPrice,
  setItemSpecialPrice,
  setItemGrowerItemNotes,
  setItemUseAvailabilityPricing,
  setItemCustomerItemCode,
  setItemUpgradeSheet,
  setItemPhytoRequired,
  setItemPhytoOrdered,
  setItemBoekestynProductionOrder,
  setItemUpgradeLabourHours,
  setItemBoekestynProducts,
  setItemQuantityPerFinishedItem,
  setItemAvailabilityPrice,
  setPrebookItemOrderQuantity,
  setAllWeightsAndMeasures,
  setAllUseAvailabilityPricing,
  setAllDateCodes,
  setAllHasPotCovers,
  setAllPotCovers,
  setAllRetails,
  setAllUpcs,
  setAllUnitPrices,
  setItemExpanded,
  setAllExpanded,
  moveItem,
  setIsParentOrder,
} = futureOrderDetailSlice.actions;

export const selectError = (state: RootState) => state.futureOrderDetail.error;
export const selectFutureOrder = (state: RootState) =>
  state.futureOrderDetail.futureOrder;
export const selectPrebooks = (state: RootState) =>
  state.futureOrderDetail.prebooks;
export const selectEmails = (state: RootState) =>
  state.futureOrderDetail.emails.map((e) => ({ ...e })).sort(sortByCreated);
export const selectCustomer = (state: RootState) =>
  state.futureOrderDetail.customer;
export const selectSalesperson = (state: RootState) =>
  state.futureOrderDetail.salesperson;
export const selectShipTo = (state: RootState) =>
  state.futureOrderDetail.shipTo;
export const selectShipVia = (state: RootState) =>
  state.futureOrderDetail.shipVia;
export const selectRequiredDate = (state: RootState) =>
  state.futureOrderDetail.requiredDate;
export const selectArrivalDate = (state: RootState) =>
  state.futureOrderDetail.arrivalDate;
export const selectSeasonName = (state: RootState) =>
  state.futureOrderDetail.seasonName;
export const selectBoxCode = (state: RootState) =>
  state.futureOrderDetail.boxCode;
export const selectRequiresLabels = (state: RootState) =>
  state.futureOrderDetail.requiresLabels;
export const selectCustomerPurchaseOrderNumber = (state: RootState) =>
  state.futureOrderDetail.customerPurchaseOrderNumber;
export const selectFreightPerCase = (state: RootState) =>
  state.futureOrderDetail.freightPerCase;
export const selectFreightPerLoad = (state: RootState) =>
  state.futureOrderDetail.freightPerLoad;
export const selectFreightIsActual = (state: RootState) =>
  state.futureOrderDetail.freightIsActual;
export const selectGrowerItemNotes = (state: RootState) =>
  state.futureOrderDetail.growerItemNotes;
export const selectSpireNotes = (state: RootState) =>
  state.futureOrderDetail.spireNotes;
export const selectComments = (state: RootState) =>
  state.futureOrderDetail.comments;
export const selectInternalComments = (state: RootState) =>
  state.futureOrderDetail.internalComments;
export const selectCustomerInfo = (state: RootState) =>
  state.futureOrderDetail.customerInfo;
export const selectItems = (state: RootState) => state.futureOrderDetail.items;
export const selectShowInventoryDialog = (state: RootState) =>
  state.futureOrderDetail.showInventoryDialog;
export const selectShowAddFromOrder = (state: RootState) =>
  state.futureOrderDetail.showAddFromOrder;
export const selectShowSplitOrder = (state: RootState) =>
  state.futureOrderDetail.showSplitOrder;
export const selectIsLoading = (state: RootState) =>
  state.futureOrderDetail.isLoading;
export const selectCustomerDetail = (state: RootState) =>
  state.futureOrderDetail.customerDetail;
export const selectCustomerItemCodeDefaults = (state: RootState) =>
  state.futureOrderDetail.customerItemCodeDefaults;
export const selectSeasons = (state: RootState) =>
  state.futureOrderDetail.seasons;
export const selectAvailabilityUnitPrices = (state: RootState) =>
  state.futureOrderDetail.availabilityUnitPrices;
export const selectDefaultVendorOverrides = (state: RootState) =>
  state.futureOrderDetail.defaultVendorOverrides;
export const selectShipTos = createSelector(
  selectCustomerDetail,
  (customerDetail) => customerDetail?.shippingAddresses || []
);
export const selectPotCovers = (state: RootState) => {
  const customerPotCovers = state.futureOrderDetail.customerPotCovers,
    potCovers = defaultPotCovers.concat(customerPotCovers);
  return potCovers;
};
export const selectDirtyPrebooks = createSelector(
  selectPrebooks,
  selectEmails,
  (state: RootState) => state.futureOrderDetail.originalPrebooks,
  (prebooks, emails) => {
    const dirty = prebooks.filter((p) => isDirty(p, emails)),
      unsent = prebooks.filter(
        (p) => !emails.some((e) => e.prebookId === p.id)
      );

    return { dirty, unsent };
  }
);

export const selectHoliday = createSelector(
  (state: RootState) => state.futureOrderDetail.holidays,
  selectRequiredDate,
  (holidays, requiredDate) =>
    (!!requiredDate &&
      holidays.find(
        (h) => h.date === requiredDate || h.observedDate === requiredDate
      )) ??
    null
);

function handleFutureOrderDetailFulfilled(
  state: FutureOrderDetailState,
  { payload }: PayloadAction<FutureOrderDetailResponse>
) {
  state.isLoading = false;
  state.futureOrder = payload.futureOrder;
  state.prebooks = payload.prebooks;
  state.originalPrebooks = payload.prebooks;
  state.emails = payload.emails;
  const {
    customerId,
    customerName,
    shipToId,
    shipToName,
    salespersonId,
    salespersonName,
    shipViaId,
    shipViaName,
    requiredDate,
    arrivalDate,
    seasonName,
    boxCode,
    requiresLabels,
    customerPurchaseOrderNumber,
    freightPerCase,
    freightPerLoad,
    freightIsActual,
    spireNotes,
    growerItemNotes,
    items,
    comments,
  } = payload.futureOrder;

  const customer = customerId
      ? { id: customerId, name: customerName || '', defaultShipTo: null }
      : null,
    salesperson = salespersonId
      ? { id: salespersonId, name: salespersonName || '', code: '' }
      : null,
    shipTo = shipToId
      ? {
          id: shipToId,
          name: shipToName || '',
          shipId: shipToName || '',
          boxCode,
          labels: requiresLabels ? models.SpecialLabels : null,
          salesperson,
          customerInfo: null,
          priceLevel: null,
          defaultFreightPerCase: freightPerCase,
        }
      : null,
    shipVia = shipViaId
      ? { id: shipViaId, description: shipViaName || '', code: '' }
      : shipViaName
      ? {
          id: null,
          description: shipViaName,
          code: '',
        }
      : null;
  state.customer = customer;
  state.shipTo = shipTo;
  state.salesperson = salesperson;
  state.shipVia = shipVia;
  state.requiredDate = requiredDate
    ? DateTime.fromISO(requiredDate).toFormat('yyyy-MM-dd')
    : null;
  state.arrivalDate = arrivalDate
    ? DateTime.fromISO(arrivalDate).toFormat('yyyy-MM-dd')
    : null;
  state.seasonName = seasonName;
  state.boxCode = boxCode;
  state.requiresLabels = requiresLabels;
  state.customerPurchaseOrderNumber = customerPurchaseOrderNumber;
  state.freightPerCase = freightPerCase;
  state.freightPerLoad = freightPerLoad;
  state.freightIsActual = freightIsActual;
  state.spireNotes = spireNotes;
  state.growerItemNotes = growerItemNotes;
  state.items = items;
  state.comments = comments;
}

export default futureOrderDetailSlice.reducer;
