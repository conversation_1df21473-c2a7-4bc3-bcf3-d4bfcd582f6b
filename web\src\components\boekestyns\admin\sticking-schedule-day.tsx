import { useAppSelector } from '@/services/hooks';
import { selectLines } from './sticking-slice';
import { StickingSchedule } from './sticking-schedule';

interface StickingScheduleDayProps {
  date: string;
}

export function StickingScheduleDay({ date }: StickingScheduleDayProps) {
  const lines = useAppSelector(selectLines);

  return (
    <div className="mb-2 flex-grow overflow-y-auto px-2">
      {lines.map((line) => (
        <StickingSchedule key={line.id} line={line} date={date} />
      ))}
    </div>
  );
}
