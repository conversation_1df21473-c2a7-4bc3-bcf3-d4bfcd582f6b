import React, { Fragment, useState, useEffect } from 'react';
import * as HeadlessUI from '@headlessui/react';
import { settingsService } from 'api/settings-service';
import * as prebooks from 'api/models/prebooks';
import { Icon } from '@/components/icon';

interface SeasonDetailProps {
  season: prebooks.Season;
  close: (reload?: boolean) => void;
}

export function SeasonDetail({ season, close }: SeasonDetailProps) {
  const [name, setName] = useState(''),
    [seasonDate, setSeasonDate] = useState('');

  useEffect(() => {
    setName(season.name);
    setSeasonDate(season.seasonDate);
  }, [season]);

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setName(e.target.value);
  };

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSeasonDate(e.target.value);
  };

  const handleCancelClick = () => {
    close();
  };

  const handleSaveClick = async () => {
    try {
      await settingsService.updateSeason({ name, seasonDate });
      close(true);
    } catch (e) {
      console.error(e);
    }
  };

  const handleClose = () => {
    close();
  };

  return (
    <HeadlessUI.Transition.Root show={true} as={Fragment}>
      <HeadlessUI.Dialog
        as="div"
        className="relative z-30"
        onClose={handleClose}
      >
        <HeadlessUI.Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </HeadlessUI.Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex items-center justify-center p-0 text-center">
            <HeadlessUI.Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-0 scale-95"
              enterTo="opacity-100 translate-y-0 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 scale-100"
              leaveTo="opacity-0 translate-y-0 scale-95"
            >
              <HeadlessUI.Dialog.Panel className="relative h-screen w-screen transform overflow-hidden p-6 transition-all">
                <div className="mx-auto flex max-w-xl flex-col rounded-lg bg-white p-6 text-left shadow-xl">
                  <div className="mb-4 flex justify-center border-b-2 pb-4">
                    <HeadlessUI.Dialog.Title
                      as="h3"
                      className="text-lg font-medium leading-6 text-gray-900"
                    >
                      <Icon
                        icon="calendar-day"
                        className="h-6 w-6"
                        aria-hidden="true"
                      />
                      &nbsp; Edit Season
                    </HeadlessUI.Dialog.Title>
                  </div>
                  <div className="flex flex-grow flex-col">
                    <form className="flex w-full">
                      <div className="mx-auto grid max-w-2xl grid-cols-2 items-start gap-4">
                        <label
                          htmlFor="name"
                          className="mt-px block pt-2 text-right text-sm font-medium text-gray-700"
                        >
                          Season Name
                        </label>
                        <input
                          type="text"
                          name="name"
                          id="name"
                          tabIndex={0}
                          value={name}
                          onChange={handleNameChange}
                          className="block w-full min-w-0 flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        />

                        <label
                          htmlFor="date"
                          className="mt-px block pt-2 text-right text-sm font-medium text-gray-700"
                        >
                          Date
                        </label>
                        <input
                          type="date"
                          name="date"
                          id="date"
                          tabIndex={3}
                          value={seasonDate}
                          onChange={handleDateChange}
                          className="block w-full min-w-0 flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        />
                      </div>
                    </form>
                  </div>

                  <div className="mt-4 flex justify-end border-t-2 pt-4">
                    <button
                      type="button"
                      className="btn-secondary text-lg"
                      onClick={handleCancelClick}
                    >
                      Cancel
                    </button>
                    <button
                      type="button"
                      className="btn-primary ml-4 text-lg"
                      onClick={handleSaveClick}
                    >
                      Save&nbsp;
                      <Icon icon="save" className="ml-2" />
                    </button>
                  </div>
                </div>
              </HeadlessUI.Dialog.Panel>
            </HeadlessUI.Transition.Child>
          </div>
        </div>
      </HeadlessUI.Dialog>
    </HeadlessUI.Transition.Root>
  );
}
