import { useState, Fragment } from 'react';
import * as Headless<PERSON> from '@headlessui/react';
import * as boeks from 'api/models/boekestyns';

interface PauseLabourDialogProps {
  open: boolean;
  onClose: () => void;
  onStart: (args: { crewSize: number }) => void;
}

export function StartLabourDialog({
  open,
  onClose,
  onStart,
}: PauseLabourDialogProps) {
  const [crewSize, setCrewSize] = useState(3);

  const handleTransitionAfterEnter = () => {
    setCrewSize(3);
  };

  const handlePauseChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCrewSize(e.target.valueAsNumber);
  };

  const handlePauseClick = () => {
    onStart({ crewSize });
  };

  return (
    <HeadlessUI.Transition.Root
      show={open}
      as={Fragment}
      afterEnter={handleTransitionAfterEnter}
    >
      <HeadlessUI.Dialog as="div" className="relative z-10" onClose={onClose}>
        <HeadlessUI.Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </HeadlessUI.Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-0 text-center">
            <HeadlessUI.Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-0 scale-95"
              enterTo="opacity-100 translate-y-0 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 scale-100"
              leaveTo="opacity-0 translate-y-0 scale-95"
            >
              <HeadlessUI.Dialog.Panel className="relative my-8 w-full max-w-sm transform overflow-hidden rounded-lg bg-white p-6 text-left shadow-xl transition-all">
                <div>
                  <div className="mt-3 text-center">
                    <HeadlessUI.Dialog.Title
                      as="h3"
                      className="text-base font-semibold leading-6 text-gray-900"
                    >
                      Start Time
                    </HeadlessUI.Dialog.Title>
                    <form className="mt-5">
                      <div className="text-left">
                        <label>Crew Size</label>
                        <input
                          type="number"
                          name="comments"
                          className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                          value={crewSize}
                          onChange={handlePauseChange}
                        />
                      </div>
                    </form>
                  </div>
                </div>
                <div className="mt-6 text-right">
                  <button
                    type="button"
                    className="btn-secondary"
                    onClick={onClose}
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn-primary ml-2"
                    onClick={handlePauseClick}
                  >
                    Start
                  </button>
                </div>
              </HeadlessUI.Dialog.Panel>
            </HeadlessUI.Transition.Child>
          </div>
        </div>
      </HeadlessUI.Dialog>
    </HeadlessUI.Transition.Root>
  );
}
