import { AxiosError, AxiosResponseHeaders, ResponseType } from 'axios';
import { serializeError } from 'serialize-error';
import {
  createProblemDetails,
  isProblemDetails,
} from '@/utils/problem-details';
import axios, { ApiBase, getFilename, downloadFile } from './api-base';
import * as models from './models/labels';

const type = 'application/pdf',
  responseType: ResponseType = 'blob';

class LabelService extends ApiBase {
  async coborns(data: CobornsLabelModel): Promise<void> {
    try {
      const { headers, data: result } = await axios.post(
          'labels/coborns',
          data,
          {
            responseType,
          }
        ),
        blob = new Blob([result], { type }),
        filename =
          getFilename(headers as AxiosResponseHeaders) || 'CobornsLabels.pdf';

      downloadFile(blob, filename);
    } catch (ex) {
      if (ex instanceof Error) {
        const error = serializeError(ex);
        throw createProblemDetails(error.message || 'Error');
      }
      const e = ex as AxiosError;
      if (e.response) {
        if (isProblemDetails(e.response.data)) {
          throw e.response.data;
        } else if (typeof e.response.data === 'string') {
          throw createProblemDetails(e.response.data);
        }
      }

      if (e.response && e.response.data) {
        throw e.response.data;
      }

      throw e.message;
    }
  }

  async albrechts(data: AlbrechtsLabelModel): Promise<void> {
    try {
      const { headers, data: result } = await axios.post(
          'labels/albrechts',
          data,
          {
            responseType,
          }
        ),
        blob = new Blob([result], { type }),
        filename =
          getFilename(headers as AxiosResponseHeaders) || 'AlbrechtsLabels.pdf';

      downloadFile(blob, filename);
    } catch (ex) {
      if (ex instanceof Error) {
        const error = serializeError(ex);
        throw createProblemDetails(error.message || 'Error');
      }
      const e = ex as AxiosError;
      if (e.response) {
        if (isProblemDetails(e.response.data)) {
          throw e.response.data;
        } else if (typeof e.response.data === 'string') {
          throw createProblemDetails(e.response.data);
        }
      }

      if (e.response && e.response.data) {
        throw e.response.data;
      }

      throw e.message;
    }
  }

  async heinens(
    data: HeinensLabelModel,
    type: models.HeinensLabelType
  ): Promise<void> {
    try {
      const { headers, data: result } = await axios.post(
          `labels/heinens?type=${type}`,
          data,
          {
            responseType,
          }
        ),
        blob = new Blob([result], { type }),
        filename =
          getFilename(headers as AxiosResponseHeaders) || 'HeinensLabels.pdf';

      downloadFile(blob, filename);
    } catch (ex) {
      if (ex instanceof Error) {
        const error = serializeError(ex);
        throw createProblemDetails(error.message || 'Error');
      }
      const e = ex as AxiosError;
      if (e.response) {
        if (isProblemDetails(e.response.data)) {
          throw e.response.data;
        } else if (typeof e.response.data === 'string') {
          throw createProblemDetails(e.response.data);
        }
      }

      if (e.response && e.response.data) {
        throw e.response.data;
      }

      throw e.message;
    }
  }
}

export interface CobornsLabelModel {
  stores: models.CobornsLabelStore[];
}

export interface AlbrechtsLabelModel {
  stores: models.AlbrechtsLabelStore[];
}

export interface HeinensLabelModel {
  stores: models.HeinensLabelStore[];
}

export const labelApi = new LabelService();
