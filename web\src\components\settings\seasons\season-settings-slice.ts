import {
  createAction,
  createAsyncThunk,
  createSlice,
  AsyncThunk,
} from '@reduxjs/toolkit';
import { settingsService, SeasonListResponse } from 'api/settings-service';
import * as prebooks from 'api/models/prebooks';
import { RootState } from '@/services/store';
import { ProblemDetails } from '@/utils/problem-details';

export interface SeasonSettingsState {
  isLoading: boolean;
  error: ProblemDetails | null;
  seasons: prebooks.Season[];
}

const initialState: SeasonSettingsState = {
  isLoading: false,
  error: null,
  seasons: [],
};

export const getData: AsyncThunk<
  SeasonListResponse,
  void,
  { state: RootState }
> = createAsyncThunk(
  'season-settings-getData',
  async (_, { rejectWithValue }) => {
    try {
      return await settingsService.getAllSeasons();
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const saveSeason: AsyncThunk<
  unknown,
  prebooks.Season,
  { state: RootState }
> = createAsyncThunk(
  'user-settings-saveSeason',
  async (season, { rejectWithValue }) => {
    try {
      return await settingsService.updateSeason(season);
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

const getDataPending = createAction(getData.pending.type),
  getDataFulfilled = createAction<SeasonListResponse>(getData.fulfilled.type),
  getDataRejected = createAction<ProblemDetails>(getData.rejected.type),
  saveSeasonPending = createAction(saveSeason.pending.type),
  saveSeasonFulfilled = createAction(saveSeason.fulfilled.type),
  saveSeasonRejected = createAction<ProblemDetails>(saveSeason.rejected.type);

export const seasonSettingsSlice = createSlice({
  name: 'season-settings',
  initialState,
  reducers: {
    clearState(state) {
      Object.assign(state, { ...initialState });
    },
    clearError(state) {
      state.error = null;
    },
  },
  extraReducers: (builder) =>
    builder
      .addCase(getDataPending, (state) => {
        state.isLoading = true;
      })
      .addCase(getDataFulfilled, (state, { payload }) => {
        state.isLoading = false;
        state.seasons = payload.seasons;
      })
      .addCase(getDataRejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      .addCase(saveSeasonPending, (state) => {
        state.isLoading = true;
      })
      .addCase(saveSeasonFulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(saveSeasonRejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      }),
});

export const { clearError, clearState } = seasonSettingsSlice.actions;

export const selectIsLoading = (state: RootState) =>
  state.seasonSettings.isLoading;
export const selectError = (state: RootState) => state.seasonSettings.error;
export const selectSeasons = (state: RootState) => state.seasonSettings.seasons;

export default seasonSettingsSlice.reducer;
