import { Icon } from '@/components/icon';
import { useAppSelector, useAppDispatch } from '@/services/hooks';
import { createProblemDetails } from '@/utils/problem-details';
import { selectItems, setError, setStep } from './split-order-slice';
import { SplitOrderSelectItem } from './split-order-select-item';

interface SplitOrderSelectItemsProps {
  onClose: () => void;
}

export function SplitOrderSelectItems({ onClose }: SplitOrderSelectItemsProps) {
  const dispatch = useAppDispatch(),
    items = useAppSelector(selectItems),
    hasSelected = items.filter((i) => i.selected && i.splitQuantity).length > 0,
    isInvalid = items.some((i) => i.splitQuantity > i.orderQuantity);

  const handleNextClick = () => {
    if (!hasSelected) {
      return dispatch(
        setError(
          createProblemDetails('Please select at least one item to move.')
        )
      );
    }

    if (isInvalid) {
      return dispatch(
        setError(
          createProblemDetails(
            'Split quantity cannot be greater than order quantity.'
          )
        )
      );
    }

    dispatch(setStep(2));
  };

  const handleCancelClick = () => {
    onClose();
  };

  return (
    <div className="flex flex-grow flex-col overflow-y-auto">
      <h3 className="mt-4 text-2xl">Select Items to Move</h3>
      <p className="italic">
        NOTE: Items that have only part of their quantity moved will be split
        into two items on the Prebook.
      </p>
      <div className="mx-8 my-4 flex flex-grow flex-col overflow-y-auto rounded border">
        <table className="mx-4">
          <thead>
            <tr className="sticky top-0 z-10 border-b bg-white shadow">
              <th className="p-2">&nbsp;</th>
              <th className="whitespace-nowrap p-2">Product No</th>
              <th className="p-2">Description</th>
              <th className="whitespace-nowrap p-2">Order Qty</th>
              <th className="whitespace-nowrap p-2">Qty to Move</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {items.map((item) => (
              <SplitOrderSelectItem key={item.id} item={item} />
            ))}
          </tbody>
        </table>
      </div>
      <div className="mx-8 flex justify-end">
        <button
          type="button"
          className="btn-secondary px-8 text-lg"
          onClick={handleCancelClick}
        >
          Cancel
        </button>
        <button
          type="button"
          className="btn-primary ml-2 px-8 text-lg disabled:px-8 disabled:text-lg"
          onClick={handleNextClick}
          disabled={!hasSelected}
        >
          Next &nbsp;
          <Icon icon="chevron-right" />
        </button>
      </div>
    </div>
  );
}
