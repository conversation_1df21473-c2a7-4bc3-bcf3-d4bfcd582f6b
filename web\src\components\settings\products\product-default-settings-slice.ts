import {
  createAction,
  createAsyncThunk,
  createSlice,
  AsyncThunk,
  PayloadAction,
  createSelector,
} from '@reduxjs/toolkit';
import { boekestynApi } from 'api/boekestyn-service';
import { spireApi } from 'api/spire-service';
import {
  settingsService,
  UpdateProductDefaultModel,
} from 'api/settings-service';
import * as boeks from 'api/models/boekestyns';
import * as spire from 'api/models/spire';
import * as settings from 'api/models/settings';
import { RootState } from '@/services/store';
import { ProblemDetails } from '@/utils/problem-details';
import { contains } from '@/utils/equals';
import { formatNumber } from '@/utils/format';
import { sortBy } from '@/utils/sort';

const sortByName = sortBy('name'),
  sortByPartNo = sortBy('partNo');

export interface ProductSettingsState {
  search: string;
  isLoading: boolean;
  error: ProblemDetails | null;
  selectedProductDefault: settings.ProductDefault | null;
  productDefaults: settings.ProductDefault[];
  inventoryItems: spire.InventoryItem[];
  plants: boeks.Plant[];
  customers: boeks.Customer[];
}

const initialState: ProductSettingsState = {
  search: '',
  isLoading: false,
  error: null,
  selectedProductDefault: null,
  productDefaults: [],
  inventoryItems: [],
  plants: [],
  customers: [],
};

export const getPlants: AsyncThunk<boeks.Plant[], void, { state: RootState }> =
  createAsyncThunk(
    'product-settings-getPlants',
    async (_, { rejectWithValue }) => {
      try {
        return await boekestynApi.plants();
      } catch (e) {
        return rejectWithValue(e as ProblemDetails);
      }
    }
  );

export const getCustomers: AsyncThunk<
  boeks.Customer[],
  void,
  { state: RootState }
> = createAsyncThunk(
  'product-settings-getCustomers',
  async (_, { rejectWithValue }) => {
    try {
      return await boekestynApi.customers();
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const saveProductDefault: AsyncThunk<
  settings.ProductDefault,
  UpdateProductDefaultModel,
  { state: RootState }
> = createAsyncThunk(
  'product-settings-saveProductDefault',
  async (arg, { rejectWithValue }) => {
    try {
      const {
        spireInventoryId,
        boekestynPlantId,
        boekestynCustomerAbbreviation,
        upgradeLabourHours,
        quantityPerFinishedItem,
        isUpgrade,
        ignoreOverrideQuantity,
        products,
        overrides,
      } = arg;
      await settingsService.updateProductDefault(arg);
      return {
        spireInventoryId,
        boekestynPlantId,
        boekestynCustomerAbbreviation,
        upgradeLabourHours,
        quantityPerFinishedItem,
        isUpgrade,
        ignoreOverrideQuantity,
        products,
        overrides,
      };
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

const getPlantsPending = createAction(getPlants.pending.type),
  getPlantsFulfilled = createAction<boeks.Plant[]>(getPlants.fulfilled.type),
  getPlantsRejected = createAction<ProblemDetails>(getPlants.rejected.type),
  getCustomersPending = createAction(getCustomers.pending.type),
  getCustomersFulfilled = createAction<boeks.Customer[]>(
    getCustomers.fulfilled.type
  ),
  getCustomersRejected = createAction<ProblemDetails>(
    getCustomers.rejected.type
  ),
  saveProductDefaultPending = createAction(saveProductDefault.pending.type),
  saveProductDefaultFulfilled = createAction<settings.ProductDefault>(
    saveProductDefault.fulfilled.type
  ),
  saveProductDefaultRejected = createAction<ProblemDetails>(
    saveProductDefault.rejected.type
  );

export const productDefaultSettingsSlice = createSlice({
  name: 'product-default-settings',
  initialState,
  reducers: {
    clearState(state) {
      Object.assign(state, { ...initialState });
    },
    clearError(state) {
      state.error = null;
    },
    setSelectedProductDefault(
      state,
      { payload }: PayloadAction<number | null>
    ) {
      const productDefault =
        state.productDefaults.find((p) => p.spireInventoryId === payload) ||
        null;
      state.selectedProductDefault = productDefault;
    },
    setSearch(state, { payload }: PayloadAction<string>) {
      state.search = payload;
    },
  },
  extraReducers: (builder) =>
    builder
      .addCase(getPlantsPending, (state) => {
        state.isLoading = true;
      })
      .addCase(getPlantsFulfilled, (state, { payload }) => {
        if (state.productDefaults.length && state.customers.length) {
          state.isLoading = false;
        }

        state.plants = payload;
      })
      .addCase(getPlantsRejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      .addCase(getCustomersPending, (state) => {
        state.isLoading = true;
      })
      .addCase(getCustomersFulfilled, (state, { payload }) => {
        if (state.productDefaults.length && state.plants.length) {
          state.isLoading = false;
        }

        state.customers = payload;
      })
      .addCase(getCustomersRejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      .addCase(saveProductDefaultPending, (state) => {
        state.isLoading = true;
      })
      .addCase(saveProductDefaultFulfilled, (state, { payload }) => {
        state.isLoading = false;
        const productDefaults = state.productDefaults.map((d) => ({ ...d })),
          index = productDefaults.findIndex(
            (d) => d.spireInventoryId === payload.spireInventoryId
          );

        if (index !== -1) {
          const productDefault = productDefaults[index];
          productDefault.boekestynPlantId = payload.boekestynPlantId;
          productDefault.boekestynCustomerAbbreviation =
            payload.boekestynCustomerAbbreviation;
          productDefault.upgradeLabourHours = payload.upgradeLabourHours;
          productDefault.quantityPerFinishedItem =
            payload.quantityPerFinishedItem;
        }

        state.productDefaults = productDefaults;
        state.selectedProductDefault = null;
      })
      .addCase(saveProductDefaultRejected, (state, { payload }) => {
        state.isLoading = false;
      })
      .addMatcher(spireApi.endpoints.inventoryItems.matchPending, (state) => {
        state.isLoading = true;
      })
      .addMatcher(
        spireApi.endpoints.inventoryItems.matchFulfilled,
        (state, { payload }) => {
          state.inventoryItems = payload.inventoryItems;
          state.productDefaults = payload.productDefaults;
          if (state.plants.length) {
            state.isLoading = false;
          }
        }
      )
      .addMatcher(
        spireApi.endpoints.inventoryItems.matchRejected,
        (state, { payload }) => {
          if (payload) {
            state.error = payload;
          }
        }
      ),
});

export interface ProductDefaultItem {
  id: number;
  partNo: string;
  description: string;
  plantName: string | null;
  customerAbbreviation: string | null;
  labourHours: string;
  quantityPerFinishedItem: string;
  isUpgrade: boolean;
}

export const { clearError, clearState, setSelectedProductDefault, setSearch } =
  productDefaultSettingsSlice.actions;

export const selectSearch = ({ productDefaultSettings }: RootState) =>
  productDefaultSettings.search;
export const selectIsLoading = ({ productDefaultSettings }: RootState) =>
  productDefaultSettings.isLoading;
export const selectError = ({ productDefaultSettings }: RootState) =>
  productDefaultSettings.error;
export const selectSelectedProductDefault = ({
  productDefaultSettings,
}: RootState) => productDefaultSettings.selectedProductDefault;

const selectAllProductDefaults = ({ productDefaultSettings }: RootState) =>
  productDefaultSettings.productDefaults;
export const selectPlants = ({ productDefaultSettings }: RootState) =>
  productDefaultSettings.plants.map((p) => ({ ...p })).sort(sortByName);
export const selectCustomers = ({ productDefaultSettings }: RootState) =>
  productDefaultSettings.customers.map((c) => ({ ...c })).sort(sortByName);
export const selectInventoryItems = ({ productDefaultSettings }: RootState) =>
  productDefaultSettings.inventoryItems
    .map((i) => ({ ...i } as spire.InventoryItem))
    .sort(sortByPartNo);

export const selectProductDefaults = createSelector(
  selectAllProductDefaults,
  selectSearch,
  selectPlants,
  selectCustomers,
  selectInventoryItems,
  (productDefaults, search, plants, customers, inventoryItems) =>
    productDefaults
      .reduce((memo, productDefault) => {
        const plantName = productDefault.products.length
            ? productDefault.products
                .map(
                  (p) =>
                    plants.find((p2) => p2._id === p.boekestynPlantId)?.name ||
                    null
                )
                .join(', ')
            : plants.find((p) => p._id === productDefault.boekestynPlantId)
                ?.name || null,
          customerAbbreviation = productDefault.products.length
            ? '-'
            : customers.find(
                (c) =>
                  c.abbreviation ===
                  productDefault.boekestynCustomerAbbreviation
              )?.abbreviation || null,
          inventoryItem = inventoryItems.find(
            (i) => i.id === productDefault.spireInventoryId
          ),
          labourHours = productDefault.upgradeLabourHours
            ? formatNumber(productDefault.upgradeLabourHours, '0,0.[00]')
            : '-',
          quantityPerFinishedItem = productDefault.products.length
            ? '-'
            : formatNumber(productDefault.quantityPerFinishedItem || 1),
          isUpgrade = productDefault.isUpgrade;

        if (inventoryItem) {
          const { id, partNo, description } = inventoryItem;
          memo.push({
            id,
            partNo,
            description,
            plantName,
            customerAbbreviation,
            labourHours,
            quantityPerFinishedItem,
            isUpgrade,
          });
        }

        return memo;
      }, [] as ProductDefaultItem[])
      .filter(
        (pd) =>
          !search ||
          contains(pd.partNo, search) ||
          contains(pd.description, search)
      )
      .sort(sortByPartNo)
);

export default productDefaultSettingsSlice.reducer;
