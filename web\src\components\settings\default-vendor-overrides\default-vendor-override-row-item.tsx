import { useAppSelector } from '@/services/hooks';
import { selectVendors } from './default-vendor-overrides-slice';
import * as settings from 'api/models/settings';

interface DefaultVendorOverrideRowItemProps {
  item: settings.DefaultVendorOverrideItem;
}

export function DefaultVendorOverrideRowItem({
  item,
}: DefaultVendorOverrideRowItemProps) {
  const vendors = useAppSelector(selectVendors),
    vendor = vendors.find((v) => v.id === item.vendorId),
    vendorName = vendor?.name ?? item.vendorId.toString();

  return (
    <div>
      Week {item.startWeek} - Week {item.endWeek}: {vendorName}
    </div>
  );
}
