import * as HeadlessUI from '@headlessui/react';
import * as prebooks from 'api/models/prebooks';
import { classNames } from '@/utils/class-names';
import { formatNumber, formatDate } from '@/utils/format';
import { weekFromId } from '@/utils/weeks';

interface BlankSlateBlanketItemsProps {
  blanketItem: prebooks.PrebookBlanketItem;
}

export function BlankSlateBlanketItem({
  blanketItem,
}: BlankSlateBlanketItemsProps) {
  const week = weekFromId(blanketItem.blanketWeekId);

  return (
    <HeadlessUI.Combobox.Option
      key={blanketItem.id}
      value={blanketItem}
      className={({ active }) =>
        classNames(
          'grid cursor-default select-none grid-cols-4 items-center border-b-2 px-12 py-2 ',
          active ? 'bg-blue-600 text-white' : 'bg-white text-gray-400'
        )
      }
    >
      {({ active }) => (
        <>
          <div
            className={classNames(
              'col-span-4 truncate text-center text-lg',
              active ? 'text-white' : 'text-gray-900'
            )}
          >
            {blanketItem.spirePartNumber}
          </div>
          <div
            className={classNames(
              'col-span-3 truncate text-sm font-medium',
              active ? 'text-white' : 'text-gray-900'
            )}
          >
            {blanketItem.description}
          </div>
          {!!blanketItem.bookedQuantity && (
            <div className="text-right">
              {formatNumber(blanketItem.blanketQuantity)}
            </div>
          )}
          <div
            className={classNames(
              'col-span-3 col-start-1 truncate text-sm',
              active ? 'text-white' : 'text-gray-900'
            )}
          >
            {blanketItem.vendorName}
          </div>
          {!!blanketItem.bookedQuantity && (
            <div className="text-right">
              {`- ${formatNumber(blanketItem.bookedQuantity)}`}
            </div>
          )}
          <div
            className={classNames(
              'col-span-3 col-start-1 truncate text-xs italic',
              active ? 'text-white' : 'text-gray-500'
            )}
          >
            {!!blanketItem.blanketStartDate &&
              `${formatDate(blanketItem.blanketStartDate, 'MMM d, yyyy')} - `}
            {formatDate(blanketItem.requiredDate, 'MMM d, yyyy')}
            {!!week && <div>Week {week.week}</div>}
          </div>
          <div
            className={classNames(
              'text-md text-right font-bold text-green-600'
            )}
          >
            {!!blanketItem.bookedQuantity && '= '}
            {formatNumber(
              blanketItem.blanketQuantity - blanketItem.bookedQuantity
            )}
          </div>
        </>
      )}
    </HeadlessUI.Combobox.Option>
  );
}
