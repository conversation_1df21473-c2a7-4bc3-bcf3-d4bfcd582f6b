import { createSlice, createSelector, PayloadAction } from '@reduxjs/toolkit';
import moment from 'moment';
import { RootState } from '@/services/store';
import { routes } from '@/services/routes';

interface BoekestynAdminState {
  lastSelected: string;
  startDate: string;
  endDate: string;
  scheduleWeek: number;
  scheduleYear: number;
  scheduleDate: string;
}

const initialState: BoekestynAdminState = {
  lastSelected: routes.boekestyns.admin.sticking.to(),
  startDate: moment().startOf('isoWeek').format('YYYY-MM-DD'),
  endDate: moment().endOf('isoWeek').add(-2, 'days').format('YYYY-MM-DD'),
  scheduleWeek: moment().isoWeek(),
  scheduleYear: moment().isoWeekYear(),
  scheduleDate: moment().format('YYYY-MM-DD'),
};

interface ScheduleWeekArgs {
  week: number;
  year: number;
}

const boekestynAdminSlice = createSlice({
  name: 'boekestynAdmin',
  initialState,
  reducers: {
    setStartDate(state, action: PayloadAction<string>) {
      state.startDate = action.payload;
    },
    setEndDate(state, action: PayloadAction<string>) {
      state.endDate = action.payload;
    },
    setLastSelected(state, action: PayloadAction<string>) {
      state.lastSelected = action.payload;
    },
    nextWeek(state) {
      const startDate = moment(state.startDate)
          .add(1, 'week')
          .startOf('isoWeek')
          .format('YYYY-MM-DD'),
        endDate = moment(state.endDate)
          .add(1, 'week')
          .endOf('isoWeek')
          .add(-2, 'days')
          .format('YYYY-MM-DD');
      state.startDate = startDate;
      state.endDate = endDate;
    },
    previousWeek(state) {
      const startDate = moment(state.startDate)
          .add(-1, 'week')
          .startOf('isoWeek')
          .format('YYYY-MM-DD'),
        endDate = moment(state.endDate)
          .add(-1, 'week')
          .endOf('isoWeek')
          .add(-2, 'days')
          .format('YYYY-MM-DD');
      state.startDate = startDate;
      state.endDate = endDate;
    },
    setScheduleWeek(state, action: PayloadAction<ScheduleWeekArgs>) {
      const { week, year } = action.payload;
      state.scheduleWeek = week;
      state.scheduleYear = year;

      const weekDay = moment(state.scheduleDate).weekday();
      state.scheduleDate = moment()
        .isoWeek(week)
        .isoWeekYear(year)
        .weekday(weekDay)
        .format('YYYY-MM-DD');
    },
    nextScheduleWeek(state) {
      const { scheduleWeek: week, scheduleYear: year, scheduleDate } = state,
        date = moment().isoWeek(week).isoWeekYear(year).add(1, 'week'),
        weekDay = moment(scheduleDate).weekday();

      state.scheduleWeek = date.isoWeek();
      state.scheduleYear = date.isoWeekYear();
      state.scheduleDate = moment()
        .isoWeek(week)
        .isoWeekYear(year)
        .weekday(weekDay)
        .format('YYYY-MM-DD');
    },
    previousScheduleWeek(state) {
      const { scheduleWeek: week, scheduleYear: year, scheduleDate } = state,
        date = moment().isoWeek(week).isoWeekYear(year).add(-1, 'week'),
        weekDay = moment(scheduleDate).weekday();

      state.scheduleWeek = date.isoWeek();
      state.scheduleYear = date.isoWeekYear();
      state.scheduleDate = moment()
        .isoWeek(week)
        .isoWeekYear(year)
        .weekday(weekDay)
        .format('YYYY-MM-DD');
    },
    setScheduleDate(state, action: PayloadAction<string>) {
      state.scheduleDate = action.payload;
      const date = moment(action.payload);
      state.scheduleWeek = date.isoWeek();
      state.scheduleYear = date.isoWeekYear();
    },
  },
});

export const {
  setStartDate,
  setEndDate,
  nextWeek,
  previousWeek,
  setLastSelected,
  setScheduleWeek,
  nextScheduleWeek,
  previousScheduleWeek,
  setScheduleDate,
} = boekestynAdminSlice.actions;

export const selectStartDate = ({ boekestynAdmin }: RootState) =>
  boekestynAdmin.startDate;
export const selectEndDate = ({ boekestynAdmin }: RootState) =>
  boekestynAdmin.endDate;
export const selectLastSelected = ({ boekestynAdmin }: RootState) =>
  boekestynAdmin.lastSelected;
export const selectScheduleDate = ({ boekestynAdmin }: RootState) =>
  boekestynAdmin.scheduleDate;
export const selectScheduleWeek = ({ boekestynAdmin }: RootState) =>
  boekestynAdmin.scheduleWeek;
export const selectScheduleYear = ({ boekestynAdmin }: RootState) =>
  boekestynAdmin.scheduleYear;

export const selectWeekDates = createSelector(
  selectScheduleWeek,
  selectScheduleYear,
  (week, year) => {
    const start = moment().isoWeekYear(year).isoWeek(week).startOf('isoWeek'),
      dates = [];

    for (let i = 0; i < 5; i++) {
      dates.push(start.clone().add(i, 'days').format('YYYY-MM-DD'));
    }
    return dates;
  }
);

export const selectSelectedIndex = createSelector(
  selectScheduleDate,
  selectWeekDates,
  (scheduleDate, scheduleDates) => {
    const index = scheduleDates.indexOf(scheduleDate);
    return index === -1 ? 0 : index;
  }
);

export default boekestynAdminSlice.reducer;
