import { useEffect, useState } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import * as models from 'api/models/boekestyns';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { routes } from '@/services/routes';
import { connection, BoekestynTaskUpdateMethod } from '@/services/signalr';
import {
  getTaskList,
  selectTaskDates,
  selectError,
  clearError,
  selectStartDate,
  setStartDate,
  selectEndDate,
  setEndDate,
  selectSort,
  selectSortDescending,
  setSort,
} from '@/components/boekestyns/tasks/task-slice';
import { PrepItem } from '@/components/boekestyns/tasks/prep-item';
import { classNames } from '@/utils/class-names';

// 10 minutes
const RefreshInterval = 10 * 60 * 1000;

export default function Prep() {
  const dispatch = useAppDispatch(),
    dates = useAppSelector(selectTaskDates),
    startDate = useAppSelector(selectStartDate),
    endDate = useAppSelector(selectEndDate),
    sort = useAppSelector(selectSort),
    sortDescending = useAppSelector(selectSortDescending),
    error = useAppSelector(selectError),
    [timer, setTimer] = useState<number | null>(null);

  useEffect(() => {
    dispatch(getTaskList());
    connection.on(BoekestynTaskUpdateMethod, () => {
      dispatch(getTaskList());
    });

    return function cleanup() {
      connection.off(BoekestynTaskUpdateMethod);
    };
  }, [dispatch]);

  useEffect(() => {
    if (!timer) {
      const interval = window.setInterval(() => {
        dispatch(getTaskList());
      }, RefreshInterval);
      setTimer(interval);
    }

    return function cleanup() {
      if (timer) {
        window.clearInterval(timer);
      }
    };
  }, [dispatch, timer]);

  const handleClearError = () => {
    dispatch(clearError());
  };

  const handleStartDateChange = async (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    await dispatch(setStartDate(e.target.value || null));
    dispatch(getTaskList());
  };

  const handleEndDateChange = async (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    await dispatch(setEndDate(e.target.value || null));
    dispatch(getTaskList());
  };

  const handleColumnSort = (sortProp: keyof models.BoekestynTaskItem) => {
    const descending = sortProp === sort ? !sortDescending : false;
    dispatch(setSort({ sort: sortProp, sortDescending: descending }));
  };

  interface HeaderButtonProps {
    text: string;
    propName: keyof models.BoekestynTaskItem;
  }
  const HeaderButton = ({ text, propName }: HeaderButtonProps) => (
    <button
      type="button"
      className="group inline-flex"
      onClick={() => handleColumnSort(propName)}
    >
      {text}
      <span
        className={classNames(
          'ml-2 flex-none rounded text-gray-400 group-hover:visible group-focus:visible',
          sort !== propName && 'invisible'
        )}
      >
        <Icon
          icon={sortDescending ? 'chevron-down' : 'chevron-up'}
          className="h-5 w-5"
          aria-hidden="true"
        />
      </span>
    </button>
  );

  return (
    <div className="flex h-full flex-col overflow-y-auto">
      <Head>
        <title>Boekestyn Order Prep</title>
      </Head>
      <header className="bg-white shadow">
        <div className="grid grid-cols-1">
          <div className="mb-4 border-b shadow">
            <div className="mx-auto flex max-w-7xl items-center justify-between px-8">
              <div className="flex">
                <nav className="ml-24 flex flex-row">
                  <Link
                    href={routes.boekestyns.list.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Boekestyn Item List
                  </Link>
                  <Link
                    href={routes.boekestyns.sales.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Boekestyn Sales
                  </Link>
                  <Link
                    href={routes.boekestyns.sticking.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Sticking
                  </Link>
                  <Link
                    href={routes.boekestyns.spacing.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Spacing
                  </Link>
                  <Link
                    href={routes.boekestyns.harvesting.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Harvesting
                  </Link>
                  <Link
                    href={routes.boekestyns.upcs.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    UPCs
                  </Link>
                  <div className="group relative my-2 text-nowrap rounded-lg bg-blue-500 p-2 text-center text-sm font-medium text-white">
                    Order Prep
                  </div>
                  <Link
                    href={routes.boekestyns.packing.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Order Packing
                  </Link>
                  <Link
                    href={routes.boekestyns.admin.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Admin
                  </Link>
                </nav>
              </div>
            </div>
          </div>
          <div className="bg-gray-100 py-2">
            <div className="mx-auto flex max-w-7xl items-center justify-between px-8">
              <div className="flex flex-grow items-center align-top">
                <div className="flex w-full flex-col rounded p-2">
                  <div className="flex w-full flex-row justify-center gap-2 rounded-sm">
                    <div>
                      <label htmlFor="start-date">From Date</label>
                      <input
                        type="date"
                        max="2050-01-01"
                        id="start-date"
                        value={startDate || ''}
                        onChange={handleStartDateChange}
                        className="w-full !max-w-none text-xs"
                      />
                    </div>
                    <div className="col-span-2 md:col-span-1">
                      <label htmlFor="end-date">To Date</label>
                      <input
                        type="date"
                        max="2050-01-01"
                        id="end-date"
                        value={endDate || ''}
                        onChange={handleEndDateChange}
                        className="w-full !max-w-none text-xs"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>
      <Error error={error} clear={handleClearError} />
      <main className="flex-grow">
        <div className="mx-auto h-full px-8">
          <div className="mt-8 flex h-full flex-col">
            <div className="-mx-8 -my-2 h-full">
              <table className="text-normal min-w-full divide-y divide-gray-300 lg:text-xl">
                <thead>
                  <tr className="sticky top-0 z-10 bg-white">
                    <th className="w-1 p-2 font-semibold text-gray-900">
                      &nbsp;
                    </th>
                    <th className="whitespace-nowrap px-8 py-2 text-center font-semibold text-gray-900">
                      <HeaderButton text="Box Code" propName="boxCode" />
                    </th>
                    <th className="px-8 py-2 text-center font-semibold text-gray-900">
                      <HeaderButton text="Cases" propName="orderQty" />
                    </th>
                    <th className="px-4 py-2 text-left font-semibold text-gray-900">
                      <HeaderButton text="Product" propName="description" />
                    </th>
                    <th className="w-1 px-8 py-2 text-center font-semibold text-gray-900">
                      <HeaderButton text="Started" propName="prepStart" />
                    </th>
                    <th className="w-1 whitespace-nowrap px-8 py-2 text-center font-semibold text-gray-900">
                      <HeaderButton
                        text="Ready to Pack"
                        propName="prepComplete"
                      />
                    </th>
                  </tr>
                </thead>
                {dates.map((date) => (
                  <PrepItem key={date} date={date} />
                ))}
              </table>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
