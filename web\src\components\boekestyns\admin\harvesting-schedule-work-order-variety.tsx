import { useMemo } from 'react';
import * as models from 'api/models/boekestyns';
import { formatNumber } from '@/utils/format';

export interface HarvestingScheduleWorkOrderVarietyProps {
  order: models.HarvestingWorkOrder;
  variety: models.HarvestingWorkOrderVariety;
}

export function HarvestingScheduleWorkOrderVariety({
  order,
  variety,
}: HarvestingScheduleWorkOrderVarietyProps) {
  const hasLabour = order.labourVarieties.length > 0,
    pots = useMemo(() => {
      return hasLabour
        ? order.labourVarieties
            .filter((lv) => lv.varietyName === variety.name)
            .reduce((sum, lv) => sum + lv.harvested + lv.thrownOut, 0)
        : variety.pots;
    }, [hasLabour, order.labourVarieties, variety.pots, variety.name]);

  return (
    <div>
      {variety.name}&nbsp;({formatNumber(pots)}
      {!hasLabour && (
        <span>&nbsp;&ndash;&nbsp;{variety.expectedHarvestPercentage}%</span>
      )}
      )
    </div>
  );
}
