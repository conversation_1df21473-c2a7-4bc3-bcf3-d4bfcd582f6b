import { Icon } from '@/components/icon';
import * as settings from 'api/models/settings';
import { setSelectedOverride } from './default-vendor-overrides-slice';
import { useAppDispatch } from '@/services/hooks';
import { DefaultVendorOverrideRowItem } from './default-vendor-override-row-item';

interface DefaultVendorOverrideRowProps {
  override: settings.DefaultVendorOverride;
}

export function DefaultVendorOverrideRow({
  override,
}: DefaultVendorOverrideRowProps) {
  const dispatch = useAppDispatch();

  const handleEditProductDefaultClick = () => {
    dispatch(setSelectedOverride(override));
  };

  return (
    <tr className="border-b text-sm">
      <td className="w-1 whitespace-nowrap p-4 text-left align-top">
        <button
          type="button"
          onClick={handleEditProductDefaultClick}
          className="secondary text-blue-600"
        >
          <Icon icon="edit" />
        </button>
      </td>
      <td className="px-2 py-4 text-left align-top">
        <div className="max-w-3xl">{override.spirePartNumbers.join(', ')}</div>
      </td>
      <td className="whitespace-nowrap px-2 py-4 text-left">
        {override.items.map((item) => (
          <DefaultVendorOverrideRowItem key={item.vendorId} item={item} />
        ))}
      </td>
    </tr>
  );
}
