import * as boeks from 'api/models/boekestyns';

interface ItemBoekestynOptionProps {
  order: boeks.WeeklyProductionOrder;
}

export function ItemBoekestynOption({ order }: ItemBoekestynOptionProps) {
  return (
    <option value={makeOptionKey(order.plantId, order.customer)}>
      {`${order.description} ${order.customer}`}
    </option>
  );
}

export function makeOptionKey(plantId: string | null, customer: string | null) {
  if (!plantId || !customer) {
    return '';
  }

  return `${plantId}:${customer}`;
}

export function parseKey(key: string) {
  if (!key) {
    return { boekestynPlantId: null, boekestynCustomerAbbreviation: null };
  }

  const [boekestynPlantId, boekestynCustomerAbbreviation] = key.split(':');

  return { boekestynPlantId, boekestynCustomerAbbreviation };
}
