import { Fragment, useRef, useState } from 'react';
import * as HeadlessUI from '@headlessui/react';
import { futureOrdersApi } from 'api/future-orders-service';
import * as models from 'api/models/future-orders';
import { Alert } from '@/components/alert';
import { Icon } from '@/components/icon';
import { classNames } from '@/utils/class-names';
import { contains, startsWith } from '@/utils/equals';
import { formatDate, formatNumber } from '@/utils/format';

export interface DeletedFutureOrdersProps {
  close: () => void;
  confirm: () => void;
}

export function DeletedFutureOrders({
  close,
  confirm,
}: DeletedFutureOrdersProps) {
  const searchRef = useRef<HTMLInputElement | null>(null),
    [orders, setOrders] = useState<models.DeletedFutureOrder[]>([]),
    [rawQuery, setRawQuery] = useState(''),
    [order, setOrder] = useState<models.DeletedFutureOrder | null>(null),
    filteredOrders = orders.filter(
      (o) =>
        !rawQuery ||
        startsWith(o.customerName, rawQuery) ||
        contains(formatNumber(o.id, '00000'), rawQuery) ||
        contains(o.customerPurchaseOrderNumber, rawQuery) ||
        contains(o.spireSalesOrderNumber, rawQuery)
    );

  const handleTransitionBeforeEnter = async () => {
    const { orders } = await futureOrdersApi.deleted();
    setOrders(orders);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setRawQuery(e.target.value);
  };

  const handleDeleteConfirm = async () => {
    if (order) {
      await futureOrdersApi.undeleteOrder(order.id);
      confirm();
    }
  };

  const handleDeleteCancel = () => {
    setOrder(null);
  };

  return (
    <>
      <HeadlessUI.Transition.Root
        show={true}
        as={Fragment}
        beforeEnter={handleTransitionBeforeEnter}
        afterLeave={() => setRawQuery('')}
        appear
      >
        <HeadlessUI.Dialog
          as="div"
          className="relative z-40"
          onClose={() => console.log('close')}
          initialFocus={searchRef}
        >
          <HeadlessUI.Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-500 bg-opacity-25 transition-opacity" />
          </HeadlessUI.Transition.Child>

          <div className="fixed inset-0 z-40 overflow-y-auto p-20">
            <HeadlessUI.Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <HeadlessUI.Dialog.Panel className="mx-auto max-w-lg transform divide-y divide-gray-100 overflow-hidden rounded-xl bg-white shadow-2xl ring-1 ring-black ring-opacity-5 transition-all">
                <HeadlessUI.Combobox onChange={setOrder}>
                  <div className="relative">
                    <Icon
                      icon="magnifying-glass"
                      className="pointer-events-none absolute left-4 top-3.5 h-5 w-5 text-gray-400"
                      aria-hidden="true"
                    />
                    <HeadlessUI.Combobox.Input
                      type="search"
                      className="h-12 w-full border-0 bg-transparent pl-11 pr-4 text-sm text-gray-800 placeholder-gray-400 focus:ring-0"
                      placeholder="Search..."
                      autoComplete="off"
                      value={rawQuery}
                      ref={searchRef}
                      onChange={handleSearchChange}
                    />
                  </div>

                  {filteredOrders.length > 0 && (
                    <HeadlessUI.Combobox.Options
                      static
                      className="max-h-80 scroll-py-10 scroll-pb-2 space-y-4 overflow-y-auto p-4 pb-2"
                    >
                      {filteredOrders.length > 0 && (
                        <li>
                          <h2 className="text-center text-lg font-semibold text-gray-900">
                            Deleted Future Orders
                          </h2>
                          <ul className="-mx-4 mt-2 text-sm text-gray-700">
                            {filteredOrders.map((order) => (
                              <HeadlessUI.Combobox.Option
                                key={order.id}
                                value={order}
                                className={({ active }) =>
                                  classNames(
                                    'grid cursor-pointer select-none grid-cols-2 items-center border-b-2 px-12 py-2 ',
                                    active
                                      ? 'bg-blue-600 text-white'
                                      : 'bg-white text-gray-400'
                                  )
                                }
                              >
                                {({ active }) => (
                                  <>
                                    <div
                                      className={classNames(
                                        'col-span-2 truncate text-center text-lg',
                                        active ? 'text-white' : 'text-gray-900'
                                      )}
                                    >
                                      #{formatNumber(order.id, '00000')}
                                    </div>
                                    <div
                                      className={classNames(
                                        'col-span-2 mb-2 truncate text-center text-xs italic',
                                        active ? 'text-white' : 'text-gray-500'
                                      )}
                                    >
                                      {`Deleted by ${order.deletedBy}`}
                                      <br />
                                      {`on ${formatDate(
                                        order.deleted,
                                        'MMM d, yyyy'
                                      )} @ ${formatDate(
                                        order.deleted,
                                        'h:mm a'
                                      )}`}
                                    </div>
                                    <div className="h-full">
                                      <div
                                        className={classNames(
                                          'truncate text-sm font-medium',
                                          active
                                            ? 'text-white'
                                            : 'text-gray-900'
                                        )}
                                      >
                                        {order.customerName}
                                      </div>
                                      <div
                                        className={classNames(
                                          'grid-col-1 text-sm',
                                          active
                                            ? 'text-white'
                                            : 'text-gray-900'
                                        )}
                                      >
                                        {order.shipToName}
                                      </div>
                                      <div
                                        className={classNames(
                                          'grid-col-1 text-sm',
                                          active
                                            ? 'text-white'
                                            : 'text-gray-900'
                                        )}
                                      >
                                        {order.salespersonName}
                                      </div>
                                    </div>
                                    <div className="h-full">
                                      {!!order.requiredDate && (
                                        <div
                                          className={classNames(
                                            'truncate text-xs italic',
                                            active
                                              ? 'text-white'
                                              : 'text-gray-500'
                                          )}
                                        >
                                          {`Required Date ${formatDate(
                                            order.requiredDate,
                                            'MMM d, yyyy'
                                          )}`}
                                        </div>
                                      )}

                                      {!!order.spireSalesOrderNumber && (
                                        <div
                                          className={classNames(
                                            'truncate text-xs italic',
                                            active
                                              ? 'text-white'
                                              : 'text-gray-500'
                                          )}
                                        >
                                          Spire # {order.spireSalesOrderNumber}
                                        </div>
                                      )}
                                      {!!order.customerPurchaseOrderNumber && (
                                        <div
                                          className={classNames(
                                            'grid-col-1 text-sm',
                                            active
                                              ? 'text-white'
                                              : 'text-gray-900'
                                          )}
                                        >
                                          PO #
                                          {order.customerPurchaseOrderNumber}
                                        </div>
                                      )}
                                    </div>
                                  </>
                                )}
                              </HeadlessUI.Combobox.Option>
                            ))}
                          </ul>
                        </li>
                      )}
                    </HeadlessUI.Combobox.Options>
                  )}
                </HeadlessUI.Combobox>
              </HeadlessUI.Dialog.Panel>
            </HeadlessUI.Transition.Child>
          </div>
        </HeadlessUI.Dialog>
      </HeadlessUI.Transition.Root>
      <Alert
        open={!!order}
        title="Undelete Future Order"
        message={`Are you sure you want to un-delete Future Order #${formatNumber(
          order?.id,
          '00000'
        )}?`}
        colour="info"
        cancelButtonText="No"
        confirmButtonText="Yes"
        cancel={handleDeleteCancel}
        confirm={handleDeleteConfirm}
      />
    </>
  );
}
