import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { Icon } from '@/components/icon';
import * as boeks from 'api/models/boekestyns';
import { handleFocus } from '@/utils/focus';
import {
  selectPlants,
  selectCustomers,
} from './product-default-settings-slice';
import { selectOverrides, setOverrides } from './product-default-detail-slice';
import React from 'react';

interface ProductDefaultDetailOverridePlantProps {
  plant: boeks.BoekestynPrebookItemProductOverride;
}

export function ProductDefaultDetailPlantOverride({
  plant,
}: ProductDefaultDetailOverridePlantProps) {
  const dispatch = useAppDispatch(),
    overrides = useAppSelector(selectOverrides),
    plants = useAppSelector(selectPlants),
    customers = useAppSelector(selectCustomers);

  const removeOverridePlant = (id: number) => {
    const updated = overrides.filter((m) => m.id !== id);

    dispatch(setOverrides(updated));
  };

  const handleOverridePlantIdChange = (
    e: React.ChangeEvent<HTMLSelectElement>,
    id: number
  ) => {
    const updated = overrides.map((m) => ({ ...m }));

    updated
      .filter((m) => m.id === id)
      .forEach((u) => (u.boekestynPlantId = e.target.value));

    dispatch(setOverrides(updated));
  };

  const handleOverrideCustomerChange = (
    e: React.ChangeEvent<HTMLSelectElement>,
    id: number
  ) => {
    const updated = overrides.map((m) => ({ ...m }));

    updated
      .filter((m) => m.id === id)
      .forEach(
        (u) => (u.boekestynCustomerAbbreviation = e.target.value || null)
      );

    dispatch(setOverrides(updated));
  };

  const handleOverrideQuantityChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    id: number
  ) => {
    const quantity = parseInt(e.target.value);

    if (!isNaN(quantity)) {
      const updated = overrides.map((m) => ({ ...m }));

      updated
        .filter((m) => m.id === id)
        .forEach((u) => (u.quantityPerFinishedItem = quantity));

      dispatch(setOverrides(updated));
    }
  };

  return (
    <tr>
      <td className="p-2">
        <select
          value={plant.boekestynPlantId || ''}
          onChange={(e) => handleOverridePlantIdChange(e, plant.id)}
          className="block w-full min-w-0 flex-1 rounded-md border-gray-300 text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
        >
          <option value="">No Boekestyn Plant</option>
          {plants.map((p) => (
            <option key={p._id} value={p._id}>
              {p.name}
            </option>
          ))}
        </select>
      </td>
      <td className="p-2">
        <select
          value={plant.boekestynCustomerAbbreviation || ''}
          onChange={(e) => handleOverrideCustomerChange(e, plant.id)}
          className="block w-auto min-w-0 flex-1 rounded-md border-gray-300 text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
        >
          <option value="">No Customer</option>
          {customers.map((customer) => (
            <option key={customer.abbreviation} value={customer.abbreviation}>
              {customer.name} ({customer.abbreviation})
            </option>
          ))}
        </select>
      </td>
      <td className="p-2">
        <input
          type="number"
          value={plant.quantityPerFinishedItem}
          onChange={(e) => handleOverrideQuantityChange(e, plant.id)}
          onFocus={handleFocus}
          className="block w-16 min-w-0 flex-1 rounded-md border-gray-300 text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
        />
      </td>
      <td className="p-2">
        <button
          type="button"
          className="btn-delete !border-transparent px-2 py-1 text-xs"
          onClick={() => removeOverridePlant(plant.id)}
        >
          <Icon icon="trash" />
        </button>
      </td>
    </tr>
  );
}
