import * as XLSX from 'xlsx';
import * as models from 'api/models/labels';
import { DateTime } from 'luxon';
import { parseQuantity } from '@/utils/format';

export function read(data: ArrayBuffer): CobornsLabels {
  const workbook = XLSX.read(data, { type: 'buffer' }),
    items: models.CobornsLabelItem[] = [],
    sheetname = workbook.SheetNames[0],
    sheet = workbook.Sheets[sheetname],
    rows = XLSX.utils.sheet_to_json<any>(sheet, {
      header: 'A',
      raw: false,
    }),
    poNumberRegex = /(\d+)$/,
    poNumberValue = (rows[1].A || '').trim(),
    purchaseOrderNumber = poNumberRegex.test(poNumberValue)
      ? poNumberRegex.exec(poNumberValue)?.[1] || ''
      : '',
    requiredDateRegex = /(\w+ \d{1,2}, \d{4}) delivery$/i,
    requiredDateValue = (rows[3].C || '').trim(),
    requiredDate = requiredDateRegex.test(requiredDateValue)
      ? DateTime.fromFormat(
          requiredDateRegex.exec(requiredDateValue)?.[1] || '',
          'MMM d, yyyy'
        ).toFormat('yyyy-MM-dd')
      : DateTime.local().toFormat('yyyy-MM-dd'),
    labels: CobornsLabels = { stores: [], purchaseOrderNumber, requiredDate };

  let row = rows.find((r) => r.__rowNum__ === 7);

  while (row?.C) {
    const rowNumber = row.__rowNum__,
      upc = (row.B || '').toString().replaceAll('-', '').replaceAll(' ', ''),
      productNumber = row.A || '',
      description = row.C,
      size = row.D || '',
      packQuantity =
        typeof row.E === 'string'
          ? parseQuantity(row.E)
          : typeof row.E === 'number'
          ? row.E
          : null,
      retail = row.F || '';
    items.push({
      rowNumber,
      upc,
      description,
      productNumber,
      size,
      packQuantity,
      retail,
    });
    row = rows.find((r) => r.__rowNum__ === rowNumber + 1);
  }

  const storeRow = rows.find((r) => r.__rowNum__ === 5),
    storeNameRow = rows.find((r) => r.__rowNum__ === 6),
    storeRegex = /(\d{4})/,
    props = Object.getOwnPropertyNames(storeRow);
  props.forEach((prop) => {
    const raw: string | number | null | undefined = storeRow[prop],
      storeNumberValue =
        typeof raw === 'string'
          ? raw
          : typeof raw === 'number'
          ? raw.toString()
          : '',
      name = (storeNameRow[prop] || '').toString();
    if (storeRegex.test(storeNumberValue)) {
      const storeNumber = parseQuantity(
          storeRegex.exec(storeNumberValue)?.[1] || ''
        ),
        store: models.CobornsLabelStore = {
          storeNumber,
          name,
          orders: [],
        };
      items.forEach((item) => {
        const itemRow = rows.find((r) => r.__rowNum__ === item.rowNumber),
          rawQuantity: string | number | null | undefined = itemRow[prop],
          quantity =
            typeof rawQuantity === 'string'
              ? parseQuantity(rawQuantity)
              : typeof rawQuantity === 'number'
              ? rawQuantity
              : 0;
        if (quantity) {
          store.orders.push({ ...item, quantity });
        }
      });
      if (store.orders.length) {
        labels.stores.push(store);
      }
    }
  });

  return labels;
}

export interface CobornsLabels {
  purchaseOrderNumber: string;
  requiredDate: string;
  stores: models.CobornsLabelStore[];
}
