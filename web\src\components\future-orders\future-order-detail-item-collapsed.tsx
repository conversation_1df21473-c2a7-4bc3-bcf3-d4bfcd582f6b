import { useRef } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import { usePermissions } from '@/services/auth';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { Icon } from '@/components/icon';
import { classNames } from '@/utils/class-names';
import { formatCurrency } from '@/utils/format';
import {
  DetailItem,
  setItemExpanded,
  moveItem,
  selectAvailabilityUnitPrices,
} from './future-order-detail-slice';

interface FutureOrderDetailItemCollapsedProps {
  item: DetailItem;
}

const type = 'FUTURE_ORDER_ITEM';

export function FutureOrderDetailItemCollapsed({
  item,
}: FutureOrderDetailItemCollapsedProps) {
  const dispatch = useAppDispatch(),
    { can } = usePermissions(),
    ref = useRef<HTMLTableRowElement>(null),
    availabilityUnitPrices = useAppSelector(selectAvailabilityUnitPrices),
    [, drag] = useDrag(() => ({
      type,
      item,
      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
      }),
    })),
    [{ isOver }, drop] = useDrop<DetailItem, void, { isOver: boolean }>(() => ({
      accept: 'FUTURE_ORDER_ITEM',
      collect: (monitor) => ({
        isOver: monitor.isOver(),
      }),
      drop(droppedItem) {
        dispatch(moveItem({ existingItem: item, movingItem: droppedItem }));
      },
    })),
    cellClassName = 'whitespace-nowrap px-1 py-1 text-gray-500 border-bottom-0',
    readonly = !can('Sales Team'),
    price = item.useAvailabilityPricing
      ? availabilityUnitPrices[item.id]
      : item.unitPrice;

  const handleExpandClick = () => {
    dispatch(setItemExpanded({ itemId: item.id, value: true }));
  };

  drag(drop(ref));

  return (
    <tr
      ref={ref}
      className={classNames(
        'border border-b-2 border-gray-200',
        isOver ? 'border-t-4 border-t-gray-500' : ''
      )}
    >
      <td className={classNames(cellClassName, 'pl-4 align-top text-xs')}>
        <div className="flex flex-row">
          <button
            type="button"
            className="btn-secondary h-8 w-8 px-2 py-1"
            onClick={handleExpandClick}
            tabIndex={-1}
          >
            <Icon icon="plus" />
          </button>
          <div className="ml-4">
            <label className="block text-xs italic text-gray-500">
              Product
            </label>
            <div className="text-left text-sm font-medium text-gray-500">
              {item.spirePartNumber}
            </div>
          </div>
        </div>
      </td>
      <td className={cellClassName}>
        <label className="block text-left text-xs italic text-gray-500">
          Description
        </label>
        <div
          className="text-left text-sm font-normal text-gray-500"
          title={item.description || ''}
        >
          {item.description}
        </div>
      </td>
      <td className={cellClassName}>
        <label className="block text-left text-xs italic text-gray-500">
          Order Quantity
        </label>
        <div className="text-left text-sm font-normal text-gray-500">
          {item.orderQuantity}
        </div>
      </td>
      <td className={cellClassName}>
        {!item.useAvailabilityPricing && !!item.unitPrice && (
          <>
            <label className="block text-left text-xs italic text-gray-500">
              Case Price
            </label>
            <div className="text-left text-sm font-normal text-gray-500">
              {formatCurrency(item.unitPrice)}
            </div>
          </>
        )}
        {item.useAvailabilityPricing && !!price && (
          <>
            <label className="block text-left text-xs italic text-gray-500">
              Availability Price
            </label>
            <div className="text-left text-sm font-normal text-gray-500">
              {formatCurrency(price)}
            </div>
          </>
        )}
      </td>
      <td className={cellClassName}>
        {!!item.customerItemCode && (
          <>
            <label className="block text-left text-xs italic text-gray-500">
              Customer Item Code
            </label>
            <div className="text-left text-sm font-normal text-gray-500">
              {item.customerItemCode}
            </div>
          </>
        )}
      </td>
      <td className={cellClassName}></td>
      <td className={cellClassName}></td>
      <td className={cellClassName}>
        {!readonly && (
          <div
            className="btn-secondary h-8 w-8 cursor-pointer px-2 py-1 text-center"
            // @ts-ignore
            ref={drag}
          >
            <Icon icon="arrows-up-down" />
          </div>
        )}
      </td>
    </tr>
  );
}
