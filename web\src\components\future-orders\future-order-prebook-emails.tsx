import { Fragment, useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import * as HeadlessUI from '@headlessui/react';
import { useAccount } from '@azure/msal-react';
import { apiUrl } from 'api/api-base';
import { UpgradesVendorId } from 'api/models/boekestyns';
import * as prebooks from 'api/models/prebooks';
import * as spire from 'api/models/spire';
import { useAppSelector, useAppDispatch } from '@/services/hooks';
import { routes } from '@/services/routes';
import {
  selectBcc,
  selectBody,
  selectUpgradesBody,
  selectUpgradesCc,
  selectSubject,
  selectTemplates,
  selectTo,
  setTo,
  setCc,
  setBcc,
  setSubject,
  setBody,
  setUpgradesBody,
  setUpgradesCc,
  clearState,
  setTemplate,
  getPrebookEmail,
  setAccount,
  clearError,
  setError,
  selectError,
  selectIsLoading,
  createPrebookEmail,
  selectCc,
  selectPrebookEmail,
} from '@/components/prebooks/prebook-email-slice';
import { selectFutureOrder } from '@/components/future-orders/future-order-detail-slice';
import { Icon } from '@/components/icon';
import { Error } from '@/components/error';
import { classNames } from '@/utils/class-names';
import { formatNumber } from '@/utils/format';
import { FutureOrderPrebookEmailImages } from './future-order-prebook-email-images';
import { Tiptap } from '../tip-tap';

interface FutureOrderPrebookEmailsProps {
  close: () => void;
  prebooks: prebooks.PrebookDetail[];
}

export function FutureOrderPrebookEmails({
  close,
  prebooks,
}: FutureOrderPrebookEmailsProps) {
  const dispatch = useAppDispatch(),
    router = useRouter(),
    account = useAccount(),
    to = useAppSelector(selectTo),
    cc = useAppSelector(selectCc),
    bcc = useAppSelector(selectBcc),
    subject = useAppSelector(selectSubject),
    body = useAppSelector(selectBody),
    upgradesBody = useAppSelector(selectUpgradesBody),
    upgradesCc = useAppSelector(selectUpgradesCc),
    templates = useAppSelector(selectTemplates),
    error = useAppSelector(selectError),
    isLoading = useAppSelector(selectIsLoading),
    futureOrder = useAppSelector(selectFutureOrder),
    prebookEmail = useAppSelector(selectPrebookEmail),
    [sentIds, setSentIds] = useState<number[]>([]),
    unsentPrebooks = prebooks.filter((p) => sentIds.indexOf(p.id) === -1);

  useEffect(() => {
    return function cleanup() {
      dispatch(clearState());
    };
  }, [dispatch]);

  useEffect(() => {
    const prebook = prebooks.filter((p) => sentIds.indexOf(p.id) === -1)[0];
    if (prebook?.id && prebook.id !== prebookEmail?.prebookId) {
      dispatch(getPrebookEmail(prebook.id));
    }
  }, [dispatch, sentIds, prebooks, prebookEmail]);

  useEffect(() => {
    dispatch(setAccount(account));
  }, [dispatch, account]);

  const handleToChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setTo(e.target.value));
  };

  const handleCcChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setCc(e.target.value || null));
  };

  const handleBccChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setBcc(e.target.value || null));
  };

  const handleSubjectChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setSubject(e.target.value));
  };

  const handleBodyChange = (value: string) => {
    dispatch(setBody(value));
  };

  const handleUpgradesBodyChange = (value: string) => {
    dispatch(setUpgradesBody(value));
  };

  const handleUpgradesCcChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setUpgradesCc(e.target.value || null));
  };

  const handleTemplateChange = (template: spire.EmailTemplate) => {
    dispatch(setTemplate(template));
  };

  const handleSkipClick = (id: number) => {
    const sent = sentIds.concat([id]);
    setSentIds(sent);
    if (prebooks.every((p) => sent.indexOf(p.id) !== -1)) {
      router.push(routes.futureOrders.list.to());
    }
  };

  const handleSendAndCloseClick = async (prebookId: number) => {
    const saved = await save(prebookId);
    if (saved) {
      const sent = sentIds.concat([prebookId]);
      setSentIds(sent);
      if (prebooks.every((p) => sent.indexOf(p.id) !== -1)) {
        router.push(routes.futureOrders.list.to());
      }
    }
  };

  const handleSendClick = async (prebookId: number) => {
    const saved = await save(prebookId);
    if (saved) {
      const sent = sentIds.concat([prebookId]);
      setSentIds(sent);
      if (prebooks.every((p) => sent.indexOf(p.id) !== -1)) {
        close();
      }
    }
  };

  const save = async (prebookId: number) => {
    if (!to) {
      dispatch(setError('Please enter the To Address.'));
    } else if (!subject) {
      dispatch(setError('Please enter the Subject.'));
    } else {
      const response = await dispatch(createPrebookEmail(prebookId));
      if (response.meta.requestStatus === 'fulfilled') {
        const email = response.payload as prebooks.PrebookEmail;
        return email;
      }
    }

    return null;
  };

  const handleClearErrorClick = () => {
    dispatch(clearError());
  };

  return (
    <HeadlessUI.Transition.Root as={Fragment} show={true}>
      <HeadlessUI.Dialog as="div" onClose={close} className="relative z-30">
        <HeadlessUI.Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </HeadlessUI.Transition.Child>

        <div className="fixed inset-0 z-30 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <HeadlessUI.Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <HeadlessUI.Dialog.Panel className="relative h-screen w-screen transform overflow-hidden p-2 transition-all">
                <div className="flex h-full flex-col rounded-lg bg-white p-2 text-left shadow-xl">
                  <div className="relative flex justify-center border-b-2 pb-4">
                    <HeadlessUI.Dialog.Title as="div" className="flex">
                      <h3 className="flex-grow text-lg font-medium leading-6 text-gray-900">
                        <Icon
                          icon="paper-plane"
                          className="h-6 w-6"
                          aria-hidden="true"
                        />
                        &nbsp; Send Prebook Emails{' '}
                        {futureOrder?.id
                          ? ` for Future Order ${formatNumber(
                              futureOrder.id,
                              '00000'
                            )}`
                          : ''}
                      </h3>
                      <button
                        className="btn-secondary absolute right-0 px-2 py-1"
                        onClick={close}
                      >
                        <Icon icon="x" />
                      </button>
                    </HeadlessUI.Dialog.Title>
                  </div>
                  <div className="flex flex-grow flex-col">
                    <HeadlessUI.Tab.Group selectedIndex={0}>
                      <HeadlessUI.Tab.List
                        as="nav"
                        className="mx-4 -mb-px flex space-x-8 overflow-hidden"
                      >
                        {unsentPrebooks.map((prebook) => (
                          <HeadlessUI.Tab key={prebook.id} as={Fragment}>
                            {({ selected }) => (
                              <button
                                type="button"
                                className={classNames(
                                  'cursor-default whitespace-nowrap border-b-2 px-1 py-2 text-xl font-medium',
                                  selected
                                    ? 'border-blue-500 text-blue-600'
                                    : 'border-transparent text-gray-500'
                                )}
                              >
                                {prebook.vendorName}
                              </button>
                            )}
                          </HeadlessUI.Tab>
                        ))}
                      </HeadlessUI.Tab.List>
                      <HeadlessUI.Tab.Panels className="flex flex-grow">
                        {unsentPrebooks.map((prebook) => (
                          <HeadlessUI.Tab.Panel
                            key={prebook.id}
                            className="mt-4 flex flex-grow rounded border p-2"
                          >
                            <div className="flex h-full flex-grow flex-col bg-white text-left">
                              <Error
                                error={error}
                                clear={handleClearErrorClick}
                                containerClasses="w-full mb-4"
                              />
                              <div className="flex flex-grow flex-col">
                                <form className="grid w-full grid-cols-2 gap-4">
                                  <div>
                                    <div className="flex flex-row">
                                      <label
                                        htmlFor="to"
                                        className="mr-4 mt-px block w-32 pt-2 text-right text-sm font-medium text-gray-700"
                                      >
                                        To
                                      </label>
                                      <input
                                        type="text"
                                        name="to"
                                        id="to"
                                        tabIndex={0}
                                        autoComplete="off"
                                        value={to}
                                        onChange={handleToChange}
                                        className="block w-full max-w-lg flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                      />
                                    </div>
                                    <div className="mt-2 flex flex-row">
                                      <label
                                        htmlFor="cc"
                                        className="mr-4 mt-px block w-32 pt-2 text-right text-sm font-medium text-gray-700"
                                      >
                                        CC
                                      </label>
                                      <input
                                        type="text"
                                        name="cc"
                                        id="cc"
                                        tabIndex={1}
                                        value={cc || ''}
                                        onChange={handleCcChange}
                                        className="block w-full max-w-lg flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                      />
                                    </div>
                                    <div className="mt-2 flex flex-row">
                                      <label
                                        htmlFor="bcc"
                                        className="mr-4 mt-px block w-32 pt-2 text-right text-sm font-medium text-gray-700"
                                      >
                                        BCC
                                      </label>
                                      <input
                                        type="text"
                                        name="bcc"
                                        id="bcc"
                                        tabIndex={1}
                                        value={bcc || ''}
                                        onChange={handleBccChange}
                                        className="block w-full max-w-lg flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                      />
                                    </div>
                                    <p className="ml-32 mt-2 pl-4 text-sm text-gray-500">
                                      Separate multiple email addresses with
                                      semicolons (;)
                                    </p>
                                    <div className="mt-2 flex flex-row">
                                      <label
                                        htmlFor="subject"
                                        className="mr-4 mt-px block w-32 pt-2 text-right text-sm font-medium text-gray-700"
                                      >
                                        Subject
                                      </label>
                                      <input
                                        type="text"
                                        name="subject"
                                        id="subject"
                                        tabIndex={2}
                                        autoComplete="off"
                                        value={subject}
                                        onChange={handleSubjectChange}
                                        className="block w-full max-w-lg flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                      />
                                      {!!templates.length && (
                                        <div className="relative">
                                          <div className="absolute inset-y-0 right-0 flex">
                                            <HeadlessUI.Menu
                                              as="div"
                                              className="relative -ml-px block h-full"
                                            >
                                              <HeadlessUI.Menu.Button className="relative inline-flex h-full items-center rounded-md px-2 py-2 text-xs font-medium text-gray-500 focus:z-10 focus:outline-none focus:ring-0">
                                                <span>Templates</span>
                                                &nbsp;
                                                <Icon
                                                  icon="chevron-down"
                                                  className="h-5 w-5"
                                                  aria-hidden="true"
                                                />
                                              </HeadlessUI.Menu.Button>
                                              <HeadlessUI.Transition
                                                as={Fragment}
                                                enter="transition ease-out duration-100"
                                                enterFrom="transform opacity-0 scale-95"
                                                enterTo="transform opacity-100 scale-100"
                                                leave="transition ease-in duration-75"
                                                leaveFrom="transform opacity-100 scale-100"
                                                leaveTo="transform opacity-0 scale-95"
                                              >
                                                <HeadlessUI.Menu.Items className="absolute right-0 z-10 -mr-1 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                                                  <div className="py-1">
                                                    {templates.map(
                                                      (template) => (
                                                        <HeadlessUI.Menu.Item
                                                          key={template.id}
                                                        >
                                                          {({ active }) => (
                                                            <button
                                                              type="button"
                                                              onClick={() =>
                                                                handleTemplateChange(
                                                                  template
                                                                )
                                                              }
                                                              className={classNames(
                                                                active
                                                                  ? 'bg-gray-100 text-gray-900'
                                                                  : 'text-gray-700',
                                                                'block w-full px-4 py-2 text-left text-sm'
                                                              )}
                                                            >
                                                              {template.name}
                                                            </button>
                                                          )}
                                                        </HeadlessUI.Menu.Item>
                                                      )
                                                    )}
                                                  </div>
                                                </HeadlessUI.Menu.Items>
                                              </HeadlessUI.Transition>
                                            </HeadlessUI.Menu>
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                    <FutureOrderPrebookEmailImages
                                      prebook={prebook}
                                    />
                                  </div>
                                  <div className="flex flex-col">
                                    <div
                                      className={classNames(
                                        'flex flex-col',
                                        prebook.items.some(
                                          (i) => i.upgradeSheet
                                        ) ||
                                          prebook.vendorId === UpgradesVendorId
                                          ? ''
                                          : 'flex-grow'
                                      )}
                                    >
                                      <label
                                        htmlFor="body"
                                        className="mt-px block text-sm font-medium text-gray-700"
                                      >
                                        Body
                                      </label>
                                      <Tiptap
                                        content={body}
                                        onChange={handleBodyChange}
                                        className={
                                          prebook.items.some(
                                            (i) => i.upgradeSheet
                                          ) ||
                                          prebook.vendorId === UpgradesVendorId
                                            ? 'h-12'
                                            : ''
                                        }
                                      />
                                    </div>
                                    {(prebook.items.some(
                                      (i) => i.upgradeSheet
                                    ) ||
                                      prebook.vendorId ===
                                        UpgradesVendorId) && (
                                      <div className="mt-2 flex flex-grow flex-col">
                                        <div className="flex flex-row">
                                          <label
                                            htmlFor="upgrades-cc"
                                            className="mr-4 mt-px block pt-2 text-sm font-medium text-gray-700"
                                          >
                                            Upgrades CC
                                          </label>
                                          <input
                                            type="text"
                                            id="upgrades-cc"
                                            autoComplete="off"
                                            value={upgradesCc || ''}
                                            onChange={handleUpgradesCcChange}
                                            className="block w-full flex-1 flex-grow rounded-md border-gray-300 text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                          />
                                        </div>
                                        <div className="flex flex-grow flex-col">
                                          <label
                                            htmlFor="upgrades-body"
                                            className="mt-px block pt-2 text-sm font-medium text-gray-700"
                                          >
                                            Upgrades Email Body
                                          </label>
                                          <Tiptap
                                            content={upgradesBody}
                                            onChange={handleUpgradesBodyChange}
                                          />
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                </form>
                              </div>
                              <div className="mt-4 grid h-full grid-cols-6 gap-4 text-right">
                                <div>
                                  <a
                                    href={apiUrl(
                                      `reports/prebooks/${prebook.id}?download`
                                    )}
                                    className="btn-secondary inline-block"
                                    tabIndex={-1}
                                  >
                                    <Icon icon="download"></Icon>
                                    &nbsp; Download PDF
                                  </a>
                                </div>
                                <iframe
                                  title="Prebook preview"
                                  src={apiUrl(`reports/prebooks/${prebook.id}`)}
                                  className="col-span-5 col-start-2 h-full w-full"
                                ></iframe>
                              </div>
                              <div className="mt-2 flex justify-between border-t-2 pt-2">
                                {!!futureOrder && (
                                  <div className="flex flex-row">
                                    <a
                                      href={apiUrl(
                                        `reports/future-orders/${futureOrder.id}`
                                      )}
                                      target="_blank"
                                      rel="noreferrer"
                                      className="btn-secondary mr-2 border-blue-600 text-lg text-blue-600"
                                    >
                                      <Icon icon="print" fixedWidth />
                                      &nbsp; Print Summary Sheet
                                    </a>
                                    <a
                                      href={apiUrl(
                                        `reports/future-orders/${futureOrder.id}/prebooks`
                                      )}
                                      target="_blank"
                                      rel="noreferrer"
                                      className="btn-secondary border-blue-600 text-lg text-blue-600"
                                    >
                                      <Icon
                                        icon="print-magnifying-glass"
                                        fixedWidth
                                      />
                                      &nbsp; Print Prebooks
                                    </a>
                                  </div>
                                )}
                                <div className="flex">
                                  <button
                                    type="button"
                                    className="btn-secondary text-lg"
                                    onClick={() => handleSkipClick(prebook.id)}
                                  >
                                    Skip
                                    <Icon
                                      icon={isLoading ? 'spinner' : 'forward'}
                                      className="ml-2"
                                      spin={isLoading}
                                    />
                                  </button>
                                  <button
                                    type="button"
                                    className="btn-secondary ml-4 text-lg"
                                    onClick={() => handleSendClick(prebook.id)}
                                    disabled={isLoading}
                                  >
                                    Send
                                    <Icon
                                      icon={isLoading ? 'spinner' : 'send'}
                                      className="ml-2"
                                      spin={isLoading}
                                    />
                                  </button>
                                  {unsentPrebooks.length === 1 && (
                                    <button
                                      type="button"
                                      className="btn-secondary ml-4 text-lg"
                                      onClick={() =>
                                        handleSendAndCloseClick(prebook.id)
                                      }
                                      disabled={isLoading}
                                    >
                                      Send &amp; Close
                                      <Icon
                                        icon={isLoading ? 'spinner' : 'send'}
                                        className="ml-2"
                                        spin={isLoading}
                                      />
                                    </button>
                                  )}
                                </div>
                              </div>
                            </div>
                          </HeadlessUI.Tab.Panel>
                        ))}
                      </HeadlessUI.Tab.Panels>
                    </HeadlessUI.Tab.Group>
                  </div>
                </div>
              </HeadlessUI.Dialog.Panel>
            </HeadlessUI.Transition.Child>
          </div>
        </div>
      </HeadlessUI.Dialog>
    </HeadlessUI.Transition.Root>
  );
}
