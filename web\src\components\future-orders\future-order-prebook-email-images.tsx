import { useEffect, useState, Fragment } from 'react';
import * as HeadlessUI from '@headlessui/react';
import { attachmentUrl } from 'api/api-base';
import { prebooksApi } from 'api/prebooks-service';
import * as futureOrders from 'api/models/future-orders';
import { PrebookDetail } from 'api/models/prebooks';
import { Icon } from '@/components/icon';
import { FutureOrderPrebookEmailImage } from './future-order-prebook-email-image';

interface FutureOrderPrebookEmailImagesProps {
  prebook: PrebookDetail;
}

export function FutureOrderPrebookEmailImages({
  prebook,
}: FutureOrderPrebookEmailImagesProps) {
  const hasUpgrades = prebook.items.some((i) => i.upgradeSheet),
    [showDialog, setShowDialog] = useState(false),
    [attachments, setAttachments] = useState<
      futureOrders.UpgradeItemAttachment[]
    >([]);

  useEffect(() => {
    prebooksApi.attachments(prebook.id).then((response) => {
      setAttachments(response.attachments);
    });
  }, [prebook.id]);

  const onImageAdded = (attachment: futureOrders.UpgradeItemAttachment) => {
    setAttachments([...attachments, attachment]);
  };

  const onImageDeleted = (id: number) => {
    setAttachments(attachments.filter((a) => a.id !== id));
  };

  const handleShowDialogClick = () => {
    setShowDialog(true);
  };

  const handleDialogClose = () => {
    setShowDialog(false);
  };

  return (
    <>
      <div className="mt-2 flex flex-row">
        <label className="mr-4 mt-px block w-32 pt-2 text-right text-sm font-medium text-gray-700">
          Image Attachments
        </label>

        <button
          type="button"
          className="btn-secondary"
          onClick={handleShowDialogClick}
        >
          <Icon icon="camera" />
        </button>
        <div className="ml-4 flex flex-grow flex-row gap-2">
          {attachments.map((attachment) => (
            <img
              key={attachment.filename}
              src={attachmentUrl(attachment.filename)}
              alt={attachment.filename}
              className="h-10 w-auto"
            />
          ))}
        </div>
        <HeadlessUI.Transition.Root as={Fragment} show={showDialog}>
          <HeadlessUI.Dialog
            as="div"
            onClose={handleDialogClose}
            className="relative z-50"
          >
            <HeadlessUI.Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0"
              enterTo="opacity-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
            </HeadlessUI.Transition.Child>

            <div className="fixed inset-0 z-50 overflow-y-auto">
              <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                <HeadlessUI.Transition.Child
                  as={Fragment}
                  enter="ease-out duration-300"
                  enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                  enterTo="opacity-100 translate-y-0 sm:scale-100"
                  leave="ease-in duration-200"
                  leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                  leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                >
                  <HeadlessUI.Dialog.Panel className="relative h-screen w-full max-w-5xl transform overflow-hidden px-8 py-20 transition-all">
                    <div className="flex h-full flex-col rounded-lg bg-white p-2 text-left shadow-xl">
                      <div className="relative flex justify-center border-b-2 pb-4">
                        <HeadlessUI.Dialog.Title as="div" className="flex">
                          <h3 className="flex-grow text-lg font-medium leading-6 text-gray-900">
                            <Icon icon="camera" className="h-6 w-6" />
                            &nbsp; Image Attachments
                          </h3>
                          <button
                            className="btn-secondary absolute right-0 px-2 py-1"
                            onClick={handleDialogClose}
                          >
                            <Icon icon="x" />
                          </button>
                        </HeadlessUI.Dialog.Title>
                      </div>
                      <div className="flex-grow overflow-y-auto">
                        <table className="min-w-full divide-y divide-gray-300">
                          <thead className="bg-gray-50">
                            <tr className="sticky top-0 z-10">
                              <th className="w-[1%] bg-gray-50 p-2 text-sm font-semibold text-gray-900">
                                Part Number
                              </th>
                              <th className="w-[1%] bg-gray-50 p-2 text-sm font-semibold text-gray-900">
                                Description
                              </th>
                              <th className="w-[1%] bg-gray-50 p-2 text-sm font-semibold text-gray-900">
                                &nbsp;
                              </th>
                              <th className="bg-gray-50 p-2 text-sm font-semibold text-gray-900">
                                Images
                              </th>
                            </tr>
                          </thead>
                          <tbody className="divide-y divide-gray-200 bg-white">
                            {prebook.items.map((item) => (
                              <FutureOrderPrebookEmailImage
                                key={item.id}
                                upgrade={item}
                                attachments={attachments}
                                onAdded={onImageAdded}
                                onDeleted={onImageDeleted}
                              />
                            ))}
                          </tbody>
                        </table>
                      </div>
                      <div className="p-2 text-right">
                        <button
                          type="button"
                          className="btn-secondary"
                          onClick={handleDialogClose}
                        >
                          Close
                        </button>
                      </div>
                    </div>
                  </HeadlessUI.Dialog.Panel>
                </HeadlessUI.Transition.Child>
              </div>
            </div>
          </HeadlessUI.Dialog>
        </HeadlessUI.Transition.Root>
      </div>
      <div className="ml-36 mr-4 mt-px text-sm italic text-gray-500">
        {hasUpgrades
          ? 'These images will be included in the Upgrades email.'
          : 'These images will be included in the Prebook email.'}
      </div>
    </>
  );
}
