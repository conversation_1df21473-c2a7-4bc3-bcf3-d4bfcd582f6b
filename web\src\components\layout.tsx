import { useEffect, useState } from 'react';
import {
  AuthenticatedTemplate,
  UnauthenticatedTemplate,
  useMsal,
} from '@azure/msal-react';
import { NavMenu } from './nav-menu';
import { useAuth } from '@/services/auth';
import { routes } from '@/services/routes';
import { Alert } from '@/components/alert';
import { Icon } from '@/components/icon';
import { Loading } from '@/components/loading';
import { Toaster } from '@/components/toaster';
import { versionApi } from 'api/version-service';

const Interval = 1000 * 60 * 5; // 5 minutes

type LayoutProps = {
  children: React.ReactNode;
};

export function Layout({ children }: LayoutProps) {
  const { instance } = useMsal(),
    { hasToken } = useAuth(),
    [version, setVersion] = useState(''),
    [outdated, setOutdated] = useState(false);

  useEffect(() => {
    window.setInterval(
      () =>
        versionApi.version().then((response) => {
          if (version && response.version !== version) {
            setOutdated(true);
          }
          setVersion(response.version);
        }),
      Interval
    );
  });

  const handleNewVersionConfirm = () => {
    const home = routes.home.to();

    if (window.location.href === home) {
      window.location.reload();
    } else {
      window.location.href = home;
    }
  };

  const handleNewVersionCancel = () => {
    setOutdated(false);
  };

  const handleLoginClick = () => {
    instance.loginRedirect();
  };

  return (
    <>
      <UnauthenticatedTemplate>
        <div className="min-h-full">
          <main>
            <div className="mx-auto max-w-xl py-10 sm:px-6 lg:px-8">
              <div className="px-4 py-6 sm:px-0">
                <div className="rounded-lg border-4 border-dashed border-gray-200 p-6 text-center">
                  <img
                    height={100}
                    src="/logo256.png"
                    className="mx-auto"
                    alt="Flora Pack"
                  />
                  <h1 className="bold text-center text-4xl text-blue-500">
                    Flora Pack Web
                  </h1>
                  <button
                    type="button"
                    className="btn-primary mt-6 px-6 py-4 text-xl"
                    onClick={handleLoginClick}
                  >
                    Log In &nbsp;
                    <Icon icon="lock" />
                  </button>
                </div>
              </div>
            </div>
          </main>
        </div>
      </UnauthenticatedTemplate>
      <AuthenticatedTemplate>
        <div className="flex h-[100vh] flex-col">
          <NavMenu />

          {hasToken ? children : <Loading />}
          <Toaster />
        </div>
        <Alert
          open={outdated}
          title="New Version"
          message="There is a new version of the FP Drive app. Would you like to update now?"
          colour="danger"
          confirmButtonText="Yes"
          confirm={handleNewVersionConfirm}
          cancelButtonText="No"
          cancel={handleNewVersionCancel}
        />
      </AuthenticatedTemplate>
    </>
  );
}
