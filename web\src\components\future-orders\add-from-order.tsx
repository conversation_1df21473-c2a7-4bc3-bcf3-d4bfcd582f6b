import { Fragment } from 'react';
import * as HeadlessUI from '@headlessui/react';
import { useItemSummaryQuery } from 'api/future-orders-service';
import * as models from 'api/models/future-orders';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import { classNames } from '@/utils/class-names';
import { formatNumber, formatCurrency } from '@/utils/format';
import {
  selectStartDate,
  selectEndDate,
  selectError,
  selectFutureOrders,
  selectSearch,
  selectSort,
  selectSortDescending,
  selectFilter,
  selectFutureOrderCustomers,
  selectFutureOrderShipTos,
  selectSelectedItems,
  setStartDate,
  setEndDate,
  setSearch,
  clearError,
  clearSelectedItems,
  setSort,
  setCustomerFilter,
  setShipToFilter,
  clearState,
} from './add-from-order-slice';
import { AddFromOrderItem } from './add-from-order-item';
import { AddFromOrderItemList } from './add-from-order-item-list';
import { AddFromOrderSelectedItemList } from './add-from-order-selected-item-list';

interface AddFromOrderProps {
  open: boolean;
  addItem: (item: models.FutureOrderDetailItem) => void;
  cancel: () => void;
}

export function AddFromOrder({ open, addItem, cancel }: AddFromOrderProps) {
  const dispatch = useAppDispatch(),
    startDate = useAppSelector(selectStartDate),
    endDate = useAppSelector(selectEndDate),
    error = useAppSelector(selectError),
    futureOrders = useAppSelector(selectFutureOrders),
    search = useAppSelector(selectSearch),
    sort = useAppSelector(selectSort),
    sortDescending = useAppSelector(selectSortDescending),
    customers = useAppSelector(selectFutureOrderCustomers),
    shipTos = useAppSelector(selectFutureOrderShipTos),
    filter = useAppSelector(selectFilter),
    selectedItems = useAppSelector(selectSelectedItems),
    { refetch } = useItemSummaryQuery({ startDate, endDate }),
    collection = futureOrders.slice(0, 100);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setSearch(e.target.value));
  };

  const handleSearchKeyUp = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      refetch();
    }
  };

  const handleStartDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const start = e.target.value;
    dispatch(setStartDate(start));
  };

  const handleEndDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const end = e.target.value;
    dispatch(setEndDate(end));
  };

  const handleCustomerFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setCustomerFilter(e.target.value || null));
  };

  const handleShipToFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setShipToFilter(e.target.value || null));
  };

  const handleCloseClick = () => {
    cancel();
  };

  const handleAddClick = () => {
    selectedItems.forEach(addItem);
    cancel();
  };

  const handleClearErrorClick = () => {
    dispatch(clearError());
  };

  const handleAfterLeave = () => {
    dispatch(clearState());
    cancel();
  };

  const handleAfterEnter = () => {
    dispatch(clearSelectedItems());
    if (!futureOrders.length) {
      refetch();
    }
  };

  const handleColumnSort = (sortProp: keyof models.FutureOrderSummaryItem) => {
    const descending = sortProp === sort ? !sortDescending : false;
    dispatch(setSort({ sort: sortProp, sortDescending: descending }));
  };

  interface HeaderButtonProps {
    text: string;
    propName: keyof models.FutureOrderSummaryItem;
  }

  const HeaderButton = ({ text, propName }: HeaderButtonProps) => (
    <button
      type="button"
      className="group inline-flex"
      onClick={() => handleColumnSort(propName)}
    >
      {text}
      <span
        className={classNames(
          'ml-2 flex-none rounded text-gray-400 group-hover:visible group-focus:visible',
          sort !== propName && 'invisible'
        )}
      >
        <Icon
          icon={sortDescending ? 'chevron-down' : 'chevron-up'}
          className="h-5 w-5"
          aria-hidden="true"
        />
      </span>
    </button>
  );

  return (
    <HeadlessUI.Transition.Root
      show={open}
      as={Fragment}
      afterLeave={handleAfterLeave}
      afterEnter={handleAfterEnter}
    >
      <HeadlessUI.Dialog
        as="div"
        className="relative z-30"
        onClose={handleCloseClick}
      >
        <HeadlessUI.Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </HeadlessUI.Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-0 text-center">
            <HeadlessUI.Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-0 scale-95"
              enterTo="opacity-100 translate-y-0 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 scale-100"
              leaveTo="opacity-0 translate-y-0 scale-95"
            >
              <HeadlessUI.Dialog.Panel className="relative my-8 w-full max-w-7xl transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all">
                <div className="h-[90vh] bg-white p-6 px-4 pb-4 pt-5">
                  <Error error={error} clear={handleClearErrorClick} />
                  <div className="ml-4 mt-0 flex h-full flex-col text-left">
                    <div className="flex flex-row">
                      <div className="mx-0 mr-5 flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-blue-100">
                        <Icon
                          icon="search"
                          className="h-6 w-6 text-blue-600"
                          aria-hidden="true"
                        />
                      </div>
                      <div className="flex flex-col">
                        <HeadlessUI.Dialog.Title
                          as="h3"
                          className="text-lg font-medium leading-6 text-gray-900"
                        >
                          Add Items From Future Order
                        </HeadlessUI.Dialog.Title>
                        <p>
                          Search for an existing Future Order to add items from.
                        </p>
                      </div>
                    </div>
                    <div className="my-4 flex w-full gap-2 rounded-sm text-xs">
                      <div>
                        <label htmlFor="start-date">From Date</label>
                        <input
                          type="date"
                          max="2050-01-01"
                          id="start-date"
                          value={startDate}
                          onChange={handleStartDateChange}
                          className="w-full !max-w-none text-xs"
                        />
                      </div>
                      <div>
                        <label htmlFor="end-date">To Date</label>
                        <input
                          type="date"
                          max="2050-01-01"
                          id="end-date"
                          value={endDate}
                          onChange={handleEndDateChange}
                          className="w-full !max-w-none text-xs"
                        />
                      </div>
                      <div className="flex-grow">
                        <label className="block" htmlFor="search">
                          Search
                        </label>
                        <input
                          type="search"
                          id="search"
                          name="search"
                          value={search}
                          onChange={handleSearchChange}
                          onKeyUp={handleSearchKeyUp}
                          className="w-full text-xs"
                          placeholder="Search"
                          autoComplete="off"
                        />
                      </div>
                      <div>
                        <label htmlFor="customer-filter">Customer</label>
                        <select
                          id="customer-filter"
                          value={filter.customer || ''}
                          onChange={handleCustomerFilterChange}
                          className="block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                        >
                          <option value="">All Customers</option>
                          {customers.map((customer) => (
                            <option key={customer}>{customer}</option>
                          ))}
                        </select>
                      </div>
                      <div className="">
                        <label htmlFor="ship-to-filter">Ship To</label>
                        <select
                          id="ship-to-filter"
                          value={filter.shipTo || ''}
                          onChange={handleShipToFilterChange}
                          className="block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                        >
                          <option value="">All Ship Tos</option>
                          {shipTos.map((shipTo) => (
                            <option key={shipTo}>{shipTo}</option>
                          ))}
                        </select>
                      </div>
                    </div>
                    <div className="flex flex-grow flex-row overflow-y-auto">
                      <div className="h-100 mr-2 flex min-w-[28rem] max-w-md flex-col overflow-y-auto rounded border">
                        <div className="flex-grow overflow-y-auto">
                          <div className="flex-grow overflow-y-auto">
                            <div className="inline-block min-w-full align-middle">
                              <table className="min-w-full divide-y divide-gray-300 text-xs">
                                <thead>
                                  <tr className="sticky top-0 z-10">
                                    <th className="bg-gray-100 p-2 text-center">
                                      <HeaderButton text="Id" propName="id" />
                                    </th>
                                    <th className="bg-gray-100 p-2">
                                      <HeaderButton
                                        text="Customer"
                                        propName="customer"
                                      />
                                    </th>
                                    <th className="bg-gray-100 p-2 text-center">
                                      <HeaderButton
                                        text="Req Date"
                                        propName="date"
                                      />
                                    </th>
                                  </tr>
                                </thead>
                                <tbody>
                                  {collection.map((order) => (
                                    <AddFromOrderItem
                                      key={order.id}
                                      futureOrder={order}
                                    />
                                  ))}
                                </tbody>
                              </table>
                            </div>
                          </div>
                        </div>
                        <div className="w-100 flex flex-row bg-gray-50 px-6 py-3">
                          <div className="flex-1 text-center text-xs">
                            {!!futureOrders.length &&
                              `${
                                futureOrders.length
                                  ? formatNumber(futureOrders.length)
                                  : 'No'
                              } order${
                                futureOrders.length === 1 ? '' : 's'
                              } found`}
                            {futureOrders.length > collection.length &&
                              ` (limited to ${collection.length})`}
                          </div>
                        </div>
                      </div>
                      <div className="h-100 col-span-2 flex flex-grow">
                        <AddFromOrderItemList />
                      </div>
                    </div>
                    <div className="mt-2 rounded border-blue-500">
                      <AddFromOrderSelectedItemList />
                    </div>
                    <div className="mt-4 text-right">
                      <button
                        type="button"
                        className="btn-secondary"
                        onClick={handleCloseClick}
                      >
                        Cancel
                      </button>
                      <button
                        type="button"
                        className="btn-primary ml-2"
                        onClick={handleAddClick}
                      >
                        Add to Future Order &nbsp;
                        <Icon icon="chevron-right" />
                      </button>
                    </div>
                  </div>
                </div>
              </HeadlessUI.Dialog.Panel>
            </HeadlessUI.Transition.Child>
          </div>
        </div>
      </HeadlessUI.Dialog>
    </HeadlessUI.Transition.Root>
  );
}
