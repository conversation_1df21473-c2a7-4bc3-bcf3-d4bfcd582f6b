import React, { Fragment, useEffect, useRef, useState } from 'react';
import { GlobalHotKeys, configure as configureHotkeys } from 'react-hotkeys';
import { DateTime } from 'luxon';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import * as HeadlessUI from '@headlessui/react';
import { apiUrl } from 'api/api-base';
import { useLazyChildrenQuery } from 'api/future-orders-service';
import {
  useOpenBlanketItemsQuery,
  useSeasonsQuery,
} from 'api/prebooks-service';
import {
  useLazyItemPricesQuery,
  useLazyHolidaysQuery,
} from 'api/future-orders-service';
import {
  useCustomersQuery,
  useSalespeopleQuery,
  useShippingMethodsQuery,
  useInventoryCommentsQuery,
  spireService,
} from 'api/spire-service';
import * as prebooks from 'api/models/prebooks';
import * as spire from 'api/models/spire';
import { usePermissions } from '@/services/auth';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { routes } from '@/services/routes';
import { Alert } from '@/components/alert';
import { useAlert } from '@/components/useAlert';
import { Combobox } from '@/components/combo-box';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import { CreateSeason } from '@/components/create-season';
import { Loading } from '@/components/loading';
import { ShipToCombobox } from '@/components/ship-to-combo-box';
import {
  clearState,
  clearError,
  setError,
  setShipTo,
  setSalesperson,
  setRequiredDate,
  setArrivalDate,
  setSeasonName,
  setShipVia,
  setBoxCode,
  setRequiresLabels,
  setCustomerPurchaseOrderNumber,
  setFreightPerCase,
  setFreightPerLoad,
  setFreightIsActual,
  setGrowerItemNotes,
  setSpireNotes,
  setComments,
  setInternalComments,
  selectError,
  selectSeasonName,
  selectFutureOrder,
  selectCustomer,
  selectSalesperson,
  selectShipTo,
  selectShipVia,
  selectRequiredDate,
  selectArrivalDate,
  selectBoxCode,
  selectRequiresLabels,
  selectCustomerPurchaseOrderNumber,
  selectFreightPerCase,
  selectFreightPerLoad,
  selectFreightIsActual,
  selectGrowerItemNotes,
  selectCustomerInfo,
  selectSpireNotes,
  selectComments,
  selectInternalComments,
  selectItems,
  selectCustomerDetail,
  getCustomerDetail,
  setCustomerDetail,
  getFutureOrderDetail,
  selectIsLoading,
  selectDirtyPrebooks,
  selectPrebooks,
  selectEmails,
  updateFutureOrder,
  sendToSpire,
  setShowInventoryDialog,
  setShowAddFromOrder,
  setShowSplitOrder,
  setCustomerInfo,
  setItemAvailabilityPrice,
  setItemUseAvailabilityPricing,
  selectHoliday,
} from '@/components/future-orders/future-order-detail-slice';
import {
  setCustomerFilter,
  setShipToFilter,
} from '@/components/future-orders/add-from-order-slice';
import { setItems } from '@/components/future-orders/split-order-slice';
import { FutureOrderDetailItems } from '@/components/future-orders/future-order-detail-items';
import { FutureOrderDetailPrebooks } from '@/components/future-orders/future-order-detail-prebooks';
import { FutureOrderDetailPrebookComment } from '@/components/future-orders/future-order-detail-prebook-comment';
import { FutureOrderPrebookEmails } from '@/components/future-orders/future-order-prebook-emails';
import { CustomerConfirmationEmail } from '@/components/future-orders/customer-confirmation-email';
import { CustomerInfoDialog } from '@/components/future-orders/customer-info-dialog';
import { OverrideAllUPCsDialog } from '@/components/future-orders/override-all-upcs-dialog';
import {
  duplicateUpcWarning,
  itemIsOverbooked,
  showPriceWarning,
} from '@/components/future-orders/item-functions';
import { classNames } from '@/utils/class-names';
import { dateIsInPast, startsWith } from '@/utils/equals';
import {
  formatCurrency,
  formatDate,
  formatNumber,
  parseNullableCurrency,
  parseQuantity,
} from '@/utils/format';
import { ProblemDetails } from '@/utils/problem-details';
import { handleFocus } from '@/utils/focus';
import { futureOrdersApi } from 'api/future-orders-service';
import {
  useDefaultVendorOverridesQuery,
  usePriceDeviationWarningsQuery,
} from 'api/settings-service';
import { BoekestynVendorId } from 'api/models/boekestyns';

export default function Detail() {
  const dispatch = useAppDispatch(),
    router = useRouter(),
    { id, action: actionParam } = router.query,
    { can } = usePermissions(),
    { data: customers } = useCustomersQuery(),
    { data: salespeople } = useSalespeopleQuery(),
    { data: shippingMethods } = useShippingMethodsQuery(),
    { data: inventoryComments } = useInventoryCommentsQuery(),
    { data: openBlanketItemsResponse } = useOpenBlanketItemsQuery(),
    { data: seasons } = useSeasonsQuery(),
    [itemPricesQuery] = useLazyItemPricesQuery(),
    [holidays] = useLazyHolidaysQuery(),
    { data: priceDeviationWarnings } = usePriceDeviationWarningsQuery(),
    [childrenQuery, { data: children, isFetching: isFetchingChildren }] =
      useLazyChildrenQuery(),
    isLoading = useAppSelector(selectIsLoading),
    error = useAppSelector(selectError),
    futureOrder = useAppSelector(selectFutureOrder),
    shipTo = useAppSelector(selectShipTo),
    customer = useAppSelector(selectCustomer),
    salesperson = useAppSelector(selectSalesperson),
    shipVia = useAppSelector(selectShipVia),
    requiredDate = useAppSelector(selectRequiredDate),
    arrivalDate = useAppSelector(selectArrivalDate),
    seasonName = useAppSelector(selectSeasonName),
    boxCode = useAppSelector(selectBoxCode),
    requiresLabels = useAppSelector(selectRequiresLabels),
    customerPurchaseOrderNumber = useAppSelector(
      selectCustomerPurchaseOrderNumber
    ),
    freightPerCase = useAppSelector(selectFreightPerCase),
    freightPerLoad = useAppSelector(selectFreightPerLoad),
    freightIsActual = useAppSelector(selectFreightIsActual),
    spireNotes = useAppSelector(selectSpireNotes),
    growerItemNotes = useAppSelector(selectGrowerItemNotes),
    customerInfo = useAppSelector(selectCustomerInfo),
    comments = useAppSelector(selectComments),
    internalComments = useAppSelector(selectInternalComments),
    items = useAppSelector(selectItems),
    customerDetail = useAppSelector(selectCustomerDetail),
    prebooks = useAppSelector(selectPrebooks),
    emails = useAppSelector(selectEmails),
    { dirty, unsent } = useAppSelector(selectDirtyPrebooks),
    holiday = useAppSelector(selectHoliday),
    [action, setAction] = useState(actionParam as string | null),
    emailPrebooks =
      action === 'send' ? prebooks : dirty.length ? dirty : unsent,
    [saving, setSaving] = useState(false),
    [showAddSeasonDialog, setShowAddSeasonDialog] = useState(false),
    [showCustomerInfoTooltip, setShowCustomerInfoTooltip] = useState(false),
    [showCustomerInfoDialog, setShowCustomerInfoDialog] = useState(false),
    [showEmails, setShowEmails] = useState(false),
    [showNewComment, setShowNewComment] = useState(false),
    [showSendToSpireDialog, setShowSendToSpireDialog] = useState(false),
    [showChildren, setShowChildren] = useState(false),
    [showConfirmPriceWarnings, setShowConfirmPriceWarnings] = useState<
      string | null
    >(null),
    [priceWarningsConfirmed, setPriceWarningsConfirmed] = useState(false),
    [showOverrideUpcsDialog, setShowOverrideUpcsDialog] = useState(false),
    { show: showConfirmSpireOrderClose, alertJSX: confirmSpireOrderClose } =
      useAlert(),
    { show: showConfirmDirty, alertJSX: confirmDirty } = useAlert(),
    { show: showConfirmDelete, alertJSX: confirmDelete } = useAlert(),
    { show: showVerifyMismatchedBoxCode, alertJSX: verifyMismatchedBoxCode } =
      useAlert(false),
    { show: showVerifyBlankBoxCode, alertJSX: verifyBlankBoxCode } =
      useAlert(false),
    [showCustomerConfirmationEmail, setShowCustomerConfirmationEmail] =
      useState(false),
    [freightPerCaseFormatted, setFreightPerCaseFormatted] = useState<
      string | null
    >(freightPerCase ? formatCurrency(freightPerCase) : null),
    [freightPerLoadFormatted, setFreightPerLoadFormatted] = useState<
      string | null
    >(freightPerLoad ? formatCurrency(freightPerLoad) : null),
    requiredDateRef = useRef<HTMLInputElement | null>(null),
    shipToRef = useRef<HTMLInputElement | null>(null),
    totalCases = items.reduce((total, i) => total + (i.orderQuantity || 0), 0),
    blanketItems = openBlanketItemsResponse?.blanketItems || [],
    seasonNames = (seasons || []).map((s) => s.name),
    hasPrintedUpcs = prebooks.some((p) => p.items.some((i) => i.upcPrinted)),
    overbookedError = items.some((i) => itemIsOverbooked(i, blanketItems))
      ? 'Please ensure no blanket items are overbooked'
      : null,
    duplicateUpcs = duplicateUpcWarning(items),
    keyMap = { ADD_ITEM: 'alt+a', ADD_EXISTING: 'alt+x', SPLIT_ORDER: 'alt+s' },
    hotKeysHandlers = {
      ADD_ITEM: () => dispatch(setShowInventoryDialog(true)),
      ADD_EXISTING: () => {
        dispatch(setCustomerFilter(customer?.name || ''));
        dispatch(setShipToFilter(shipTo?.shipId || ''));
        dispatch(setShowAddFromOrder(true));
      },
      SPLIT_ORDER: () => {
        if (futureOrder) {
          dispatch(setItems(futureOrder.items));
          dispatch(setShowSplitOrder(true));
        }
      },
    },
    readonly = !can('Sales Team'),
    requiredDateInPastError = dateIsInPast(requiredDate)
      ? 'The Required Date is in the past'
      : null,
    hasUnsentPrebooks = prebooks.some(
      (p) => !emails.some((e) => e.prebookId === p.id)
    ),
    hasUnconfirmedPrebooks = prebooks.every((p) =>
      emails.some((e) => e.prebookId === p.id)
    );

  useDefaultVendorOverridesQuery();

  configureHotkeys({ ignoreTags: [] });

  useEffect(() => {
    if (typeof id === 'string') {
      const parsed = parseQuantity(id);
      dispatch(getFutureOrderDetail(parsed));
    }

    return function cleanup() {
      dispatch(clearState());
      setShowEmails(false);
      setSaving(false);
      setShowAddSeasonDialog(false);
      setShowEmails(false);
      setShowNewComment(false);
      setShowSendToSpireDialog(false);
      setShowConfirmPriceWarnings(null);
    };
  }, [dispatch, id]);

  useEffect(() => {
    if (futureOrder && showChildren) {
      childrenQuery(futureOrder.id);
    }
  }, [showChildren, futureOrder, childrenQuery]);

  useEffect(() => {
    setAction(actionParam as string | null);
  }, [actionParam]);

  useEffect(() => {
    if (futureOrder && action === 'send') {
      setShowEmails(true);
    }
  }, [dispatch, futureOrder, action]);

  useEffect(() => {
    const shipTos = customerDetail?.shippingAddresses || [];
    if (shipTo && !shipTos.find((st) => st.id === shipTo.id)) {
      setShipTo(null);
    } else if (!shipTo && shipTos.length === 1) {
      setShipTo(shipTos[0]);
    }
  }, [shipTo, customerDetail]);

  useEffect(() => {
    const customerId = customer?.id,
      shipToId = shipTo?.id,
      availabilityPricedItems = items.filter((i) => i.useAvailabilityPricing),
      ids = availabilityPricedItems.flatMap((i) =>
        i.spireInventoryId ? [i.spireInventoryId] : []
      );
    itemPricesQuery({ customerId, shipToId, requiredDate, items: ids }).then(
      (result) => {
        if (result.data) {
          availabilityPricedItems.forEach((i) => {
            const price = result.data!.find(
              (p) => p.spireInventoryItemId === i.spireInventoryId
            );
            if (price) {
              dispatch(
                setItemAvailabilityPrice({ itemId: i.id, value: price.price })
              );
            }
          });
        }
      }
    );
  }, [customer, shipTo, requiredDate, items, itemPricesQuery, dispatch]);

  useEffect(() => {
    if (customer && customer.id !== customerDetail?.id) {
      dispatch(getCustomerDetail(customer.id));
    }
  }, [dispatch, customer, customerDetail]);

  useEffect(() => {
    if (shipTo) {
      spireService.shipToDetail(shipTo.id).then(({ shipTo }) => {
        dispatch(setCustomerInfo(shipTo?.customerInfo || null));
      });
    } else {
      dispatch(setCustomerInfo(null));
    }
  }, [dispatch, shipTo]);

  useEffect(() => {
    if (requiredDate) {
      const date = DateTime.fromFormat(requiredDate, 'yyyy-MM-dd');
      holidays(date.year);
    }
  }, [holidays, requiredDate]);

  useEffect(() => {
    if (freightPerCase) {
      setFreightPerCaseFormatted(formatCurrency(freightPerCase));
    } else {
      setFreightPerCaseFormatted(null);
    }
  }, [freightPerCase]);

  useEffect(() => {
    if (freightPerLoad) {
      setFreightPerLoadFormatted(formatCurrency(freightPerLoad));
    } else {
      setFreightPerLoadFormatted(null);
    }
  }, [freightPerLoad]);

  useEffect(() => {
    if (freightPerCase) {
      const perLoad = Math.round(freightPerCase * totalCases * 100) / 100;
      dispatch(setFreightPerLoad(perLoad));
    }
  }, [dispatch, freightPerCase, totalCases]);

  const handleClearErrorClick = () => {
    dispatch(clearError());
  };

  const handleCustomerChange = async (customer: spire.Customer | null) => {
    await dispatch(getCustomerDetail(customer?.id || null));

    if (customer) {
      shipToRef.current?.focus();
    }
  };

  const handleShipToChange = (
    shipTo: spire.CustomerShipTo | null,
    customer?: spire.CustomerDetail
  ) => {
    if (customer) {
      dispatch(setCustomerDetail(customer));
    }

    dispatch(setShipTo(shipTo));
  };

  const handleCustomerInfoTooltipMouseEnter = () => {
    setShowCustomerInfoTooltip(true);
  };

  const handleCustomerInfoTooltipMouseLeave = () => {
    setShowCustomerInfoTooltip(false);
  };

  const handleCustomerInfoClick = () => {
    setShowCustomerInfoDialog(true);
  };

  const handleCustomerInfoDialogClose = () => {
    setShowCustomerInfoDialog(false);
  };

  const handleCustomerInfoDialogSave = async (info: string | null) => {
    console.log(info);
  };

  const handleRequiresLabelsChange = (value: boolean) => {
    dispatch(setRequiresLabels(value));
  };

  const handleSalespersonChange = (salesperson: spire.Salesperson | null) => {
    dispatch(setSalesperson(salesperson));
  };

  const handleShipViaChange = (shipVia: spire.ShippingMethod | null) => {
    dispatch(setShipVia(shipVia));
  };

  const handleRequiredDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value || null;
    dispatch(setRequiredDate(value));
  };

  const handleArrivalDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setArrivalDate(e.target.value || null));
  };

  const handleSeasonNameChange = (season: string | null) => {
    dispatch(setSeasonName(season));
  };

  const handleAddSeasonClick = () => {
    setShowAddSeasonDialog(true);
  };

  const handleAddSeasonCancel = () => {
    setShowAddSeasonDialog(false);
  };

  const handleAddSeasonConfirm = async (season: prebooks.Season) => {
    dispatch(setSeasonName(season.name));
    setShowAddSeasonDialog(false);
  };

  const handleBoxCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setBoxCode(e.target.value || null));
  };

  const handleCustomerPurchaseOrderNumberChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    dispatch(setCustomerPurchaseOrderNumber(e.target.value || null));
  };

  const handleFreightPerCaseChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setFreightPerCaseFormatted(e.target.value);
  };

  const handleFreightPerCaseBlur = () => {
    const value = parseNullableCurrency(freightPerCaseFormatted),
      formatted = value ? formatCurrency(value) : null,
      perLoad =
        value == null || totalCases === 0
          ? null
          : formatted === freightPerCaseFormatted
          ? freightPerLoad
          : Math.round(value * totalCases * 100) / 100;

    dispatch(setFreightPerCase(value));
    dispatch(setFreightPerLoad(perLoad));
  };

  const handleFreightPerLoadChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setFreightPerLoadFormatted(e.target.value);
  };

  const handleFreightPerLoadBlur = () => {
    const value = parseNullableCurrency(freightPerLoadFormatted),
      formatted = value ? formatCurrency(value) : null,
      perCase =
        value == null || totalCases === 0
          ? null
          : formatted === freightPerLoadFormatted
          ? freightPerCase
          : Math.round((value / totalCases) * 100) / 100;

    dispatch(setFreightPerLoad(value));
    dispatch(setFreightPerCase(perCase));
  };

  const handleFreightIsActualChange = (value: boolean) => {
    dispatch(setFreightIsActual(value));
  };

  const handleSpireNotesChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    dispatch(setSpireNotes(e.target.value || null));
  };

  const handleGrowerItemNotesChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    dispatch(setGrowerItemNotes(e.target.value || null));
  };

  const handleAddCommentsClick = () => {
    setShowNewComment(true);
  };

  const handleInternalCommentsChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const comment = e.target.value || null;
    dispatch(setInternalComments(comment));
  };

  const handleAddStandardComment = (comment: spire.InventoryComment) => {
    const updated = comments.map((c) => ({ ...c })),
      id = comments.reduce((min, c) => Math.min(min, c.id), 0) - 1;

    updated.push({
      id,
      comments: comment.comments,
      isStandardComment: true,
      created: null,
      createdBy: null,
    });

    dispatch(setComments(updated));
  };

  const handleDeleteStandardCommentClick = (id: number) => {
    const updated = comments.filter((c) => c.id !== id);

    dispatch(setComments(updated));
  };

  const handlePriceWarningConfirm = async () => {
    setPriceWarningsConfirmed(true);
    setShowConfirmPriceWarnings(null);
  };

  const handlePriceWarningCancel = () => {
    setShowConfirmPriceWarnings(null);
  };

  const handleOverrideUpcsClick = () => {
    setShowOverrideUpcsDialog(true);
  };

  const handleOverrideUpcsClose = () => {
    setShowOverrideUpcsDialog(false);
  };

  const handleCancelClick = () => {
    if (window.history.length > 1) {
      router.back();
    } else {
      router.push(routes.futureOrders.list.to());
    }
  };

  const handleSaveAndCloseClick = async () => {
    const isDirty = dirty.filter(
        (p) => p.vendorId !== BoekestynVendorId
      ).length,
      sendEmails =
        isDirty &&
        (await showConfirmDirty({
          title: 'Unsent changes',
          message:
            'You have made changes to one or more Prebooks that have not been sent to the Growers. Would you like to send them now?',
          colour: 'info',
          confirmButtonText: 'Yes',
          cancelButtonText: 'No',
          icon: 'question-circle',
        })),
      saved = await save();

    if (saved) {
      if (sendEmails) {
        setShowEmails(true);
      } else {
        router.push(routes.futureOrders.list.to());
      }
    }
  };

  const handleSaveAndSendClick = async () => {
    const saved = await save();
    if (saved) {
      setShowEmails(true);
    }
  };

  const handlePrintClick = async () => {
    const saved = await save();
    if (saved) {
      window.open(apiUrl(`reports/future-orders/${id}`), '_blank');
    }

    // if we don't do this, the user will get prompted to send the emails again
    if (futureOrder && action === 'send') {
      setShowEmails(false);
      router.push(routes.futureOrders.detail.to(futureOrder.id));
    }
  };

  const handlePrintCustomerConfirmationClick = async () => {
    const saved = await save();
    if (saved) {
      window.open(
        apiUrl(`reports/future-orders/${id}/customer-confirmation`),
        '_blank'
      );
    }
  };

  const handlePrintPrebooksClick = async () => {
    const saved = await save();
    if (saved) {
      window.open(apiUrl(`reports/future-orders/${id}/prebooks`), '_blank');
    }
  };

  const save = async () => {
    if (readonly) {
      return true;
    }

    try {
      setSaving(true);
      if (!requiredDate && !seasonName) {
        const error =
          !requiredDate && !seasonName
            ? 'Please enter either the Required Date or the Season'
            : 'Please enter the Required Date';
        dispatch(setError(error));
      } else if (!customer) {
        dispatch(setError('Please select the Customer.'));
      } else if (!items.length) {
        dispatch(setError('Please add one or more items.'));
      } else {
        if (futureOrder?.spireSalesOrderNumber) {
          const result = await showConfirmSpireOrderClose({
            title: 'Future Order in Spire',
            message:
              'This Future Order has already been sent to Spire. Would you like to continue?',
            colour: 'info',
          });
          if (!result) {
            return false;
          }
        }

        try {
          const response = await dispatch(updateFutureOrder());
          if (response.meta.requestStatus === 'fulfilled') {
            // Clear comments after successful save
            dispatch(setInternalComments(null));
            setShowNewComment(false);
            return true;
          }
        } catch (e) {
          dispatch(setError(e as ProblemDetails));
          return false;
        }
      }

      return false;
    } finally {
      setSaving(false);
    }
  };

  const handleShowEmailsClose = () => {
    setShowEmails(false);
    const url = new URL(window.location.href);
    if (url.searchParams.has('action')) {
      url.searchParams.delete('action');
      window.history.replaceState({}, '', url.toString());
      setAction(null);
    }
  };

  const handleSendToSpireClick = () => {
    setShowSendToSpireDialog(true);
  };

  const handleCopyClick = async () => {
    const saved = await save();
    if (saved) {
      router.push(`${routes.futureOrders.new.to()}?from=${futureOrder?.id}`);
    }
  };

  const handleDeleteClick = async () => {
    if (futureOrder) {
      try {
        const response = await showConfirmDelete({
          title: 'Delete Future Order',
          message: 'Are you sure you want to delete this Future Order?',
          confirmButtonText: 'Yes',
          cancelButtonText: 'No',
          colour: 'danger',
        });
        if (response) {
          await futureOrdersApi.deleteOrder(futureOrder.id);
          router.push(routes.futureOrders.list.to());
        }
      } catch (e) {
        dispatch(setError(e as ProblemDetails));
      }
    }
  };

  const handleSendToSpireCancel = () => {
    setShowSendToSpireDialog(false);
  };

  const boxCodeNotifications = async () => {
    if (futureOrder?.shipToId) {
      const shipTo = customerDetail?.shippingAddresses.find(
        (a) => a.id === futureOrder.shipToId
      );
      if (shipTo) {
        if (!shipTo.boxCode) {
          await showVerifyBlankBoxCode({
            title: 'Blank Box Code',
            colour: 'danger',
            message: `The Ship To address ${shipTo.name} does not have a Box Code. Please enter this information in Spire.`,
            confirmButtonText: 'OK',
          });
        } else if (shipTo.boxCode !== boxCode) {
          await showVerifyMismatchedBoxCode({
            title: 'Mismatched Box Code',
            colour: 'danger',
            message:
              `The Box Code for the Ship To address ${shipTo.name} is ${shipTo.boxCode}, but the Future Order has a Box Code of ${boxCode}. \n\n` +
              'This will cause a difference between the Prebook and the Purchase Order to the Grower.',
            confirmButtonText: 'OK',
          });
        }
      }
    }
  };

  const handleSendToSpireConfirm = async () => {
    if (futureOrder) {
      // just give warnings
      await boxCodeNotifications();

      // if there are any items that don't have a price and are not using availability pricing,
      // just use availability pricing for them
      items
        .filter((i) => !i.unitPrice && !i.useAvailabilityPricing)
        .map(async (i) => {
          await dispatch(
            setItemUseAvailabilityPricing({ itemId: i.id, value: true })
          );
        });

      const saved = await save();

      try {
        setSaving(true);

        if (saved) {
          if (!requiredDate) {
            return dispatch(setError('Please choose the Required Date.'));
          }
          if (!arrivalDate) {
            return dispatch(setError('Please choose the Arrival Date.'));
          }

          if (items.some((i) => i.phytoRequired && !i.phytoOrdered)) {
            return dispatch(
              setError(
                "Please ensure Phytos have been ordered for all the items they're required for."
              )
            );
          }

          const response: any = await dispatch(sendToSpire(futureOrder.id));
          if (!response?.error) {
            router.push(routes.futureOrders.list.to());
          }
        }
      } finally {
        setShowSendToSpireDialog(false);
        setSaving(false);
      }
    }
  };

  return (
    <div>
      <GlobalHotKeys keyMap={keyMap} handlers={hotKeysHandlers} allowChanges />
      <Head>
        <title>{`Future Order #${formatNumber(id, '00000')}`}</title>
      </Head>
      <header className="sticky top-0 z-20 bg-white shadow">
        <div className="flex w-full items-center justify-between px-8 py-6">
          <div className="flex flex-grow justify-between">
            <div>
              <h2 className="text-2xl font-bold leading-7 text-gray-900">
                <Icon icon="file-check" />
                &nbsp; {`Edit Future Order #${formatNumber(id, '00000')}`}
              </h2>
              {!!futureOrder?.spireSalesOrderNumber && (
                <h4 className="text-xl font-semibold italic text-gray-500">
                  Spire Order #{futureOrder.spireSalesOrderNumber}
                </h4>
              )}
            </div>
            <div className="-my-4 mx-8 flex flex-col align-top">
              <HeadlessUI.Switch.Group as="div">
                <HeadlessUI.Switch
                  checked={requiresLabels}
                  onChange={handleRequiresLabelsChange}
                  disabled={readonly}
                  className={classNames(
                    requiresLabels ? 'bg-blue-400' : 'bg-gray-200',
                    'relative inline-flex h-4 w-8 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
                  )}
                >
                  <span
                    aria-hidden="true"
                    className={classNames(
                      requiresLabels ? 'translate-x-4' : 'translate-x-0',
                      'pointer-events-none inline-block h-3 w-3 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                    )}
                  />
                </HeadlessUI.Switch>
                <HeadlessUI.Switch.Label className="ml-2">
                  <span className="text-xs">Special Labels</span>
                </HeadlessUI.Switch.Label>
              </HeadlessUI.Switch.Group>
            </div>
          </div>
          <div className="flex">
            <button
              type="button"
              onClick={handleCancelClick}
              disabled={saving}
              className="btn-secondary inline-flex"
            >
              {readonly ? 'Close' : 'Cancel'}
            </button>
            {!readonly && (
              <>
                <button
                  type="button"
                  className="btn-secondary ml-3 inline-flex border-blue-600 px-2 text-blue-600 disabled:border-blue-300 disabled:text-blue-300 disabled:hover:border-blue-300 disabled:hover:text-blue-300"
                  disabled={saving}
                  onClick={handleSaveAndCloseClick}
                >
                  Save &amp; Close &nbsp;
                  <Icon icon="save" />
                </button>
                {confirmDirty}
                <button
                  type="button"
                  className="btn-primary ml-3 inline-flex rounded-r-none"
                  disabled={saving}
                  onClick={handleSaveAndSendClick}
                >
                  Save &amp; Send &nbsp;
                  <Icon icon="save" />
                  &nbsp;
                  <Icon icon="paper-plane" />
                </button>
              </>
            )}

            <HeadlessUI.Menu as="div" className="relative -ml-px block">
              <HeadlessUI.Menu.Button className="btn-secondary relative rounded-l-none border-blue-600 px-2 text-blue-600 focus:outline-none focus:ring-0">
                <span className="sr-only">Open options</span>
                <Icon
                  icon="chevron-down"
                  className="h-5 w-5"
                  aria-hidden="true"
                />
              </HeadlessUI.Menu.Button>
              <HeadlessUI.Transition
                as={Fragment}
                enter="transition ease-out duration-100"
                enterFrom="transform opacity-0 scale-95"
                enterTo="transform opacity-100 scale-100"
                leave="transition ease-in duration-75"
                leaveFrom="transform opacity-100 scale-100"
                leaveTo="transform opacity-0 scale-95"
              >
                <HeadlessUI.Menu.Items className="absolute right-0 z-10 -mr-1 mt-2 w-64 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                  <div className="py-1">
                    <HeadlessUI.Menu.Item>
                      {({ active }) => (
                        <button
                          type="button"
                          className={classNames(
                            'block w-full px-4 py-2 text-left text-sm disabled:text-gray-500 disabled:hover:bg-white',
                            active
                              ? 'bg-gray-100 text-gray-900'
                              : 'text-gray-700'
                          )}
                          disabled={saving}
                          onClick={handlePrintClick}
                        >
                          <Icon icon="print" fixedWidth />
                          &nbsp; Print Summary Sheet
                        </button>
                      )}
                    </HeadlessUI.Menu.Item>
                    <HeadlessUI.Menu.Item>
                      {({ active }) => (
                        <button
                          type="button"
                          className={classNames(
                            'block w-full px-4 py-2 text-left text-sm disabled:text-gray-500 disabled:hover:bg-white',
                            active
                              ? 'bg-gray-100 text-gray-900'
                              : 'text-gray-700'
                          )}
                          disabled={saving}
                          onClick={handlePrintCustomerConfirmationClick}
                        >
                          <Icon icon="file-check" fixedWidth />
                          &nbsp; Print Customer Confirmation
                        </button>
                      )}
                    </HeadlessUI.Menu.Item>
                    <HeadlessUI.Menu.Item>
                      {({ active }) => (
                        <button
                          type="button"
                          className={classNames(
                            'block w-full px-4 py-2 text-left text-sm disabled:text-gray-500 disabled:hover:bg-white',
                            active
                              ? 'bg-gray-100 text-gray-900'
                              : 'text-gray-700'
                          )}
                          disabled={saving}
                          onClick={() => setShowCustomerConfirmationEmail(true)}
                        >
                          <Icon icon="paper-plane" fixedWidth />
                          &nbsp; Email Customer Confirmation
                        </button>
                      )}
                    </HeadlessUI.Menu.Item>
                    <HeadlessUI.Menu.Item>
                      {({ active }) => (
                        <button
                          type="button"
                          className={classNames(
                            'block w-full px-4 py-2 text-left text-sm disabled:text-gray-500 disabled:hover:bg-white',
                            active
                              ? 'bg-gray-100 text-gray-900'
                              : 'text-gray-700'
                          )}
                          disabled={saving}
                          onClick={handlePrintPrebooksClick}
                        >
                          <Icon icon="print-magnifying-glass" fixedWidth />
                          &nbsp; Print Prebooks
                        </button>
                      )}
                    </HeadlessUI.Menu.Item>
                    {!readonly && (
                      <>
                        <HeadlessUI.Menu.Item>
                          {({ active }) => (
                            <button
                              type="button"
                              className={classNames(
                                'block w-full px-4 py-2 text-left text-sm disabled:text-gray-500 disabled:hover:bg-white',
                                active
                                  ? 'bg-gray-100 text-gray-900'
                                  : 'text-gray-700'
                              )}
                              disabled={saving}
                              onClick={handleCopyClick}
                            >
                              <Icon icon="copy" fixedWidth />
                              &nbsp; Copy Future Order
                            </button>
                          )}
                        </HeadlessUI.Menu.Item>
                        <HeadlessUI.Menu.Item>
                          {({ active }) => (
                            <>
                              <button
                                type="button"
                                className={classNames(
                                  'block w-full px-4 py-2 text-left text-sm disabled:text-red-400 disabled:hover:bg-white',
                                  active
                                    ? 'bg-gray-100 text-red-600'
                                    : 'text-red-500'
                                )}
                                disabled={saving}
                                onClick={handleDeleteClick}
                              >
                                <Icon icon="trash" fixedWidth />
                                &nbsp; Delete Future Order
                              </button>
                              {confirmDelete}
                            </>
                          )}
                        </HeadlessUI.Menu.Item>
                        <HeadlessUI.Menu.Item>
                          {({ active }) => (
                            <button
                              type="button"
                              className={classNames(
                                'block w-full px-4 py-2 text-left text-sm disabled:text-gray-500 disabled:hover:bg-white',
                                active
                                  ? 'bg-gray-100 text-gray-900'
                                  : 'text-gray-700'
                              )}
                              disabled={saving}
                              onClick={handleSendToSpireClick}
                            >
                              <Icon icon="arrow-circle-right" fixedWidth />
                              &nbsp; Send to Spire
                            </button>
                          )}
                        </HeadlessUI.Menu.Item>
                      </>
                    )}
                  </div>
                </HeadlessUI.Menu.Items>
              </HeadlessUI.Transition>
            </HeadlessUI.Menu>
          </div>
        </div>
      </header>
      <main>
        <div className="mx-auto max-w-7xl px-8">
          {isLoading && <Loading />}
          <Error
            error={error}
            clear={handleClearErrorClick}
            containerClasses="w-full mt-4"
          />
          <Error
            error={overbookedError}
            type="warning"
            containerClasses="w-full mt-4 whitespace-pre"
          />
          <Error
            error={requiredDateInPastError}
            type="warning"
            containerClasses="w-full mt-4 whitespace-pre"
          />
          <Error
            error={duplicateUpcs}
            type="warning"
            containerClasses="w-full mt-4"
          />
          <Error
            error={
              holiday
                ? `The Required Date for this order is on a holiday: ${holiday.nameEn}` +
                  (holiday.observedDate != holiday.date
                    ? ` (observed ${holiday.observedDate})`
                    : '')
                : null
            }
            type="warning"
            containerClasses="w-full mt-4"
          />
          {futureOrder && (
            <form className="space-y-8 divide-y divide-gray-200">
              <div className="space-y-8 divide-y divide-gray-200">
                <div className="mt-6 grid grid-cols-4 gap-x-4 gap-y-6">
                  <div className="col-span-2">
                    {!!futureOrder?.isParentOrder && (
                      <div className="inline-block rounded border p-4">
                        <div className="flex flex-row items-start">
                          <h3 className="mb-2 text-gray-400">
                            <Icon icon="folder-tree" />
                            &nbsp; This order has been split
                          </h3>
                          <button
                            type="button"
                            onClick={() => setShowChildren(!showChildren)}
                            className="btn-secondary ml-2 p-1 text-xs text-gray-400"
                          >
                            <Icon
                              icon={
                                showChildren ? 'chevron-up' : 'chevron-down'
                              }
                            />
                          </button>
                        </div>
                        {showChildren && (
                          <>
                            {isFetchingChildren && <Icon icon="spinner" spin />}
                            {!isFetchingChildren && !!children?.length && (
                              <table className="text-xs">
                                <tbody className="divide-y divide-gray-200">
                                  {children.map((child) => (
                                    <tr key={child.id}>
                                      <td className="w-1 whitespace-nowrap p-1">
                                        <Link
                                          href={routes.futureOrders.detail.to(
                                            child.id
                                          )}
                                        >
                                          {formatNumber(child.id, '00000')}
                                        </Link>
                                      </td>
                                      <td className="w-1 whitespace-nowrap p-1 text-gray-400">
                                        {!!child.requiredDate &&
                                          formatDate(child.requiredDate)}
                                      </td>
                                      <td className="whitespace-nowrap p-1 text-gray-400">
                                        {!!child.customerName && (
                                          <span className="semibold ml-2">
                                            {child.customerName}
                                          </span>
                                        )}
                                      </td>
                                    </tr>
                                  ))}
                                </tbody>
                              </table>
                            )}
                          </>
                        )}
                      </div>
                    )}
                    {!!futureOrder?.parentOrderId && (
                      <div className="inline-block rounded border p-4">
                        <div className="flex flex-row items-start">
                          <h3 className="mb-2 text-gray-400">
                            <Icon icon="folders" />
                            &nbsp; This order was split from
                            <Link
                              href={routes.futureOrders.detail.to(
                                futureOrder.parentOrderId
                              )}
                            >
                              &nbsp;#
                              {formatNumber(futureOrder.parentOrderId, '00000')}
                            </Link>
                          </h3>
                        </div>
                      </div>
                    )}
                  </div>
                  <div className="col-span-2 text-right">
                    {hasUnsentPrebooks && (
                      <p className="my-2 text-xs text-red-600">
                        <Icon icon="exclamation-triangle" />
                        &nbsp; One or more Prebooks have not been sent to the
                        Grower
                      </p>
                    )}
                    {hasUnconfirmedPrebooks &&
                      prebooks.some((p) => !p.confirmed) && (
                        <p className="my-2 text-xs text-yellow-600">
                          <Icon icon="question-circle" />
                          &nbsp; One or more Prebooks have not been Confirmed
                        </p>
                      )}
                    <p className="my-2 text-xs text-gray-500">
                      Created by{' '}
                      <span className="font-medium">
                        {futureOrder.createdBy}
                      </span>{' '}
                      on{' '}
                      <span className="font-medium">
                        {formatDate(futureOrder.created)}
                      </span>{' '}
                      @{' '}
                      <span className="font-medium">
                        {formatDate(futureOrder.created, 'h:mm a')}
                      </span>
                    </p>
                    {futureOrder.created !== futureOrder.modified && (
                      <p className="my-2 text-xs text-gray-500">
                        Updated by{' '}
                        <span className="font-medium">
                          {futureOrder.modifiedBy}
                        </span>{' '}
                        on{' '}
                        <span className="font-medium">
                          {formatDate(futureOrder.modified)}
                        </span>{' '}
                        @{' '}
                        <span className="font-medium">
                          {formatDate(futureOrder.modified, 'h:mm a')}
                        </span>
                      </p>
                    )}
                    {!!futureOrder.sentToSpire && (
                      <p className="my-2 text-xs text-gray-500">
                        Sent to Spire by{' '}
                        <span className="font-medium">
                          {futureOrder.sentToSpireBy}
                        </span>{' '}
                        on{' '}
                        <span className="font-medium">
                          {formatDate(futureOrder.sentToSpire)}
                        </span>{' '}
                        @{' '}
                        <span className="font-medium">
                          {formatDate(futureOrder.sentToSpire, 'h:mm a')}
                        </span>
                      </p>
                    )}
                  </div>
                  <div className="col-start-1 mt-1 grid grid-cols-2 gap-x-2">
                    <label
                      htmlFor="required-date"
                      className="block text-sm font-medium text-gray-500"
                    >
                      Required Date &nbsp;
                      <span className="text-red-500">&nbsp;*</span>
                    </label>
                    <label
                      htmlFor="required-date"
                      className="block text-sm font-medium text-gray-500"
                    >
                      Arrival Date
                      <span className="text-red-500">&nbsp;*</span>
                    </label>
                    <div className="mt-1">
                      <input
                        type="date"
                        max="2050-01-01"
                        name="requiredDate"
                        id="required-date"
                        value={requiredDate || ''}
                        onChange={handleRequiredDateChange}
                        ref={requiredDateRef}
                        disabled={readonly || hasPrintedUpcs}
                        className="block w-full rounded-md border-gray-300 text-sm shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                    <div className="mt-1">
                      <input
                        type="date"
                        max="2050-01-01"
                        name="arrivalDate"
                        id="arrival-date"
                        value={arrivalDate || ''}
                        onChange={handleArrivalDateChange}
                        min={requiredDate || ''}
                        disabled={readonly}
                        className="block w-full rounded-md border-gray-300 text-sm shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                    {hasPrintedUpcs && (
                      <div className="col-span-2 bg-yellow-50 py-2 text-center text-sm">
                        UPCs have been printed.
                        <br />
                        Click to override the UPCs. &nbsp;
                        <button
                          type="button"
                          className="btn-secondary small py-1"
                          onClick={handleOverrideUpcsClick}
                        >
                          <Icon icon="arrow-right" />
                        </button>
                      </div>
                    )}
                  </div>
                  <div>
                    <div className="flex align-bottom">
                      <div className="flex-grow rounded shadow-sm">
                        <Combobox
                          value={seasonName}
                          onChange={handleSeasonNameChange}
                          label="Season / Holiday"
                          collection={seasonNames}
                          filter={(q, s) => startsWith(s, q)}
                          disabled={readonly}
                        />
                      </div>
                      {!readonly && (
                        <div className="mt-auto flex">
                          <button
                            type="button"
                            className="btn-secondary"
                            onClick={handleAddSeasonClick}
                            tabIndex={-1}
                          >
                            <Icon icon="plus" />
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                  <div>
                    <div className="mt-1 rounded-md shadow-sm">
                      <Combobox
                        value={customer}
                        onChange={handleCustomerChange}
                        label="Customer"
                        required
                        collection={customers}
                        filter={(q, c) =>
                          startsWith(c.name, q) || startsWith(c.customerNo, q)
                        }
                        disabled={readonly}
                        secondaryDisplayTextProp="customerNo"
                        nullDisplayText="No Customer"
                      />
                    </div>
                  </div>
                  <div>
                    <div className="mt-1 rounded-md shadow-sm">
                      <label className="block text-sm font-medium text-gray-500">
                        Ship-To
                        <HeadlessUI.Popover
                          className="relative inline-block cursor-pointer"
                          onMouseEnter={handleCustomerInfoTooltipMouseEnter}
                          onMouseLeave={handleCustomerInfoTooltipMouseLeave}
                        >
                          {!!customerInfo && (
                            <>
                              <button
                                type="button"
                                className="btn-secondary small ml-2 border-transparent p-0 text-yellow-500 shadow-none"
                                onClick={handleCustomerInfoClick}
                              >
                                <FontAwesomeIcon
                                  icon={['fas', 'sticky-note']}
                                  className="text-yellow-500"
                                />
                              </button>
                              <HeadlessUI.Transition
                                as={Fragment}
                                show={showCustomerInfoTooltip}
                                enter="transition ease-out duration-200"
                                enterFrom="opacity-0"
                                enterTo="opacity-100"
                                leave="transition ease-in duration-150"
                                leaveFrom="opacity-100"
                                leaveTo="opacity-0"
                              >
                                <HeadlessUI.Popover.Panel
                                  static
                                  className="absolute left-1/2 top-0 z-10 -translate-x-1/2 translate-y-1/2 transform bg-yellow-50"
                                >
                                  <div className="w-auto whitespace-nowrap rounded-lg border p-4 shadow-lg">
                                    <Icon icon="info-circle" />
                                    &nbsp;Additional customer info
                                  </div>
                                </HeadlessUI.Popover.Panel>
                              </HeadlessUI.Transition>
                            </>
                          )}
                        </HeadlessUI.Popover>
                      </label>
                      <ShipToCombobox
                        value={shipTo}
                        onChange={handleShipToChange}
                        inputRef={shipToRef}
                        customer={customerDetail}
                        disabled={readonly}
                        hideLabel
                      />
                    </div>
                  </div>
                  <div className="col-start-1">
                    <div className="mt-1 rounded shadow-sm">
                      <Combobox
                        value={salesperson}
                        onChange={handleSalespersonChange}
                        label="Salesperson"
                        collection={salespeople}
                        filter={(q, s) => startsWith(s.name, q)}
                        disabled={readonly}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="mt-1">
                      <div className="mt-1 rounded shadow-sm">
                        <Combobox
                          value={shipVia}
                          onChange={handleShipViaChange}
                          label="Truck"
                          displayTextProp="description"
                          collection={shippingMethods}
                          filter={(q, s) => startsWith(s.description, q)}
                          disabled={readonly}
                          allowCustom
                        />
                      </div>
                    </div>
                  </div>
                  <div>
                    <label
                      htmlFor="box-code"
                      className="block text-sm font-medium text-gray-500"
                    >
                      Box Code
                    </label>
                    <div className="mt-1">
                      <input
                        type="text"
                        name="boxCode"
                        id="box-code"
                        autoComplete="off"
                        value={boxCode || ''}
                        onChange={handleBoxCodeChange}
                        disabled={readonly}
                        className="block w-full rounded-md border-gray-300 text-sm shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                  <div>
                    <label
                      htmlFor="customer-purchase-order-number"
                      className="block text-sm font-medium text-gray-500"
                    >
                      Customer PO
                    </label>
                    <div className="mt-1">
                      <input
                        type="text"
                        name="customerPurchaseOrderNumber"
                        id="customer-purchase-order-number"
                        maxLength={20}
                        autoComplete="off"
                        value={customerPurchaseOrderNumber || ''}
                        onChange={handleCustomerPurchaseOrderNumberChange}
                        disabled={readonly}
                        className="block w-full rounded-md border-gray-300 text-sm shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                  <div className="col-span-2">
                    <label
                      htmlFor="spire-notes"
                      className="block text-sm font-medium text-gray-500"
                    >
                      Shipment Details&nbsp;
                      <HeadlessUI.Popover className="relative inline-block">
                        <HeadlessUI.Popover.Button
                          className="btn-secondary mb-1 rounded-full px-1 py-0 text-sm text-blue-500 focus:ring-0"
                          tabIndex={-1}
                        >
                          <Icon icon="question-circle" />
                        </HeadlessUI.Popover.Button>
                        <HeadlessUI.Transition
                          as={Fragment}
                          enter="transition ease-out duration-200"
                          enterFrom="opacity-0 translate-y-1"
                          enterTo="opacity-100 translate-y-0"
                          leave="transition ease-in duration-150"
                          leaveFrom="opacity-100 translate-y-0"
                          leaveTo="opacity-0 translate-y-1"
                        >
                          <HeadlessUI.Popover.Panel className="absolute bottom-10 z-10 w-96 -translate-x-1/2 transform px-4">
                            <div className="rounded-lg bg-yellow-50 p-4 font-normal shadow-lg ring-1 ring-black ring-opacity-5">
                              Shipment Details will go into the Ship Sheet
                              Comments field on the User Defined tab in the
                              Spire Order. These notes will not be seen by the
                              Customer.
                            </div>
                          </HeadlessUI.Popover.Panel>
                        </HeadlessUI.Transition>
                      </HeadlessUI.Popover>
                    </label>
                    <textarea
                      rows={2}
                      name="spireNotes"
                      id="spire-notes"
                      value={spireNotes || ''}
                      onChange={handleSpireNotesChange}
                      disabled={readonly}
                      className="block w-full rounded-md border-gray-300 text-sm shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    />
                  </div>
                  <div className="col-span-2">
                    <div className="flex w-full justify-between">
                      <label
                        htmlFor="comments"
                        className="text-sm font-medium text-gray-500"
                      >
                        Internal Comments &nbsp;
                        <HeadlessUI.Popover className="relative inline-block">
                          <HeadlessUI.Popover.Button
                            className="btn-secondary mb-1 rounded-full px-1 py-0 text-sm text-blue-500 focus:ring-0"
                            tabIndex={-1}
                          >
                            <Icon icon="question-circle" />
                          </HeadlessUI.Popover.Button>
                          <HeadlessUI.Transition
                            as={Fragment}
                            enter="transition ease-out duration-200"
                            enterFrom="opacity-0 translate-y-1"
                            enterTo="opacity-100 translate-y-0"
                            leave="transition ease-in duration-150"
                            leaveFrom="opacity-100 translate-y-0"
                            leaveTo="opacity-0 translate-y-1"
                          >
                            <HeadlessUI.Popover.Panel className="absolute bottom-10 z-10 w-96 -translate-x-1/2 transform px-4">
                              <div className="rounded-lg bg-yellow-50 p-4 font-normal shadow-lg ring-1 ring-black ring-opacity-5">
                                Internal notes are Flora Pack notes that will
                                not be visible to the Customer or Growers.
                              </div>
                            </HeadlessUI.Popover.Panel>
                          </HeadlessUI.Transition>
                        </HeadlessUI.Popover>
                      </label>
                      {!readonly && !showNewComment && (
                        <button
                          type="button"
                          onClick={handleAddCommentsClick}
                          className="btn-secondary mb-1 border-green-600 px-2 py-1 text-xs text-green-600"
                        >
                          <Icon icon="comment-plus" />
                          &nbsp; Add Comment
                        </button>
                      )}
                    </div>
                    {showNewComment && (
                      <textarea
                        rows={2}
                        name="comments"
                        id="comments"
                        value={internalComments || ''}
                        onChange={handleInternalCommentsChange}
                        className="block w-full rounded-md border-gray-300 text-sm shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      />
                    )}
                    {!!comments.filter((c) => !c.isStandardComment).length && (
                      <div>
                        {comments
                          .filter((c) => !c.isStandardComment)
                          .map((comment) => (
                            <FutureOrderDetailPrebookComment
                              key={comment.id}
                              comment={comment}
                            />
                          ))}
                      </div>
                    )}
                  </div>
                  <div className="col-span-2">
                    <label
                      htmlFor="grower-item-notes"
                      className="block text-sm font-medium text-gray-500"
                    >
                      Grower Item Notes&nbsp;
                      <span className="font-normal text-gray-400">
                        (Prebook / PO)
                      </span>
                      &nbsp;
                      <HeadlessUI.Popover className="relative inline-block">
                        <HeadlessUI.Popover.Button
                          className="btn-secondary mb-1 rounded-full px-1 py-0 text-sm text-blue-500 focus:ring-0"
                          tabIndex={-1}
                        >
                          <Icon icon="question-circle" />
                        </HeadlessUI.Popover.Button>
                        <HeadlessUI.Transition
                          as={Fragment}
                          enter="transition ease-out duration-200"
                          enterFrom="opacity-0 translate-y-1"
                          enterTo="opacity-100 translate-y-0"
                          leave="transition ease-in duration-150"
                          leaveFrom="opacity-100 translate-y-0"
                          leaveTo="opacity-0 translate-y-1"
                        >
                          <HeadlessUI.Popover.Panel className="absolute bottom-10 z-10 w-96 -translate-x-1/2 transform px-4">
                            <div className="rounded-lg bg-yellow-50 p-4 font-normal shadow-lg ring-1 ring-black ring-opacity-5">
                              <p>
                                Grower Item notes will be included on{' '}
                                <span className="font-semibold italic">
                                  each item
                                </span>{' '}
                                of{' '}
                                <span className="font-semibold italic">
                                  each Prebook
                                </span>
                                , as well as the Spire PO.
                              </p>
                              <p>
                                Grower Item Notes{' '}
                                <span className="font-semibold italic">
                                  will not
                                </span>{' '}
                                be displayed on the Customer&apos;s Invoice or
                                Packing Slip.
                              </p>
                            </div>
                          </HeadlessUI.Popover.Panel>
                        </HeadlessUI.Transition>
                      </HeadlessUI.Popover>
                    </label>
                    <textarea
                      rows={2}
                      name="growerItemNotes"
                      id="grower-item-notes"
                      value={growerItemNotes || ''}
                      onChange={handleGrowerItemNotesChange}
                      disabled={readonly}
                      className="block w-full rounded-md border-gray-300 text-sm shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    />
                  </div>
                  <div className="row-span-2">
                    <div>
                      <label
                        htmlFor="standard-comments"
                        className="text-sm font-medium text-gray-500"
                      >
                        Standard Comments &nbsp;
                        <HeadlessUI.Popover className="relative inline-block">
                          <HeadlessUI.Popover.Button
                            className="btn-secondary mb-1 rounded-full px-1 py-0 text-sm text-blue-500 focus:ring-0"
                            tabIndex={-1}
                          >
                            <Icon icon="question-circle" />
                          </HeadlessUI.Popover.Button>
                          <HeadlessUI.Transition
                            as={Fragment}
                            enter="transition ease-out duration-200"
                            enterFrom="opacity-0 translate-y-1"
                            enterTo="opacity-100 translate-y-0"
                            leave="transition ease-in duration-150"
                            leaveFrom="opacity-100 translate-y-0"
                            leaveTo="opacity-0 translate-y-1"
                          >
                            <HeadlessUI.Popover.Panel className="absolute bottom-10 z-10 w-96 -translate-x-1/2 transform px-4">
                              <div className="rounded-lg bg-yellow-50 p-4 font-normal shadow-lg ring-1 ring-black ring-opacity-5">
                                Standard Comments will be included as line-items
                                on the Spire order.
                              </div>
                            </HeadlessUI.Popover.Panel>
                          </HeadlessUI.Transition>
                        </HeadlessUI.Popover>
                      </label>
                      {!readonly && (
                        <HeadlessUI.Menu as="div" className="relative block">
                          <HeadlessUI.Menu.Button className="btn-secondary mb-1 border-blue-600 px-2 py-1 text-xs text-blue-600">
                            <Icon icon="comment-alt-captions" />
                            &nbsp; Add Standard Comment&nbsp;
                            <Icon icon="chevron-down" />
                          </HeadlessUI.Menu.Button>
                          <HeadlessUI.Transition
                            as={Fragment}
                            enter="transition duration-100 ease-out"
                            enterFrom="transform scale-95 opacity-0"
                            enterTo="transform scale-100 opacity-100"
                            leave="transition duration-75 ease-out"
                            leaveFrom="transform scale-100 opacity-100"
                            leaveTo="transform scale-95 opacity-0"
                          >
                            <HeadlessUI.Menu.Items className="absolute right-0 z-40 mt-2 max-h-60 w-56 origin-top-right divide-y divide-gray-100 overflow-y-auto rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                              {(inventoryComments || []).map((comment) => (
                                <div className="px-1 py-1" key={comment.id}>
                                  <HeadlessUI.Menu.Item>
                                    {({ active }) => (
                                      <button
                                        type="button"
                                        className={`${
                                          active
                                            ? 'bg-blue-500 text-white'
                                            : 'text-gray-900'
                                        } group flex w-full flex-col items-start rounded-md px-2 py-1 text-sm`}
                                        onClick={() =>
                                          handleAddStandardComment(comment)
                                        }
                                      >
                                        <div>{comment.code}</div>
                                        <div className="truncate text-xs italic">
                                          {comment.description}
                                        </div>
                                      </button>
                                    )}
                                  </HeadlessUI.Menu.Item>
                                </div>
                              ))}
                            </HeadlessUI.Menu.Items>
                          </HeadlessUI.Transition>
                        </HeadlessUI.Menu>
                      )}
                    </div>
                    <div className="overflow-y-auto">
                      {comments
                        .filter((c) => c.isStandardComment)
                        .map((comment) => (
                          <div
                            key={comment.id}
                            className="border-b-2 text-xs text-gray-500"
                          >
                            {!readonly && (
                              <button
                                type="button"
                                className="btn-delete border-transparent px-2 py-1"
                                onClick={() =>
                                  handleDeleteStandardCommentClick(comment.id)
                                }
                              >
                                <Icon icon="trash" />
                              </button>
                            )}
                            {comment.comments}
                          </div>
                        ))}
                    </div>
                  </div>
                  <div>
                    <div>
                      <label
                        htmlFor="freight-per-case"
                        className="block text-xs font-medium text-gray-500"
                      >
                        Freight ($/case)
                      </label>
                      <div className="mt-1">
                        <input
                          type="text"
                          name="freightPerCase"
                          id="freight-per-case"
                          autoComplete="off"
                          value={freightPerCaseFormatted || ''}
                          onChange={handleFreightPerCaseChange}
                          onBlur={handleFreightPerCaseBlur}
                          onFocus={handleFocus}
                          disabled={readonly}
                          className="block w-full max-w-48 rounded-md border-gray-300 text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        />
                      </div>
                    </div>
                    <div>
                      <label
                        htmlFor="freight-per-case"
                        className="block text-xs font-medium text-gray-500"
                      >
                        Total Freight
                      </label>
                      <div className="mt-1">
                        <input
                          type="text"
                          name="freightPerLoad"
                          id="freight-per-load"
                          autoComplete="off"
                          value={freightPerLoadFormatted || ''}
                          onChange={handleFreightPerLoadChange}
                          onBlur={handleFreightPerLoadBlur}
                          onFocus={handleFocus}
                          disabled={readonly}
                          className="block w-full max-w-48 rounded-md border-gray-300 text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        />
                      </div>
                      <HeadlessUI.Switch.Group as="div" className="mt-1">
                        <HeadlessUI.Switch
                          checked={freightIsActual}
                          onChange={handleFreightIsActualChange}
                          disabled={readonly}
                          className={classNames(
                            freightIsActual ? 'bg-blue-400' : 'bg-gray-200',
                            'relative inline-flex h-4 w-8 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
                          )}
                        >
                          <span
                            aria-hidden="true"
                            className={classNames(
                              freightIsActual
                                ? 'translate-x-4'
                                : 'translate-x-0',
                              'pointer-events-none inline-block h-3 w-3 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                            )}
                          />
                        </HeadlessUI.Switch>
                        <HeadlessUI.Switch.Label className="ml-2">
                          <span className="text-xs">Actual Freight</span>
                        </HeadlessUI.Switch.Label>
                      </HeadlessUI.Switch.Group>
                    </div>
                  </div>
                </div>
                <div className="!mt-0">
                  <HeadlessUI.Tab.Group>
                    <HeadlessUI.Tab.List
                      as="nav"
                      className="-mb-px flex space-x-8"
                    >
                      <HeadlessUI.Tab as={Fragment}>
                        {({ selected }) => (
                          <button
                            type="button"
                            className={classNames(
                              'whitespace-nowrap border-b-2 px-1 py-2 text-xl font-medium',
                              selected
                                ? 'border-blue-500 text-blue-600'
                                : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                            )}
                          >
                            Items
                          </button>
                        )}
                      </HeadlessUI.Tab>
                      <HeadlessUI.Tab as={Fragment}>
                        {({ selected }) => (
                          <button
                            type="button"
                            className={classNames(
                              'whitespace-nowrap border-b-2 px-1 py-2 text-xl font-medium',
                              selected
                                ? 'border-blue-500 text-blue-600'
                                : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                            )}
                          >
                            Prebooks
                            {!!dirty.length && (
                              <Icon
                                icon="exclamation-triangle"
                                title="One or more Prebooks do not match the Future Order"
                                className="-my-1 ml-4 mr-2 font-bold text-red-500"
                              />
                            )}
                          </button>
                        )}
                      </HeadlessUI.Tab>
                    </HeadlessUI.Tab.List>
                    <HeadlessUI.Tab.Panels>
                      <HeadlessUI.Tab.Panel>
                        <FutureOrderDetailItems save={save} />
                      </HeadlessUI.Tab.Panel>
                      <HeadlessUI.Tab.Panel>
                        <FutureOrderDetailPrebooks />
                      </HeadlessUI.Tab.Panel>
                    </HeadlessUI.Tab.Panels>
                  </HeadlessUI.Tab.Group>
                </div>
              </div>
            </form>
          )}
        </div>
      </main>
      <CreateSeason
        open={showAddSeasonDialog}
        cancel={handleAddSeasonCancel}
        confirm={handleAddSeasonConfirm}
      />
      <HeadlessUI.Transition.Root show={showSendToSpireDialog} as={Fragment}>
        <HeadlessUI.Dialog
          as="div"
          className="relative z-40"
          onClose={handleSendToSpireCancel}
        >
          <HeadlessUI.Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
          </HeadlessUI.Transition.Child>

          <div className="fixed inset-0 z-40 overflow-y-auto">
            <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
              <HeadlessUI.Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                enterTo="opacity-100 translate-y-0 sm:scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              >
                <HeadlessUI.Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg">
                  <div className="bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                    <div className="sm:flex sm:items-start">
                      <div
                        className={classNames(
                          'mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full sm:mx-0 sm:h-10 sm:w-10',
                          futureOrder?.spireSalesOrderId
                            ? `bg-red-100`
                            : 'bg-blue-100'
                        )}
                      >
                        <Icon
                          icon="question-circle"
                          className={classNames(
                            'h-6 w-6',
                            futureOrder?.spireSalesOrderId
                              ? `text-red-600`
                              : `text-blue-600`
                          )}
                          aria-hidden="true"
                        />
                      </div>
                      <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                        <HeadlessUI.Dialog.Title
                          as="h3"
                          className="text-lg font-medium leading-6 text-gray-900"
                        >
                          Send to Spire
                        </HeadlessUI.Dialog.Title>
                        <div className="mt-2">
                          <p className="text-sm text-gray-500">{`This will create a Sales Order in Spire. ${
                            futureOrder?.spireSalesOrderId
                              ? 'NOTE: This order was already sent to Spire. THIS WILL DELETE SPIRE ORDER NUMBER ' +
                                futureOrder.spireSalesOrderNumber +
                                '.'
                              : ''
                          } Would you like to continue?`}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                    <button
                      type="button"
                      className={classNames(
                        'ml-2',
                        futureOrder?.spireSalesOrderId
                          ? 'btn-danger'
                          : 'btn-primary'
                      )}
                      onClick={handleSendToSpireConfirm}
                      disabled={saving}
                    >
                      Yes
                    </button>
                    <button
                      type="button"
                      className="btn-secondary"
                      onClick={handleSendToSpireCancel}
                      disabled={saving}
                    >
                      No
                    </button>
                  </div>
                </HeadlessUI.Dialog.Panel>
              </HeadlessUI.Transition.Child>
            </div>
          </div>
        </HeadlessUI.Dialog>
      </HeadlessUI.Transition.Root>

      {showEmails && (
        <>
          {!!emailPrebooks.length && (
            <FutureOrderPrebookEmails
              close={handleShowEmailsClose}
              prebooks={emailPrebooks}
            />
          )}
          {!emailPrebooks.length && (
            <Alert
              open={true}
              confirm={handleShowEmailsClose}
              colour="info"
              title="No Changed Prebooks"
              message="None of the Prebooks for this Future Order have been changed, and all have been sent to the Growers."
            />
          )}
        </>
      )}
      <CustomerConfirmationEmail
        open={showCustomerConfirmationEmail}
        close={() => setShowCustomerConfirmationEmail(false)}
      />
      {confirmSpireOrderClose}

      <CustomerInfoDialog
        open={showCustomerInfoDialog}
        customerInfo={customerInfo}
        close={handleCustomerInfoDialogClose}
        save={handleCustomerInfoDialogSave}
      />
      <HeadlessUI.Transition.Root
        show={!!showConfirmPriceWarnings}
        as={Fragment}
      >
        <HeadlessUI.Dialog
          as="div"
          className="relative z-40"
          onClose={handlePriceWarningCancel}
        >
          <HeadlessUI.Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
          </HeadlessUI.Transition.Child>

          <div className="fixed inset-0 z-40 overflow-y-auto">
            <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
              <HeadlessUI.Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                enterTo="opacity-100 translate-y-0 sm:scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              >
                <HeadlessUI.Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg">
                  <div className="bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                    <div className="sm:flex sm:items-start">
                      <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                        <Icon
                          icon="exclamation-triangle"
                          className="h-6 w-6 text-red-600"
                        />
                      </div>
                      <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                        <HeadlessUI.Dialog.Title
                          as="h3"
                          className="text-lg font-medium leading-6 text-gray-900"
                        >
                          Price Warning
                        </HeadlessUI.Dialog.Title>
                        <div className="mt-2">
                          <p className="whitespace-pre-wrap text-sm text-gray-500">
                            {showConfirmPriceWarnings}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                    <button
                      type="button"
                      className="focus:ring-blue-red-500 inline-flex w-full justify-center rounded-md border border-transparent bg-red-500 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm"
                      onClick={handlePriceWarningConfirm}
                    >
                      Yes
                    </button>
                    <button
                      type="button"
                      className="mt-3 inline-flex w-auto justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 sm:ml-3 sm:mt-0"
                      onClick={handlePriceWarningCancel}
                    >
                      No
                    </button>
                  </div>
                </HeadlessUI.Dialog.Panel>
              </HeadlessUI.Transition.Child>
            </div>
          </div>
        </HeadlessUI.Dialog>
      </HeadlessUI.Transition.Root>
      <OverrideAllUPCsDialog
        open={showOverrideUpcsDialog}
        onClose={handleOverrideUpcsClose}
      />
      {verifyMismatchedBoxCode}
      {verifyBlankBoxCode}
    </div>
  );
}
