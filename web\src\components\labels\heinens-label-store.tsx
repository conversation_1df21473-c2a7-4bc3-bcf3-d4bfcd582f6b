import * as models from 'api/models/labels';

interface HeinensLabelStoreProps {
  store: models.HeinensLabelStore;
}

export function HeinensLabelStore({ store }: HeinensLabelStoreProps) {
  const cutsCount = store.orders
      .filter((o) => o.type === 'Cuts')
      .reduce((total, l) => total + l.quantity, 0),
    pottedCount = store.orders
      .filter((o) => o.type === 'Potted')
      .reduce((total, l) => total + l.quantity, 0);

  return (
    <div className="m-2 rounded border p-2">
      <h3 className="text-lg font-medium text-slate-900">
        Store {store.storeNumber}:{' '}
      </h3>
      {!!cutsCount && (
        <h4 className="text-sm font-normal italic text-slate-500">
          Cuts Labels: &nbsp;{cutsCount}
        </h4>
      )}
      {!!pottedCount && (
        <h4 className="text-sm font-normal italic text-slate-500">
          Potted Labels: &nbsp;{pottedCount}
        </h4>
      )}
      <ul className="mt-2">
        {store.orders.map((order) => (
          <li
            key={order.description}
            className="border-bottom flex flex-col p-2 text-sm"
          >
            <div className="text-base font-semibold">
              {order.description} (pack {order.packQuantity})
            </div>
            <div>
              <span className="font-semibold">SKU: </span>
              {order.productNumber}
            </div>
            <div>
              <span className="font-semibold">UPC: </span>
              {order.upc}
            </div>
            <div>
              <span className="font-semibold">Type: </span>
              {order.type}
            </div>
            <div className="italic">
              {order.quantity} case
              {order.quantity === 1 ? '' : 's'}
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
}
