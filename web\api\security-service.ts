import { createApi } from '@reduxjs/toolkit/query/react';
import { ApiBase, axiosBaseQuery } from './api-base';
import * as security from './models/security';

class SecurityService extends ApiBase {
  data(): Promise<SecurityDataResponse> {
    return this.get('security');
  }

  updateGroupUsers(groupUsers: security.GroupUser[]): Promise<void> {
    return this.post('security/group-users', { groupUsers });
  }
}

export const securityQueryApi = createApi({
  reducerPath: 'security-query-api',
  baseQuery: axiosBaseQuery('security/'),
  endpoints: (builder) => ({
    me: builder.query<MeResponse, void>({
      query: () => ({
        url: 'me',
      }),
    }),
  }),
});

export interface SecurityDataResponse {
  groups: security.Group[];
  groupUsers: security.GroupUser[];
  users: string[];
}

export interface MeResponse {
  groups: security.Groups[];
}

export const { useMeQuery } = securityQueryApi;

export const securityApi = new SecurityService();
