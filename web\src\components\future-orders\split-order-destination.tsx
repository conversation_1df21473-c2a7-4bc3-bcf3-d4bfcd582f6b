import * as Headless<PERSON> from '@headlessui/react';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { classNames } from '@/utils/class-names';
import { selectDestinationType, setDestinationType } from './split-order-slice';
import { SplitOrderDestinationNew } from './split-order-destination-new';
import { SplitOrderDestinationExisting } from './split-order-destination-existing';

interface SplitOrderDestinationProps {
  onClose: () => void;
}

export function SplitOrderDestination({ onClose }: SplitOrderDestinationProps) {
  const dispatch = useAppDispatch(),
    destinationType = useAppSelector(selectDestinationType);

  const handleTypeChange = (value: 'new' | 'existing') => {
    dispatch(setDestinationType(value));
  };

  return (
    <div className="mx-8 flex flex-grow flex-row overflow-y-auto">
      <HeadlessUI.RadioGroup
        value={destinationType}
        onChange={handleTypeChange}
        className="mx-4 mt-4 flex flex-col space-y-4"
      >
        <HeadlessUI.RadioGroup.Option
          value="new"
          className={({ active }) =>
            classNames(
              'relative block cursor-pointer rounded-lg border bg-white px-6 py-4 shadow-sm focus:outline-none',
              active ? 'border-blue-600 ring-2 ring-blue-600' : '',
              'border-gray-300'
            )
          }
        >
          {({ active, checked }) => (
            <>
              <span className="flex items-center">
                <span className="flex flex-col text-sm">
                  <HeadlessUI.RadioGroup.Label
                    as="span"
                    className="text-xl font-medium text-gray-900"
                  >
                    New
                  </HeadlessUI.RadioGroup.Label>
                  <HeadlessUI.RadioGroup.Description
                    as="span"
                    className="text-gray-500"
                  >
                    <span className="block sm:inline">
                      Move items to a new Future Order.
                    </span>
                  </HeadlessUI.RadioGroup.Description>
                </span>
              </span>
              <span
                className={classNames(
                  active ? 'border' : 'border-2',
                  checked ? 'border-blue-600' : 'border-transparent',
                  'pointer-events-none absolute -inset-px rounded-lg'
                )}
                aria-hidden="true"
              />
            </>
          )}
        </HeadlessUI.RadioGroup.Option>
        <HeadlessUI.RadioGroup.Option
          value="existing"
          className={({ active }) =>
            classNames(
              'relative block cursor-pointer rounded-lg border bg-white px-6 py-4 shadow-sm focus:outline-none',
              active ? 'border-blue-600 ring-2 ring-blue-600' : '',
              'border-gray-300'
            )
          }
        >
          {({ active, checked }) => (
            <>
              <span className="flex items-center">
                <span className="flex flex-col text-sm">
                  <HeadlessUI.RadioGroup.Label
                    as="span"
                    className="text-xl font-medium text-gray-900"
                  >
                    Existing
                  </HeadlessUI.RadioGroup.Label>
                  <HeadlessUI.RadioGroup.Description
                    as="span"
                    className="text-gray-500"
                  >
                    <span className="block sm:inline">
                      Move items to an existing Future Order.
                    </span>
                  </HeadlessUI.RadioGroup.Description>
                </span>
              </span>
              <span
                className={classNames(
                  active ? 'border' : 'border-2',
                  checked ? 'border-blue-600' : 'border-transparent',
                  'pointer-events-none absolute -inset-px rounded-lg'
                )}
                aria-hidden="true"
              />
            </>
          )}
        </HeadlessUI.RadioGroup.Option>
      </HeadlessUI.RadioGroup>
      {destinationType === 'new' && (
        <SplitOrderDestinationNew onClose={onClose} />
      )}
      {destinationType === 'existing' && (
        <SplitOrderDestinationExisting onClose={onClose} />
      )}
    </div>
  );
}
