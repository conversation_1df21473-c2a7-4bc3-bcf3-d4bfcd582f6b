import Link from 'next/link';
import { useRouter } from 'next/router';
import * as HeadlessUI from '@headlessui/react';
import { routes } from '@/services/routes';
import { classNames } from '@/utils/class-names';
import { equals } from '@/utils/equals';

type NavMenuItemMobileProps = {
  text: string;
  href: string;
};

export function NavMenuItemMobile({ text, href }: NavMenuItemMobileProps) {
  const { pathname } = useRouter(),
    isCurrent = equals(href, pathname);
  //href.length === 1 ? href === pathname : pathname.indexOf(href) === 0;

  return (
    <HeadlessUI.Disclosure.Button
      as="a"
      href={href}
      className={classNames(
        'block border-l-4 py-2 pl-3 pr-4 text-base font-medium',
        isCurrent
          ? 'border-blue-500 bg-blue-50 text-blue-500'
          : 'border-transparent text-gray-500'
      )}
    >
      {text}
    </HeadlessUI.Disclosure.Button>
  );
}
