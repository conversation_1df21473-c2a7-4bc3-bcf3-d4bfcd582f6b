import Link from 'next/link';
import { useRouter } from 'next/router';
import { classNames } from '@/utils/class-names';

type NavMenuItemProps = {
  text: string;
  href: string;
};

export function NavMenuItem({ text, href }: NavMenuItemProps) {
  const { pathname } = useRouter(),
    isCurrent =
      href.length === 1 ? href === pathname : pathname.indexOf(href) === 0;

  return (
    <Link
      href={href}
      className={classNames(
        isCurrent
          ? 'border-blue-500 text-gray-900'
          : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700',
        'inline-flex items-center border-b-2 px-1 pt-1 font-medium hover:no-underline'
      )}
      aria-current={isCurrent ? 'page' : undefined}
    >
      {text}
    </Link>
  );
}
