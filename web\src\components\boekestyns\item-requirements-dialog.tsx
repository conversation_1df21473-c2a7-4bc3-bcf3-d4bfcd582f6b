import { useState, Fragment, useEffect } from 'react';
import { DateTime } from 'luxon';
import * as HeadlessUI from '@headlessui/react';
import {
  useItemRequirementsListQuery,
  boekestynApi,
} from 'api/boekestyn-service';
import { formatDate } from '@/utils/format';
import { Icon } from '@/components/icon';
import { Loading } from '@/components/loading';
import * as models from 'api/models/boekestyns';
import { ItemRequirementsRow } from './item-requirements-row';
import { ItemRequirementsFooter } from './item-requirements-footer';

interface ItemRequirementsDialogProps {
  open: boolean;
  onClose: () => void;
}

export function ItemRequirementsDialog({
  open,
  onClose,
}: ItemRequirementsDialogProps) {
  const thisMonday = DateTime.now().startOf('week').toFormat('yyyy-MM-dd'),
    thisFriday = DateTime.now()
      .endOf('week')
      .minus({ days: 2 })
      .toFormat('yyyy-MM-dd'),
    [startDate, setStartDate] = useState(thisMonday),
    [endDate, setEndDate] = useState(thisFriday),
    [sortDate, setSortDate] = useState(''),
    [items, setItems] = useState<string[]>([]),
    [dates, setDates] = useState<string[]>([]),
    [itemDateMap, setItemDateMap] = useState<Map<string, number>>(new Map()),
    [itemDetails, setItemDetails] = useState<
      Map<string, models.ItemListRequirementItem[]>
    >(new Map());

  // Reset date range and expanded items when dialog opens or closes
  useEffect(() => {
    if (open) {
      setStartDate(thisMonday);
      setEndDate(thisFriday);
    }
  }, [open, thisMonday, thisFriday]);

  const { data, isLoading } = useItemRequirementsListQuery({
    startDate: startDate,
    endDate: endDate,
  });

  const handleStartDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setStartDate(e.target.value);
  };

  const handleEndDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEndDate(e.target.value);
  };

  const handleDownloadClick = async () => {
    if (data) {
      try {
        await boekestynApi.boekestynItemRequirementsDownload(
          { items: data.items },
          startDate,
          endDate
        );
      } catch (error) {
        console.error('Download failed:', error);
      }
    }
  };

  useEffect(() => {
    if (data?.items && data.items.length > 0) {
      const uniqueItems = Array.from(
          new Set(
            data.items.map(
              (item: models.ItemListRequirementItem) => item.product
            )
          )
        ).sort(),
        uniqueDates = Array.from(
          new Set(
            data.items.map(
              (item: models.ItemListRequirementItem) => item.requiredDate
            )
          )
        ).sort(),
        itemDateMap = new Map<string, number>(),
        // Group items by product for detailed view
        itemDetailsMap = new Map<string, models.ItemListRequirementItem[]>();

      data.items.forEach((item: models.ItemListRequirementItem) => {
        const key = `${item.product}_${item.requiredDate}`,
          productKey = item.product;
        if (!itemDateMap.has(key)) {
          itemDateMap.set(key, 0);
        }
        itemDateMap.set(key, (itemDateMap.get(key) ?? 0) + item.orderQuantity);

        if (!itemDetailsMap.has(productKey)) {
          itemDetailsMap.set(productKey, []);
        }
        itemDetailsMap.get(productKey)?.push(item);
      });

      if (sortDate) {
        uniqueItems.sort((a, b) => {
          const keyA = `${a}_${sortDate}`;
          const keyB = `${b}_${sortDate}`;
          return (itemDateMap.get(keyB) || 0) - (itemDateMap.get(keyA) || 0);
        });
      } else {
        uniqueItems.sort();
      }

      setItems(uniqueItems);
      setDates(uniqueDates);
      setItemDateMap(itemDateMap);
      setItemDetails(itemDetailsMap);
    }
  }, [data, sortDate]);

  return (
    <HeadlessUI.Transition.Root show={open} as={Fragment}>
      <HeadlessUI.Dialog as="div" className="relative z-10" onClose={onClose}>
        <HeadlessUI.Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </HeadlessUI.Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center sm:p-0">
            <HeadlessUI.Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <HeadlessUI.Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-4xl sm:p-6">
                <div>
                  <HeadlessUI.Dialog.Title
                    as="h3"
                    className="text-lg font-medium leading-6 text-gray-900"
                  >
                    Item Requirements
                  </HeadlessUI.Dialog.Title>

                  <div className="mt-4 flex items-center space-x-4">
                    <div>
                      <label
                        htmlFor="start-date"
                        className="block text-sm font-medium text-gray-700"
                      >
                        Start Date
                      </label>
                      <input
                        type="date"
                        id="start-date"
                        name="start-date"
                        value={startDate || ''}
                        onChange={handleStartDateChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      />
                    </div>
                    <div>
                      <label
                        htmlFor="end-date"
                        className="block text-sm font-medium text-gray-700"
                      >
                        End Date
                      </label>
                      <input
                        type="date"
                        id="end-date"
                        name="end-date"
                        value={endDate || ''}
                        onChange={handleEndDateChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      />
                    </div>
                    <div>
                      <label className="invisible block text-sm font-medium text-gray-700">
                        &nbsp;
                      </label>
                      <button
                        type="button"
                        className="btn-secondary"
                        onClick={handleDownloadClick}
                      >
                        <Icon icon="file-excel" />
                        &nbsp;Download
                      </button>
                    </div>
                  </div>

                  <div className="mt-4">
                    {isLoading && <Loading />}

                    {!isLoading && (
                      <div className="mt-4 max-h-96 overflow-auto">
                        <table className="min-w-full divide-y divide-gray-300">
                          <thead className="sticky top-0 z-10 bg-gray-50">
                            <tr>
                              <th
                                scope="col"
                                className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 hover:cursor-pointer hover:bg-gray-100 hover:text-blue-500 hover:underline"
                                onClick={() => {
                                  setSortDate('');
                                }}
                              >
                                Item
                              </th>
                              {dates.map((date) => (
                                <th
                                  key={date}
                                  scope="col"
                                  className="px-3 py-3.5 text-center text-sm font-semibold text-gray-900 hover:cursor-pointer hover:bg-gray-100 hover:text-blue-500 hover:underline"
                                  onClick={() => {
                                    setSortDate(date);
                                  }}
                                >
                                  {formatDate(date, 'MMM d')}
                                </th>
                              ))}
                              <th
                                scope="col"
                                className="px-3 py-3.5 text-center text-sm font-semibold text-gray-900"
                              >
                                Total
                              </th>
                            </tr>
                          </thead>
                          <tbody className="divide-y divide-gray-200 bg-white">
                            {items.map((item) => (
                              <ItemRequirementsRow
                                key={item}
                                item={item}
                                dates={dates}
                                items={items}
                                itemDateMap={itemDateMap}
                                itemDetails={itemDetails}
                              />
                            ))}
                          </tbody>
                          <ItemRequirementsFooter
                            dates={dates}
                            items={items}
                            itemDateMap={itemDateMap}
                          />
                        </table>
                      </div>
                    )}
                  </div>
                </div>

                <div className="mt-5 text-right sm:mt-6">
                  <button
                    type="button"
                    className="btn-secondary"
                    onClick={onClose}
                  >
                    Close
                  </button>
                </div>
              </HeadlessUI.Dialog.Panel>
            </HeadlessUI.Transition.Child>
          </div>
        </div>
      </HeadlessUI.Dialog>
    </HeadlessUI.Transition.Root>
  );
}
