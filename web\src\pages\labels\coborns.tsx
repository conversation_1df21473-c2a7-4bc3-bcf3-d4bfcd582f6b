import React, { useEffect, useState } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { labelApi } from 'api/label-service';
import { routes } from '@/services/routes';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import { CobornsLabels, read } from '@/components/labels/coborns-excel-reader';
import { ProblemDetails } from '@/utils/problem-details';

export default function Labels() {
  const [labels, setLabels] = useState<CobornsLabels | null>(null),
    [error, setError] = useState<ProblemDetails>(),
    [printing, setPrinting] = useState(false),
    labelCount = labels?.stores.reduce(
      (total, store) =>
        total + store.orders.reduce((t, o) => t + o.quantity, 0),
      0
    );

  useEffect(() => {
    setLabels(null);
  }, []);

  const handleLabelDragEnter = (e: React.DragEvent<HTMLLabelElement>) => {
    (e.target as HTMLElement).classList.add('border-green-700');
  };

  const handleLabelDragLeave = (e: React.DragEvent<HTMLLabelElement>) => {
    (e.target as HTMLElement).classList.remove('border-green-700');
  };

  const handleLabelDrop = (e: React.DragEvent<HTMLLabelElement>) => {
    e.preventDefault();
    const file = e.dataTransfer.files?.[0];

    if (file) {
      readFile(file);
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (file) {
      await readFile(file);
    }
  };

  const readFile = async (file: File) => {
    var data = await file.arrayBuffer(),
      labels = read(data);

    setLabels(labels);
  };

  const handleClearErrorClick = () => {
    setError(undefined);
  };

  const handleRequiredDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (labels) {
      const requiredDate = e.target.value,
        updated = { ...labels, requiredDate };
      setLabels(updated);
    }
  };

  const handlePurchaseOrderNumberChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (labels) {
      const purchaseOrderNumber = e.target.value,
        updated = { ...labels, purchaseOrderNumber };
      setLabels(updated);
    }
  };

  const handleResetClick = () => {
    setLabels(null);
  };

  const handlePrintClick = () => {
    if (labels) {
      try {
        setPrinting(true);
        labelApi.coborns(labels).finally(() => setPrinting(false));
      } catch (e) {
        setError(e as ProblemDetails);
      }
    }
  };

  return (
    <>
      <Head>
        <title>Labels: Coborn&apos;s</title>
      </Head>
      <header className="sticky top-0 z-20 flex-wrap bg-white shadow">
        <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 md:flex md:items-center md:justify-between lg:px-8">
          <h2 className="pointer-events-none text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight md:flex-shrink-0">
            <Icon icon="barcode" />
            &nbsp; Coborn&apos;s Labels
          </h2>
          <div className="flex">
            <Link
              href={routes.labels.list.to()}
              className="btn-secondary inline-flex"
            >
              Close
            </Link>
            {!!labels && (
              <>
                <button
                  className="btn-secondary ml-8"
                  onClick={handleResetClick}
                >
                  Choose New File &nbsp;
                  <Icon icon="undo" />
                </button>
                <button
                  className="btn-primary ml-2"
                  onClick={handlePrintClick}
                  disabled={printing}
                >
                  Print Labels &nbsp;
                  <Icon icon={printing ? 'spinner' : 'print'} spin={printing} />
                </button>
              </>
            )}
          </div>
        </div>
      </header>
      <main className="flex-grow overflow-auto">
        <div className="mx-auto max-w-7xl px-8 py-6">
          <Error error={error} clear={handleClearErrorClick} />
          <form className="flex w-full justify-center">
            {!labels && (
              <div>
                <label
                  className="group/upload mx-auto mt-1 flex h-32 w-56 cursor-pointer items-center justify-center rounded-md border-2 border-dashed border-gray-300 hover:border-green-700"
                  htmlFor="label-file"
                  onDragEnter={handleLabelDragEnter}
                  onDragLeave={handleLabelDragLeave}
                  onDragOver={(e) => e.preventDefault()}
                  onDrop={handleLabelDrop}
                >
                  <div className="space-y-1 text-center text-gray-500 group-hover/upload:text-green-700">
                    <Icon icon="file-upload" className="mx-auto" size="2x" />
                    <div className="flex text-sm  focus-within:outline-none ">
                      <span>Upload a file</span>
                      <input
                        id="label-file"
                        name="label-file"
                        type="file"
                        className="sr-only"
                        onChange={handleFileChange}
                      />
                    </div>
                  </div>
                </label>
                <div className="mx-auto mt-8 w-96 text-sm text-gray-500">
                  <h3 className="text-lg font-bold text-gray-700">
                    Label File Notes:
                  </h3>
                  <h4 className="text-md font-semibold">PO #</h4>
                  <p className="mb-2">
                    The program looks for a numeric PO # at the end of cell A2.
                  </p>
                  <h4 className="text-md font-semibold">Required Date</h4>
                  <p className="mb-2">
                    The program looks for the Required Date in cell C4, in the
                    format{' '}
                    <span className="italic text-gray-900">
                      &hellip;mmm d, yyyy Delivery
                    </span>
                  </p>
                  <h4 className="text-md font-semibold">Stores</h4>
                  <p className="mb-2">
                    The Store Numbers are expected to be numeric values in Row
                    6, starting in Column G. The store names are expected in Row
                    7.
                  </p>
                  <h4 className="text-md font-semibold">Items</h4>
                  <p className="mb-2">
                    Items are expected beginning in Row 8, with the Customer
                    Item Code in Column A, UPC in Column B, Description in
                    Column C, Size in Column D, Pack Quantity in Column E, and
                    Retail (unused) in Column F.
                  </p>
                  <h4 className="text-md font-semibold">Quantities</h4>
                  <p className="mb-2">
                    The Quantities are expected under the Store Numbers,
                    starting in cell G8.
                  </p>
                </div>
              </div>
            )}
            {!!labels && (
              <div className="grid w-full grid-cols-3">
                <div className="col-span-3 flex flex-grow justify-center space-x-8">
                  <div>
                    <label
                      htmlFor="required-date"
                      className="block text-xs italic text-gray-500"
                    >
                      Required Date
                    </label>
                    <input
                      id="required-date"
                      type="date"
                      max="2050-01-01"
                      value={labels.requiredDate}
                      onChange={handleRequiredDateChange}
                      className="block w-full rounded-md border-gray-300 text-sm shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label
                      htmlFor="purchase-order-number"
                      className="block text-xs italic text-gray-500"
                    >
                      PO #
                    </label>
                    <input
                      id="purchase-order-number"
                      type="text"
                      value={labels.purchaseOrderNumber}
                      onChange={handlePurchaseOrderNumberChange}
                      className="block w-full rounded-md border-gray-300 text-sm shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    />
                  </div>
                </div>
                <div className="col-span-3 flex flex-grow justify-center space-x-8">
                  <h4 className="px-4 py-2 text-xl font-medium text-gray-500">
                    Total: {labelCount} Labels
                  </h4>
                </div>
                {labels.stores.map((store) => (
                  <div
                    key={store.storeNumber}
                    className="m-2 rounded border p-2"
                  >
                    <h3 className="text-lg font-medium text-slate-900">
                      Store #{store.storeNumber}:{' '}
                      <span className="text-sm font-normal italic text-slate-500">
                        {store.orders.reduce(
                          (total, l) => total + l.quantity,
                          0
                        )}{' '}
                        Labels
                      </span>
                    </h3>
                    <h4 className="text-lg font-normal italic text-slate-500">
                      {store.name}
                    </h4>
                    <ul className="mt-2">
                      {store.orders.map((order) => (
                        <li
                          key={order.description}
                          className="border-bottom flex flex-col p-2 text-sm"
                        >
                          <div className="text-base font-semibold">
                            {order.description} (pack {order.packQuantity})
                          </div>
                          <div>
                            <span className="font-semibold">SKU: </span>
                            {order.productNumber}
                          </div>
                          <div>
                            <span className="font-semibold">UPC: </span>
                            {order.upc}
                          </div>
                          <div>
                            <span className="font-semibold">Retail:</span>{' '}
                            {order.retail}
                          </div>
                          <div className="italic">
                            {order.quantity} case
                            {order.quantity === 1 ? '' : 's'}
                          </div>
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            )}
          </form>
        </div>
      </main>
    </>
  );
}
