import { Fragment, useRef } from 'react';
import { IconName } from '@fortawesome/fontawesome-common-types';
import { Dialog, Transition } from '@headlessui/react';
import { Icon } from './icon';
import { classNames } from '@/utils/class-names';

export type AlertColours = 'danger' | 'info';

export interface AlertProps {
  title: string;
  message: string;
  confirmButtonText?: string;
  cancelButtonText?: string;
  colour: AlertColours;
  icon?: IconName;
  open: boolean;
  cancel?: () => void;
  confirm: () => void;
}

export function Alert({
  title,
  message,
  confirmButtonText,
  cancelButtonText,
  colour,
  icon,
  open,
  cancel,
  confirm,
}: AlertProps) {
  const confirmButtonRef = useRef(null);

  return (
    <Transition.Root show={open} as={Fragment}>
      <Dialog
        as="div"
        className="relative z-40"
        initialFocus={confirmButtonRef}
        onClose={cancel || confirm}
      >
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-40 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg">
                <div className="bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                  <div className="sm:flex sm:items-start">
                    <div
                      className={classNames(
                        'mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full sm:mx-0 sm:h-10 sm:w-10',
                        colour === 'danger' && `bg-red-100`,
                        colour === 'info' && 'bg-blue-100'
                      )}
                    >
                      <Icon
                        icon={icon || 'exclamation-triangle'}
                        className={classNames(
                          'h-6 w-6',
                          colour === 'danger' && `text-red-600`,
                          colour === 'info' && `text-blue-600`
                        )}
                        aria-hidden="true"
                      />
                    </div>
                    <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                      <Dialog.Title
                        as="h3"
                        className="text-lg font-medium leading-6 text-gray-900"
                      >
                        {title}
                      </Dialog.Title>
                      <div className="mt-2">
                        <p className="text-sm text-gray-500">{message}</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                  <button
                    type="button"
                    className={classNames(
                      'inline-flex w-full justify-center rounded-md border border-transparent px-4 py-2 text-base font-medium text-white shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm',
                      colour === 'danger' &&
                        `bg-red-500 hover:bg-red-700 focus:ring-blue-500`,
                      colour === 'info' &&
                        `bg-blue-500 hover:bg-blue-700 focus:ring-blue-500`
                    )}
                    onClick={confirm}
                    ref={confirmButtonRef}
                  >
                    {confirmButtonText || 'OK'}
                  </button>
                  {!!cancel && (
                    <button
                      type="button"
                      className="mt-3 inline-flex w-auto justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 sm:ml-3 sm:mt-0"
                      onClick={cancel}
                    >
                      {cancelButtonText || 'Cancel'}
                    </button>
                  )}
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
}
