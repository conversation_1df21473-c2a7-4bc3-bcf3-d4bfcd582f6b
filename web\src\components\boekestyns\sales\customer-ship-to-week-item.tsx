import React, { Fragment, useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import * as HeadlessUI from '@headlessui/react';
import { routes } from '@/services/routes';
import * as boeks from 'api/models/boekestyns';
import { useAppSelector } from '@/services/hooks';
import { Alert } from '@/components/alert';
import { Icon } from '@/components/icon';
import { parsePackQuantity } from '@/components/future-orders/item-functions';
import { classNames } from '@/utils/class-names';
import { formatNumber } from '@/utils/format';
import { selectPlants } from './sales-slice';
import { boekCaseQuantity } from './boekestyn-sales-functions';

interface CustomerShiptoWeekItemProps {
  item: boeks.BoekestynPrebookItem;
}

export function CustomerShiptoWeekItem({ item }: CustomerShiptoWeekItemProps) {
  const router = useRouter(),
    plants = useAppSelector(selectPlants),
    [showFutureOrderWarning, setShowFutureOrderWarning] = useState(false),
    [showPackQuantityTooltip, setShowPackQuantityTooltip] = useState(false),
    [
      showQuantityPerFinishedItemTooltip,
      setShowQuantityPerFinishedItemTooltip,
    ] = useState(false),
    [showMultipleProductsTooltip, setShowMultipleProductsTooltip] =
      useState(false),
    floraPackPackQuantity = parsePackQuantity(item.description) || 1,
    plant = plants?.find((p) => p._id === item.boekestynPlantId),
    boekestynPackQuantity = plant?.potsPerCase || 1,
    quantityPerFinishedItem = item.quantityPerFinishedItem || 1,
    quantity = boekCaseQuantity(plants, item),
    hasNote =
      quantityPerFinishedItem !== 1 ||
      floraPackPackQuantity !== boekestynPackQuantity ||
      item.multipleProducts;

  const handlePrebookClick = (e: React.MouseEvent) => {
    if (item.futureOrderId) {
      e.preventDefault();
      setShowFutureOrderWarning(true);
    }
  };

  const handleEditPrebook = () => {
    if (showFutureOrderWarning)
      router.push(routes.prebooks.detail.to(item.prebookId));
  };

  const handleEditFutureOrder = () => {
    if (item.futureOrderId) {
      router.push(routes.futureOrders.detail.to(item.futureOrderId));
    }
  };

  const handlePackQuantityTooltipMouseEnter = () => {
    setShowPackQuantityTooltip(true);
  };

  const handlePackQuantityTooltipMouseLeave = () => {
    setShowPackQuantityTooltip(false);
  };

  const handleQuantityPerFinishedItemTooltipMouseEnter = () => {
    setShowQuantityPerFinishedItemTooltip(true);
  };

  const handleQuantityPerFinishedItemTooltipMouseLeave = () => {
    setShowQuantityPerFinishedItemTooltip(false);
  };

  const handleMultipleProductsTooltipMouseEnter = () => {
    setShowMultipleProductsTooltip(true);
  };

  const handleMultipleProductsTooltipMouseLeave = () => {
    setShowMultipleProductsTooltip(false);
  };

  return (
    <div className="grid grid-cols-2">
      <Link
        href={routes.prebooks.detail.to(item.prebookId)}
        className={classNames('block text-center', hasNote ? '' : 'col-span-2')}
        onClick={handlePrebookClick}
      >
        {formatNumber(quantity)}
      </Link>
      {hasNote && (
        <div className="space-x-2">
          {!item.multipleProducts &&
            floraPackPackQuantity !== boekestynPackQuantity && (
              <HeadlessUI.Popover
                className="relative inline-block cursor-pointer"
                onMouseEnter={handlePackQuantityTooltipMouseEnter}
                onMouseLeave={handlePackQuantityTooltipMouseLeave}
              >
                <>
                  <div>
                    <Icon icon="box-open-full" />
                  </div>
                  <HeadlessUI.Transition
                    as={Fragment}
                    show={showPackQuantityTooltip}
                    enter="transition ease-out duration-200"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="transition ease-in duration-150"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                  >
                    <HeadlessUI.Popover.Panel
                      static
                      className="absolute left-1/2 top-0 z-10 -translate-x-1/2 -translate-y-full transform bg-yellow-50"
                    >
                      <div className="w-auto whitespace-nowrap rounded-lg border p-4 shadow-lg">
                        Prebook Quantity: {formatNumber(item.orderQuantity)}{' '}
                        {floraPackPackQuantity === 1 ? 'pots' : 'cases'}
                        <div className="italic">
                          (FP:{' '}
                          {floraPackPackQuantity === 1
                            ? 'pots'
                            : `${floraPackPackQuantity}/cs`}
                          , Boeks: {boekestynPackQuantity}/cs
                        </div>
                      </div>
                    </HeadlessUI.Popover.Panel>
                  </HeadlessUI.Transition>
                </>
              </HeadlessUI.Popover>
            )}
          {!item.multipleProducts && quantityPerFinishedItem !== 1 && (
            <HeadlessUI.Popover
              className="relative inline-block cursor-pointer"
              onMouseEnter={handleQuantityPerFinishedItemTooltipMouseEnter}
              onMouseLeave={handleQuantityPerFinishedItemTooltipMouseLeave}
            >
              <>
                <div>
                  <Icon icon="merge" rotation={90} />
                </div>
                <HeadlessUI.Transition
                  as={Fragment}
                  show={showQuantityPerFinishedItemTooltip}
                  enter="transition ease-out duration-200"
                  enterFrom="opacity-0"
                  enterTo="opacity-100"
                  leave="transition ease-in duration-150"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <HeadlessUI.Popover.Panel
                    static
                    className="absolute left-1/2 top-0 z-10 -translate-x-1/2 -translate-y-full transform bg-yellow-50"
                  >
                    <div className="w-auto whitespace-nowrap rounded-lg border p-4 shadow-lg">
                      <div className="mb-1 text-3xl">
                        <Icon icon="merge" rotation={90} />
                      </div>
                      Prebook: {item.orderQuantity} cases of{' '}
                      {item.spirePartNumber}
                      <div className="italic">
                        {quantityPerFinishedItem} pots = 1 finished good pot
                      </div>
                    </div>
                  </HeadlessUI.Popover.Panel>
                </HeadlessUI.Transition>
              </>
            </HeadlessUI.Popover>
          )}
          {item.multipleProducts && (
            <HeadlessUI.Popover
              className="relative inline-block cursor-pointer"
              onMouseEnter={handleMultipleProductsTooltipMouseEnter}
              onMouseLeave={handleMultipleProductsTooltipMouseLeave}
            >
              <>
                <div>
                  <Icon icon="boxes" />
                </div>
                <HeadlessUI.Transition
                  as={Fragment}
                  show={showMultipleProductsTooltip}
                  enter="transition ease-out duration-200"
                  enterFrom="opacity-0"
                  enterTo="opacity-100"
                  leave="transition ease-in duration-150"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <HeadlessUI.Popover.Panel
                    static
                    className="absolute left-1/2 top-0 z-10 -translate-x-1/2 -translate-y-full transform bg-yellow-50"
                  >
                    <div className="w-auto whitespace-nowrap rounded-lg border p-4 shadow-lg">
                      <div className="mb-1 text-3xl">
                        <Icon icon="boxes" />
                      </div>
                      Prebook: {formatNumber(item.orderQuantity)}{' '}
                      {quantityPerFinishedItem === 1 ? 'pots' : 'cases'} of{' '}
                      {item.spirePartNumber},
                      <div className="italic">
                        {quantityPerFinishedItem === 1
                          ? ''
                          : `${quantityPerFinishedItem}/cs`}
                      </div>
                      {floraPackPackQuantity !== boekestynPackQuantity && (
                        <div className="italic">
                          (FP: {item.packQuantity}/cs , Boeks:{' '}
                          {boekestynPackQuantity}/cs
                        </div>
                      )}
                    </div>
                  </HeadlessUI.Popover.Panel>
                </HeadlessUI.Transition>
              </>
            </HeadlessUI.Popover>
          )}
        </div>
      )}
      <Alert
        open={!!showFutureOrderWarning}
        icon="question-circle"
        title="Edit Prebook"
        message="Making changes to this Prebook will not update the Future Order. What would you like to do?"
        colour="info"
        confirmButtonText="Continue to Prebook"
        confirm={handleEditPrebook}
        cancelButtonText="Edit Future Order"
        cancel={handleEditFutureOrder}
      />
    </div>
  );
}
