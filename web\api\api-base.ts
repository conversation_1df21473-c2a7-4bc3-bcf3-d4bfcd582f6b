import { BaseQueryFn } from '@reduxjs/toolkit/query';
import Axios, {
  AxiosError,
  AxiosRequestConfig,
  AxiosResponse,
  AxiosResponseHeaders,
} from 'axios';
import { serializeError } from 'serialize-error';
import { msalInstance, scopes } from '@/services/auth';
import {
  createProblemDetails,
  isProblemDetails,
  ProblemDetails,
} from '@/utils/problem-details';

const baseURL = `${process.env.NEXT_PUBLIC_API_URL}api/`;

export function apiUrl(path: string) {
  return `${baseURL}${path}`;
}

export function attachmentUrl(path: string) {
  return `${process.env.NEXT_PUBLIC_API_URL}attachments/${path}`;
}

console.debug('API baseURL', baseURL);

const axios = Axios.create({
  baseURL,
});

axios.interceptors.request.use(async (config) => {
  const account = msalInstance.getActiveAccount();
  if (account != null) {
    const request = { scopes, account },
      result = await msalInstance.acquireTokenSilent(request),
      token = result.accessToken;
    if (config.headers) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
  }

  return config;
});

export class ApiBase {
  protected get<T>(url: string): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      return axios
        .get<T>(url)
        .then((r) => checkResult(r, resolve, reject))
        .catch((e: AxiosError) => handleError(e, reject));
    });
  }
  protected post<T>(url: string, payload: any = {}): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      return axios
        .post<T>(url, payload)
        .then((r) => checkResult(r, resolve, reject))
        .catch((e: AxiosError) => handleError(e, reject));
    });
  }
  protected put<T>(url: string, payload: any = {}): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      return axios
        .put<T>(url, payload)
        .then((r) => checkResult(r, resolve, reject))
        .catch((e: AxiosError) => handleError(e, reject));
    });
  }
  protected delete<T>(url: string, payload: any = {}): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      return axios
        .delete<T>(url, payload)
        .then((r) => checkResult(r, resolve, reject))
        .catch((e: AxiosError) => handleError(e, reject));
    });
  }
  protected patch<T>(url: string, payload: any = {}): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      return axios
        .patch<T>(url, payload)
        .then((r) => checkResult(r, resolve, reject))
        .catch((e: AxiosError) => handleError(e, reject));
    });
  }
}

function checkResult<T>(
  response: AxiosResponse<T>,
  resolve: (data: T) => void,
  reject: (reason?: any) => void
): T | void {
  if (!isSuccess(response)) {
    if (response) {
      if (isProblemDetails(response.data)) {
        return reject(response.data);
      } else if (typeof response.data === 'string') {
        return reject(createProblemDetails(response.data));
      }
    }

    if (response && response.data) {
      return reject(response.data);
    }

    return reject(response.data);
  }

  return resolve(response.data);
}

export function handleError(e: AxiosError, reject: (reason?: any) => void) {
  console.error(e);

  if (e.response) {
    if (isProblemDetails(e.response.data)) {
      return reject(e.response.data);
    } else if (typeof e.response.data === 'string') {
      return reject(createProblemDetails(e.response.data));
    }
  }

  if (e.response && e.response.data) {
    return reject(e.response.data);
  }

  return reject(e.message);
}

export const axiosBaseQuery =
  (
    baseUrl: string
  ): BaseQueryFn<
    {
      url: string;
      method?: AxiosRequestConfig['method'];
      data?: AxiosRequestConfig['data'];
    },
    unknown,
    ProblemDetails
  > =>
  async ({ url, method = 'GET', data }) =>
    new Promise((resolve) =>
      axios({ url: baseUrl + url, method, data })
        .then((response) => {
          if (!isSuccess(response)) {
            if (isAxiosError(response)) {
              if (response.response) {
                if (isProblemDetails(response.response.data)) {
                  return resolve({ error: response.response.data });
                }

                if (typeof response.response.data === 'string') {
                  return resolve({
                    error: createProblemDetails(response.response.data),
                  });
                }
              }
            } else {
              if (response) {
                if (isError(response)) {
                  const error = serializeError(response);
                  return resolve({
                    error: createProblemDetails(
                      error.name || 'Error',
                      error.message
                    ),
                  });
                }

                if (response.data && isProblemDetails(response.data)) {
                  return resolve({ error: response.data });
                }

                if (typeof response.data === 'string') {
                  return resolve({
                    error: createProblemDetails(response.data),
                  });
                }
              }

              if (response && response.data) {
                return resolve({ error: response.data });
              }

              return resolve({ error: response.data });
            }
          }

          return resolve({ data: (response as AxiosResponse).data });
        })
        .catch((e: AxiosError) => {
          console.error(e);

          if (e.response) {
            if (e.response.status === 401) {
              return;
            }

            if (isProblemDetails(e.response.data)) {
              return resolve({ error: e.response.data });
            }

            if (typeof e.response.data === 'string') {
              return resolve({ error: createProblemDetails(e.response.data) });
            }
          }

          return resolve({ error: createProblemDetails(e.message) });
        })
    );

function isSuccess(response: AxiosResponse<any> | AxiosError | Error) {
  return (
    (response as AxiosResponse).status >= 200 &&
    (response as AxiosResponse).status < 300
  );
}

function isAxiosError(
  response: AxiosResponse<any> | AxiosError | Error
): response is AxiosError {
  return 'isAxiosError' in response && response.isAxiosError;
}

export function isError(response: any): response is Error {
  return response instanceof Error;
}

export function getFilename(headers: AxiosResponseHeaders) {
  const contentDisposition: string = headers['content-disposition'] || '',
    dispositionParts = contentDisposition.split(';'),
    filenamePart =
      dispositionParts.find((a) => a.indexOf('filename') !== -1) || '',
    filename = filenamePart.replace(' filename=', '');

  return filename;
}

export function downloadFile(blob: Blob, filename: string) {
  const a = document.createElement('a');
  a.download = filename;
  a.style.display = 'none';
  a.href = URL.createObjectURL(blob);
  document.body.appendChild(a);
  a.click();
  URL.revokeObjectURL(a.href);
  document.body.removeChild(a);
}

export default axios;
