import { useItemSummaryQuery } from 'api/future-orders-service';
import * as models from 'api/models/future-orders';
import { Icon } from '@/components/icon';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { classNames } from '@/utils/class-names';
import { formatNumber } from '@/utils/format';
import { createProblemDetails } from '@/utils/problem-details';
import { selectFutureOrder } from './future-order-detail-slice';
import {
  selectSearch,
  selectFilter,
  selectSort,
  selectSortDescending,
  selectFutureOrders,
  selectFutureOrderCustomers,
  selectFutureOrderShipTos,
  setSearch,
  setCustomerFilter,
  setShipToFilter,
  setSort,
  setStep,
  setError,
  clearError,
  selectFutureOrderDetail,
} from './split-order-slice';
import { SplitOrderFutureOrderRow } from './split-order-future-order-row';

interface SplitOrderDestinationExistingProps {
  onClose: () => void;
}

export function SplitOrderDestinationExisting({
  onClose,
}: SplitOrderDestinationExistingProps) {
  const dispatch = useAppDispatch(),
    search = useAppSelector(selectSearch),
    filter = useAppSelector(selectFilter),
    sort = useAppSelector(selectSort),
    sortDescending = useAppSelector(selectSortDescending),
    futureOrders = useAppSelector(selectFutureOrders),
    filterCustomers = useAppSelector(selectFutureOrderCustomers),
    filterShipTos = useAppSelector(selectFutureOrderShipTos),
    futureOrderDetail = useAppSelector(selectFutureOrderDetail),
    mainFutureOrder = useAppSelector(selectFutureOrder),
    { refetch } = useItemSummaryQuery({
      startDate: mainFutureOrder?.requiredDate,
      endDate: mainFutureOrder?.requiredDate,
    }),
    collection = futureOrders.slice(0, 100);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setSearch(e.target.value));
  };

  const handleSearchKeyUp = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      refetch();
    }
  };

  const handleCustomerFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setCustomerFilter(e.target.value || null));
  };

  const handleShipToFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setShipToFilter(e.target.value || null));
  };

  const handleCancelClick = () => {
    onClose();
  };

  const handleBackClick = () => {
    dispatch(setStep(1));
  };

  const handleNextClick = () => {
    dispatch(clearError());

    if (!futureOrderDetail) {
      return dispatch(
        setError(
          createProblemDetails(
            'Please select the Future Order to move the items to.'
          )
        )
      );
    }

    dispatch(setStep(3));
  };

  const handleColumnSort = (sortProp: keyof models.FutureOrderSummaryItem) => {
    const descending = sortProp === sort ? !sortDescending : false;
    dispatch(setSort({ sort: sortProp, sortDescending: descending }));
  };

  interface HeaderButtonProps {
    text: string;
    propName: keyof models.FutureOrderSummaryItem;
  }

  const HeaderButton = ({ text, propName }: HeaderButtonProps) => (
    <button
      type="button"
      className="group inline-flex"
      onClick={() => handleColumnSort(propName)}
    >
      {text}
      <span
        className={classNames(
          'ml-2 flex-none rounded text-gray-400 group-hover:visible group-focus:visible',
          sort !== propName && 'invisible'
        )}
      >
        <Icon
          icon={sortDescending ? 'chevron-down' : 'chevron-up'}
          className="h-5 w-5"
          aria-hidden="true"
        />
      </span>
    </button>
  );

  return (
    <div className="flex flex-grow flex-col overflow-y-auto">
      <div className="flex flex-grow flex-col overflow-y-auto rounded border p-4">
        <div className="flex flex-col">
          <p>Search for an existing Future Order to add items from.</p>
          <div className="my-4 flex w-full flex-col gap-2 rounded-sm text-xs">
            <div className="grid grid-cols-4 gap-x-4">
              <div>
                <label htmlFor="customer-filter">Customer</label>
                <select
                  id="customer-filter"
                  value={filter.customer || ''}
                  onChange={handleCustomerFilterChange}
                  className="block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                >
                  <option value="">All Customers</option>
                  {filterCustomers.map((customer) => (
                    <option key={customer}>{customer}</option>
                  ))}
                </select>
              </div>
              <div>
                <label htmlFor="ship-to-filter">Ship To</label>
                <select
                  id="ship-to-filter"
                  value={filter.shipTo || ''}
                  onChange={handleShipToFilterChange}
                  className="block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                >
                  <option value="">All Ship Tos</option>
                  {filterShipTos.map((shipTo) => (
                    <option key={shipTo}>{shipTo}</option>
                  ))}
                </select>
              </div>
              <div className="col-span-2">
                <label className="block" htmlFor="search">
                  Search
                </label>
                <input
                  type="search"
                  id="search"
                  name="search"
                  value={search}
                  onChange={handleSearchChange}
                  onKeyUp={handleSearchKeyUp}
                  className="w-full text-xs"
                  placeholder="Search"
                  autoComplete="off"
                />
              </div>
            </div>
          </div>
        </div>
        <div className="flex flex-grow flex-row overflow-y-auto">
          <div className="h-100 flex w-full flex-col overflow-y-auto rounded border">
            <div className="flex-grow overflow-y-auto">
              <table className="min-w-full divide-y divide-gray-300 text-xs">
                <thead>
                  <tr className="sticky top-0 z-10">
                    <th className="bg-gray-100 p-2 text-center">
                      <HeaderButton text="Id" propName="id" />
                    </th>
                    <th className="bg-gray-100 p-2">
                      <HeaderButton text="Customer" propName="customer" />
                    </th>
                    <th className="bg-gray-100 p-2">
                      <HeaderButton text="Ship To" propName="shipTo" />
                    </th>
                    <th className="bg-gray-100 p-2 text-center">
                      <HeaderButton text="Req Date" propName="date" />
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {collection.map((order) => (
                    <SplitOrderFutureOrderRow
                      key={order.id}
                      futureOrder={order}
                    />
                  ))}
                </tbody>
              </table>
            </div>
            <div className="w-100 flex flex-row bg-gray-50 px-6 py-3">
              <div className="flex-1 text-center text-xs">
                {!!futureOrders.length &&
                  `${
                    futureOrders.length
                      ? formatNumber(futureOrders.length)
                      : 'No'
                  } order${futureOrders.length === 1 ? '' : 's'} found`}
                {futureOrders.length > collection.length &&
                  ` (limited to ${collection.length})`}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="mt-4 flex justify-end">
        <button
          type="button"
          className="btn-secondary px-8 text-lg"
          onClick={handleCancelClick}
        >
          Cancel
        </button>
        <button
          type="button"
          className="btn-secondary ml-2 px-8 text-lg disabled:px-8 disabled:text-lg"
          onClick={handleBackClick}
        >
          <Icon icon="chevron-left" />
          &nbsp; Back
        </button>
        <button
          type="button"
          className="btn-primary ml-2 px-8 text-lg disabled:px-8 disabled:text-lg"
          onClick={handleNextClick}
        >
          Next &nbsp;
          <Icon icon="chevron-right" />
        </button>
      </div>
    </div>
  );
}
