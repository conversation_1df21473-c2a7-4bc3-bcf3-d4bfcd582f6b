import { useMemo } from 'react';
import { formatNumber } from '@/utils/format';

interface ItemRequirementsFooterCellProps {
  date: string;
  items: string[];
  itemDateMap: Map<string, number>;
}

export function ItemRequirementsFooterCell({
  date,
  items,
  itemDateMap,
}: ItemRequirementsFooterCellProps) {
  const colTotal = useMemo(
    () =>
      items.reduce((memo, item) => {
        const key = `${item}_${date}`,
          itemAmount = itemDateMap.get(key) || 0;
        return memo + itemAmount;
      }, 0),
    [date, items, itemDateMap]
  );

  return (
    <th className="px-3 py-3.5 text-center text-sm font-semibold text-gray-900">
      {formatNumber(colTotal)}
    </th>
  );
}
