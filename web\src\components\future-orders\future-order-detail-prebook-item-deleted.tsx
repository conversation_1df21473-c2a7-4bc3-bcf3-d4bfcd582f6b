import * as prebooks from 'api/models/prebooks';
import { Icon } from '@/components/icon';
import { classNames } from '@/utils/class-names';
import { formatNumber } from '@/utils/format';

interface FutureOrderDetailPrebookItemDeletedProps {
  item: prebooks.PrebookEmailItem;
  prebookDeleted: boolean;
}

export function FutureOrderDetailPrebookItemDeleted({
  item,
  prebookDeleted,
}: FutureOrderDetailPrebookItemDeletedProps) {
  const cellClassName =
    'whitespace-nowrap px-1 py-1 border-bottom-0 bg-gray-50 text-gray-500 italic';

  return (
    <tr className="text-xs">
      <td className={cellClassName}>
        <div className="flex flex-row">
          <div className="invisible mx-2">
            <Icon icon="tombstone-blank" />
          </div>
          <div className="flex-grow">{item.spirePartNumber}</div>
        </div>
      </td>
      <td className={cellClassName}>
        {item.description}
        {!prebookDeleted && (
          <div className="font-semibold">
            Item was deleted from the Prebook after being sent to the Grower.
          </div>
        )}
      </td>
      <td className={classNames(cellClassName, 'text-center')}></td>
      <td className={classNames(cellClassName, 'text-center')}>
        <input
          type="text"
          className="mx-auto w-full max-w-[100px] cursor-default rounded-md border-transparent bg-gray-50 text-right text-xs"
          value={formatNumber(item.orderQuantity)}
          disabled
        />
      </td>
      <td className={classNames(cellClassName, 'text-center')}>
        {item.potCover}
      </td>
      <td className={classNames(cellClassName, 'text-center')}>{item.upc}</td>
      <td className={classNames(cellClassName, 'text-center')}>
        {item.dateCode}
      </td>
      <td className={classNames(cellClassName, 'text-center')}>
        <Icon icon={item.weightsAndMeasures ? 'check-square' : 'square'} />
      </td>
      <td className={classNames(cellClassName, 'text-center')}>
        {item.retail}
      </td>
    </tr>
  );
}
