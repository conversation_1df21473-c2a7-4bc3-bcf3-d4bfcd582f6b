import { Fragment, useState } from 'react';
import Link from 'next/link';
import { Popover, Transition } from '@headlessui/react';
import * as models from 'api/models/prebooks';
import { useAppDispatch } from '@/services/hooks';
import { Icon } from '@/components/icon';
import { routes } from '@/services/routes';
import { classNames } from '@/utils/class-names';
import { formatDate, formatNumber } from '@/utils/format';
import { sortBy } from '@/utils/sort';
import { weekFromId } from '@/utils/weeks';
import { setGrowerConfirmed } from './prebook-detail-slice';

export interface BlanketItemItemProps {
  item: models.BlanketItemListItem;
  hasSeasons: boolean;
  refresh: () => void;
}

export function BlanketItemItem({
  item,
  hasSeasons,
  refresh,
}: BlanketItemItemProps) {
  const dispatch = useAppDispatch(),
    [tooltipOpen, setTooltipOpen] = useState(false),
    [showBookings, setShowBookings] = useState(false),
    bookingItems = item.bookingItems
      .map((i) => ({ ...i }))
      .sort(sortBy('requiredDate')),
    remaining = item.blanketQuantity - item.bookedQuantity,
    week = weekFromId(item.blanketWeekId);

  const handleToggleBookingsClick = () => {
    setShowBookings(!showBookings);
  };

  const handleTooltipMouseEnter = () => {
    setTooltipOpen(true);
  };

  const handleTooltipMouseLeave = () => {
    setTooltipOpen(false);
  };

  const handleSetGrowerConfirmedClick = async () => {
    await dispatch(setGrowerConfirmed(item.prebookId));
    refresh();
  };

  return (
    <>
      <tr className={classNames(!showBookings && 'border-b-2 border-gray-300')}>
        <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-700">
          <Link href={routes.prebooks.detail.to(item.prebookId)}>
            {formatNumber(item.prebookId, '00000')}
          </Link>
        </td>
        <td className="whitespace-nowrap px-3 py-4 text-left text-sm text-gray-700">
          <Popover
            className="-z-1 relative inline-block cursor-pointer"
            onMouseEnter={handleTooltipMouseEnter}
            onMouseLeave={handleTooltipMouseLeave}
          >
            <>
              <Popover.Button as="div" className="font-lg  px-2 py-1">
                <Icon
                  icon={
                    !item.sent
                      ? 'triangle-exclamation'
                      : !item.confirmed
                      ? 'question-circle'
                      : 'check-circle'
                  }
                  className={
                    !item.sent
                      ? 'text-red-600'
                      : !item.confirmed
                      ? 'text-yellow-600'
                      : 'text-green-600'
                  }
                />
              </Popover.Button>
              <Transition
                as={Fragment}
                show={tooltipOpen}
                enter="transition ease-out duration-200"
                enterFrom="opacity-0 translate-y-1"
                enterTo="opacity-100 translate-y-0"
                leave="transition ease-in duration-150"
                leaveFrom="opacity-100 translate-y-0"
                leaveTo="opacity-0 translate-y-1"
              >
                <Popover.Panel
                  static
                  className="absolute left-[25px] z-10 -translate-y-1/2 transform bg-white"
                >
                  <div className="rounded-lg border p-4 shadow-lg">
                    <p className="my-2 text-xs text-gray-500">
                      Created by{' '}
                      <span className="font-medium">{item.createdBy}</span> on{' '}
                      <span className="font-medium">
                        {formatDate(item.created)}
                      </span>{' '}
                      @{' '}
                      <span className="font-medium">
                        {formatDate(item.created, 'h:mm a')}
                      </span>
                    </p>
                    {item.created !== item.modified && (
                      <p className="my-2 text-xs text-gray-500">
                        Updated by{' '}
                        <span className="font-medium">{item.modifiedBy}</span>{' '}
                        on{' '}
                        <span className="font-medium">
                          {formatDate(item.modified)}
                        </span>{' '}
                        @{' '}
                        <span className="font-medium">
                          {formatDate(item.modified, 'h:mm a')}
                        </span>
                      </p>
                    )}
                    {!!item.sent && (
                      <p className="my-2 text-xs text-gray-500">
                        Last sent by{' '}
                        <span className="font-medium">{item.sentBy}</span> on{' '}
                        <span className="font-medium">
                          {formatDate(item.sent)}
                        </span>{' '}
                        @{' '}
                        <span className="font-medium">
                          {formatDate(item.sent, 'h:mm a')}
                        </span>
                      </p>
                    )}
                    {!item.sent && (
                      <p className="my-2 text-xs font-bold italic text-red-500">
                        Not sent to Grower
                      </p>
                    )}
                    {!!item.confirmed && (
                      <p className="my-2 text-xs text-gray-500">
                        Grower Confirmed by{' '}
                        <span className="font-medium">{item.confirmedBy}</span>{' '}
                        on{' '}
                        <span className="font-medium">
                          {formatDate(item.confirmed)}
                        </span>{' '}
                        @{' '}
                        <span className="font-medium">
                          {formatDate(item.confirmed, 'h:mm a')}
                        </span>
                      </p>
                    )}
                    {!!item.sent && !item.confirmed && (
                      <p className="my-2 text-xs font-bold italic text-yellow-500">
                        <div className="mr-4 inline-block">
                          Grower has not confirmed
                        </div>
                        <button
                          type="button"
                          onClick={handleSetGrowerConfirmedClick}
                          className="btn-secondary px-2 py-1 text-xs"
                        >
                          Confirm
                        </button>
                      </p>
                    )}
                  </div>
                </Popover.Panel>
              </Transition>
            </>
          </Popover>
        </td>
        <td className="whitespace-nowrap px-3 py-4 text-left align-top text-sm text-gray-700">
          {!!bookingItems.length && (
            <button
              type="button"
              className="btn-secondary p-2 text-xs focus:ring-0"
              onClick={handleToggleBookingsClick}
            >
              <Icon icon={showBookings ? 'chevron-up' : 'chevron-down'} />
            </button>
          )}
        </td>
        <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-700">
          {item.vendor}
        </td>
        <td className="whitespace-nowrap px-3 py-4 text-left text-sm text-gray-700">
          {item.spirePartNumber}
          <br />
          <span className="italic text-gray-400">{item.description}</span>
        </td>
        <td className="whitespace-nowrap px-3 py-4 text-center text-sm text-gray-700">
          {!!item.blanketStartDate && (
            <div className="whitespace-nowrap">
              {`${formatDate(item.blanketStartDate, 'MMM d')} - `}
              {formatDate(item.requiredDate, 'MMM d')}
            </div>
          )}
          {!item.blanketStartDate && (
            <div>{formatDate(item.requiredDate, 'MMM d')}</div>
          )}
          {!!week && (
            <div className="italic text-gray-400">Week {week.week}</div>
          )}
        </td>
        <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-700">
          {item.salesperson}
        </td>
        <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-700">
          {item.customer}
          {!!item.shipTo && (
            <>
              <br />
              <span className="italic text-gray-500">{item.shipTo}</span>
            </>
          )}
        </td>
        {hasSeasons && (
          <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-700">
            {item.season}
          </td>
        )}
        <td className="whitespace-nowrap px-3 py-4 text-center text-sm text-gray-700">
          {!showBookings && formatNumber(item.blanketQuantity)}
        </td>
        <td className="whitespace-nowrap px-3 py-4 text-center text-sm text-gray-700">
          {!showBookings && formatNumber(item.bookedQuantity)}
        </td>
        <td
          className={classNames(
            'whitespace-nowrap px-3 py-4 text-center text-sm',
            !showBookings && remaining < 0 ? 'text-red-600' : 'text-gray-700'
          )}
        >
          {!showBookings && formatNumber(remaining)}
        </td>
      </tr>
      {!!showBookings && (
        <>
          {bookingItems.map((booking) => (
            <tr key={booking.id}>
              <td
                colSpan={5}
                className="whitespace-nowrap p-2 text-right text-xs"
              >
                {!!booking.prebookId && (
                  <Link href={routes.prebooks.detail.to(booking.prebookId)}>
                    Edit Prebook
                  </Link>
                )}
                {!!booking.futureOrderId && (
                  <Link
                    href={routes.futureOrders.detail.to(booking.futureOrderId)}
                  >
                    Edit Future Order
                  </Link>
                )}
              </td>
              <td
                className={classNames(
                  'whitespace-nowrap border-b-2 border-white p-2 text-left text-xs text-gray-600',
                  booking.prebookId && 'bg-blue-100',
                  booking.futureOrderId && 'bg-orange-100'
                )}
              >
                {formatDate(booking.requiredDate, 'MMM d')}
              </td>
              <td
                className={classNames(
                  'whitespace-nowrap border-b-2 border-white p-2 text-left text-xs text-gray-600',
                  booking.prebookId && 'bg-blue-100',
                  booking.futureOrderId && 'bg-orange-100'
                )}
              >
                {booking.salesperson}
              </td>
              <td
                className={classNames(
                  'whitespace-nowrap border-b-2 border-white p-2 text-left text-xs text-gray-600',
                  booking.prebookId && 'bg-blue-100',
                  booking.futureOrderId && 'bg-orange-100'
                )}
              >
                {booking.customer}
                {!!booking.shipTo && (
                  <>
                    <br />
                    <span className="italic">{booking.shipTo}</span>
                  </>
                )}
              </td>
              {hasSeasons && (
                <td
                  className={classNames(
                    'whitespace-nowrap border-b-2 border-white p-2 text-left text-xs text-gray-600',
                    booking.prebookId && 'bg-blue-100',
                    booking.futureOrderId && 'bg-orange-100'
                  )}
                >
                  {booking.season}
                </td>
              )}
              <td
                className={classNames(
                  'whitespace-nowrap border-b-2 border-white p-2 text-left text-xs text-gray-600',
                  booking.prebookId && 'bg-blue-100',
                  booking.futureOrderId && 'bg-orange-100'
                )}
              >
                &nbsp;
              </td>
              <td
                className={classNames(
                  'whitespace-nowrap border-b-2 border-white p-2 text-center text-xs text-gray-600',
                  booking.prebookId && 'bg-blue-100',
                  booking.futureOrderId && 'bg-orange-100'
                )}
              >
                {formatNumber(booking.orderQuantity)}
              </td>
              <td
                className={classNames(
                  'whitespace-nowrap border-b-2 border-white p-2 text-left text-xs text-gray-600'
                )}
              >
                &nbsp;
              </td>
            </tr>
          ))}
          <tr className="border-b-2 border-gray-300">
            <td
              colSpan={3}
              className="whitespace-nowrap p-2 text-right align-top text-sm text-gray-700"
            >
              &nbsp;
            </td>
            <td
              colSpan={hasSeasons ? 6 : 5}
              className="whitespace-nowrap bg-gray-200 p-2 text-right text-sm italic text-gray-700"
            >
              Blanket Total:
            </td>
            <td className="whitespace-nowrap bg-gray-200 p-2 text-center text-sm italic text-gray-700">
              {formatNumber(item.blanketQuantity)}
            </td>
            <td className="whitespace-nowrap bg-gray-200 p-2 text-center text-sm italic text-gray-700">
              {formatNumber(item.bookedQuantity)}
            </td>
            <td
              className={classNames(
                'whitespace-nowrap bg-gray-200 p-2 text-center text-sm italic',
                remaining < 0 ? 'text-red-600' : 'text-gray-700'
              )}
            >
              {formatNumber(remaining)}
            </td>
          </tr>
        </>
      )}
    </>
  );
}
