import React, { Fragment, useRef, useState } from 'react';
import { DateTime } from 'luxon';
import { Dialog, Transition } from '@headlessui/react';
import { useSeasonsQuery, useAddSeasonMutation } from 'api/prebooks-service';
import * as prebooks from 'api/models/prebooks';
import { Icon } from '@/components/icon';
import { Error } from '@/components/error';
import { equals } from '@/utils/equals';
import { ProblemDetails } from '@/utils/problem-details';

interface CreateSeasonProps {
  open: boolean;
  cancel: () => void;
  confirm: (value: prebooks.Season) => void;
}

export function CreateSeason({ open, cancel, confirm }: CreateSeasonProps) {
  const { data: seasons, refetch: refetchSeasons } = useSeasonsQuery(),
    [addSeason, { error: addSeasonError }] = useAddSeasonMutation(),
    [name, setName] = useState(''),
    [seasonDate, setSeasonDate] = useState(
      DateTime.now().toFormat('yyyy-MM-dd')
    ),
    [error, setError] = useState<ProblemDetails | null>(null),
    inputRef = useRef(null);

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setName(e.target.value);
  };

  const handleSeasonDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSeasonDate(e.target.value);
  };

  const handleNameKeydown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && name && seasonDate) {
      save();
    }
  };

  const handleSaveClick = () => {
    save();
  };

  const save = async () => {
    if (!name || !seasonDate) {
      return;
    }

    if (!seasons?.some((s) => equals(s.name, name))) {
      try {
        const response = await addSeason({ name, seasonDate });
        if ('error' in response) {
          return;
        }

        refetchSeasons();
      } catch (e) {
        setError(e as ProblemDetails);
      }
    }

    setName('');
    setSeasonDate(DateTime.now().toFormat('yyyy-MM-dd'));
    setError(null);

    confirm({ name, seasonDate });
  };

  const handleClearError = () => {
    setError(null);
  };

  return (
    <Transition.Root show={open} as={Fragment}>
      <Dialog
        as="div"
        className="relative z-30"
        onClose={cancel}
        initialFocus={inputRef}
      >
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-sm sm:p-6">
                <div className="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
                  <button
                    type="button"
                    className="btn-secondary px-2 py-1"
                    onClick={cancel}
                  >
                    <span className="sr-only">Close</span>
                    <Icon icon="x" aria-hidden="true" />
                  </button>
                </div>
                <div>
                  <div className="mt-3 text-center sm:mt-5">
                    <Dialog.Title
                      as="h3"
                      className="text-lg font-medium leading-6 text-gray-900"
                    >
                      New Season
                    </Dialog.Title>
                    <div className="mt-2 grid grid-cols-2">
                      <label
                        htmlFor="season-name"
                        className="my-auto text-sm text-gray-500"
                      >
                        Season Name
                      </label>
                      <input
                        type="text"
                        id="season-name"
                        value={name}
                        onChange={handleNameChange}
                        onKeyDown={handleNameKeydown}
                        ref={inputRef}
                        className="my-2 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      />
                      <label
                        htmlFor="season-date"
                        className="my-auto text-sm text-gray-500"
                      >
                        Season Date
                      </label>
                      <input
                        type="date"
                        max="2050-01-01"
                        id="season-date"
                        value={seasonDate}
                        onChange={handleSeasonDateChange}
                        className="my-2 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      />
                    </div>
                    <div className="col-span-2 text-right">
                      <button
                        type="button"
                        onClick={cancel}
                        className="btn-secondary mr-2"
                      >
                        Cancel
                      </button>
                      <button
                        type="button"
                        onClick={handleSaveClick}
                        className="btn-primary"
                        disabled={!name || !seasonDate}
                      >
                        Save
                      </button>
                    </div>
                    <Error
                      error={error}
                      clear={handleClearError}
                      containerClasses="col-span-2"
                    />
                    <Error
                      error={addSeasonError as ProblemDetails | undefined}
                      containerClasses="col-span-2"
                    />
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
}
