import { useMemo } from 'react';
import { formatNumber } from '@/utils/format';

interface ItemRequirementsRowCellProps {
  item: string;
  date: string;
  itemDateMap: Map<string, number>;
  handleToggle: () => void;
}

export function ItemRequirementsRowCell({
  item,
  date,
  itemDateMap,
  handleToggle,
}: ItemRequirementsRowCellProps) {
  const key = `${item}_${date}`,
    quantity = useMemo(() => itemDateMap?.get(key) || 0, [key, itemDateMap]);

  return (
    <td
      className="whitespace-nowrap px-3 py-4 text-center text-sm text-gray-500"
      onClick={handleToggle}
    >
      {quantity > 0 ? formatNumber(quantity) : ''}
    </td>
  );
}
