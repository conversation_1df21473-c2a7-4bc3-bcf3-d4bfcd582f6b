import { useAppSelector } from '@/services/hooks';
import { equals } from '@/utils/equals';
import { formatNumber } from '@/utils/format';
import { Week, weekFromDate } from '@/utils/weeks';
import { selectProductionOrders } from './sales-slice';

interface ProductionItemProps {
  week: Week;
  customer: string;
}

export function ProductionItem({ week, customer }: ProductionItemProps) {
  const productionOrders = useAppSelector(selectProductionOrders),
    quantity = productionOrders
      .filter(
        (p) =>
          weekFromDate(p.flowerDate)?.weekId === week.weekId &&
          equals(p.customer?.abbreviation, customer)
      )
      .reduce((total, p) => total + p.cases, 0);

  return (
    <th className="border bg-gray-100 font-semibold text-gray-900">
      {formatNumber(quantity)}
    </th>
  );
}
