import { dequal } from 'dequal';
import * as prebooks from 'api/models/prebooks';
import { formatDate } from '@/utils/format';
import { sortBy } from '@/utils/sort';

const sortByCreated = sortBy('created');

export function isDirty(
  prebook: prebooks.PrebookDetail,
  emails: prebooks.PrebookEmail[]
) {
  if (prebook.id < 1) {
    return true;
  }

  const latest = emails
    .sort(sortByCreated)
    .findLast((e) => e.prebookId === prebook.id);

  if (!latest) {
    return true;
  }

  const email: ComparePrebook = {
      prebookId: latest.prebookId,
      vendorName: latest.vendorName,
      requiredDate: latest.requiredDate ? formatDate(latest.requiredDate) : '',
      seasonName: latest.seasonName ?? '',
      comments: latest.comments ?? '',
      items: latest.items.map(
        ({
          prebookItemId,
          spirePartNumber,
          description,
          orderQuantity,
          boxCode,
          dateCode,
          potCover,
          upc,
          weightsAndMeasures,
          retail,
          comments,
        }) => ({
          prebookItemId,
          spirePartNumber,
          description,
          orderQuantity,
          boxCode: boxCode ?? '',
          dateCode: dateCode ?? '',
          potCover: potCover ?? '',
          upc: upc ?? '',
          weightsAndMeasures,
          retail: retail ?? '',
          comments: comments ?? '',
        })
      ),
    },
    compare: ComparePrebook = {
      prebookId: prebook.id,
      vendorName: prebook.vendorName ?? '',
      requiredDate: prebook.requiredDate
        ? formatDate(prebook.requiredDate)
        : '',
      seasonName: prebook.seasonName ?? '',
      comments: prebook.comments ?? '',
      items: prebook.items.map(
        ({
          id,
          spirePartNumber,
          description,
          orderQuantity,
          dateCode,
          hasPotCover,
          potCover,
          upc,
          weightsAndMeasures,
          retail,
          comments,
        }) => ({
          prebookItemId: id,
          spirePartNumber,
          description,
          orderQuantity,
          boxCode: prebook.boxCode ?? '',
          dateCode: dateCode ?? '',
          potCover: hasPotCover ? potCover ?? '' : '',
          upc: upc ?? '',
          weightsAndMeasures,
          retail: retail ?? '',
          comments: comments ?? '',
        })
      ),
    },
    dirty = !dequal(compare, email);

  if (dirty) {
    console.log('Dirty prebook detected:', compare);
    console.log('Original prebook:', email);

    const { items: b, ...rest } = compare,
      { items: a, ...emailRest } = email;

    if (!dequal(rest, emailRest)) {
      console.log('Dirty prebook rest:', rest);
      console.log('Original prebook rest:', emailRest);
    } else {
      if (b.length !== a.length) {
        console.log('Different number of items:', b.length, a.length);
      } else {
        for (let i = 0; i < b.length; i++) {
          if (!dequal(b[i], a[i])) {
            console.log('Dirty prebook item:', b[i]);
            console.log('Original prebook item:', a[i]);
          }
        }
      }
    }
  }
  return dirty;
}

interface ComparePrebook {
  prebookId: number;
  vendorName: string;
  requiredDate: string;
  seasonName: string;
  comments: string;

  items: ComparePrebookItem[];
}

interface ComparePrebookItem {
  prebookItemId: number;
  spirePartNumber: string;
  description: string;
  orderQuantity: number;
  boxCode: string;
  dateCode: string;
  potCover: string;
  upc: string;
  weightsAndMeasures: boolean;
  retail: string;
  comments: string;
}
